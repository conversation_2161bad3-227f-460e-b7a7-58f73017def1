# Use an official ARM or M1 compatible Node.js runtime as a parent image
FROM --platform=linux/arm64 node:18.13

# Set the maintainer information
LABEL maintainer="<EMAIL>"

# Install nginx and gettext (for envsubst command)
RUN apt-get update && \
    apt-get install -y nginx && \
    apt-get install -y gettext

# Install n (Node version manager) and ensure the correct node and npm versions are active
RUN npm install -g n
RUN n list 18.13.0
RUN node --version
RUN npm --version

# Install TypeScript globally
RUN npm install -g typescript

# Set the working directory for the app
WORKDIR /tmp/nms-frontend

# Copy the package.json file for the app and install dependencies
COPY ./package.json /tmp/nms-frontend/package.json
COPY ./package-lock.json /tmp/nms-frontend/package-lock.json
RUN npm ci --verbose

# Copy the rest of the application source
COPY . .

# Build the application
RUN npm run build --verbose

# Copy the built application files to the nginx directory
RUN cp -r dist/* /usr/share/nginx/html/

# Copy necessary environment files to the nginx directory
COPY cicd/env.sh /usr/share/nginx/html/
COPY cicd/.env /usr/share/nginx/html/

# Copy nginx configuration and entrypoint script
COPY cicd/nms-frontend.template /etc/nginx/templates/
COPY cicd/entrypoint.sh /entrypoint.sh

# Expose port 8000
EXPOSE 8000

# Provide execution permissions for the environment script
RUN chmod 777 /usr/share/nginx/html/env.sh

# Provide execution permissions for the entrypoint script
RUN chmod 777 /entrypoint.sh

# Reset the working directory
WORKDIR /
