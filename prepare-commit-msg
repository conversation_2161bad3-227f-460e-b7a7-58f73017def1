#!/bin/sh

# Name of the file where the commit message is stored
COMMIT_MSG_FILE=$1

# The type of commit message being prepared
COMMIT_SOURCE=$2

# Extract the current branch name
BR<PERSON>CH_NAME=$(git branch --show-current)

# Regex to match the Jira Ticket ID in the branch name
JIRA_TICKET_REGEX="([A-Za-z]+-[0-9]+)"

if [[ $BRANCH_NAME =~ $JIRA_TICKET_REGEX ]]; then
  JIRA_TICKET_ID=${BASH_REMATCH[1]}

  # Read the original commit message
  ORIGINAL_COMMIT_MSG=$(cat $COMMIT_MSG_FILE)

  # Prepend the Jira Ticket ID to the commit message
  echo "$JIRA_TICKET_ID: $ORIGINAL_COMMIT_MSG" > $COMMIT_MSG_FILE
fi
