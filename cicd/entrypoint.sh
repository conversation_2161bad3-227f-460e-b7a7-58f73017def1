#!/bin/bash

# Run the shell script env.sh with the following options:
# -e: Exit immediately if any command in the script returns a non-zero exit status.
# -x: Print each command before executing it

bash -ex /usr/share/nginx/html/env.sh

#Building the nginx config file
#This update is to ensure `$uri` remains untouched during environment variable substitution.
# envsubst "$NMS_URL $NMS_MC_URL $NMS_CELL_URL $NMS_INV_CELL_URL $NMS_ORCH_URL" </etc/nginx/templates/nms-frontend.template >/etc/nginx/conf.d/nms-frontend.conf && nginx -g 'daemon off;'
envsubst "$NMS_URL $NMS_MC_URL $NMS_CELL_URL $NMS_INV_CELL_URL $NMS_ORCH_URL $NMS_RAD_URL" </etc/nginx/templates/nms-frontend.template >/etc/nginx/conf.d/nms-frontend.conf && nginx -g 'daemon off;'
