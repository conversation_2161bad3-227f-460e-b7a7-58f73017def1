#!/bin/bash

# Define the directory where the env-config.js and .env files are located
ENV_DIR=/usr/share/nginx/html
OUTPUT_DIR=/usr/share/nginx/html/assets

# Remove any existing env-config.js and create an empty one
rm -rf $OUTPUT_DIR/env-config.js
touch $OUTPUT_DIR/env-config.js

# Add an assignment to start the window._env_ object in JavaScript
echo "window._env_ = {" >>$OUTPUT_DIR/env-config.js

# Read each line in .env file
# Each line represents key=value pairs
while IFS= read -r line || [[ -n "$line" ]]; do
    # Skip empty lines and comments
    if [[ -z "$line" || "$line" == \#* ]]; then
        continue
    fi

    # Split env variables by character `=`
    if printf '%s\n' "$line" | grep -q -e '='; then
        varname=$(printf '%s\n' "$line" | sed -e 's/=.*//')
        varvalue=$(printf '%s\n' "$line" | sed -e 's/^[^=]*=//')
    fi

    # Read value of current variable if exists as Environment variable
    value=${!varname:-$varvalue}

    # Ensure varname is not empty before appending to the JS file
    if [ -n "$varname" ]; then
        echo "  $varname: \"$value\"," >>$OUTPUT_DIR/env-config.js
    fi
done <$ENV_DIR/.env

echo "}" >>$OUTPUT_DIR/env-config.js
