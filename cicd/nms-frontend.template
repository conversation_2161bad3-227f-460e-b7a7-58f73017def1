

##
#Configuration for NMS Frontend
##

server {
  listen   8000;
  listen  [::]:8000;
  server_name _$;

  location / {
    root   /usr/share/nginx/html;
    index  index.html index.html;
    try_files $uri $uri/ /index.html;
  }

  location /nms/inv {
    proxy_pass http://${NMS_INV_CELL_URL};
  }

  location /nms/mc {
    proxy_pass http://${NMS_MC_URL};
  }

  location /nms/orc {
    proxy_pass http://${NMS_ORCH_URL};
  }

  location /nms/rad {
    proxy_pass http://${NMS_RAD_URL};
  }

  error_page   500 502 503 504  /50x.html;
  location = /50x.html {
    root   /usr/share/nginx/html;
  }
}


