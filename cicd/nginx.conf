user nginx;
worker_processes 2;

error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
  worker_connections 1024;
  use epoll;
  accept_mutex off;
}

http {
  include /etc/nginx/mime.types;
  proxy_set_header X-Real-IP $remote_addr;
  proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

  default_type application/octet-stream;

  log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                  '$status $request_method $body_bytes_sent "$http_referer" '
                  '"$http_user_agent" "$http_x_forwarded_for" "$uri"';

  access_log /var/log/nginx/access.log main;

  sendfile on;
  #tcp_nopush on;


  keepalive_timeout 65;

  client_max_body_size 300m;
  client_body_buffer_size 128k;

  gzip on;
  gzip_http_version 1.1;
  gzip_comp_level 6;
  gzip_min_length 0;
  gzip_buffers 16 8k;
  gzip_proxied any;
  gzip_types text/plain text/css text/xml text/javascript text/x-component image/gif image/png image/svg+xml image/x-icon font/opentype application/font-woff application/font-tff application/vnd.ms-fontobject application/xhtml+xml application/xml application/xml+rss application/javascript application/json application/x-web-app-manifest+json

  location / {
    root /usr/share/nginx/html;
    index index.html index.htm;
    try_files $uri $uri/ /index.html =404;
    add_header Access-Control-Allow-Origin *;
  }
}

  gzip_disable "MSIE [1-6]\.";
  gzip_vary on;

  include /etc/nginx/conf.d/*.conf;
