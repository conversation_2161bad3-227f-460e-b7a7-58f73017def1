{"name": "nms-frontend", "private": true, "version": "3.12.1", "type": "module", "scripts": {"dev": "vite --mode local.dev_1 ", "dev:2": "vite --mode local.dev2_01 ", "dev:test:1": "vite --mode local.test_1 ", "lint": "eslint --fix --ext .js,.jsx,.ts,.tsx .", "test:ui": "vitest", "test:ui:jenkins": "vitest run", "test:ui:debug": "DEBUG_PRINT_LIMIT=10000 npm run test:ui", "test:e2e": "cypress run --headless --browser chrome", "test:all": "npm run test:e2e && vitest run --coverage", "all-tests": "npm run lint && npm run test:e2e && vitest run --coverage", "coverage": "vitest run --coverage", "configure-husky": "npx husky install && npx husky add .husky/pre-commit \"npx --no-install lint-staged\"", "build": "tsc && vite build", "type-check": "tsc --noEmit", "preview": "vite preview", "cypress": "cypress open"}, "lint-staged": {"**/*.{ts,tsx,js,jsx,json}": ["eslint . --fix", "prettier --write ."]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "lint-staged"}}, "dependencies": {"@chakra-ui/icons": "^2.1.0", "@chakra-ui/react": "^2.8.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@fontsource/poppins": "^4.5.10", "@hookform/resolvers": "^3.3.1", "@reduxjs/toolkit": "^1.9.5", "@tanstack/react-query": "^4.33.0", "@tanstack/react-query-devtools": "^4.33.0", "@tanstack/react-table": "^8.9.3", "axios": "^1.5.0", "cmdk": "^0.2.0", "cronstrue": "^2.48.0", "date-fns": "^2.30.0", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "framer-motion": "^8.5.5", "jose": "^4.14.5", "lodash": "^4.17.21", "moment": "^2.30.1", "react": "^18.2.0", "react-day-picker": "^8.8.1", "react-dom": "^18.2.0", "react-error-boundary": "^4.0.11", "react-helmet-async": "^1.3.0", "react-hook-form": "^7.46.0", "react-icons": "^4.12.0", "react-minimal-datetime-range": "^2.1.0", "react-redux": "^8.1.2", "react-router-dom": "^6.15.0", "react-select": "^5.8.0", "react-test-renderer": "^18.2.0", "react-transition-group": "^4.2.2", "react-use": "^17.6.0", "reactflow": "^11.11.3", "vite-plugin-svgr": "^3.2.0", "zod": "^3.22.2"}, "devDependencies": {"@hookform/devtools": "^4.3.1", "@testing-library/dom": "^10.1.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.2", "@types/react": "^18.2.21", "@types/react-dom": "^18.2.7", "@types/react-router-dom": "^5.3.3", "@types/react-test-renderer": "^18.0.1", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@vitejs/plugin-react": "^3.1.0", "@vitest/coverage-istanbul": "^0.28.5", "@welldone-software/why-did-you-render": "^8.0.1", "cypress": "^13.12.0", "cypress-real-events": "^1.10.1", "cypress-xpath": "^2.0.1", "eslint": "^8.48.0", "eslint-config-prettier": "^8.10.0", "eslint-plugin-cypress": "^2.14.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jest-dom": "^4.0.3", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-testing-library": "^5.11.1", "husky": "^8.0.3", "jsdom": "^21.1.2", "lint-staged": "^13.3.0", "msw": "^1.3.0", "postcss": "^8.4.35", "prettier": "^2.8.8", "typescript": "^4.9.5", "vite": "^4.4.9", "vitest": "^0.27.3"}, "msw": {"workerDirectory": "public"}}