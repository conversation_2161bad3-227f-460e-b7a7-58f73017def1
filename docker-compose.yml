# Define the services for the application
services:
  # Define the service for the NMS Frontend using Nginx
  nms-frontend-nginx:
    # Set the name of the container
    container_name: nms-frontend-nginx
    # Specify the image to use, based on the latest version of wildfly_server
    image: wildfly_server:latest
    # Build details for the image
    build:
      # Context for the build will be the current directory
      context: .
      # Specify the Dockerfile location
      dockerfile: ./Dockerfile
    # Environment variables for the container
    environment:
      - 'ENV_NAME=nms-dev2-1'
      - 'NMS_HOST_URL=************'

      - 'NMS_INV_CELL_URL=************:8052/nms/inv'
      - 'NMS_MC_URL=************:8080/nms/mc'
      # -  Keeping the below in line with the template
      # - 'NMS_ORCH_URL=************:8050/nms'
      - 'NMS_ORCH_URL=************:8050/nms/orc'
      - 'NMS_RAD_URL=************:8062/nms/rad'
      - 'NMS_serv_Grafana_URL="*************:3000'
    # Map the host port 8080 to the container's port 8000
    ports:
      - '8080:8000'
    # Ensure the container restarts automatically if it stops
    restart: always
    # Define the entrypoint script for the container
    entrypoint: /entrypoint.sh
