{
  "compilerOptions": {
    "target": "ESNext",
    "lib": ["dom", "dom.iterable", "esnext"],
    //"types": ["vite/client"],
    "types": ["vite/client", "cypress", "node", "cypress-real-events"],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": false,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "sourceMap": true
    //"outDir": "out",
  },
  "include": ["vite.config.ts", "cypress.config.ts", "cypress", "src"],
  //"exclude": ["node_modules", "**/dist",],
  "exclude": ["node_modules", "**/dist", "cypress", "./cypress.config.ts", "**/*.cy.tsx"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
