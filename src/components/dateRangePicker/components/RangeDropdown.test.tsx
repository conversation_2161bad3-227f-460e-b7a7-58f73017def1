import { render, fireEvent, screen } from '@testing-library/react';
import RangeDropdown from './RangeDropdown';
import { expect, vi } from 'vitest';

describe('RangeDropdown Component', () => {
  const mockHandleRangeSelect = vi.fn();
  const mockSetSelectedOption = vi.fn();
  const options = ['Last hour', 'Today', 'Last 24 hours', 'Yesterday', 'Last 7 days', 'Last Week'];

  it('renders correctly', () => {
    render(
      <RangeDropdown
        handleRangeSelect={mockHandleRangeSelect}
        setSelectedOption={mockSetSelectedOption}
        selectedOption="Select Option"
      />
    );
    expect(screen.getByText('Select Option')).toBeInTheDocument();
  });

  it('opens dropdown and shows all options', () => {
    render(
      <RangeDropdown
        handleRangeSelect={mockHandleRangeSelect}
        setSelectedOption={mockSetSelectedOption}
        selectedOption="Select Option"
      />
    );
    fireEvent.click(screen.getByText('Select Option'));
    options.forEach((option) => {
      expect(screen.getByText(option)).toBeInTheDocument();
    });
  });

  it('calls handleRangeSelect and setSelectedOption on option select', () => {
    render(
      <RangeDropdown
        handleRangeSelect={mockHandleRangeSelect}
        setSelectedOption={mockSetSelectedOption}
        selectedOption="Select Option"
      />
    );

    // Simulate selecting an option
    fireEvent.click(screen.getByText('Select Option'));
    fireEvent.click(screen.getByText('Today'));

    expect(mockSetSelectedOption).toHaveBeenCalledWith('Today');
    expect(mockHandleRangeSelect).toHaveBeenCalled();
  });
});
