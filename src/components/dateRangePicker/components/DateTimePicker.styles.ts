import styled from '@emotion/styled';
import ArrowIcon from '../../../assets/icons/rightArrowIcon.svg';

export const StyledDateTimePicker = styled.div`
  .react-minimal-datetime-range {
    width: max-content;
    margin-top: 0.5rem;
  }
  .react-minimal-datetime-range-calendar__table {
    width: 100%;
  }
  .react-minimal-datetime-range__range-input-wrapper {
    border: none;
    background-color: transparent;
    display: none;
  }
  .react-minimal-datetime-range-calendar__body-container {
    height: 200px !important;
  }
  .react-minimal-datetime-range__clear {
    right: 7%;
    color: grey;
  }
  /* Hide the original separator */
  .react-minimal-datetime-range__range-input-separator {
    display: none;
  }

  /* new separator with an arrow icon */
  .react-minimal-datetime-range__range-input-wrapper::after {
    color: #bada55 !important;
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    background-image: url(${ArrowIcon});
    background-size: cover;
    position: absolute;
    top: 50%;
    left: 44%;
    transform: translate(-50%, -50%);
    /* margin: 0 4px; */
  }
`;
