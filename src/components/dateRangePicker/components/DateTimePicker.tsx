import { useEffect, useState } from 'react';
import { Theme, useTheme, IconButton, Input, InputGroup, InputRightElement, HStack } from '@chakra-ui/react';
import { RangePicker } from 'react-minimal-datetime-range';
import 'react-minimal-datetime-range/lib/react-minimal-datetime-range.min.css';
import { ArrowForwardIcon, CalendarIcon } from '@chakra-ui/icons';
import { StyledDateTimePicker } from './DateTimePicker.styles';

interface DateTimePickerProps {
  onChange: (value: any) => void;
  selected: any;
}

const DateTimePicker: React.FC<DateTimePickerProps> = ({ onChange, selected }) => {
  const theme: Theme = useTheme();
  const [rangeDate, setRangeDate] = useState({ startDate: selected[0], endDate: selected[1] });
  const [rangePickerKey, setRangePickerKey] = useState(Math.random()); // Key to force re-render

  useEffect(() => {
    setRangeDate({
      startDate: selected[0],
      endDate: selected[1],
    });
    setRangePickerKey(Math.random());
  }, [selected]);

  // date and time from the ISO strings
  const startDateObj = new Date(rangeDate?.startDate);
  const endDateObj = new Date(rangeDate?.endDate);

  // to format date for display on UI
  const displayFormatDate = (date: any) => {
    const year = date.getFullYear();
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const month = monthNames[date.getMonth()];
    const day = String(date.getDate()).padStart(2, '0');
    return `${day} ${month} ${year}`;
  };
  // to format date for RangePicker component
  const rangePickerFormatDate = (date: any) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };
  const formatTime = (date: any) => {
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${hours}:${minutes}`;
  };

  const startRangePickerFormat = rangePickerFormatDate(startDateObj);
  const endRangePickerFormat = rangePickerFormatDate(endDateObj);
  const startTimeFormat = formatTime(startDateObj);
  const endTimeFormat = formatTime(endDateObj);

  const startDisplayFormat = displayFormatDate(startDateObj);
  const endDisplayFormat = displayFormatDate(endDateObj);

  const handleConfirm = (res: any) => {
    const convertToISOFormat = (dateString: any) => {
      return dateString.replace(' ', 'T');
    };

    const from = new Date(convertToISOFormat(res[0]));
    const to = new Date(convertToISOFormat(res[1]));
    onChange({ from, to });
  };
  const triggerClick = () => {
    const targetSpan: any = document.querySelector('.react-minimal-datetime-range__range-input-wrapper');
    if (targetSpan) {
      targetSpan.click();
    }
  };
  const fromStyles = {
    borderTopRightRadius: '0px',
    borderBottomRightRadius: '0px',
    border: 'none',
    width: '100%',
    padding: '10px',
  };
  const toStyles = {
    borderTopLeftRadius: '0px',
    borderBottomLeftRadius: '0px',
    border: 'none',
    width: '100%',
    padding: '10px',
  };
  return (
    <StyledDateTimePicker theme={theme}>
      <HStack onClick={triggerClick}>
        <InputGroup>
          <Input
            isReadOnly
            value={`${startDisplayFormat} ${startTimeFormat}`}
            placeholder="From Date"
            data-testid="startDateInput"
            {...fromStyles}
          />

          <InputRightElement>
            <IconButton
              aria-label="ArrowForwardIcon"
              icon={<ArrowForwardIcon />}
              variant="unstyled"
              size="sm"
              right="-5px"
            />
          </InputRightElement>
        </InputGroup>
        <InputGroup>
          <Input
            isReadOnly
            value={`${endDisplayFormat} ${endTimeFormat}`}
            placeholder="To Date"
            data-testid="endDateInput"
            {...toStyles}
          />
          <InputRightElement>
            <IconButton aria-label="Calendar" icon={<CalendarIcon />} variant="unstyled" size="sm" />
          </InputRightElement>
        </InputGroup>
      </HStack>
      <RangePicker
        key={rangePickerKey}
        locale="en-us" // ['en-us', 'zh-cn','ko-kr']; default is en-us
        show={false}
        disabled={false}
        allowPageClickToClose={true}
        onConfirm={(res) => handleConfirm(res)}
        style={{ width: '25rem', margin: '0 auto' }}
        showOnlyTime={false} // default is false, only select time
        defaultDates={[startRangePickerFormat, endRangePickerFormat]}
        defaultTimes={[startTimeFormat, endTimeFormat]}
      />
    </StyledDateTimePicker>
  );
};

export default DateTimePicker;
