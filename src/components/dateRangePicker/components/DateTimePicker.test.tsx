import { render, fireEvent, screen } from '@testing-library/react';
import DateTimePicker from './DateTimePicker';
import { expect, vi } from 'vitest';

describe('DateTimePicker Component', () => {
  const mockOnChange = vi.fn();
  const selectedDateRange = ['2024-01-01', '2024-01-07'];

  it('renders correctly with default selected dates', () => {
    render(<DateTimePicker onChange={mockOnChange} selected={selectedDateRange} />);
    const startDateInput = screen.getByTestId('startDateInput');
    const endDateInput = screen.getByTestId('endDateInput');

    expect(startDateInput).toHaveValue('01 Jan 2024 00:00');
    expect(endDateInput).toHaveValue('07 Jan 2024 00:00');
  });

  it('triggers onChange when date range is confirmed', () => {
    render(<DateTimePicker onChange={mockOnChange} selected={selectedDateRange} />);
    fireEvent.click(screen.getByLabelText('Calendar'));

    const startDates = screen.getAllByText('10');
    const startDate = startDates[0];
    fireEvent.click(startDate);

    const endDates = screen.getAllByText('17');
    const endDate = endDates[0];
    fireEvent.click(endDate);

    fireEvent.click(screen.getByText('Confirm'));

    // Assume new range is passed to onChange
    expect(mockOnChange).toHaveBeenCalledWith({
      from: new Date('2024-01-10'),
      to: new Date('2024-01-17'),
    });
  });

  it('updates the input fields when new dates are selected', () => {
    render(<DateTimePicker onChange={mockOnChange} selected={selectedDateRange} />);
    fireEvent.click(screen.getByLabelText('Calendar'));

    const allTens = screen.getAllByText('10');
    // Assuming the date you want to select is the first one
    const selectedStartDate = allTens[0];
    fireEvent.click(selectedStartDate);

    const allSeventeens = screen.getAllByText('17');
    // Assuming the date you want to select is the first one
    const selectedEndDate = allSeventeens[0];
    fireEvent.click(selectedEndDate);

    fireEvent.click(screen.getByText('Confirm'));
    const startDateInput = screen.getByTestId('startDateInput');
    const endDateInput = screen.getByTestId('endDateInput');

    expect(startDateInput).toHaveValue('01 Jan 2024 00:00');
    expect(endDateInput).toHaveValue('07 Jan 2024 00:00');
  });
});
