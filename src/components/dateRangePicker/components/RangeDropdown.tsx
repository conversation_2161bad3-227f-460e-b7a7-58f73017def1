import { ChevronDownIcon } from '@chakra-ui/icons';
import { Button, Flex, Menu, MenuButton, MenuItem, MenuList } from '@chakra-ui/react';
import { endOfWeek, startOfWeek, subDays } from 'date-fns';
import { DateRange } from '../DateRangePicker';
import { FaChevronDown } from 'react-icons/fa';

interface IRangeDropdown {
  handleRangeSelect: (range: DateRange | undefined) => void;
  setSelectedOption: React.Dispatch<React.SetStateAction<string>>;
  selectedOption: string;
}

export function RangeDropdown({ handleRangeSelect, setSelectedOption, selectedOption }: IRangeDropdown) {
  const setQuickDateRange = (rangeOption: string) => {
    let fromDate, toDate;

    switch (rangeOption) {
      case 'hour':
        toDate = new Date();
        fromDate = new Date(toDate.getTime() - 60 * 60 * 1000);
        break;
      case 'today':
        fromDate = new Date();
        toDate = new Date();
        break;
      case 'yesterday':
        fromDate = subDays(new Date(), 1);
        toDate = subDays(new Date(), 1);
        break;
      case 'lastWeek':
        fromDate = startOfWeek(subDays(new Date(), 7));
        toDate = endOfWeek(subDays(new Date(), 7));
        break;
      case '24 hours':
        toDate = new Date();
        fromDate = subDays(new Date(), 1);
        break;
      case '7 days':
        toDate = new Date();
        fromDate = subDays(new Date(), 7);
        break;
      default:
        return;
    }

    if (rangeOption !== 'hour' && rangeOption !== '24 hours' && rangeOption !== '7 days') {
      fromDate.setHours(0, 0, 0, 0);
      toDate.setHours(23, 59, 59, 999);
    }

    handleRangeSelect({ from: fromDate, to: toDate });
  };

  const selectOption = [
    {
      key: 'Last hour',
      value: 'hour',
    },
    {
      key: 'Today',
      value: 'today',
    },
    { key: 'Last 24 hours', value: '24 hours' },
    {
      key: 'Yesterday',
      value: 'yesterday',
    },
    { key: 'Last 7 days', value: '7 days' },
    {
      key: 'Last Week',
      value: 'lastWeek',
    },
  ];
  const handleSelectChange = (value: string, key: string) => {
    setQuickDateRange(value);
    setSelectedOption(key);
  };
  return (
    <Flex maxW={'10rem'} alignItems={'end'} marginLeft={2}>
      <Menu>
        <MenuButton as={Button} rightIcon={<FaChevronDown />}>
          {selectedOption}
        </MenuButton>
        <MenuList>
          {selectOption.map(({ key, value }) => (
            <MenuItem key={value} onClick={() => handleSelectChange(value, key)}>
              {key}
            </MenuItem>
          ))}
        </MenuList>
      </Menu>
    </Flex>
  );
}

export default RangeDropdown;
