import { Flex, FormControl, FormLabel, HStack, useStyleConfig } from '@chakra-ui/react';
import { formatISO, isSameDay, isValid, parse } from 'date-fns';
import { useEffect, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import DateTimePicker from './components/DateTimePicker';
import { stripZFromEnd } from '../../pages/MetricsCollector/components/Events/Events';
import RangeDropdown from './components/RangeDropdown';

export interface DateRange {
  from: Date | null;
  to: Date | null;
}
export type ValuePiece = Date | null;

export type Value = [ValuePiece, ValuePiece];

export function DateRangePicker({ setLimit, version }: { setLimit: any; version?: string }) {
  const { setValue, watch } = useFormContext();
  const fromValue = watch('fromDate');
  const toValue = watch('toDate');

  const [selectedRange, setSelectedRange] = useState<Value>([fromValue, toValue]);
  const [selectedOption, setSelectedOption] = useState('Select Range');

  useEffect(() => {
    if (version === 'v2' && selectedOption !== 'Select Range') {
      setLimit(1000);
    }
  }, [selectedOption]);

  useEffect(() => {
    const fromDate = parse(fromValue, 'dd-MM-yyyy', new Date());
    fromDate.setHours(0, 0, 0, 0);

    let toDate = parse(toValue, 'dd-MM-yyyy', new Date());
    toDate.setHours(0, 0, 0, 0);

    const now = new Date();
    now.setHours(now.getHours(), now.getMinutes(), now.getSeconds(), now.getMilliseconds());

    if (isSameDay(toDate, now)) {
      toDate = now;
    }

    if (isValid(fromDate) && isValid(toDate)) {
      setSelectedRange([fromDate, toDate]);
    }
  }, [fromValue, toValue]);

  const handleRangeSelect = (range: any) => {
    setSelectedRange([range?.from, range?.to]);
    if (range?.from) {
      setValue('fromDate', stripZFromEnd(formatISO(range.from)));
    }
    if (range?.to) {
      setValue('toDate', stripZFromEnd(formatISO(range.to)));
    }
    setSelectedOption('Select Range');
  };

  const styles: any = useStyleConfig('Input');

  return (
    <Flex maxW={'40rem'}>
      <FormControl>
        <HStack
          spacing="0"
          borderRadius="md"
          border={styles.field?.border}
          boxShadow="sm"
          _hover={styles.field?._hover}
          _focusWithin={styles.field?._focus}
          width="25rem"
          height="2.5rem"
        >
          <DateTimePicker onChange={handleRangeSelect} selected={selectedRange} />
        </HStack>
      </FormControl>
      <RangeDropdown
        selectedOption={selectedOption}
        handleRangeSelect={handleRangeSelect}
        setSelectedOption={setSelectedOption}
      />
    </Flex>
  );
}
