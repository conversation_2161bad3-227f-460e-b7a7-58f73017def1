import {
  <PERSON>,
  <PERSON>lex,
  <PERSON>conButton,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>over<PERSON>ody,
  <PERSON>over<PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Text,
  Tooltip,
  useToken,
} from '@chakra-ui/react';

import { useQuery } from '@tanstack/react-query';
import _ from 'lodash';
import { useState } from 'react';
import { Md<PERSON>rrowFor<PERSON>, Md<PERSON><PERSON> } from 'react-icons/md';
import {
  ColorToStatus,
  StatusToColor,
  TRAFFIC_CRITICAL_PURPLE,
  TRAFFIC_ERROR_RED,
  TRAFFIC_OK_GREEN,
  TRAFFIC_SHUTDOWN_BLACK,
  TRAFFIC_UNKNOWN_GRAY,
  TRAFFIC_WARNING_ORANGE,
} from '../../data/constants';
import { getStatusHistory } from '../../services/inventoryManager';
import { ComponentTypeEnum } from '../../services/types';
import { UnstyledTable } from '../nodeComponents/airspan/utils';

type IErrorResponse = {
  response?: {
    data?: {
      message?: string;
      detail?: string;
    };
    status?: number;
  };
};

type IStatusComponentProps = {
  boxSize: string;
  color: string;
  status?: string;
  dataTestId?: string;
  cell_ref?: string;
  node_id?: string;
  component_id?: string | number;
  component_type?: ComponentTypeEnum;
};

type IStatusHistory = {
  component_type: string;
  component_id: string;
  manager: string;
  cell_refs: string[];
  node_id: string;
  event_timestamp: string;
  status: string;
  last_status: string;
};

type IClickableStatusButtonProps = {
  handleIconClick: () => void;
  dataTestId?: string;
  boxSize: string;
  color: string;
  status?: string;
};

type IStaticStatusCircleIconProps = {
  color: StatusToColor;
  size: number;
  dataTestId?: string;
};

export const StaticStatusCircleIcon: React.FC<IStaticStatusCircleIconProps> = ({ color, size, dataTestId }) => {
  const colorMap = {
    green: TRAFFIC_OK_GREEN,
    orange: TRAFFIC_WARNING_ORANGE,
    red: TRAFFIC_ERROR_RED,
    gray: TRAFFIC_UNKNOWN_GRAY,
    black: TRAFFIC_SHUTDOWN_BLACK,
    purple: TRAFFIC_CRITICAL_PURPLE,
  };
  const [colorToken] = useToken('colors', [colorMap[color]]);
  return (
    <Tooltip label={ColorToStatus[color]}>
      <button style={{ background: 'none', border: 'none', cursor: 'pointer' }} data-testid={dataTestId}>
        <MdLens size={size} color={colorToken} />
      </button>
    </Tooltip>
  );
};

const StatusHistory = ({ statusHistory }: { statusHistory: IStatusHistory[] | undefined }) => {
  let tableData = statusHistory?.reduce((acc: any, item: any) => {
    // Parse the ISO string to a Date object
    const date = new Date(item.event_timestamp);

    // Format the date to 'DD MM YYYY at HH:MM:SS' in local timezone
    const formattedDate = date
      .toLocaleString(undefined, {
        day: 'numeric',
        month: 'short',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false,
      })
      .replace(',', ' at');

    acc[formattedDate] = [
      <StaticStatusCircleIcon
        key={item.last_status}
        size={30}
        color={StatusToColor[item.last_status as keyof typeof StatusToColor]}
      />,
      <MdArrowForward key={item.status} size={24} color="black" />,
      <StaticStatusCircleIcon
        key={item.status}
        size={30}
        color={StatusToColor[item.status as keyof typeof StatusToColor]}
      />,
    ];
    return acc;
  }, {});

  if (!_.isUndefined(tableData) && !_.isEmpty(tableData)) {
    tableData = { ...{ 'Status Changed On': ['From', '', 'To'] }, ...tableData };
    return <UnstyledTable tableData={tableData} rowBorderBottom={true} useKeyColon={false} />;
  } else {
    return <Text> No Status History Found</Text>;
  }
};

const ClickableStatusCircleButton: React.FC<IClickableStatusButtonProps> = ({
  handleIconClick,
  dataTestId,
  color,
  boxSize,
  status = TRAFFIC_UNKNOWN_GRAY,
}) => {
  return (
    <Tooltip label={status}>
      <IconButton
        icon={<MdLens />}
        onClick={handleIconClick}
        data-testid={dataTestId}
        aria-label="Custom Circle Icon Button"
        size={boxSize}
        borderRadius="full"
        backgroundColor={color}
        color={color}
        boxShadow="5px 5px 5px gray"
        _hover={{
          boxShadow: '0px 2px 20px gray',
          transform: 'translateY(-3px)',
        }}
        _active={{
          boxShadow: 'none',
          transform: 'translateY(2px)',
        }}
        transition="all 0.3s ease"
      />
    </Tooltip>
  );
};

const StatusComponent = (props: IStatusComponentProps) => {
  const [isOpen, setIsOpen] = useState(false);

  const {
    data: statusHistory,
    isLoading,
    error,
  } = useQuery<IStatusHistory[], IErrorResponse>(
    ['statusHistory', props.status],
    () =>
      getStatusHistory({
        cell_ref: props.cell_ref,
        node_id: props.node_id,
        component_type: props.component_type,
        component_id: props.component_id as string,
        limit: 5,
      }),
    {
      enabled: isOpen,
      retry: false,
    }
  );

  const handleIconClick = () => {
    setIsOpen(!isOpen);
  };

  const handleClick = (event: React.MouseEvent<HTMLDivElement>) => {
    event.stopPropagation();
    setIsOpen(!isOpen);
  };

  return (
    <Box onClick={handleClick}>
      <Popover isOpen={isOpen} onClose={() => setIsOpen(false)} placement="auto">
        <PopoverTrigger>
          <span>
            <ClickableStatusCircleButton
              handleIconClick={handleIconClick}
              dataTestId={props.dataTestId}
              boxSize={props.boxSize}
              color={props.color}
              status={props?.status}
            />
          </span>
        </PopoverTrigger>
        <PopoverContent>
          <PopoverArrow />
          <PopoverHeader>
            <Flex justifyContent="center" width="100%" fontSize="lg">
              Status History
            </Flex>
          </PopoverHeader>
          <PopoverBody width="xlg">
            {isLoading ? (
              <Spinner />
            ) : error ? (
              <Text>
                {`Error: ${error.response?.data?.message || error.response?.data?.detail}` || 'Unknown error occurred'}
              </Text>
            ) : (
              <Box borderRadius="lg" overflowY="auto">
                <StatusHistory statusHistory={statusHistory} />
              </Box>
            )}
          </PopoverBody>
        </PopoverContent>
      </Popover>
    </Box>
  );
};

export default StatusComponent;
