import React from 'react';

type ArrowLineLengthProps = {
  arrowLineLength?: string | undefined; // optional prop to set line length
};

const LongArrowIconComponent: React.FC<ArrowLineLengthProps> = ({ arrowLineLength }) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="100" height="50" viewBox="0 0 100 50">
      <line x1={arrowLineLength ? arrowLineLength : '74'} y1="25" x2="16" y2="25" stroke="black" strokeWidth="2" />
      <polygon points={`10,25,20,20,20,30`} fill="black" />
    </svg>
  );
};

export default LongArrowIconComponent;
