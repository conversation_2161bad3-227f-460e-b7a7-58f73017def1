import { ArrowUpDownIcon, ChevronDownIcon, ChevronUpIcon } from '@chakra-ui/icons';

type CellTableSortIconsProps = {
  cellTypeAsc: null | boolean;
};

const CellTableSortIcons = ({ cellTypeAsc }: CellTableSortIconsProps) => {
  switch (cellTypeAsc) {
    case null:
      return (
        <>
          <ArrowUpDownIcon boxSize={3} />
        </>
      );
    case true:
      return (
        <>
          <ChevronDownIcon boxSize={4} />
        </>
      );
    case false:
      return (
        <>
          <ChevronUpIcon boxSize={4} />
        </>
      );
    default:
      return <></>;
  }
};

export default CellTableSortIcons;
