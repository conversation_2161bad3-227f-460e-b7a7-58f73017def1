import { InfoOutlineIcon } from '@chakra-ui/icons';
import {
  Box,
  Heading,
  Popover,
  PopoverArrow,
  PopoverBody,
  PopoverContent,
  PopoverHeader,
  PopoverTrigger,
} from '@chakra-ui/react';

const NmsPopover = ({
  children,
  heading,
  ml,
  bodyWidth,
}: {
  children: React.ReactNode;
  heading: React.ReactElement;
  ml?: number;
  bodyWidth?: string;
}) => {
  // const { isOpen } = useDisclosure();
  return (
    <Popover>
      {({ isOpen }) => (
        <>
          <PopoverTrigger>
            <InfoOutlineIcon fontSize="1.25rem" ml={ml} data-testid="nms-popover" />
          </PopoverTrigger>
          <Box display={isOpen ? '' : 'none'}>
            <PopoverContent width="100%">
              <PopoverArrow />
              <PopoverHeader fontSize="lg" fontWeight="normal">
                <Heading fontSize="md">{heading}</Heading>
              </PopoverHeader>
              <PopoverBody width={bodyWidth} p="4" fontWeight="normal">
                {children}
              </PopoverBody>
            </PopoverContent>
          </Box>
        </>
      )}
    </Popover>
  );
};

export default NmsPopover;
