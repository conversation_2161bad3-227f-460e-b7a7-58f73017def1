import React from 'react';
import {
  Box,
  Popover,
  PopoverArrow,
  PopoverBody,
  PopoverContent,
  PopoverTrigger,
  Text,
  HStack,
  Flex,
  IconButton,
  Grid,
  Portal,
} from '@chakra-ui/react';
import { InfoOutlineIcon } from '@chakra-ui/icons';
// import { ReactComponent as InfoIcon } from '../../../src/assets/icons/Info.svg';
// import { ReactComponent as <PERSON>LineArrow } from '../../../src/assets/icons/longLineArrow.svg';
import LongArrowIconComponent from '../icons/LongArrowIconComponent';
interface ToolTipDisplayProps {
  id?: string;
}

const idStructure = [
  {
    mainText: 'US',
    pointedText: 'Country',
    description: 'ISO3166-1 alpha-2 codes. E.g., US, GB, IE, BE etc.',
    width: '120px', // add width to have 3rd row its set width for text and desc
    arrowPosition: { bottom: '-42px', left: '-60px' },
    arrowTransform: 'rotate(-35deg)',
    paddingLeft: '18px',
  },
  {
    mainText: 'MIDO',
    pointedText: 'City or location',
    description: 'Up to 1,679,616<br /> possibilities',
    arrowPosition: { bottom: '-40px', left: '-14px' },
    arrowTransform: 'rotate(-42deg)',
  },
  {
    mainText: 'L',
    pointedText: 'Technology',
    description: 'L for 4G<br /> N for 5G',
    arrowPosition: { bottom: '-28px', left: '-15px' },
    arrowTransform: 'rotate(-90deg)',
    lineLength: '56',
  },
  {
    mainText: 'S',
    pointedText: 'Indoor or <br /> Outdoor',
    description: 'S-outdoor,<br /> B-indoor',
    arrowPosition: { bottom: '-28px', left: '18px' },
    arrowTransform: 'rotate(-90deg)',
    paddingLeft: '60px',
    lineLength: '56',
  },
  {
    mainText: '00',
    pointedText: 'Reserved for <br /> future use',
    description: '',
    arrowPosition: { bottom: '-30px', left: '5px' },
    arrowTransform: 'rotate(-122deg)',
    lineLength: '60',
  },
  {
    mainText: '1234',
    pointedText: 'Unique ID',
    description: '',
    arrowPosition: { bottom: '-38px', left: '40px' },
    arrowTransform: 'rotate(-138deg)',
  },
];

const ToolTipDisplay: React.FC<ToolTipDisplayProps> = ({ id }) => {
  const CharactersInfo = (
    <Flex direction="column" align="left" justify="left">
      {/* 1st row */}
      <Flex justifyContent="space-between" fontSize="sm" width="100%">
        <Text>#Characters:</Text>
        <Flex flexGrow={1} justifyContent="space-between" mx={4}>
          <Text pl={7}>2</Text>
          <Text pl={2}>+4</Text>
          <Text ml="-2">+1</Text>
          <Text>+1</Text>
          <Text ml="-4">+2</Text>
          <Text>+4</Text>
        </Flex>
        <Text>
          in total 14 <br /> (without spaces)
        </Text>
      </Flex>
      {/* 2nd row */}
      <Flex fontSize="2xl" mb={10} mt={1}>
        Example:
        {idStructure.map((item, index) => (
          <Box as="span" key={index} fontWeight="bold" position="relative">
            <Text pl={item.paddingLeft ? item.paddingLeft : '30px'}>{item.mainText}</Text>
            <Box
              position="absolute"
              bottom={item.arrowPosition?.bottom}
              left={item.arrowPosition?.left}
              transform={item.arrowTransform}
              zIndex="-1"
            >
              <LongArrowIconComponent arrowLineLength={item.lineLength} />
            </Box>
          </Box>
        ))}
      </Flex>
      {/* last row */}
      <Grid templateColumns={`repeat(${idStructure.length}, 1fr)`} fontSize="sm">
        {idStructure.map((item, index) => (
          <Box key={index} textAlign="center">
            <Text fontWeight="bold" fontSize={'md'} dangerouslySetInnerHTML={{ __html: item.pointedText }} />
            <Text dangerouslySetInnerHTML={{ __html: item.description }} whiteSpace="normal" wordBreak="break-word" />
          </Box>
        ))}
      </Grid>
    </Flex>
  );

  return (
    <HStack spacing={4} align="center">
      {id ? (
        <>
          <Text borderWidth="1px" borderColor="gray.200" p="2" borderRadius="md">
            {id}
          </Text>
          <Popover trigger="hover" placement="top">
            <PopoverTrigger>
              <IconButton aria-label="info" cursor="default" icon={<InfoOutlineIcon />} data-testid="info-icon" />
            </PopoverTrigger>
            <Portal>
              <PopoverContent minW="650px">
                <PopoverArrow />
                <PopoverBody>{CharactersInfo}</PopoverBody>
              </PopoverContent>
            </Portal>
          </Popover>
        </>
      ) : (
        <Popover trigger="hover" placement="top">
          <PopoverTrigger>
            <IconButton aria-label="info" cursor="default" icon={<InfoOutlineIcon />} data-testid="info-icon" />
          </PopoverTrigger>
          <Portal>
            <PopoverContent minW="650px">
              <PopoverArrow />
              <PopoverBody>{CharactersInfo}</PopoverBody>
            </PopoverContent>
          </Portal>
        </Popover>
      )}
    </HStack>
  );
};

export default ToolTipDisplay;
