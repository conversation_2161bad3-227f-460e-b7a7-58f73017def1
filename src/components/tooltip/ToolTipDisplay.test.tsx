import { render, screen, within } from '@testing-library/react';
import { expect, vi } from 'vitest';
import userEvent from '@testing-library/user-event';
import ToolTipDisplay from './ToolTipDisplay';

describe('ToolTipDisplay', () => {
  // Mock LongArrowIconComponent
  vi.mock('./LongArrowIconComponent', () => {
    return {
      __esModule: true,
      default: () => <div>Long Arrow Icon</div>,
    };
  });

  it('renders #Characters correctly', () => {
    render(<ToolTipDisplay />);
    const charactersText = screen.getByText(/#Characters:/i);
    expect(charactersText).toBeInTheDocument();
  });

  it('renders the Example ID', () => {
    render(<ToolTipDisplay />);
    const exampleSection = screen.getByText(/Example:/i);
    expect(exampleSection).toBeInTheDocument();

    // Check each ID part
    ['US', 'MIDO', 'L', 'S', '00', '1234'].forEach((idPart) => {
      expect(screen.getAllByText(new RegExp(idPart, 'i')).length).toBeGreaterThan(0);
    });
  });

  it('displays popover on hover over info icon', async () => {
    render(<ToolTipDisplay />);
    // assuming we have an info icon with a tooltip that appears on hover
    const infoIcon = screen.getByTestId('info-icon');
    userEvent.hover(infoIcon);
    // Wait for the popover body to appear in the document
    const popoverBody = await screen.findByRole('tooltip', {}, { timeout: 1000 });
    expect(popoverBody).toBeInTheDocument();
  });
});
