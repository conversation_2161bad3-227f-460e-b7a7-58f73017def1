import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>on,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  ModalOverlay,
} from '@chakra-ui/react';
import React from 'react';

interface IProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  submitText?: string;
  onSubmit?: () => void;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'full';
}

export const CustomModal: React.FC<IProps> = ({
  isOpen,
  onClose,
  title = '',
  children,
  submitText = 'Submit',
  size = 'md',
  onSubmit = () => {
    void 0;
  },
}) => (
  <Modal isOpen={isOpen} onClose={onClose} size={size}>
    <ModalOverlay />
    <ModalContent>
      <ModalHeader>{title}</ModalHeader>
      <ModalCloseButton />
      <ModalBody>{children}</ModalBody>

      <ModalFooter>
        <Button colorScheme="brand" mr={3} onClick={onClose}>
          Close
        </Button>
        <Button onClick={onSubmit}>{submitText}</Button>
      </ModalFooter>
    </ModalContent>
  </Modal>
);
