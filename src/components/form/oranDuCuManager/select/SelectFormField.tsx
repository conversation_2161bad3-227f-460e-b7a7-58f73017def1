import React from 'react';
import { useFormContext, Controller } from 'react-hook-form';
import { FormControl, FormLabel, Select, FormErrorMessage, Tooltip } from '@chakra-ui/react';

export type UseCaseOption = {
  id: number;
  use_case: number;
  band0: string | null;
  freq0: number | null;
  arfcn0: number | null;
  bw0: number | null;
  band1: string | null;
  freq1: number | null;
  arfcn1: number | null;
  bw1: number | null;
  band2: string | null;
  freq2: number | null;
  arfcn2: number | null;
  bw2: number | null;
  band3: string | null;
  freq3: number | null;
  arfcn3: number | null;
  bw3: number | null;
  updated: string;
  source_version: string;
};

export type Option = {
  value: string | number;
  label: string;
  min?: number;
  max?: number;
  step?: number;
  raw?: UseCaseOption;
};

type SelectFormFieldProps = {
  name: string;
  label: string;
  options: Option[];
  onChangeHandler?: any;
  mx?: string;
  tooltip?: string;
  isDisabled?: boolean;
  defaultValue?: string;
};

const SelectFormField: React.FC<SelectFormFieldProps> = ({
  name,
  label,
  options,
  onChangeHandler,
  mx,
  tooltip,
  isDisabled,
  defaultValue,
}) => {
  const {
    control,
    formState: { errors },
    trigger,
  } = useFormContext();

  return (
    <FormControl isInvalid={!!errors[name]} isRequired mb="4" mx={mx ? mx : ''}>
      <Tooltip label={tooltip} placement="top-start">
        <FormLabel htmlFor={name}>{label}</FormLabel>
      </Tooltip>
      <Controller
        name={name}
        control={control}
        defaultValue={defaultValue ?? ''}
        render={({ field }) => {
          return (
            <Select
              fontSize="medium"
              isDisabled={isDisabled}
              placeholder={`Select ${label.toLowerCase()}`}
              {...field}
              value={Array.isArray(field.value) ? field.value[0] : field.value || ''}
              onChange={(e) => {
                const value = e.target.value;
                field.onChange(value);
                if (onChangeHandler) {
                  onChangeHandler(value);
                }
                trigger(name);
              }}
            >
              {options?.map((option, index) => {
                const key = option.value ?? index;

                return (
                  <option key={key} value={option.value}>
                    {option.label}
                  </option>
                );
              })}
            </Select>
          );
        }}
      />
      <FormErrorMessage>{errors[name]?.message as string}</FormErrorMessage>
    </FormControl>
  );
};

export default SelectFormField;
