import React from 'react';
import { useFormContext } from 'react-hook-form';
import { FormControl, FormLabel, Input, FormErrorMessage, Tooltip } from '@chakra-ui/react';

interface InputFormFieldProps {
  name: string;
  label: string;
  isDisabled?: boolean;
  defaultValue?: string;
  options?: {
    dlArfcn_min?: number;
    dlArfcn_max?: number;
  };
  mx?: string;
  mr?: string;
  ml?: string;
  tooltip?: string;
  placeholder?: string;
  onChangeHandler?: (value: string) => void;
}

const InputFormField: React.FC<InputFormFieldProps> = ({
  name,
  label,
  isDisabled,
  defaultValue,
  options,
  mx,
  mr,
  ml,
  tooltip,
  placeholder,
  onChangeHandler,
}) => {
  const {
    register,
    formState: { errors },
    trigger,
  } = useFormContext();

  const getNestedError = (path: string) => {
    const fields = path.split('.');
    let error = errors;

    for (const field of fields) {
      if (!error || !error[field]) return undefined;
      error = error[field] as any;
    }

    return error;
  };

  const fieldError = getNestedError(name);

  return (
    <FormControl isInvalid={!!fieldError} mb="4" mx={mx ? mx : ''} ml={ml ? ml : ''} mr={mr ? mr : ''} isRequired>
      <Tooltip label={tooltip} placement="top-start">
        <FormLabel htmlFor={name}>{label}</FormLabel>
      </Tooltip>
      {name === 'arfcns' ? (
        <Input
          id={name}
          type="number"
          mr="2"
          disabled={isDisabled}
          {...register(name, {
            setValueAs: (val) => [parseInt(val, 10)],
          })}
          min={options?.dlArfcn_min}
          max={options?.dlArfcn_max}
          placeholder={
            options?.dlArfcn_min && options?.dlArfcn_max ? `${options.dlArfcn_min} - ${options.dlArfcn_max}` : ''
          }
          onBlur={() => trigger(name)}
          {...(onChangeHandler
            ? {
                onChange: (e) => {
                  const value = e.target.value;
                  onChangeHandler(value);
                  trigger(name);
                },
              }
            : {})}
        />
      ) : (
        <Input
          id={name}
          {...register(name)}
          type="text"
          placeholder={placeholder || `Enter ${label.toLowerCase()}`}
          disabled={isDisabled}
          {...(onChangeHandler
            ? {
                onChange: (e) => {
                  const value = e.target.value;
                  onChangeHandler(value);
                  trigger(name);
                },
              }
            : {})}
        />
      )}

      <FormErrorMessage>{fieldError && 'message' in fieldError ? String(fieldError.message) : ''}</FormErrorMessage>
    </FormControl>
  );
};

export default InputFormField;
