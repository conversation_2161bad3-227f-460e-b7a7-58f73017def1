import { cleanup, render, screen } from '@testing-library/react';
import { expect } from 'vitest';
import NodeConfigPortal from './Portal';

describe('NodeConfigPortal', () => {
  let portalRoot: HTMLDivElement;

  beforeEach(() => {
    portalRoot = document.createElement('div');
    portalRoot.setAttribute('id', 'node-config-portal');
    document.body.appendChild(portalRoot);
  });

  afterEach(() => {
    cleanup();
    document.body.removeChild(portalRoot);
  });

  it('renders the children inside the portal', () => {
    const { container } = render(
      <NodeConfigPortal>
        <div data-testid="child">Child</div>
      </NodeConfigPortal>
    );

    const child = screen.getByTestId('child');
    expect(portalRoot).toContainElement(child);
    expect(container).not.toContainElement(child);
  });
});
