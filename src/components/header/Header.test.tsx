import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { render, screen } from '@testing-library/react';
import { ReactNode } from 'react';
import { MemoryRouter } from 'react-router-dom';
import Header from './Index';

type ChildrenProps = {
  children: ReactNode;
};

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});
const wrapper = ({ children }: ChildrenProps) => (
  <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
);

describe('Header ', () => {
  it('Render the header', () => {
    render(
      <MemoryRouter>
        <Header />
      </MemoryRouter>,
      { wrapper }
    );

    screen.getAllByTestId('header-theme-switch');
  });
  it('Render the login icon', () => {
    render(
      <MemoryRouter>
        <Header />
      </MemoryRouter>,
      { wrapper }
    );

    screen.getByRole('img', { name: /avatar/i });
  });
});
