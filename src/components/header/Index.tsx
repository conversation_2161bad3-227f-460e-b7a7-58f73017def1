import {
  Avatar,
  AvatarBadge,
  Box,
  Button,
  ButtonGroup,
  Center,
  Circle,
  Container,
  Flex,
  HStack,
  IconButton,
  ListItem,
  Menu,
  MenuButton,
  MenuItem,
  MenuList,
  Popover,
  PopoverArrow,
  PopoverBody,
  PopoverContent,
  PopoverTrigger,
  Switch,
  UnorderedList,
  useColorMode,
  useColorModeValue,
} from '@chakra-ui/react';

import { MoonIcon, SettingsIcon, SunIcon } from '@chakra-ui/icons';
import { Logo } from '../logo/Index';

import { Link } from 'react-router-dom';
import { ReactComponent as Abstract } from '../../assets/icons/abstract.svg';
import { ReactComponent as Alarm } from '../../assets/icons/alarm.svg';
import { ReactComponent as AppsIcon } from '../../assets/icons/appsIcon.svg';
import { ReactComponent as Cell } from '../../assets/icons/cell.svg';
import { ReactComponent as Home } from '../../assets/icons/home.svg';
import { ReactComponent as Research } from '../../assets/icons/research.svg';
import { ReactComponent as OranDuCuManager } from '../../assets/icons/oranManager.svg';

import { AUTH_TOKEN_KEY, READ_WRITE_ACCESS_ROLES } from '../../data/constants';
import useLogin from '../../hooks/useLogin';

type HeaderProps = {
  defaultMenuIconColor?: string;
};
const Header = ({ defaultMenuIconColor = '' }: HeaderProps) => {
  const { toggleColorMode } = useColorMode();
  const Icon = useColorModeValue(MoonIcon, SunIcon);
  const appsIconFillColor = useColorModeValue('gray.800', 'white');
  const {
    jwtTokenFromLocalStorage: loginToken,
    removeTokenFromLocalStorage,
    decodeJwtToken,
    checkApplicationAccess,
  } = useLogin(AUTH_TOKEN_KEY);

  const decodedJwt = decodeJwtToken();
  const checkRoleAccess = checkApplicationAccess(READ_WRITE_ACCESS_ROLES);
  return (
    <Box as="nav" bg="bg-surface" boxShadow={useColorModeValue('sm', 'sm-dark')}>
      <Container
        py={{
          base: '3',
          lg: '4',
        }}
      >
        <Flex justify="space-between">
          <HStack>
            <Menu>
              <MenuButton as={Button} variant="link">
                <AppsIcon fill={defaultMenuIconColor ? defaultMenuIconColor : appsIconFillColor} />
              </MenuButton>
              <MenuList zIndex={2}>
                <MenuItem>
                  <Link style={{ width: '100%' }} to="/">
                    <Flex flexShrink={0} gap="2">
                      <Home width="50px" height="30px" />
                      <Center>Home</Center>
                    </Flex>
                  </Link>
                </MenuItem>
                <MenuItem>
                  <Link style={{ width: '100%' }} to="/cell-overview/cells">
                    <Flex>
                      <Circle bg="accent">
                        <Cell width="50px" height="50px" />
                      </Circle>{' '}
                      <Center ml={2}>Cell Overview</Center>
                    </Flex>
                  </Link>
                </MenuItem>
                <MenuItem>
                  <Link style={{ width: '100%' }} to="/alarms-and-events/alarms">
                    <Flex>
                      <Circle bg="accent">
                        <Research width="50px" height="50px" />
                      </Circle>
                      <Center ml={2}>Alarms and Events</Center>
                    </Flex>
                  </Link>
                </MenuItem>
                <MenuItem>
                  <Link style={{ width: '100%' }} to="/site-manager">
                    <Flex>
                      <Circle bg="accent">
                        <Abstract width="50px" height="50px" />
                      </Circle>
                      <Center ml={2}>Site Manager</Center>
                    </Flex>
                  </Link>
                </MenuItem>
                <MenuItem>
                  <Link style={{ width: '100%' }} to="/manifest-overview/device-manifests">
                    <Flex>
                      <Circle bg="accent">
                        <Alarm width="50px" height="50px" />
                      </Circle>
                      <Center ml={2}>Manifests</Center>
                    </Flex>
                  </Link>
                </MenuItem>
                <MenuItem>
                  <Link style={{ width: '100%' }} to="/oran-du-cu-manager">
                    <Flex>
                      <Circle bg="accent">
                        <OranDuCuManager width="50px" height="50px" />
                      </Circle>
                      <Center ml={2}>O-RAN DU/CU Manager</Center>
                    </Flex>
                  </Link>
                </MenuItem>
                <MenuItem>
                  <Link style={{ width: '100%' }} to="/software-upgrade/rollout">
                    <Flex>
                      <Circle bg="accent">
                        <Research width="50px" height="50px" />
                      </Circle>
                      <Center ml={2}>Software Upgrade</Center>
                    </Flex>
                  </Link>
                </MenuItem>
              </MenuList>
            </Menu>
          </HStack>
          <HStack>
            <Logo />
          </HStack>
          <HStack>
            <ButtonGroup variant="ghost" spacing="1">
              <IconButton mr="0.5em" icon={<SettingsIcon fontSize="1.25rem" />} aria-label="Settings" />
              <Switch
                textAlign="right"
                id="theme-switch"
                onChange={toggleColorMode}
                mt="0.6em"
                mr="0.5em"
                data-testid="header-theme-switch"
              >
                <Icon mb="0.4em" mr="0.5em" aria-label="toggle theme" />
              </Switch>
            </ButtonGroup>

            <Popover>
              <PopoverTrigger>
                <Avatar boxSize="10">
                  <AvatarBadge boxSize="1.25em" bg={loginToken ? 'green.500' : 'red.500'} data-testid="profile-icon" />
                </Avatar>
              </PopoverTrigger>
              <PopoverContent width="100%" data-testid="profile-icon-content">
                <PopoverArrow />
                <PopoverBody width="-moz-fit-content" p="1em">
                  {loginToken && decodedJwt ? (
                    <>
                      <Box textAlign="center">
                        {decodedJwt.FirstName} {decodedJwt.LastName}
                      </Box>
                      <Box textAlign="center">{decodedJwt.Organization}</Box>
                      <Box mb="1em" textAlign="center">
                        {decodedJwt.email}
                      </Box>
                      {checkRoleAccess && (
                        <Box mb="1em">
                          Roles:{' '}
                          <UnorderedList listStyleType="none">
                            {decodedJwt.role.map((role: string, index: number) => (
                              <ListItem fontSize="0.8em" key={index}>
                                {role}
                              </ListItem>
                            ))}
                          </UnorderedList>
                        </Box>
                      )}
                      {checkRoleAccess && (
                        <Box mb="1em">
                          Scope:{' '}
                          <UnorderedList listStyleType="none">
                            {decodedJwt.scope.map((scope: string, index: number) => (
                              <ListItem fontSize="0.8em" key={index}>
                                {scope}
                              </ListItem>
                            ))}
                          </UnorderedList>
                        </Box>
                      )}
                      <Center>
                        <Button width="100%" colorScheme="red" onClick={() => removeTokenFromLocalStorage()}>
                          Logout
                        </Button>
                      </Center>
                    </>
                  ) : (
                    <Button colorScheme="green">Login</Button>
                  )}
                </PopoverBody>
              </PopoverContent>
            </Popover>
          </HStack>
        </Flex>
      </Container>
      {/* <Container
        py={{
          base: '3',
          lg: '4',
        }}
      ></Container> */}
    </Box>
  );
};
export default Header;
