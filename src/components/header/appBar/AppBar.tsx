import { Circle, Flex } from '@chakra-ui/react';
import { Link } from 'react-router-dom';

import { ReactComponent as Abstract } from '../../../assets/icons/abstract.svg';
import { ReactComponent as Alarm } from '../../../assets/icons/alarm.svg';
import { ReactComponent as Cell } from '../../../assets/icons/cell.svg';
import { ReactComponent as Research } from '../../../assets/icons/research.svg';

const AppBar = () => {
  return (
    <Flex>
      <Link style={{ width: '100%', marginRight: '2rem' }} to="/cell-overview/cells">
        <Flex>
          <Circle bg="accent">
            <Cell width="50px" height="50px" />
          </Circle>{' '}
          {/* <Center ml={2}>Cell Overview</Center> */}
        </Flex>
      </Link>

      <Link style={{ width: '100%', marginRight: '2rem' }} to="/alarms-and-events/alarms">
        <Flex>
          <Circle bg="accent">
            <Research width="50px" height="50px" />
          </Circle>
          {/* <Center ml={2}>Events and Alarms</Center> */}
        </Flex>
      </Link>

      <Link style={{ width: '100%', marginRight: '2rem' }} to="/site-manager">
        <Flex>
          <Circle bg="accent">
            <Abstract width="50px" height="50px" />
          </Circle>
          {/* <Center ml={2}>Site Manager</Center> */}
        </Flex>
      </Link>

      <Link style={{ width: '100%', marginRight: '2rem' }} to="/manifest-overview/device-manifests">
        <Flex>
          <Circle bg="accent">
            <Alarm width="50px" height="50px" />
          </Circle>
          {/* <Center ml={2}>Manifests</Center> */}
        </Flex>
      </Link>
      <Link style={{ width: '100%', marginRight: '2rem' }} to="/oran-du-cu-manager">
        <Flex>
          <Circle bg="accent">
            <Cell width="50px" height="50px" />
          </Circle>
          {/* <Center ml={2}>OranDuCuManager</Center> */}
        </Flex>
      </Link>
    </Flex>
  );
};

export default AppBar;
