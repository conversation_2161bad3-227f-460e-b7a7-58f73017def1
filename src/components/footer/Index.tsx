import { EmailIcon, ExternalLinkIcon } from '@chakra-ui/icons';
import { ButtonGroup, Container, IconButton, Image, Stack, Text, useColorModeValue } from '@chakra-ui/react';
import darkLogo from '../../assets/images/darkLogo.png';
import logo from '../../assets/images/logo.png';
import SoftwareVersionPopover from './notifications/SoftwareVersionPopover';

const Footer = () => (
  <Container as="footer" role="contentinfo" py={{ base: '12', md: '5' }}>
    <Stack spacing={{ base: '4', md: '5' }}>
      <Stack justify="space-between" direction="row" align="center">
        <Image
          src={useColorModeValue(logo, darkLogo)}
          alt="Dense air"
          mb={{ base: '-1.5rem', md: '-2.5rem' }}
          ml="-0.5rem"
          height={{ base: '30px', md: '60px' }}
          data-testid="footer-main-image"
        />
        <ButtonGroup variant="ghost">
          <IconButton
            aria-label="Software versions info"
            icon={<SoftwareVersionPopover />}
            data-testid="footer-info-icon"
          />

          <IconButton
            as="a"
            href="mailto:<EMAIL>"
            aria-label="Email"
            icon={<EmailIcon fontSize="1.25rem" />}
            data-testid="footer-email-icon"
          />
          <IconButton
            as="a"
            href="https://denseair.atlassian.net/jira/software/c/projects/OS/boards/83"
            aria-label="External Link"
            target="_blank"
            rel="noopener noreferrer"
            icon={<ExternalLinkIcon fontSize="1.25rem" />}
            data-testid="footer-external-link-icon"
          />
        </ButtonGroup>
      </Stack>
      <Text fontSize="sm" color="subtle">
        &copy; {new Date().getFullYear()} Dense Air. All rights reserved.
      </Text>
    </Stack>
  </Container>
);
export default Footer;
