import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { fireEvent, render, screen } from '@testing-library/react';
import { ReactNode } from 'react';
import { MemoryRouter } from 'react-router-dom';
import Footer from '../Index';

type ChildrenProps = {
  children: ReactNode;
};

// Wrapper for react query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});
const wrapper = ({ children }: ChildrenProps) => (
  <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
);

describe('Software Version Popover ', () => {
  it('Show loading component', () => {
    render(
      <MemoryRouter>
        <Footer />
      </MemoryRouter>,
      { wrapper }
    );
    expect(screen.getByText(/loading.../i));
  });
  it('Render Software Version Popover Icon ', () => {
    render(
      <MemoryRouter>
        <Footer />
      </MemoryRouter>,
      { wrapper }
    );
    expect(screen.getByTestId('footer-info-icon'));
  });
  it('Open Software Version Popover', async () => {
    render(
      <MemoryRouter>
        <Footer />
      </MemoryRouter>,
      { wrapper }
    );
    const infoButton = screen.getByTestId('footer-info-icon');
    fireEvent.click(infoButton);
    await screen.findByText(/NMS Release/i);
    await screen.findByText(/Frontend/i);
  });
});
