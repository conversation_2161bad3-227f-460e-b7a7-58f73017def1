import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { render, screen } from '@testing-library/react';
import { ReactNode } from 'react';
import { MemoryRouter } from 'react-router-dom';
import { describe, it } from 'vitest';
import Footer from '../Index';

type ChildrenProps = {
  children: ReactNode;
};

// Wrapper for react query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});
const wrapper = ({ children }: ChildrenProps) => (
  <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
);
// Add snapshots when the specs for the views are fully agreed
describe('Footer ', () => {
  it('Render the footer', () => {
    render(
      <MemoryRouter>
        <Footer />
      </MemoryRouter>,
      { wrapper }
    );
    screen.getAllByTestId('footer-main-image');
    // screen.getAllByTestId('footer-email-icon');
    // screen.getAllByTestId('footer-external-link-icon');
    const emailIconLink = screen.getByRole('link', { name: 'Email' });
    expect(emailIconLink).toHaveAttribute('href', 'mailto:<EMAIL>');

    // Check for external link icon and its attributes
    const externalLinkIconLink = screen.getByRole('link', { name: 'External Link' });
    expect(externalLinkIconLink).toHaveAttribute(
      'href',
      'https://denseair.atlassian.net/jira/software/c/projects/OS/boards/83'
    );
    expect(externalLinkIconLink).toHaveAttribute('target', '_blank');
  });
});
