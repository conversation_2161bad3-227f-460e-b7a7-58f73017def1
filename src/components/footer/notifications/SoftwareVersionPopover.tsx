import { ExternalLinkIcon } from '@chakra-ui/icons';
import { Box, Link, Text } from '@chakra-ui/react';
import { useQuery } from '@tanstack/react-query';
import { STATUS, StatusToColor } from '../../../data/constants';
import { getServicesHealthCheck } from '../../../services/httpCommon';
import { StaticStatusCircleIcon } from '../../icons/StatusIcon';
import Loader from '../../loader/Loader';
import { UnstyledTable } from '../../nodeComponents/airspan/utils';
import { TimeElapsed } from '../../nodeComponents/server/acp/AcpCard';
import NmsPopover from '../../popover/Index';

interface NmsRelease {
  label: string;
  note: string;
  link: string;
  created_at: string;
}

interface SystemHealth {
  app: string;
  version: string;
  sha: string;
  started_at: string;
  up_time: string;
  status: STATUS;
}

interface ApiHealthData {
  [key: string]: NmsRelease | SystemHealth;
}

const SoftwareVersionPopover = () => {
  const { data: apiHealthData, isLoading } = useQuery<ApiHealthData, Error>({
    queryKey: ['apiHealth'],
    queryFn: () => getServicesHealthCheck(),
    retry: false,
  });
  if (isLoading) return <Loader />;

  const nmsReleaseData = apiHealthData && 'nms-release' in apiHealthData && apiHealthData['nms-release'];

  const Heading = nmsReleaseData ? (
    <Text fontSize="xl">
      <Link
        key="nms-release"
        textAlign="center"
        isExternal
        rel="noopener noreferrer"
        href={(nmsReleaseData as NmsRelease)?.link}
      >
        NMS Release {(nmsReleaseData as NmsRelease)?.label?.split(' ')[1]}
        <ExternalLinkIcon mx="2px" />
      </Link>
    </Text>
  ) : (
    <Text> NMS Release Not Found </Text>
  );

  const createDisplayDataEntry = (value: SystemHealth) => [
    value.version !== '' ? value.version : 'N/A',
    value.started_at ? <TimeElapsed key={value.app} initialUptime={value.started_at} /> : 'N/A',
    value.status ? (
      <StaticStatusCircleIcon
        key={value.app}
        color={StatusToColor[value.status as keyof typeof StatusToColor]}
        size={20}
      />
    ) : (
      <Text>N/A</Text>
    ),
  ];

  const DisplayData = apiHealthData
    ? Object.entries(apiHealthData).reduce((acc, [key, value]): Record<string, (string | JSX.Element)[]> => {
        if (value && 'app' in value && 'version' in value && 'started_at' in value) {
          acc[key.toUpperCase()] = createDisplayDataEntry(value as SystemHealth);
        }
        return acc;
      }, {} as Record<string, (string | JSX.Element)[]>)
    : {};

  const SortedBackendDisplayData = Object.keys(DisplayData || {})
    .sort()
    .reduce((obj, key): Record<string, (string | JSX.Element)[]> => {
      obj[key] = (DisplayData || {})[key];
      return obj;
    }, {} as Record<string, (string | JSX.Element)[]>);

  const FinalDisplayData = {
    FRONTEND: [APP_VERSION, 'N/A', <StaticStatusCircleIcon key={'frontend'} color={StatusToColor['OK']} size={20} />],
    ...SortedBackendDisplayData,
  };

  return (
    <NmsPopover heading={Heading} bodyWidth="xlg">
      <Box display="flex" flexDirection="column" justifyContent="start" width="100%">
        <Text fontSize="sm" align="left" ml="1">
          SOFTWARE VERSIONS
        </Text>
        <Box height="16px" />
        <UnstyledTable tableData={FinalDisplayData} fontSize="xs" />
      </Box>
    </NmsPopover>
  );
};

export default SoftwareVersionPopover;
