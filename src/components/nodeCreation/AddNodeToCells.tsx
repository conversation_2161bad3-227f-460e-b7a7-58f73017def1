import React, { Fragment, useEffect, useRef, useState } from 'react';
import { Box, Button, Table, Thead, Tbody, Tr, Th, Td, Heading, Stack, Icon, Flex, Text } from '@chakra-ui/react';
import { ArrowBackIcon } from '@chakra-ui/icons';
import { useLocation, useNavigate } from 'react-router-dom';
import Loader from '../loader/Loader';
import QueryError from '../errorComponents/QueryError';
import useGetNodeDataByNodeId from '../../pages/CellOverview/hooks/services/use_GetNodeDatabyNodeId';
import useGetCellList from '../../pages/CellOverview/hooks/services/use_Inv_GetCellList';
import useAddNodeToCell from '../../pages/CellOverview/hooks/services/use_Inv_AddNodeToCell';
import { useDeleteNodeFromCell } from '../../pages/CellOverview/hooks/services/use_Inv_DeleteNodeFromCell';
import useLogin from '../../hooks/useLogin';
import { AUTH_TOKEN_KEY, ROLE_TO_COUNTRY_MAPPING } from '../../data/constants';

const AddNodeToCell: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const state = location.state as { nodeType: string; nodeId: string } | null;
  const nodeId = state?.nodeId || '';
  const { getNMSRoles } = useLogin(AUTH_TOKEN_KEY);
  const roles: string[] = getNMSRoles();
  const validRoles: string[] = ['DenseAirGB', 'DenseAirUS'];
  const userCountryCode = roles
    .filter((role: string) => validRoles.includes(role))
    .map((role) => ROLE_TO_COUNTRY_MAPPING[role] || role) // Use the mapped value or role itself if not found in mappings
    .join(', ');
  const cellRefs = useRef<string[]>([]);
  const { isLoading, error: nodeError, data: nodeData } = useGetNodeDataByNodeId(nodeId);
  const { isLoading: isCellRefsLoading, error: cellError, data: cellRefData } = useGetCellList(1000);
  const selectedNode = nodeData?.find((node) => node.node_id === nodeId);
  const [selectedRefs, setSelectedRefs] = useState<string[]>([]);
  const error = nodeError || cellError;

  const addNode = useAddNodeToCell(nodeId);
  const { deleteNodeFromCellMutation } = useDeleteNodeFromCell(true);

  useEffect(() => {
    if (selectedNode?.cell_refs?.length) setSelectedRefs(selectedNode?.cell_refs);
  }, [selectedNode]);

  const handleToggleCellRef = (cellRef: string, action?: string) => {
    cellRefs.current.push(cellRef);
    if (action === 'add') addNode.mutate(cellRef);
    else deleteNodeFromCellMutation({ cell_ref: cellRef, node_id: nodeId });
  };

  if (isLoading || isCellRefsLoading) return <Loader />;
  if (error) return <QueryError error={error} />;
  const columnNames = ['Cell Reference', 'Oran Split', 'Site Name', 'Lifecycle'];
  const columnsHeaders = ['cell_ref', 'oran_split', 'site_name', 'lifecycle'];
  const columns = Object.keys(cellRefData[0])?.filter((cell: string) => columnsHeaders.includes(cell));

  return (
    <Stack spacing="4" p="5">
      <Flex alignItems="start" flexDirection="column" mb="2">
        <Flex alignItems="center">
          <Icon as={ArrowBackIcon} w="5" h="5" mr="4" onClick={() => navigate(-1)} />
          <Text size="md" fontSize="2xl" fontWeight="bold">
            Node Id: {selectedNode?.node_id}
          </Text>
        </Flex>
        <Box ml="9">
          <Text size="md" fontSize="lg" fontWeight="bold">
            Node Type: {selectedNode?.node_type}
          </Text>
        </Box>
      </Flex>

      <Flex p={5} justifyContent="space-between" gap="20">
        <Box flex="1" p="5" backgroundColor="white" data-testid="available-cells">
          <Heading size="md" mb="4" textAlign="center">
            Available Cells
          </Heading>
          <Table p="10" variant="simple" colorScheme="gray" data-testid="available-cells-table">
            <Thead>
              <Tr>
                {columnNames.map((header: string, index: number) => (
                  <Fragment key={index}>
                    <Th>{header}</Th>
                  </Fragment>
                ))}
                <Th>Action</Th>
              </Tr>
            </Thead>
            <Tbody>
              {cellRefData
                .filter(({ country_code }: { country_code: string }) => country_code === userCountryCode)
                .filter((cell: any) => !selectedRefs.includes(cell.cell_ref))
                .map((cell: any) => (
                  <Tr key={cell.cell_ref} background={cellRefs.current.includes(cell.cell_ref) ? 'brand.200' : ''}>
                    {columns.map((value: string) => (
                      <Fragment key={value}>
                        <Td>{cell[value]}</Td>
                      </Fragment>
                    ))}
                    <Td>
                      <Button
                        size="sm"
                        colorScheme="blue"
                        onClick={() => handleToggleCellRef(cell.cell_ref, 'add')}
                        data-testid="available-cells-add"
                      >
                        +
                      </Button>
                    </Td>
                  </Tr>
                ))}
            </Tbody>
          </Table>
        </Box>
        <Box flex="1" p="5" backgroundColor="white" data-testid="linked-cells">
          <Heading size="md" mb="4" textAlign="center">
            Linked Cells
          </Heading>
          <Table p="10" colorScheme="gray" variant="simple" data-testid="linked-cells-table">
            <Thead>
              <Tr>
                {columnNames.map((header: string, index: number) => (
                  <Fragment key={index}>
                    <Th>{header}</Th>
                  </Fragment>
                ))}
                <Th>Action</Th>
              </Tr>
            </Thead>
            <Tbody>
              {selectedRefs?.length > 0 ? (
                selectedRefs.map((cellRef) => {
                  const selectCellRef = cellRefData.find((cell: any) => cell.cell_ref === cellRef);
                  return (
                    <Tr key={cellRef} background={cellRefs.current.includes(cellRef) ? 'brand.200' : ''}>
                      {columns.map((value: any) => (
                        <Fragment key={value}>
                          <Td>{selectCellRef[value]}</Td>
                        </Fragment>
                      ))}
                      <Td>
                        <Button
                          size="sm"
                          colorScheme="red"
                          onClick={() => handleToggleCellRef(cellRef)}
                          data-testid="linked-cells-remove"
                        >
                          -
                        </Button>
                      </Td>
                    </Tr>
                  );
                })
              ) : (
                <Tr>
                  <Td colSpan={5} textAlign="center">
                    <Text fontSize="xl">{`This node is not linked to any of the cell reference.`}</Text>
                    <Text mt="2" fontSize="lg">
                      Try Adding this node to cell refs
                    </Text>
                  </Td>
                </Tr>
              )}
            </Tbody>
          </Table>
        </Box>
      </Flex>
    </Stack>
  );
};

export default AddNodeToCell;
