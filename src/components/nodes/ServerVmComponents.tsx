import { Box } from '@chakra-ui/react';
import Server from '../nodeComponents/server/server/Server';
import useGetServerNodeDetailsByNodeId from '../../pages/CellOverview/hooks/services/use_Orc_GetServerNodeDetailsByNodeId';
import QueryError from '../errorComponents/QueryError';
import Loader from '../loader/Loader';
import Vsr from '../nodeComponents/server/vsr/Vsr';
import Acp from '../nodeComponents/server/acp/Acp';
import Druid from '../nodeComponents/server/druid/Druid';
// import { getDruidNodeMockData } from '../../services/mocks/getDruidNodeMock';

const ServerVmComponents = ({ nodeOpen, queryNodeId }: { nodeOpen: boolean; queryNodeId: string }) => {
  //Query
  const { isLoading, error, data: serverNodeData } = useGetServerNodeDetailsByNodeId(queryNodeId, nodeOpen);
  // const serverNodeData = getDruidNodeMockData;
  if (isLoading) return <Loader />;
  if (error) return <QueryError error={error} />;

  return (
    <Box
      data-testid="cells-node-ServerComponents"
      marginLeft="8"
      justifyContent="space-between"
      display="flex"
      maxWidth="95%"
    >
      {serverNodeData?.server && <Server data={serverNodeData.server} nodeId={queryNodeId} />}
      {serverNodeData?.acp && <Acp data={serverNodeData.acp} nodeId={queryNodeId} />}
      {serverNodeData?.druid && <Druid data={serverNodeData.druid} nodeId={queryNodeId} />}
    </Box>
  );
};

export default ServerVmComponents;
