import { Box } from '@chakra-ui/react';
import { <PERSON><PERSON><PERSON> } from 'lodash';
import { useState, useEffect, Fragment } from 'react';
import useGetNodeComponents from '../../pages/CellOverview/hooks/services/use_Inv_GetNodeComponents';
import useGetNodeCompByNodeId from '../../pages/CellOverview/hooks/services/use_Orc_GetNodeCompByNodeId';
import { getManager } from '../../services/inventoryManager';
import QueryError from '../errorComponents/QueryError';
import Loader from '../loader/Loader';
import AirspanControlPlane from '../nodeComponents/airspan/controlPlane/AirspanControlPlane';
import AirspanDistributedUnit from '../nodeComponents/airspan/distributedUnit/AirspanDistributedUnit';
import AirspanRadioUnit from '../nodeComponents/airspan/radioUnit/AirspanRadioUnit';
import AirspanUserPlane from '../nodeComponents/airspan/userPlane/AirspanUserPlane';
import MmWave from '../nodeComponents/mmwave/MmWave';
import ParticleBoard from '../nodeComponents/particleboard/ParticleBoard';
import Switch from '../nodeComponents/switch/Switch';
import _ from 'lodash';
import AirspanXpuUnit from '../nodeComponents/airspan/xPU/AirspanXpuPlane';

const StreetCellComponents = ({
  nodeOpen,
  queryNodeId,
  nodeType,
}: {
  nodeOpen: boolean;
  queryNodeId: string;
  nodeType: string;
}) => {
  const [acpInstanceAddress, setAcpInstanceAddress] = useState<string | Falsey>();
  const [bluwirelessInstanceAddress, setBluwirelessInstanceAddress] = useState<string | Falsey>();

  //Query// This is failing for MeshRootComponents
  const { isLoading, error, data: componentData } = useGetNodeCompByNodeId(queryNodeId, false, nodeOpen);

  //Query
  const { data: nodeListComponentsData } = useGetNodeComponents(queryNodeId, false, nodeOpen);

  // Effects
  useEffect(() => {
    handleManagerData('AIRSPAN', acpInstanceAddress, setAcpInstanceAddress, nodeListComponentsData);
    handleManagerData('BLUWIRELESS', bluwirelessInstanceAddress, setBluwirelessInstanceAddress, nodeListComponentsData);
  }, [nodeListComponentsData]);

  //Derived state
  const isAirspanNodeNull = _.isNull(componentData?.sc_v1?.airspan_node);
  const airSpanNode = componentData?.sc_v1?.airspan_node;
  const gnbDuHaveData = airSpanNode?.gnbDu?.length > 0;
  const gnbCuCpHaveData = airSpanNode?.gnbCuCp?.length > 0;
  const gnbCuUpHaveData = airSpanNode?.gnbCuUp?.length > 0;
  const gnbRuHaveData = airSpanNode?.gnbRu?.length > 0;
  const gnbXpuHaveData = airSpanNode?.gnbXpu;
  const isMMWaveNull = _.isNull(componentData?.sc_v1?.bw_mmwave);
  const isParticleBoardNull = _.isNull(componentData?.sc_v1?.particle_board);
  const isSwitchNull = _.isNull(componentData?.sc_v1?.fibrolan_switch);

  if (isLoading) return <Loader />;
  if (error) return <QueryError error={error} />;
  return (
    <Box data-testid="cells-node-StreetCellComponents" marginLeft="8">
      <>
        {!isAirspanNodeNull && (
          <>
            <Box padding={'1rem 2rem'}>
              <AirspanXpuUnit
                nodeComponentData={componentData.sc_v1.airspan_node}
                nodeListComponentsData={nodeListComponentsData}
                acpInstanceAddress={acpInstanceAddress}
                gnbXpuHaveData={gnbXpuHaveData}
              />
            </Box>{' '}
            <Box padding={'1rem 2rem'} display="flex" justifyContent="space-between">
              <AirspanControlPlane
                nodeComponentsData={componentData}
                nodeListComponentsData={nodeListComponentsData}
                acpInstanceAddress={acpInstanceAddress}
                gnbCuCpHaveData={gnbCuCpHaveData}
              />

              <AirspanUserPlane
                nodeComponentsData={componentData}
                nodeListComponentsData={nodeListComponentsData}
                acpInstanceAddress={acpInstanceAddress}
                gnbCuUpHaveData={gnbCuUpHaveData}
              />
            </Box>
            <Box padding={'1rem 2rem'} display="flex" justifyContent="space-between">
              <AirspanDistributedUnit
                nodeComponentsData={componentData}
                nodeListComponentsData={nodeListComponentsData}
                acpInstanceAddress={acpInstanceAddress}
                gnbDuHaveData={gnbDuHaveData}
              />

              <AirspanRadioUnit
                nodeComponentsData={componentData}
                nodeListComponentsData={nodeListComponentsData}
                acpInstanceAddress={acpInstanceAddress}
                gnbRuHaveData={gnbRuHaveData}
                node_id={queryNodeId}
              />
            </Box>
          </>
        )}

        {!isParticleBoardNull && (
          <Box padding={'1rem 2rem'}>
            <ParticleBoard
              nodeComponentsData={componentData?.sc_v1}
              nodeListComponentsData={nodeListComponentsData}
              acpInstanceAddress={acpInstanceAddress}
              particleBoardHaveData={!isParticleBoardNull}
            />
          </Box>
        )}
        <Box padding={'1rem 2rem'} display="flex" justifyContent="space-between">
          {!isSwitchNull && (
            <Switch
              nodeComponentsData={componentData?.sc_v1}
              nodeListComponentsData={nodeListComponentsData}
              bluwirelessInstanceAddress={bluwirelessInstanceAddress}
              switchHaveData={!isSwitchNull}
              queryNodeId={queryNodeId}
              nodeType={nodeType}
              caller={'Cells'}
            />
          )}
          {!isMMWaveNull && (
            <MmWave
              nodeComponentsData={componentData}
              nodeListComponentsData={nodeListComponentsData}
              bluwirelessInstanceAddress={bluwirelessInstanceAddress}
              mmWaveHaveData={!isMMWaveNull}
            />
          )}
        </Box>
      </>
    </Box>
  );
};

export const handleManagerData = (
  componentType: string,
  instanceAddress: string | Falsey,
  setInstanceAddress: React.Dispatch<React.SetStateAction<string | Falsey>>,
  nodeListComponentsData?: any
) => {
  const manager = nodeListComponentsData?.find((item: any) => item.component_type === componentType);
  if (manager && !instanceAddress && manager.component_address === null) {
    const managerName = manager.manager_instance;
    //Query
    getManager(managerName).then((instance) => {
      // setInstanceAddress(instance[0].manager_url);
      setInstanceAddress(instance[0]?.manager?.manager_url);
    });
  } else if (manager) {
    setInstanceAddress(manager?.component_address);
  }
};

export default StreetCellComponents;
