import { Box } from '@chakra-ui/react';
import Server from '../../components/nodeComponents/server/server/Server';
import useGetServerNodeDetailsByNodeId from '../../pages/CellOverview/hooks/services/use_Orc_GetServerNodeDetailsByNodeId';
import QueryError from '../errorComponents/QueryError';
import Loader from '../loader/Loader';
import Vsr from '../nodeComponents/server/vsr/Vsr';
// import { getDruidNodeMockData } from '../../services/mocks/getDruidNodeMock';
import Druid from '../nodeComponents/server/druid/Druid';
// import { getVsrMock } from '../../services/mocks/VsrMock';
const MeshRootComponents = ({ queryNodeId }: { nodeOpen: boolean; queryNodeId: string }) => {
  //Query
  const { isLoading, error, data: serverNodeData } = useGetServerNodeDetailsByNodeId(queryNodeId);
  // const serverNodeData = getDruidNodeMockData;
  if (isLoading) return <Loader />;
  if (error) return <QueryError error={error} />;

  return (
    <Box data-testid="cells-node-MeshRootComponents" marginLeft="8">
      <Box display="flex" flexWrap="wrap" justifyContent="space-between">
        {serverNodeData?.server && <Server data={serverNodeData.server} nodeId={queryNodeId} />}
        {serverNodeData?.vsr && <Vsr data={serverNodeData.vsr} nodeId={queryNodeId} />}
        {serverNodeData?.druid && <Druid data={serverNodeData.druid} nodeId={queryNodeId} />}
      </Box>
    </Box>
  );
};
export default MeshRootComponents;
