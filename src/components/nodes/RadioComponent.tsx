import { Box } from '@chakra-ui/react';
import { ErrorBoundary } from 'react-error-boundary';
import { NODE_COMPONENT_TITLES } from '../../data/constants';
import useGetRadioNodeDetails from '../../pages/CellOverview/hooks/services/use_Orc_GetRadioNodeDetails';
import { ErrorBoundaryFallback, ErrorBoundaryLogError } from '../errorComponents/ErrorBoundaryFallback';
import NodeComponentErrorCard from '../errorComponents/NodeComponentErrorCard';
import RadioCard from '../nodeComponents/du-cuManager/RadioCard';
import Loader from '../loader/Loader';

const RadioComponent = ({
  nodeOpen,
  queryNodeId,
  nodeType,
  cellData,
}: {
  nodeOpen: boolean;
  queryNodeId: string;
  nodeType: string;
  cellData?: any;
}) => {
  //Query
  const { isLoading, error, data } = useGetRadioNodeDetails(queryNodeId, false, nodeOpen);

  if (isLoading) {
    return <Loader />;
  }

  return (
    <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
      <Box data-testid="cells-node-pod-radio-components" marginLeft="8">
        {data?.ru?.error || data?.ru?.error === null ? (
          <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
            <NodeComponentErrorCard
              id={data?.id}
              errorData={data?.ru}
              compName={NODE_COMPONENT_TITLES.ORAN_RU}
              testId="node-comp-error-card"
              marginSpace="8"
            />
          </ErrorBoundary>
        ) : (
          <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
            <RadioCard
              nodeOpen={nodeOpen}
              queryNodeId={queryNodeId}
              type={nodeType}
              data={data}
              isLoading={isLoading}
              error={error}
            />
          </ErrorBoundary>
        )}
      </Box>
    </ErrorBoundary>
  );
};

export default RadioComponent;
