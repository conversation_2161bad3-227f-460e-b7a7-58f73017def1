import { Box } from '@chakra-ui/react';
import Server from '../../components/nodeComponents/server/server/Server';
import useGetServerNodeDetailsByNodeId from '../../pages/CellOverview/hooks/services/use_Orc_GetServerNodeDetailsByNodeId';
import QueryError from '../errorComponents/QueryError';
import Loader from '../loader/Loader';
import Acp from '../nodeComponents/server/acp/Acp';
import Druid from '../nodeComponents/server/druid/Druid';
import Nexus from '../nodeComponents/server/nexus/Nexus';

const GcpVmComponents = ({ nodeOpen, queryNodeId }: { nodeOpen: boolean; queryNodeId: string }) => {
  //Query
  const { isLoading, error, data: serverNodeData } = useGetServerNodeDetailsByNodeId(queryNodeId, nodeOpen);

  // const serverNodeData = getDruidNodeMockData;
  if (isLoading) return <Loader />;
  if (error) return <QueryError error={error} />;

  return (
    <Box data-testid="cells-node-GcpVmComponents" marginLeft="8">
      <Box display="flex" flexWrap="wrap" justifyContent="space-between">
        {serverNodeData?.server && <Server data={serverNodeData.server} nodeId={queryNodeId} />}
        {serverNodeData?.acp && <Acp data={serverNodeData.acp} nodeId={queryNodeId} />}
        {serverNodeData?.druid && <Druid data={serverNodeData.druid} nodeId={queryNodeId} />}
        {serverNodeData?.cm && <Nexus data={serverNodeData.cm} nodeId={queryNodeId} />}
      </Box>
    </Box>
  );
};

export default GcpVmComponents;
