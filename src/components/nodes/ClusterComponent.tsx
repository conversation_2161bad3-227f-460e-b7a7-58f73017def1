import { Box } from '@chakra-ui/react';
import useGetClusterResourceByNodeId from '../../pages/CellOverview/hooks/services/use_Orc_GetClusterResources';
import Cluster from '../nodeComponents/server/cluster/Cluster';
import { useState } from 'react';

import { ErrorBoundary } from 'react-error-boundary';
import { ErrorBoundaryFallback, ErrorBoundaryLogError } from '../errorComponents/ErrorBoundaryFallback';
import NodeComponentErrorCard from '../errorComponents/NodeComponentErrorCard';
import { NODE_COMPONENT_TITLES } from '../../data/constants';

const ClusterComponent = ({
  nodeOpen,
  queryNodeId,
  compName,
  cellData,
}: {
  nodeOpen: boolean;
  queryNodeId: string;
  compName: string;
  cellData?: any;
}) => {
  const { isLoading, error, data: clusterData } = useGetClusterResourceByNodeId(queryNodeId, nodeOpen);

  return (
    <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
      <Box data-testid="cells-node-pod-du-components" marginLeft="8">
        {clusterData?.pod?.error || clusterData?.pod?.error === null ? (
          <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
            <NodeComponentErrorCard
              id={clusterData?.id}
              errorData={clusterData}
              compName={NODE_COMPONENT_TITLES.DU_POD}
              testId="node-comp-error-card"
              marginSpace="8"
            />
          </ErrorBoundary>
        ) : (
          <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
            {clusterData?.resources?.map((cluster: any, index: number) => (
              <Cluster key={index} data={cluster} nodeId={queryNodeId} compName={compName} />
            ))}
          </ErrorBoundary>
        )}
      </Box>
    </ErrorBoundary>
  );
};

export default ClusterComponent;
