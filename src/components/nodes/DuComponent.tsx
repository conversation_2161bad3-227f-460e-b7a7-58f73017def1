import { Box } from '@chakra-ui/react';
import { useState } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { NODE_COMPONENT_TITLES } from '../../data/constants';
import useGetPodNodeDetails from '../../pages/CellOverview/hooks/services/use_Orc_GetPodNodesDetails';
import { ErrorBoundaryFallback, ErrorBoundaryLogError } from '../errorComponents/ErrorBoundaryFallback';
import NodeComponentErrorCard from '../errorComponents/NodeComponentErrorCard';
import OranCard from '../nodeComponents/du-cuManager/OranCard';

const DuComponent = ({
  nodeOpen,
  queryNodeId,
  nodeType,
  cellData,
}: {
  nodeOpen: boolean;
  queryNodeId: string;
  nodeType: string;
  cellData?: any;
}) => {
  //Query
  const { isLoading, error, data } = useGetPodNodeDetails(queryNodeId, nodeOpen);

  return (
    <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
      <Box data-testid="cells-node-pod-du-components" marginLeft="8">
        {data?.pod?.error || data?.pod?.error === null ? (
          <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
            <NodeComponentErrorCard
              id={data?.id}
              errorData={data?.ru}
              compName={NODE_COMPONENT_TITLES.DU_POD}
              testId="node-comp-error-card"
              marginSpace="8"
            />
          </ErrorBoundary>
        ) : (
          <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
            <OranCard
              nodeOpen={nodeOpen}
              queryNodeId={queryNodeId}
              type={nodeType}
              data={data}
              isLoading={isLoading}
              error={error}
            />
          </ErrorBoundary>
        )}
      </Box>
    </ErrorBoundary>
  );
};

export default DuComponent;
