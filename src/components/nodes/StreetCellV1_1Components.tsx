import { Box } from '@chakra-ui/react';
import QueryError from '../errorComponents/QueryError';
import Loader from '../loader/Loader';
import useGet4gNodeCompByNodeId from '../../pages/CellOverview/hooks/services/use_Orc_Get4gNodeCompByNodeId';
import Airsapn4g from '../nodeComponents/airspan4g/Airspan4gCard';
import { ErrorBoundary } from 'react-error-boundary';
import { ErrorBoundaryFallback, ErrorBoundaryLogError } from '../errorComponents/ErrorBoundaryFallback';
import NodeComponentErrorCard from '../errorComponents/NodeComponentErrorCard';
import { ReactComponent as FourGIcon } from '../../assets/icons/4G.svg';
import { useEffect, useState } from 'react';
import { Falsey } from 'lodash';
import { handleManagerData } from './StreetCellComponents';
import useGetNodeComponents from '../../pages/CellOverview/hooks/services/use_Inv_GetNodeComponents';

const StreetCellV1_1Components = ({ nodeOpen, queryNodeId }: { nodeOpen: boolean; queryNodeId: string }) => {
  const [acpInstanceAddress, setAcpInstanceAddress] = useState<string | Falsey>();
  const { isLoading, error, data: streetcellv1_1_Data } = useGet4gNodeCompByNodeId(queryNodeId, false, nodeOpen);

  //Query
  const { data: nodeListComponentsData } = useGetNodeComponents(queryNodeId, false, nodeOpen);

  useEffect(() => {
    handleManagerData('AIRSPAN', acpInstanceAddress, setAcpInstanceAddress, nodeListComponentsData);
  }, [nodeListComponentsData]);

  if (isLoading) return <Loader />;
  if (error) return <QueryError error={error} />;

  return (
    <Box width="100%">
      {streetcellv1_1_Data.airspan_4g_node.error ? (
        <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
          <Box padding={'1rem 2rem'} marginLeft="8">
            <NodeComponentErrorCard
              id={queryNodeId}
              errorData={streetcellv1_1_Data.airspan_4g_node.error}
              compName={'Airspan'}
              testId="node-comp-error-card"
              linkText="View in ACP"
              linkUrl={null}
              marginSpace="8"
              CardIcon={FourGIcon}
            />
          </Box>
        </ErrorBoundary>
      ) : (
        <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
          <Box padding={'1rem 2rem'} marginLeft="8">
            <Airsapn4g
              streetcellv1_1_Data={streetcellv1_1_Data}
              nodeId={queryNodeId}
              acpInstanceAddress={acpInstanceAddress}
            />
          </Box>
        </ErrorBoundary>
      )}
    </Box>
  );
};

export default StreetCellV1_1Components;
