import { Image, useColorModeValue } from '@chakra-ui/react';
import { Link } from 'react-router-dom';
import darkLogo from '../../assets/images/darkLogo.png';
import logo from '../../assets/images/logo.png';
//TODO: add dark logo
export const Logo = () => (
  <Link to="/">
    <Image
      src={useColorModeValue(logo, darkLogo)}
      alt="Dense air"
      mb={{ base: '-1.5rem', md: '-2rem' }}
      // ml="-0.5rem"
      height={{ base: '75px', md: '80px' }}
      data-testid="logo-image"
    />
  </Link>
);
