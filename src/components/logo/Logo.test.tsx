import { render, screen } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { describe, expect, test, vi } from 'vitest';
import { Logo } from './Index';
describe('Logo', () => {
  test('renders the correct image', () => {
    render(
      <MemoryRouter>
        <Logo />
      </MemoryRouter>
    );
    const image = screen.getByTestId('logo-image') as HTMLImageElement;

    expect(image.src).toMatch(/logo.png/i);
  });

  //TODO need to investigate intermittent test failure cause
  test.skip('renders the correct image in dark mode', () => {
    // Set the color mode to dark
    vi.mock('@chakra-ui/react', async () => {
      const mod = await vi.importActual('@chakra-ui/react');
      return {
        ...(mod as Record<string, unknown>),
        useColorModeValue: vi.fn().mockImplementation((lightValue: string, darkValue: string): string => darkValue),
      };
    });

    render(
      <MemoryRouter>
        <Logo />
      </MemoryRouter>
    );
    const image = screen.getByTestId('logo-image') as HTMLImageElement;
    expect(image.src).toMatch(/darklogo.png/i);
  });

  test('navigates to the homepage when clicked', () => {
    render(<Logo />, { wrapper: MemoryRouter });
    const link = screen.getByRole('link') as HTMLAnchorElement;
    expect(link.href).toContain('/');
  });
});
