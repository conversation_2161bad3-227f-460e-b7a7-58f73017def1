import React from 'react';
import {
  Box,
  Step,
  StepDescription,
  StepIndicator,
  StepN<PERSON>ber,
  StepSeparator,
  StepStatus,
  StepTitle,
  Stepper,
} from '@chakra-ui/react';

interface Step {
  title: string;
  description: string;
}

interface StepperComponentProps {
  size?: string;
  steps?: Step[];
  activeStep?: number;
  setActiveStep: (index: number) => void;
  colorScheme?: string;
}

const defaultSteps: Step[] = [
  { title: 'First', description: 'Contact Info' },
  { title: 'Second', description: 'Date & Time' },
  { title: 'Third', description: 'Select Rooms' },
];

const StepperComponent: React.FC<StepperComponentProps> = ({
  size = 'lg',
  steps = defaultSteps,
  activeStep = 0,
  colorScheme,
}) => {
  return (
    <Stepper size={size} index={activeStep} colorScheme={colorScheme}>
      {steps.map((step, index) => (
        <Step key={index}>
          <StepIndicator>
            <StepStatus complete={<StepNumber />} incomplete={<StepNumber />} active={<StepNumber />} />
          </StepIndicator>
          <Box flexShrink="0">
            <StepTitle>{step.title}</StepTitle>
            <StepDescription>{step.description}</StepDescription>
          </Box>
          <StepSeparator />
        </Step>
      ))}
    </Stepper>
  );
};

export default StepperComponent;
