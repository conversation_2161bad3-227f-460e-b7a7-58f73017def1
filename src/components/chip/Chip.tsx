import { Badge, ChakraProps, Icon, useColorModeValue } from '@chakra-ui/react';
import { STATUS } from '../../data/constants';

const CircleIcon = (props: ChakraProps) => (
  <Icon viewBox="0 0 200 200" {...props}>
    <path fill="currentColor" d="M 100, 100 m -75, 0 a 75,75 0 1,0 150,0 a 75,75 0 1,0 -150,0" />
  </Icon>
);

interface ChipProps {
  statusText?: string;
  hasStatusLight?: boolean;
}

const Chip = ({ statusText, hasStatusLight = false }: ChipProps) => {
  const textColor = useColorModeValue('black', 'black');
  let color: string;
  let text: string;
  switch (statusText?.toLowerCase()) {
    case 'high':
    case 'major':
      color = 'red';
      text = statusText;
      break;
    case 'medium':
    case 'minor':
    case STATUS.ERROR:
      color = 'orange';
      text = statusText;
      break;
    case STATUS.WARNING.toLowerCase():
      color = 'yellow';
      text = statusText;
      break;
    case 'low':
    case 'none':
    case STATUS.OK:
      color = 'green';
      text = statusText;
      break;
    case STATUS.SHUTDOWN:
      color = 'gray';
      text = statusText;
      break;
    case STATUS.CRITICAL.toLowerCase():
      color = 'purple';
      text = statusText;
      break;
    case STATUS.UNKNOWN:
    default:
      color = 'lightgray';
      text = 'unknown';
  }

  const chipContent =
    text == 'unknown' ? (
      <>{statusText}</>
    ) : (
      <Badge
        borderRadius="full"
        px="2"
        letterSpacing={'0.03rem'}
        border="1px solid"
        borderColor={`${color}.500`}
        textTransform="capitalize"
        fontSize="0.8em"
        bgColor={`${color}.100`}
        lineHeight={'1.2rem'}
        color={textColor}
      >
        {hasStatusLight && <CircleIcon boxSize={2} color={`${color}.500`} marginRight="0.2rem" />}
        {text}
      </Badge>
    );

  return chipContent;
};

export default Chip;
