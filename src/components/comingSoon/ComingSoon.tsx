import { Bad<PERSON>, Card, CardHeader, Flex, Heading, Text } from '@chakra-ui/react';

const ComingSoon = ({
  componentName,
  marginRight = '8',
  Icon,
}: {
  componentName: string;
  marginRight?: string;
  Icon?: any;
}) => (
  <Card width="100%" marginRight={marginRight} data-test-id={componentName}>
    <CardHeader display="flex" alignItems="center" py={8} justifyContent="space-between">
      <Heading size="xs" fontWeight="500">
        <Flex alignItems="center">
          {Icon && <Icon width="30px" height="17px" />}
          <Text>
            {componentName}
            <Badge variant="outline" ml="1" fontSize="0.8em" colorScheme="green">
              Coming soon
            </Badge>
          </Text>
        </Flex>
      </Heading>
    </CardHeader>
  </Card>
);

export default ComingSoon;
