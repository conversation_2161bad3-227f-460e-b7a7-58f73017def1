import { Badge, Box, Flex, Table, Tbody, Td, Text, Tr } from '@chakra-ui/react';
import _ from 'lodash';
import { IconType } from 'react-icons';
import { MdCancel, MdCheckCircle, Md<PERSON>elp, MdHighlightOff } from 'react-icons/md';
import { UtilizationBar } from '../common/UtilizationBar';

interface DataPairProps {
  IconComponent?: IconType;
  label: string;
  value?: string | number;
  ml?: string;
  iconColor?: string;
  valueColor?: string;
}

export type ValueType =
  | string
  | number
  | boolean
  | JSX.Element
  | (string | number | boolean | JSX.Element)[]
  | undefined;

export type KeyValuePair = {
  key: string;
  value: ValueType;
};

export type UnstyledTableProps = {
  tableData: Record<string, ValueType> | KeyValuePair[];
  useValueBadge?: boolean;
  fontSize?: string;
  rowBorderBottom?: boolean;
  useKeyColon?: boolean;
  firstColWidth?: string;
};

const DataPair = ({ IconComponent, iconColor, label, value, valueColor, ml }: DataPairProps) => (
  <Flex justifyContent="flex-start" pt="2">
    {IconComponent && (
      <Box mr="2">
        <IconComponent size="25" color={iconColor ? iconColor : 'teal'} />
      </Box>
    )}
    <Text pt="1" fontSize="sm" color={valueColor ? valueColor : 'black'}>
      {label}
    </Text>
    {value ? (
      <Badge pt="1" pb="1" ml={ml ? ml : '2'} flexWrap="wrap">
        <Text color={valueColor ? valueColor : 'black'} wordBreak="break-word" whiteSpace="normal">
          {value}
        </Text>
      </Badge>
    ) : (
      <Text ml="1">{<MdCancel size="25" color="red" />}</Text>
    )}
  </Flex>
);

export const BooleanIndicator = ({ label, value }: any) => (
  <Box alignItems="center" display="flex">
    {label && (
      <Text pt="4" fontSize="sm" padding="0.1em" mr="2">
        {label}:{' '}
      </Text>
    )}
    {value === true ? <MdCheckCircle size="25" color="green" /> : <MdHighlightOff size="25" color="red" />}
  </Box>
);

export const DisplayTextInfo = ({
  label,
  value,
  valueml,
  labelFontSize,
}: {
  label: string;
  value: string | number | null | undefined;
  valueml?: string;
  labelFontSize?: string;
}) => {
  return (
    <Flex flexDirection="row" justifyItems="space-between" alignItems="center">
      <Text pt="2" pl="1" fontSize={labelFontSize ? labelFontSize : 'sm'}>
        {label}:
      </Text>
      {value ? (
        <Badge mt="2" pl="2" pr="2" ml={valueml}>
          {value}
        </Badge>
      ) : (
        <Text pt="2" pl="2">
          <MdHelp size="25" color="orange" />
        </Text>
      )}
    </Flex>
  );
};

export const ValueRepresentation = ({
  value,
  useValueBadge,
}: {
  value: string | number | boolean | JSX.Element | undefined;
  useValueBadge?: boolean;
}) => {
  if (_.isUndefined(value)) {
    return <MdHelp size="25" color="orange" />;
  } else if (_.isString(value)) {
    if (value.includes('%')) {
      return <UtilizationBar percentage={Number(value.replace('%', ''))} />;
    }
    return useValueBadge ? (
      <Badge pt="1" pb="1" ml="2" flexWrap="wrap">
        <Text color="black" wordBreak="break-word" whiteSpace="normal">
          {value}
        </Text>
      </Badge>
    ) : (
      <Text color="black" wordBreak="break-word" whiteSpace="normal">
        {value}
      </Text>
    );
  } else if (_.isNumber(value)) {
    return useValueBadge ? (
      <Badge whiteSpace="normal" fontSize="sm">
        {value}
      </Badge>
    ) : (
      <Text> {value}</Text>
    );
  } else if (_.isBoolean(value)) {
    return value === true ? <MdCheckCircle size="25" color="green" /> : <MdHighlightOff size="25" color="red" />;
  } else {
    return value;
  }
};

const convertObjectToKeyValueArray = (data: { [key: string]: any }): KeyValuePair[] => {
  const keyValueArray: KeyValuePair[] = [];
  for (const key in data) {
    if (Object.prototype.hasOwnProperty.call(data, key)) {
      keyValueArray.push({ key, value: data[key] });
    }
  }
  return keyValueArray;
};

export const UnstyledTable = ({
  tableData,
  useValueBadge,
  fontSize,
  rowBorderBottom = false,
  useKeyColon = true,
  firstColWidth = '50%',
}: UnstyledTableProps) => {
  const dataArray = Array.isArray(tableData) ? tableData : convertObjectToKeyValueArray(tableData);

  return (
    <Table variant="unstyled">
      <Tbody>
        {dataArray.map(({ key, value }, index) => (
          <Tr
            key={index}
            sx={{
              ...(rowBorderBottom ? { borderBottom: '2px', borderColor: 'gray.100' } : {}),
            }}
          >
            <Td p="1" width={firstColWidth}>
              <Text fontSize={fontSize ? fontSize : 'sm'}>
                {key}
                {useKeyColon ? ':' : ''}
              </Text>
            </Td>
            {_.isArray(value) ? (
              value.map((item, index) => (
                <Td p={1} key={index}>
                  <ValueRepresentation value={item} useValueBadge={useValueBadge} />
                </Td>
              ))
            ) : (
              <Td p="1">
                <ValueRepresentation value={value} useValueBadge={useValueBadge} />
              </Td>
            )}
          </Tr>
        ))}
      </Tbody>
    </Table>
  );
};

export default DataPair;
