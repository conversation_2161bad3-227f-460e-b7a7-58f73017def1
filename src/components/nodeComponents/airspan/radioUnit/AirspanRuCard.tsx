import { ExternalLinkIcon } from '@chakra-ui/icons';
import {
  Badge,
  Box,
  Button,
  Card,
  CardBody,
  CardHeader,
  Flex,
  Heading,
  Icon,
  Link,
  Stack,
  StackDivider,
  Text,
  useDisclosure,
} from '@chakra-ui/react';
import React, { useState } from 'react';
import { NODE_COMPONENT_TITLES, StatusToColor } from '../../../../data/constants';
import { recursiveSearch } from '../../../../utils/recursiveSearch';
import { StaticStatusCircleIcon } from '../../../icons/StatusIcon';
import ComponentModal from '../../ComponentModal';
import { BsArrowReturnRight } from 'react-icons/bs';
import { getComponentBgColor } from '../../utils';
import { MdWarning } from 'react-icons/md';
import _ from 'lodash';
import { ReactComponent as FiveGIcon } from '../../../../assets/icons/5G.svg';
import DataPair, { BooleanIndicator, UnstyledTable } from '../utils';

import ControlActions from '../../airspanControlActions/ControlActions';

const RuAs1900Details = ({ ruData }: { ruData: any }) => {
  const Table1 = {
    Bands: ruData?.bands,
    Bandwidth: ruData?.bandwidth,
  };
  const Table2 = {
    ['Center Frequency']: ruData?.centreFrequency,
    ['Duplex Mode']: ruData?.duplexMode,
    ['Nr Arfcn']: ruData?.nrArfcn,
  };

  return (
    <Flex justifyContent="space-between" mt="2">
      <Flex display="flex" flexWrap="wrap" flexDirection="column" justifyContent="space-between">
        <UnstyledTable tableData={Table1} />
      </Flex>
      <Flex display="flex" flexWrap="wrap" flexDirection="column" justifyContent="space-between">
        <UnstyledTable tableData={Table2} />
      </Flex>
    </Flex>
  );
};

const RuAs2900Details = ({ ruData }: { ruData: any }) => {
  const Table1 = {
    ['CBRS Enabled']: <BooleanIndicator value={ruData?.duCellCbrsState === 'Enabled'} />,
    ['CBSD FCC ID']: ruData?.cbsdProperties?.cbsdFccId,
  };
  const Table2 = {
    ['Antenna Gain']: ruData?.cbsdProperties?.cbsdAntennaGain,
    ['CBSD Meas Report Type']: ruData?.cbsdProperties?.cbsdMeasReportType,
  };

  return (
    <Flex justifyContent="space-between" mt="2">
      <Flex display="flex" flexWrap="wrap" flexDirection="column" justifyContent="space-between">
        <UnstyledTable tableData={Table1} />
      </Flex>
      <Flex display="flex" flexWrap="wrap" flexDirection="column" justifyContent="space-between">
        <UnstyledTable tableData={Table2} />
      </Flex>
    </Flex>
  );
};

const AirspanRuCard = ({ data, airspanNodeProperties, id, acpInstanceAddress, local_node_id }: any) => {
  const ruData = data[0] && data[0]?.sectorCarriers[0];

  const { isOpen, onOpen, onClose } = useDisclosure();

  const [showContent, setShowContent] = useState<boolean>(false);
  const ignore = [
    'ruSectorConfiguration',
    'ruSectorConfiguration',
    'gnbRuSectorServiceManagerProperties',
    'sectorCarriers',
  ];
  const flattenedData: any = recursiveSearch(data, ignore);
  const statusColor = StatusToColor[data[0].status as keyof typeof StatusToColor];
  const bgColor = getComponentBgColor(statusColor);
  const unitOverHeating = ruData?.ruSectorConfiguration?.gnbRuSectorServiceManagerProperties?.unitOverHeating;
  const outOfSync = ruData?.ruSectorConfiguration?.gnbRuSectorServiceManagerProperties?.outOfSync;

  return (
    <>
      <Card width="100%" borderRadius="lg">
        <CardHeader
          _hover={{
            bg: bgColor,
            cursor: 'pointer',
            boxShadow: `0 0 12px ${bgColor}`,
          }}
          transition="background-color 0.2s ease, box-shadow 0.2s ease"
          borderRadius="lg"
          onClick={() => setShowContent(!showContent)}
          display="flex"
          alignItems="center"
          justifyContent="space-between"
        >
          <Heading size="xs" fontWeight="500" display="flex" justifyContent="space-between">
            <Flex alignItems="center">
              <Icon as={BsArrowReturnRight} />
              <Flex ml="2">
                <FiveGIcon width="30px" height="30px" />
                <Text ml="2" mt="2">
                  {NODE_COMPONENT_TITLES.RU_5G}
                </Text>
              </Flex>
            </Flex>
          </Heading>
          <StaticStatusCircleIcon size={40} color={statusColor} />
        </CardHeader>
        {showContent && (
          <CardBody borderTop="1px solid #e2e2e2">
            <Stack divider={<StackDivider />} spacing="4">
              <Box display="flex" justifyContent="space-between">
                <Box width="50%">
                  <Text pt="2" fontSize="sm" mr="2">
                    Component id:{' '}
                    <Badge pl="2" pr="2">
                      {id}
                    </Badge>
                  </Text>
                </Box>
                <Box width="40%">
                  <ControlActions local_node_id={local_node_id} />
                </Box>
              </Box>

              <Box>
                <Heading size="xs" textTransform="uppercase">
                  RU Cell : {ruData?.duCell}
                </Heading>
                {/* TODO: Need to handle Split6 */}
                {'duCellCbrsState' in ruData ? (
                  <RuAs2900Details ruData={ruData} />
                ) : (
                  <RuAs1900Details ruData={ruData} />
                )}
              </Box>
              <Box display="flex" justifyContent="space-around">
                <BooleanIndicator label="Out of sync" value={outOfSync === 'Enabled'} />
                <BooleanIndicator label="Unit over heating" value={unitOverHeating === 'Enabled'} />
              </Box>

              {data[0].status !== 'OK' && (
                <DataPair
                  IconComponent={MdWarning}
                  iconColor="red"
                  label="Reason:"
                  value={data[0].reason}
                  valueColor="red"
                  ml="20px"
                />
              )}

              <Button onClick={onOpen}>More Data</Button>
              {acpInstanceAddress ? (
                <Link
                  textAlign="center"
                  isExternal
                  rel="noopener noreferrer"
                  href={`${acpInstanceAddress}/Management?DBID=${airspanNodeProperties.id}`}
                >
                  View in ACP
                  <ExternalLinkIcon mx="2px" />
                </Link>
              ) : (
                <Text>No manger Url</Text>
              )}
            </Stack>
          </CardBody>
        )}
      </Card>

      <ComponentModal isOpen={isOpen} onClose={onClose} flattenedData={flattenedData} />
    </>
  );
};

export default AirspanRuCard;
