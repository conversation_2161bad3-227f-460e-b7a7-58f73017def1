import { ErrorBoundary } from 'react-error-boundary';
import { ErrorBoundaryFallback, ErrorBoundaryLogError } from '../../../errorComponents/ErrorBoundaryFallback';
import { NODE_COMPONENT_TITLES } from '../../../../data/constants';
import ComingSoon from '../../../comingSoon/ComingSoon';
import { Box } from '@chakra-ui/react';
import NodeComponentErrorCard from '../../../errorComponents/NodeComponentErrorCard';
import AirspanRuCard from './AirspanRuCard';
import { ReactComponent as FiveGIcon } from '../../../../assets/icons/5G.svg';
import { AirspanRadioUnitProps } from '../types';

const AirspanRadioUnit = ({
  nodeComponentsData,
  nodeListComponentsData,
  acpInstanceAddress,
  gnbRuHaveData,
  node_id,
}: AirspanRadioUnitProps) => {
  return (
    <Box width={'50%'}>
      {nodeComponentsData.sc_v1.airspan_node.error || nodeComponentsData.sc_v1.airspan_node.error === null ? (
        <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
          <NodeComponentErrorCard
            id={nodeListComponentsData?.find((item: any) => item.component_type === 'AIRSPAN')?.component_id}
            errorData={nodeComponentsData.sc_v1.airspan_node}
            compName={NODE_COMPONENT_TITLES.RU_5G}
            testId="node-comp-error-card"
            linkText="View in ACP"
            linkUrl={acpInstanceAddress}
            marginSpace="8"
            CardIcon={FiveGIcon}
          />
        </ErrorBoundary>
      ) : (
        <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
          {gnbRuHaveData ? (
            <AirspanRuCard
              data={nodeComponentsData.sc_v1.airspan_node.gnbRu}
              airspanNodeProperties={nodeComponentsData.sc_v1.airspan_node.nodeProperties}
              id={nodeListComponentsData?.find((item: any) => item.component_type === 'AIRSPAN')?.component_id}
              acpInstanceAddress={acpInstanceAddress}
              local_node_id={node_id}
            />
          ) : (
            <ComingSoon componentName="RU" Icon={FiveGIcon} />
          )}
        </ErrorBoundary>
      )}
    </Box>
  );
};

export default AirspanRadioUnit;
