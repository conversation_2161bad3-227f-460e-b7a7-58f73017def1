import { Box, Flex, Heading } from '@chakra-ui/react';
import { FaMapMarkerAlt } from 'react-icons/fa';
import DataPair, { BooleanIndicator } from './utils';

const NodeProperties = ({ airspanData }: any) => {
  const node = airspanData.nodeProperties;
  const plmData = airspanData.gnbProperties?.primaryPlmnInfo?.plmn;
  return (
    <Box display="flex" justifyContent="space-between" flexDirection="column">
      <Heading size="sm" textTransform={'uppercase'}>
        Node Details
      </Heading>
      <Box display="flex" flexWrap="wrap" justifyContent="space-between" mt="1">
        <Box display="flex" flexWrap="wrap" flexDirection="column" mr="10">
          <DataPair label="Node Serial No:" value={node?.name} />
          <DataPair label="Node Rest Id:" value={node?.id} ml="5" />
        </Box>
        <Box display="flex" flexWrap="wrap" flexDirection="column">
          <DataPair IconComponent={FaMapMarkerAlt} label="Lat:" value={node?.latitude} ml="5" />
          <DataPair IconComponent={FaMapMarkerAlt} label="Long:" value={node?.longitude} />
        </Box>
      </Box>
      <Box display="flex" flexWrap="wrap" flexDirection="row" justifyContent="space-between">
        <Box display="flex" flexWrap="wrap" flexDirection="column" mt="5">
          <Heading size="xs" textTransform={'uppercase'}>
            PLMN
          </Heading>
          <Box display="flex" flexWrap="wrap" flexDirection="column" justifyContent="space-between">
            <DataPair label="MCC:" value={plmData?.mcc} />
            <DataPair label="MNC:" value={plmData?.mnc} />
          </Box>
        </Box>
        <Box display="flex" flexWrap="wrap" flexDirection="column" mt="10">
          <Box display="flex" flexWrap="wrap" justifyContent="space-between" mt="1">
            <BooleanIndicator label="Managed" value={node?.isManaged}></BooleanIndicator>
          </Box>
          <Box display="flex" flexWrap="wrap" justifyContent="space-between" mt="1">
            <BooleanIndicator
              label="NbifEventAlarmForwarding"
              value={node?.isNbifEventAlarmForwarding}
            ></BooleanIndicator>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default NodeProperties;
