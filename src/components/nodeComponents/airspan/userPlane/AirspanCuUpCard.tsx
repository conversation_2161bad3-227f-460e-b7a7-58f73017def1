import { ExternalLinkIcon } from '@chakra-ui/icons';
import {
  Badge,
  Box,
  Button,
  Card,
  CardBody,
  CardHeader,
  Flex,
  Heading,
  Icon,
  Link,
  Stack,
  StackDivider,
  Text,
  useDisclosure,
} from '@chakra-ui/react';
import { useState } from 'react';
import { NODE_COMPONENT_TITLES, StatusToColor } from '../../../../data/constants';
import { getStatusColor } from '../../../../pages/CellOverview/hooks/useStatus';
import { recursiveSearch } from '../../../../utils/recursiveSearch';
import StatusComponent, { StaticStatusCircleIcon } from '../../../icons/StatusIcon';
import ComponentModal from '../../ComponentModal';
import { BsArrowReturnRight } from 'react-icons/bs';
import { getComponentBgColor } from '../../utils';
import { ReactComponent as FiveGIcon } from '../../../../assets/icons/5G.svg';
import DataPair from '../utils';
import { MdWarning } from 'react-icons/md';
import { ComponentTypeEnum } from '../../../../services/types';

const AirspanCuUpCard = ({ data, node, id, acpInstanceAddress }: any) => {
  const cuUpData = data[0] && data[0]?.endpointList[0];
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [showContent, setShowContent] = useState(false);

  const flattenedData: any = recursiveSearch(data);
  const statusColor = getStatusColor(data[0].status);
  const bgColor = getComponentBgColor(statusColor);
  return (
    <>
      <Card width="100%" borderRadius="lg">
        <CardHeader
          _hover={{
            bg: bgColor,
            cursor: 'pointer',
            boxShadow: `0 0 12px ${bgColor}`,
          }}
          transition="background-color 0.2s ease, box-shadow 0.2s ease"
          borderRadius="lg"
          onClick={() => setShowContent(!showContent)}
          display="flex"
          alignItems="center"
          justifyContent="space-between"
        >
          <Heading size="xs" fontWeight="500" display="flex" justifyContent="space-between">
            <Flex alignItems="center">
              <Icon as={BsArrowReturnRight} />
              <Flex ml="2">
                <FiveGIcon width="30px" height="30px" />
                <Text ml="2" mt="2">
                  {NODE_COMPONENT_TITLES.CU_UP_5G}
                </Text>
              </Flex>
            </Flex>
          </Heading>
          <StaticStatusCircleIcon size={40} color={StatusToColor[data[0].status as keyof typeof StatusToColor]} />
        </CardHeader>
        {showContent && (
          <CardBody borderTop={'1px solid #e2e2e2'}>
            <Stack divider={<StackDivider />} spacing="4">
              <Box>
                <Text pt="2" fontSize="sm">
                  Component id:{' '}
                  <Badge pl="2" pr="2">
                    {id}
                  </Badge>
                </Text>
              </Box>
              <Box>
                <Heading size="xs" textTransform={'uppercase'}>
                  Local Details
                </Heading>
                <Box display="flex" flexWrap="wrap">
                  <Text pt="2" pr="2" fontSize="sm">
                    Local address:{' '}
                    <Badge pl="2" pr="2">
                      {cuUpData?.localAddress ?? 'No Data'}
                    </Badge>
                  </Text>
                  <Text pt="2" fontSize="sm">
                    Port:{' '}
                    <Badge pl="2" pr="2">
                      {cuUpData?.localPort ?? 'No Data'}
                    </Badge>
                  </Text>
                </Box>
              </Box>
              <Box>
                <Heading size="xs" textTransform={'uppercase'}>
                  Remote Details
                </Heading>
                <Box display="flex" flexWrap="wrap">
                  <Text pt="2" fontSize="sm">
                    Remote address:{' '}
                    <Badge pl="2" pr="2">
                      {cuUpData?.remoteAddress ?? 'No Data'}
                    </Badge>
                  </Text>
                  <Text pt="2" px="2" fontSize="sm">
                    Remote port:{' '}
                    <Badge pl="2" pr="2">
                      {cuUpData?.remotePort ?? 'No Data'}
                    </Badge>
                  </Text>
                  <Text pt="2" fontSize="sm">
                    Remote cell id:{' '}
                    <Badge pl="2" pr="2">
                      {cuUpData?.remoteId ?? 'No Data'}
                    </Badge>
                  </Text>
                </Box>
              </Box>

              {data[0].status !== 'OK' && (
                <DataPair
                  IconComponent={MdWarning}
                  iconColor={'red'}
                  label="Reason:"
                  value={data[0].reason}
                  valueColor={'red'}
                  ml="20px"
                />
              )}

              <Button onClick={onOpen}>More Data</Button>

              <Link
                textAlign="center"
                isExternal
                rel="noopener noreferrer"
                href={`${acpInstanceAddress}/Management?DBID=${node.id}`}
              >
                View in ACP
                <ExternalLinkIcon mx={'2px'} />
              </Link>
            </Stack>
          </CardBody>
        )}
      </Card>

      {/* Modal */}
      <ComponentModal isOpen={isOpen} onClose={onClose} flattenedData={flattenedData} />
    </>
  );
};

export default AirspanCuUpCard;
