import { ErrorBoundary } from 'react-error-boundary';
import { ErrorBoundaryFallback, ErrorBoundaryLogError } from '../../../errorComponents/ErrorBoundaryFallback';
import { NODE_COMPONENT_TITLES } from '../../../../data/constants';
import ComingSoon from '../../../comingSoon/ComingSoon';
import { Box, Card, Text } from '@chakra-ui/react';
import NodeComponentErrorCard from '../../../errorComponents/NodeComponentErrorCard';
import AirspanCuUpCard from './AirspanCuUpCard';
import { Falsey } from 'lodash';
import { ReactComponent as FiveGIcon } from '../../../../assets/icons/5G.svg';

type AirspanUserPlaneProps = {
  nodeComponentsData: any;
  nodeListComponentsData: any;
  acpInstanceAddress: string | Falsey;
  gnbCuUpHaveData: boolean;
};

const AirspanUserPlane = ({
  nodeComponentsData,
  nodeListComponentsData,
  acpInstanceAddress,
  gnbCuUpHaveData,
}: AirspanUserPlaneProps) => {
  return (
    <Box width={'50%'}>
      {nodeComponentsData.sc_v1.airspan_node.error || nodeComponentsData.sc_v1.airspan_node.error === null ? (
        <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
          <NodeComponentErrorCard
            id={nodeListComponentsData?.find((item: any) => item.component_type === 'AIRSPAN')?.component_id}
            errorData={nodeComponentsData.sc_v1.airspan_node}
            compName={NODE_COMPONENT_TITLES.CU_UP_5G}
            testId="node-comp-error-card"
            linkText="View in ACP"
            linkUrl={acpInstanceAddress}
            marginSpace="8"
            CardIcon={FiveGIcon}
          />
        </ErrorBoundary>
      ) : (
        <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
          {gnbCuUpHaveData ? (
            <AirspanCuUpCard
              data={nodeComponentsData.sc_v1.airspan_node.gnbCuUp}
              node={nodeComponentsData.sc_v1.airspan_node.nodeProperties}
              id={nodeListComponentsData?.find((item: any) => item.component_type === 'AIRSPAN')?.component_id}
              acpInstanceAddress={acpInstanceAddress}
            />
          ) : (
            <ComingSoon componentName="CU User plane" Icon={FiveGIcon} />
          )}
        </ErrorBoundary>
      )}
    </Box>
  );
};

export default AirspanUserPlane;
