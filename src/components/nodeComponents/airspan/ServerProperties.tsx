import { Badge, Box, Flex, Heading, Text } from '@chakra-ui/react';
import { FaInfoCircle } from 'react-icons/fa';
import DataPair from './utils';

const ACPServerProperties = ({ airspanData }: { airspanData: any }) => {
  return (
    <Box>
      <Heading size="sm" textTransform={'uppercase'}>
        Server Details
      </Heading>
      <Box display="flex" justifyContent="space-between" flexDirection="row">
        <DataPair IconComponent={FaInfoCircle} label="Platform Version:" value={airspanData.platformVersion} />
        <DataPair IconComponent={FaInfoCircle} label="Application Version:" value={airspanData.applicationVersion} />
      </Box>
    </Box>
  );
};

export default ACPServerProperties;
