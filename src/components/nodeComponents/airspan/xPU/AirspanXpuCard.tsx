import { ExternalLinkIcon } from '@chakra-ui/icons';
import {
  Badge,
  Box,
  Button,
  Card,
  CardBody,
  CardHeader,
  Divider,
  Flex,
  HStack,
  Heading,
  Icon,
  Link,
  Stack,
  StackDivider,
  Text,
  VStack,
  useDisclosure,
} from '@chakra-ui/react';
import { useState } from 'react';
import { NODE_COMPONENT_TITLES, StatusToColor } from '../../../../data/constants';
import { getStatusColor } from '../../../../pages/CellOverview/hooks/useStatus';
import { recursiveSearch } from '../../../../utils/recursiveSearch';
import StatusComponent, { StaticStatusCircleIcon } from '../../../icons/StatusIcon';
import ComponentModal from '../../ComponentModal';
import NodeProperties from '../NodeProperties';
import { BsArrowReturnRight } from 'react-icons/bs';
import { getComponentBgColor } from '../../utils';
import { MdOpenInNew, MdSettingsEthernet, MdWarning } from 'react-icons/md';
import { FaEthernet, FaIdCard } from 'react-icons/fa';
import { ReactComponent as FiveGIcon } from '../../../../assets/icons/5G.svg';
import ACPServerProperties from '../ServerProperties';
import DataPair from '../utils';
import { ComponentTypeEnum } from '../../../../services/types';

const AirspanXpuCard = ({ airspanData, acpInstanceAddress }: any) => {
  const xpuData = airspanData.gnbXpu;
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [showContent, setShowContent] = useState(false);

  const flattenedData: any = recursiveSearch(xpuData);
  const nodeStatusColor = getStatusColor(airspanData.status);
  const bgColor = getComponentBgColor(nodeStatusColor);

  return (
    <>
      <Card width="100%" borderRadius="lg">
        <CardHeader
          _hover={{
            bg: bgColor,
            cursor: 'pointer',
            boxShadow: `0 0 12px ${bgColor}`,
          }}
          transition="background-color 0.2s ease, box-shadow 0.2s ease"
          borderRadius="lg"
          onClick={() => setShowContent(!showContent)}
          display="flex"
          alignItems="center"
          justifyContent="space-between"
        >
          <Box minWidth={'min-content'}>
            <Flex boxSize={10} />
          </Box>
          <Heading size="xs" fontWeight="500" display="flex" justifyContent="space-between" alignItems="center">
            <Flex alignItems="center">
              <Icon as={BsArrowReturnRight} />
            </Flex>
            <Flex ml="2" justifyContent="center">
              <FiveGIcon width="30px" height="30px" />
              <Text ml="2" mt="2">
                {NODE_COMPONENT_TITLES.NODE}
              </Text>
            </Flex>
          </Heading>
          <Box minWidth={'min-content'}>
            <StatusComponent
              dataTestId="cell-main-table-status-icon"
              status={airspanData.status}
              boxSize="sm"
              color={nodeStatusColor}
              component_type={ComponentTypeEnum.AIRSPAN}
              component_id={airspanData.serialNumber}
            />
          </Box>
        </CardHeader>
        {showContent && (
          <CardBody borderTop={'1px solid #e2e2e2'}>
            <Flex pb="6" justifyContent="space-between">
              <Box ml="5" width="50%">
                <Box mt="10">
                  <NodeProperties airspanData={airspanData} />
                </Box>
                <Box mt="10">
                  <ACPServerProperties airspanData={airspanData} />
                </Box>
                {airspanData.status !== 'OK' && (
                  <DataPair
                    IconComponent={MdWarning}
                    iconColor={'red'}
                    label="Reason:"
                    value={airspanData.reason}
                    valueColor={'red'}
                    ml="20px"
                  />
                )}
                {acpInstanceAddress ? (
                  <Box display="flex" mt="10" alignItems="center" justifyContent="space-around" flexDirection="row">
                    <Link
                      textAlign="center"
                      isExternal
                      rel="noopener noreferrer"
                      href={`${acpInstanceAddress}/Management?DBID=${airspanData.nodeProperties.id}`}
                    >
                      <Flex alignItems="center">
                        <Text> View in ACP</Text>
                        <MdOpenInNew size="20" color="teal" />
                      </Flex>
                    </Link>
                  </Box>
                ) : (
                  <Text>No manger Url</Text>
                )}
              </Box>

              <VStack align="stretch" borderLeft="1px solid" borderColor="gray.200" m="10"></VStack>

              <Box mt="10" width="50%">
                <Heading size="sm" textTransform={'uppercase'}>
                  <Flex justifyContent="space-between" alignItems="center">
                    <Text>XPU Details</Text>
                    <StaticStatusCircleIcon
                      size={40}
                      color={StatusToColor[xpuData.status as keyof typeof StatusToColor]}
                    />
                  </Flex>
                </Heading>
                <Box display="flex" flexWrap="wrap" flexDirection="column" justifyContent="center" marginTop="2">
                  <DataPair IconComponent={FaIdCard} label="Xpu Rest Id:" value={xpuData.id} />
                  <DataPair
                    IconComponent={MdSettingsEthernet}
                    label="IP Address:"
                    value={xpuData.netconfProperties?.ipAddress}
                    ml="3"
                  />
                  <DataPair
                    IconComponent={FaEthernet}
                    label="Port:"
                    value={xpuData.netconfProperties?.port}
                    ml="60px"
                  />
                  {xpuData.status !== 'OK' && (
                    <DataPair
                      IconComponent={MdWarning}
                      iconColor={'red'}
                      label="Reason:"
                      value={xpuData.reason}
                      valueColor={'red'}
                      ml="20px"
                    />
                  )}
                  <Box display="flex" mt="5" justifyContent="space-around">
                    <Button onClick={onOpen} width="100px" mt="10">
                      More Data
                    </Button>
                  </Box>
                </Box>
              </Box>
            </Flex>
          </CardBody>
        )}
      </Card>

      {/* Modal */}
      <ComponentModal isOpen={isOpen} onClose={onClose} flattenedData={flattenedData} />
    </>
  );
};

export default AirspanXpuCard;
