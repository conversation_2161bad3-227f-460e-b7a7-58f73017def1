import { ErrorBoundary } from 'react-error-boundary';
import { ErrorBoundaryFallback, ErrorBoundaryLogError } from '../../../errorComponents/ErrorBoundaryFallback';
import { NODE_COMPONENT_TITLES } from '../../../../data/constants';
import ComingSoon from '../../../comingSoon/ComingSoon';
import { Box, Flex } from '@chakra-ui/react';
import NodeComponentErrorCard from '../../../errorComponents/NodeComponentErrorCard';
import AirspanXpuCard from './AirspanXpuCard';
import { Falsey } from 'lodash';
import { ReactComponent as FiveGIcon } from '../../../../assets/icons/5G.svg';

type AirspanXpuUnitProps = {
  nodeComponentData: any;
  nodeListComponentsData: any;
  acpInstanceAddress: string | Falsey;
  gnbXpuHaveData: boolean;
};

const AirspanXpuUnit = ({
  nodeComponentData,
  nodeListComponentsData,
  acpInstanceAddress,
  gnbXpuHaveData: gnbXpuHaveData,
}: AirspanXpuUnitProps) => {
  return (
    <Box width="100%">
      {nodeComponentData.error || nodeComponentData.error === null ? (
        <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
          <NodeComponentErrorCard
            id={nodeListComponentsData?.find((item: any) => item.component_type === 'AIRSPAN')?.component_id}
            errorData={nodeComponentData}
            compName={NODE_COMPONENT_TITLES.NODE}
            testId="node-comp-error-card"
            linkText="View in ACP"
            linkUrl={acpInstanceAddress}
            marginSpace="8"
            CardIcon={FiveGIcon}
          />
        </ErrorBoundary>
      ) : (
        <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
          {gnbXpuHaveData ? (
            <AirspanXpuCard airspanData={nodeComponentData} acpInstanceAddress={acpInstanceAddress} />
          ) : (
            <ComingSoon componentName="XPU" Icon={FiveGIcon} />
          )}
        </ErrorBoundary>
      )}
    </Box>
  );
};

export default AirspanXpuUnit;
