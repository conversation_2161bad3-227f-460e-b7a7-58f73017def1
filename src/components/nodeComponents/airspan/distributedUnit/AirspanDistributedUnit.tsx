import { ErrorBoundary } from 'react-error-boundary';
import AirspanDuCard from './AirspanDuCard';
import { ErrorBoundaryFallback, ErrorBoundaryLogError } from '../../../errorComponents/ErrorBoundaryFallback';
import { NODE_COMPONENT_TITLES } from '../../../../data/constants';
import ComingSoon from '../../../comingSoon/ComingSoon';
import { Box, Card, Text } from '@chakra-ui/react';
import NodeComponentErrorCard from '../../../errorComponents/NodeComponentErrorCard';
import { Falsey } from 'lodash';
import { ReactComponent as FiveGIcon } from '../../../../assets/icons/5G.svg';

type AirspanDistributedUnitProps = {
  nodeComponentsData: any;
  nodeListComponentsData: any;
  acpInstanceAddress: string | Falsey;
  gnbDuHaveData: boolean;
};

const AirspanDistributedUnit = ({
  nodeComponentsData,
  nodeListComponentsData,
  acpInstanceAddress,
  gnbDuHaveData,
}: AirspanDistributedUnitProps) => {
  return (
    <Box width={'50%'} marginRight="4">
      {nodeComponentsData.sc_v1.airspan_node.error || nodeComponentsData.sc_v1.airspan_node.error === null ? (
        <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
          <NodeComponentErrorCard
            id={nodeListComponentsData?.find((item: any) => item.component_type === 'AIRSPAN')?.component_id}
            errorData={nodeComponentsData.sc_v1.airspan_node}
            compName={NODE_COMPONENT_TITLES.DU_5G}
            testId="node-comp-error-card"
            linkText="View in ACP"
            linkUrl={acpInstanceAddress}
            marginSpace="8"
            CardIcon={FiveGIcon}
          />
        </ErrorBoundary>
      ) : (
        <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
          {gnbDuHaveData ? (
            <AirspanDuCard
              data={nodeComponentsData.sc_v1.airspan_node.gnbDu}
              node={nodeComponentsData.sc_v1.airspan_node.nodeProperties}
              id={nodeListComponentsData?.find((item: any) => item.component_type === 'AIRSPAN')?.component_id}
              marginSpace="8"
              acpInstanceAddress={acpInstanceAddress}
            />
          ) : (
            <ComingSoon componentName="DU" Icon={FiveGIcon} />
          )}
        </ErrorBoundary>
      )}
    </Box>
  );
};

export default AirspanDistributedUnit;
