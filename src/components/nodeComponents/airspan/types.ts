import { Falsey } from 'lodash';
import { ActionPhase, ServerActionType } from '../../../pages/CellOverview/hooks/services/use_Orc_ServerTasks';

export interface UsePostActionParams {
  local_node_id: string;
  selectedOption: ActionPhase | '';
  actionType: ServerActionType | '';
}

export interface AirspanRadioUnitProps {
  nodeComponentsData: any;
  nodeListComponentsData: any;
  acpInstanceAddress: string | Falsey;
  gnbRuHaveData: boolean;
  node_id: string;
}
