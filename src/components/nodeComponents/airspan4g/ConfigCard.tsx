import {
  Box,
  Card,
  Card<PERSON>ody,
  Card<PERSON>eader,
  Flex,
  <PERSON>ing,
  Icon,
  Stack,
  StackDivider,
  Text,
  VStack,
} from '@chakra-ui/react';

import { UnstyledTable } from '../airspan/utils';
import { BsArrowReturnRight } from 'react-icons/bs';
import React, { useState } from 'react';
import _ from 'lodash';
import LteCellCard from './LteCellCard';
import { processedData } from './helper';

const ConfigCard = ({ configData }: any) => {
  const [showContent, setShowContent] = useState(false);

  const basicInfo = {
    'Led Mode': configData.ledMode,
    'Nbif Event Alarm Forwarding': configData.nbifEventAlarmForwarding,
    'Management Profile': configData.managementProfile,
    'Advanced Config Profile': configData.advancedConfigProfile,
    'Fault Management Profile': configData.faultManagementProfile,
    'Network Profile': configData.networkProfile,
    'Security Profile': configData.securityProfile,
    'Son Profile': configData.sonProfile,
    'Sync Profile': configData.syncProfile,
    'System Default Profile': configData.systemDefaultProfile,
  };
  const processedBasicInfo = processedData(basicInfo);

  const basicInfoBool = {
    'Auto Hardware Swap': configData.autoHardwareSwap,
    'Call Trace Interface Enabled': configData.isCallTraceInterfaceEnabled,
    'CSon Server Interface Enabled': configData.isCSonServerInterfaceEnabled,
    'Enb Neighbour Management Group Enabled': configData.isEnbNeighbourManagementGroupEnabled,
    'M1 Interface Enabled': configData.isM1InterfaceEnabled,
    'M2 Interface Enabled': configData.isM2InterfaceEnabled,
    'PTP Slave Interface Enabled': configData.isPtpSlaveInterfaceEnabled,
    'S1C Interface Enabled': configData.isS1CInterfaceEnabled,
    'S1C SeGw Interface Enabled': configData.isS1CSeGwInterfaceEnabled,
    'S1U Interface Enabled': configData.isS1UInterfaceEnabled,
    'S1U SeGw Interface Enabled': configData.isS1USeGwInterfaceEnabled,
    'Twamp Sender Interface Enabled': configData.isTwampSenderInterfaceEnabled,
    'X2C Interface Enabled': configData.isX2CInterfaceEnabled,
    'X2U Interface Enabled': configData.isX2UInterfaceEnabled,
    'X2 Configuration Update For Neighbours Enabled': configData.isX2ConfigurationUpdateForNeighboursEnabled,
  };

  const processedBasicInfoBool = processedData(basicInfoBool);
  return (
    <>
      <Card width="100%" borderRadius="lg" p="4" data-testid="airspan4g-config-card">
        <CardHeader
          onClick={() => setShowContent(!showContent)}
          _hover={{
            cursor: 'pointer',
          }}
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          transition="background-color 0.2s ease, box-shadow 0.2s ease"
          borderRadius="lg"
        >
          <Heading size="xs" fontWeight="500" display="flex" justifyContent="space-between">
            <Flex alignItems="center">
              <Icon as={BsArrowReturnRight} />
              <Text ml="2">Enb Config</Text>
            </Flex>
          </Heading>
        </CardHeader>
        {showContent && (
          <CardBody borderTop={'1px solid #e2e2e2'}>
            <Stack divider={<StackDivider />} spacing="4">
              <Flex width="100%">
                <Box width="50%">
                  <Heading size="sm">
                    <Flex justifyContent="space-between" alignItems="center">
                      <Text>Basic Info</Text>
                    </Flex>
                  </Heading>
                  <Box display="flex" flexWrap="wrap" flexDirection="column" justifyContent="center" marginTop="2">
                    <UnstyledTable tableData={processedBasicInfo} />
                  </Box>
                </Box>
                <VStack align="stretch" borderLeft="1px solid" borderColor="gray.200" m="10"></VStack>
                <Box width="50%">
                  <Box display="flex" flexWrap="wrap" flexDirection="column" justifyContent="center" marginTop="2">
                    <UnstyledTable tableData={processedBasicInfoBool} />
                  </Box>
                </Box>
              </Flex>

              {configData.lteCellList.map((cell: any) =>
                cell.isEnabled === true ? <LteCellCard key={cell.cellNumber} lteCellData={cell} /> : null
              )}
            </Stack>
          </CardBody>
        )}
      </Card>
    </>
  );
};

export default ConfigCard;
