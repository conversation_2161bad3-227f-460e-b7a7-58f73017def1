import { Box, Card, CardBody, CardHeader, Flex, Heading, Icon, Text, VStack } from '@chakra-ui/react';

import { UnstyledTable } from '../airspan/utils';
import { BsArrowReturnRight } from 'react-icons/bs';
import React, { useState } from 'react';
import _ from 'lodash';

const SonPciStatusCard = ({ sonPciData }: any) => {
  const [showContent, setShowContent] = useState(false);

  return (
    <>
      <Card width="100%" borderRadius="lg" p="4" data-testid="airspan4g-config-card">
        <CardHeader
          onClick={() => setShowContent(!showContent)}
          _hover={{
            cursor: 'pointer',
          }}
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          transition="background-color 0.2s ease, box-shadow 0.2s ease"
          borderRadius="lg"
        >
          <Heading size="xs" fontWeight="500" display="flex" justifyContent="space-between">
            <Flex alignItems="center">
              <Icon as={BsArrowReturnRight} />
              <Text ml="2">Son Pci Status</Text>
            </Flex>
          </Heading>
        </CardHeader>
        {showContent && (
          <CardBody borderTop={'1px solid #e2e2e2'}>
            <Flex width="100%">
              <Box width="50%">
                <Heading size="sm">
                  <Flex justifyContent="space-between" alignItems="center">
                    <Text mt="4" mb="10">
                      Pci Status: {sonPciData.pciStatus}
                    </Text>
                  </Flex>
                </Heading>
              </Box>
            </Flex>

            <Flex justifyContent="space-between" gap="4">
              {sonPciData.cell.map((cell: any, index: number) => (
                <Flex key={index} width="48%">
                  <Box width="100%" key={index}>
                    <UnstyledTable tableData={cell} />
                  </Box>
                  {index !== sonPciData.cell.length - 1 && (
                    <VStack align="stretch" borderLeft="1px solid" borderColor="gray.200" mr="10"></VStack>
                  )}
                </Flex>
              ))}
            </Flex>
          </CardBody>
        )}
      </Card>
    </>
  );
};

export default SonPciStatusCard;
