import {
  Box,
  Card,
  CardBody,
  CardHeader,
  Flex,
  Heading,
  Icon,
  Stack,
  StackDivider,
  Text,
  VStack,
} from '@chakra-ui/react';

import { UnstyledTable } from '../airspan/utils';
import { BsArrowReturnRight } from 'react-icons/bs';

import React, { useState } from 'react';
import { FaLock, FaUnlock } from 'react-icons/fa';
import _ from 'lodash';
import { processedData } from './helper';

const LteCellCard = ({ lteCellData }: any) => {
  const [showContent, setShowContent] = useState(false);

  const bsicLteCellInfo = {
    'Cell Identity': lteCellData.cellIdentityHex,
    Discovered: lteCellData.isDiscovered,
    'Barring Policy':
      lteCellData.cellBarringPolicy === 'NotBarred' ? (
        <FaUnlock size="30" color="green" />
      ) : (
        <FaLock size="30" color="red" />
      ),
    'Csg Mode':
      lteCellData.csgMode === 'Open' ? <FaUnlock size="30" color="green" /> : <FaLock size="30" color="red" />,
    'Mobility Profile': lteCellData.mobilityProfile,
    'Prach Freq Offset': lteCellData.prachFreqOffset,
    'Prach Rsi': lteCellData.prachRsi,
    'Tracking Area Code': lteCellData.trackingAreaCode,
    'Cell Advanced Configuration Profile': lteCellData.cellAdvancedConfigurationProfile,

    ...(lteCellData.foreignNidReg !== undefined && { 'Foreign Nid Registration': lteCellData.foreignNidReg }),
    ...(lteCellData.foreignSidReg !== undefined && { 'Foreign Sid Registration': lteCellData.foreignSidReg }),
    ...(lteCellData.homeReg !== undefined && { 'Home Registration': lteCellData.homeReg }),
    ...(lteCellData.multipleNid !== undefined && { 'Multiple Nid': lteCellData.multipleNid }),
    ...(lteCellData.multipleSid !== undefined && { 'Multiple Sid': lteCellData.multipleSid }),
    ...(lteCellData.parameterReg !== undefined && { 'Parameter Registration': lteCellData.parameterReg }),
    ...(lteCellData.powerDownReg !== undefined && { 'Power Down Registration': lteCellData.powerDownReg }),
    ...(lteCellData.powerUpReg !== undefined && { 'Power Up Registration': lteCellData.powerUpReg }),
  };

  const processedBasicInfo = processedData(bsicLteCellInfo);

  return (
    <>
      <Card width="100%" borderRadius="lg" p="4" data-testid={`lte-cell-card-${lteCellData.cellNumber}`}>
        <CardHeader
          onClick={() => setShowContent(!showContent)}
          _hover={{
            cursor: 'pointer',
          }}
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          transition="background-color 0.2s ease, box-shadow 0.2s ease"
          borderRadius="lg"
        >
          <Heading size="xs" fontWeight="500" display="flex" justifyContent="space-between">
            <Flex alignItems="center">
              <Icon as={BsArrowReturnRight} />
              <Text ml="2">Lte Cell {lteCellData.cellNumber}</Text>
            </Flex>
          </Heading>
        </CardHeader>
        {showContent && (
          <CardBody borderTop={'1px solid #e2e2e2'}>
            <Stack divider={<StackDivider />} spacing="4" mt="5">
              <Flex width="100%">
                <Box ml="5" width="50%">
                  <Heading size="sm">
                    <Flex justifyContent="space-between" alignItems="center">
                      <Text>Basic Info</Text>
                    </Flex>
                  </Heading>
                  <Box display="flex" flexWrap="wrap" flexDirection="column" justifyContent="center" marginTop="2">
                    <UnstyledTable tableData={processedBasicInfo} />
                  </Box>
                </Box>
                {!_.isEmpty(lteCellData.cbsdDetails) && (
                  <>
                    {' '}
                    <VStack align="stretch" borderLeft="1px solid" borderColor="gray.200" m="10"></VStack>
                    <Box width="50%">
                      <Heading size="sm">
                        <Flex justifyContent="space-between" alignItems="center">
                          <Text>CBSD Details</Text>
                        </Flex>
                      </Heading>
                      <Box display="flex" flexWrap="wrap" flexDirection="column" justifyContent="center" marginTop="2">
                        <UnstyledTable tableData={lteCellData.cbsdDetails} />
                      </Box>
                    </Box>
                  </>
                )}
              </Flex>

              <Flex justifyContent="space-between">
                <LteCellComponentCard
                  key={`${lteCellData.cellNumber}-radio-profile`}
                  heading="Radio Profile"
                  data={lteCellData.radioProfile}
                />
                <LteCellComponentCard
                  key={`${lteCellData.cellNumber}-embms-profile`}
                  heading="Embms Profile"
                  data={lteCellData.embmsProfile}
                />
              </Flex>
              <Flex justifyContent="space-between">
                <LteCellComponentCard
                  key={`${lteCellData.cellNumber}-traffic-management-profile`}
                  heading="Traffic Management Profile"
                  data={lteCellData.trafficManagementProfile}
                />
                <LteCellComponentCard
                  key={`${lteCellData.cellNumber}-call-trace-profile`}
                  heading="Call Trace Profile"
                  data={lteCellData.callTraceProfile}
                />
              </Flex>
              <Flex justifyContent="space-between">
                <LteCellComponentCard
                  key={`${lteCellData.cellNumber}-csfb-cdma-2k-mobility`}
                  heading="CSFB CDMA 2k Mobility"
                  data={lteCellData.csfbCdma2kMobilityParam}
                />
                <LteCellComponentCard
                  key={`${lteCellData.cellNumber}-csfb-cdma-2k-sib8`}
                  heading="CSFB CDMA 2k Sib8"
                  data={lteCellData.csfbCdma2kSib8Param}
                />
              </Flex>
            </Stack>
          </CardBody>
        )}
      </Card>
    </>
  );
};

const LteCellComponentCard = ({ heading, data }: any) => {
  const [showContent, setShowContent] = useState(false);

  return (
    <Box width="50%" mr="4">
      <Card width="100%" borderRadius="lg" p="2" data-testid={`${heading}-card`}>
        <CardHeader
          onClick={() => setShowContent(!showContent)}
          _hover={{
            cursor: 'pointer',
          }}
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          transition="background-color 0.2s ease, box-shadow 0.2s ease"
          borderRadius="lg"
        >
          <Heading size="xs" fontWeight="500" display="flex" justifyContent="space-between">
            <Flex alignItems="center">
              <Icon as={BsArrowReturnRight} />
              <Text ml="2">{heading}</Text>
            </Flex>
          </Heading>
        </CardHeader>
        {showContent && (
          <CardBody borderTop={'1px solid #e2e2e2'}>
            <Stack divider={<StackDivider />} spacing="4">
              <UnstyledTable tableData={processedData(data)} />
            </Stack>
          </CardBody>
        )}
      </Card>
    </Box>
  );
};

export default LteCellCard;
