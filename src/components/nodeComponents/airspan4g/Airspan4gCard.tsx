import {
  Box,
  Button,
  Card,
  CardBody,
  CardHeader,
  Flex,
  Heading,
  Icon,
  Link,
  Stack,
  StackDivider,
  Text,
  useDisclosure,
} from '@chakra-ui/react';
import { useState } from 'react';
import { recursiveSearch } from '../../../utils/recursiveSearch';
import { getComponentBgColor } from '../utils';
import { UnstyledTable } from '../airspan/utils';
import { BsArrowReturnRight } from 'react-icons/bs';
import { StatusToColor } from '../../../data/constants';
import { StaticStatusCircleIcon } from '../../icons/StatusIcon';
import ComponentModal from '../ComponentModal';
import ConfigCard from './ConfigCard';
import { processedData } from './helper';
import SonPciStatusCard from './SonPciStatusCard';
import SonRsiStatusCard from './SonRsiStatusCard';
import { ReactComponent as FourGIcon } from './../../../assets/icons/4G.svg';
import ControlActions from '../airspanControlActions/ControlActions';
import { MdOpenInNew } from 'react-icons/md';

const Airsapn4g = ({ streetcellv1_1_Data, nodeId, acpInstanceAddress }: any) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [showContent, setShowContent] = useState(true);

  const flattenedData = recursiveSearch(streetcellv1_1_Data.airspan_4g_node);
  const bgColor = getComponentBgColor(streetcellv1_1_Data.airspan_4g_node.status);
  const data = streetcellv1_1_Data.airspan_4g_node;

  return (
    <>
      <Card width="100%" borderRadius="lg">
        <CardHeader
          onClick={() => setShowContent(!showContent)}
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          _hover={{
            bg: bgColor,
            cursor: 'pointer',
            boxShadow: `0 0 12px ${bgColor}`,
          }}
          transition="background-color 0.2s ease, box-shadow 0.2s ease"
          borderRadius="lg"
        >
          <Heading size="xs" fontWeight="500" display="flex" justifyContent="space-between">
            <Flex alignItems="center">
              <Icon as={BsArrowReturnRight} />
              <FourGIcon width="30" height="30" />
              <Text ml="2"> Airspan</Text>
            </Flex>
          </Heading>
          <StaticStatusCircleIcon
            size={40}
            color={StatusToColor[(data.status as keyof typeof StatusToColor) || StatusToColor['UNKNOWN']]}
          />
        </CardHeader>
        {showContent && (
          <CardBody borderTop={'1px solid #e2e2e2'}>
            {data.nodeName ? (
              <Stack divider={<StackDivider />} spacing="4">
                <Box display="flex" justifyContent="space-between" position="relative">
                  <Box width="50%">
                    <BasicInfoCard data={data} />
                  </Box>

                  <Box width="40%" position="relative">
                    <Box>
                      <ControlActions local_node_id={nodeId} />
                    </Box>

                    <Box
                      position="absolute"
                      bottom="0"
                      left="50%"
                      transform="translateX(-50%)"
                      width="100%"
                      textAlign="center"
                      pt={4}
                    >
                      <Link
                        textAlign="center"
                        isExternal
                        rel="noopener noreferrer"
                        href={`${acpInstanceAddress}/Management?DBID=${data.id}`}
                      >
                        <Flex alignItems="center" justifyContent="center">
                          <Text fontSize="1rem"> View in ACP</Text>
                          <MdOpenInNew size="20" color="teal" />
                        </Flex>
                      </Link>
                    </Box>
                  </Box>
                </Box>
                {data.enbConfig && <ConfigCard configData={data.enbConfig} />}
                {data.enbStatusSonPci && data.enbStatusSonRsi && (
                  <Flex justifyContent="space-between" gap="4">
                    <Box width="50%">
                      <SonPciStatusCard sonPciData={data.enbStatusSonPci} />
                    </Box>
                    <Box width="50%">
                      <SonRsiStatusCard sonRsiData={data.enbStatusSonRsi} />
                    </Box>
                  </Flex>
                )}
                <Button onClick={onOpen}>More Data</Button>
              </Stack>
            ) : (
              <Box>
                <Text fontSize="sm" color="red">
                  {' '}
                  {data.detail}
                </Text>
              </Box>
            )}
          </CardBody>
        )}
      </Card>
      <ComponentModal isOpen={isOpen} onClose={onClose} flattenedData={flattenedData} />
    </>
  );
};

const BasicInfoCard = ({ data }: any) => {
  const basicInfo = {
    'Node Name': data.nodeName,
    'ENodeB ID': data.enbConfig.eNodeBID,
    Description: data.enbConfig.description,
    'Site Name': data.enbConfig.site,
    Region: data.enbConfig.region,
    ...(data.status !== 'OK' && {
      'Status Reason': (
        <Text color="red" whiteSpace="normal">
          {data.reason}
        </Text>
      ),
    }),
  };
  const basicInfoProcessed = processedData(basicInfo);

  return (
    <Box ml="5" width="100%">
      <Heading size="sm">
        <Flex justifyContent="space-between" alignItems="center">
          <Text>Basic Info</Text>
        </Flex>
      </Heading>
      <Box display="flex" flexWrap="wrap" flexDirection="column" justifyContent="center" marginTop="2">
        <UnstyledTable tableData={basicInfoProcessed} />
      </Box>
    </Box>
  );
};

export default Airsapn4g;
