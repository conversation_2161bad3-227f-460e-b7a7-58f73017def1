import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>r,
  <PERSON>lex,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>ack,
  StackDivider,
  Text,
} from '@chakra-ui/react';
import { useState } from 'react';
import { StatusToColor } from '../../../data/constants';
import { StaticStatusCircleIcon } from '../../icons/StatusIcon';
import { BsArrowReturnRight } from 'react-icons/bs';
import { UtilizationBar } from '../common/UtilizationBar';
import { UnstyledTable } from '../airspan/utils';
import _ from 'lodash';
import { parseSize } from '../server/server/utils';
import { RenderErrorDetails, safeHasOwn } from './Helpers';
import { getComponentBgColor } from '../utils';
import { getStatusColor } from '../../../pages/CellOverview/hooks/useStatus';

type DiskResultsType = {
  [key: string]: {
    Size: string;
    Used: string;
    Avail: string;
    'Use%': string;
    'Mounted on': string;
  };
};

type DiskDataType = {
  name: string;
  status: string;
  cause: string;
  reason: string;
  repairs: string[];
  results: DiskResultsType;
  updated_at: string;
};

function sortDisksByUsageDesc(diskResults: DiskResultsType): DiskResultsType {
  // 1. Get [key, value] pairs (entries) from the object
  const entries = Object.entries(diskResults);

  // 2. Sort the entries based on the 'Use%' value
  entries.sort(([, diskA], [, diskB]) => {
    // Extract the numeric part of 'Use%' and handle potential errors
    const usageA = parseFloat(diskA['Use%']) || 0; // Default to 0 if parsing fails
    const usageB = parseFloat(diskB['Use%']) || 0; // Default to 0 if parsing fails

    // Sort descending (higher usage first)
    return usageB - usageA;
  });

  // 3. Convert the sorted array of entries back into an object
  const sortedDiskResults = Object.fromEntries(entries);

  return sortedDiskResults;
}

const DiskCard = ({ diskData }: { diskData: DiskDataType }) => {
  const [showDiskDetails, setShowDiskDetails] = useState(false);
  const diskResults: DiskResultsType = diskData.results;

  const sortedDiskResults = sortDisksByUsageDesc(diskResults);

  const convertToKeyValueArray = (data: any) => {
    const keyValueArray: { key: string; value: any }[] = [];
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        if (key === 'Use%') {
          continue;
        }
        keyValueArray.push({ key, value: data[key] });

        if (!_.isNull(data[key]) && !_.isUndefined(data[key]) && !_.isNaN(data[key])) {
          const totalMem = parseSize(data['Size']);

          if (key === 'Used') {
            const usedPercentage = parseFloat(((parseSize(data['Used']) / totalMem) * 100).toFixed(2));
            keyValueArray.push({
              key: `Used`,
              value: <UtilizationBar percentage={usedPercentage} usePadding={false} threshold={90} />,
            });
          }
        }
      }
    }
    return keyValueArray;
  };

  const renderTableData = (data: DiskResultsType) => {
    const keyValueArray = convertToKeyValueArray(data);
    return <UnstyledTable tableData={keyValueArray} fontSize="sm" rowBorderBottom={true} />;
  };
  const diskStatusColor = getStatusColor(diskData.status);
  const bgColor = getComponentBgColor(diskStatusColor);
  return (
    <Card>
      <Stack divider={<StackDivider />} spacing="4">
        <CardHeader
          onClick={() => setShowDiskDetails(!showDiskDetails)}
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          _hover={{
            bg: bgColor,
            cursor: 'pointer',
            boxShadow: `0 0 12px ${bgColor}`,
          }}
          borderRadius="lg"
          data-testid="disk-card-header"
          h="60px"
          minH="60px"
        >
          <Heading size="xs" fontWeight="500" display="flex" justifyContent="space-between" width="100%">
            <Flex alignItems="center" width="100%">
              <Icon as={BsArrowReturnRight} />
              <Text ml="2">DISK</Text>
              {diskResults && (
                <Box flex="1" ml="10" display="flex" flexDirection="row" alignItems="center">
                  <Text mr="2">Used</Text>
                  <Box flex="1">
                    <UtilizationBar
                      percentage={parseFloat(
                        ((parseSize(diskResults?.total?.Used) / parseSize(diskResults?.total?.Size)) * 100).toFixed(2)
                      )}
                      usePadding={false}
                      threshold={90}
                    />
                  </Box>
                </Box>
              )}
            </Flex>
          </Heading>
          <Box ml="5">
            <StaticStatusCircleIcon size={40} color={StatusToColor[diskData.status as keyof typeof StatusToColor]} />
          </Box>
        </CardHeader>
        {showDiskDetails && (
          <CardBody data-testid="disk-card-body">
            <Flex wrap="wrap">
              {safeHasOwn(diskData, 'status') && diskData.status !== 'OK' && (
                <>
                  <Flex ml="4" mb="4">
                    <RenderErrorDetails data={diskData} />
                  </Flex>
                  <Divider />
                </>
              )}
            </Flex>

            <Flex wrap="wrap" ml="4" justifyContent="center" mt="4">
              {diskResults && (
                <Box key="total" flexBasis="calc(50% - 8px)" mb={8}>
                  <Text fontWeight="bold" wordBreak="break-word" whiteSpace="normal">
                    Total
                  </Text>
                  {renderTableData(diskResults.total as unknown as DiskResultsType)}
                </Box>
              )}
              <Flex wrap="wrap" justifyContent="flex-start" width="100%">
                {diskResults &&
                  Object.entries(sortedDiskResults).map(([key, diskData]: [string, any], index, array) => {
                    if (key === 'total') return null;
                    const isLastItem = index === array.length - 1;
                    const isOdd = array.length % 2 !== 0;
                    return (
                      <Box
                        key={key}
                        flexBasis="calc(50% - 8px)"
                        mb={8}
                        ml={isLastItem && isOdd ? '0' : '8px'} // Align last item to the left if odd
                      >
                        <Text fontWeight="bold" wordBreak="break-word" whiteSpace="normal">
                          {key}
                        </Text>
                        {renderTableData(diskData)}
                      </Box>
                    );
                  })}
              </Flex>
            </Flex>
          </CardBody>
        )}
      </Stack>
    </Card>
  );
};

export default DiskCard;
