import {
  Card,
  CardBody,
  CardHeader,
  Flex,
  Heading,
  Icon,
  List<PERSON>tem,
  <PERSON>ack,
  StackDivider,
  Text,
  UnorderedList,
} from '@chakra-ui/react';
import { useState } from 'react';
import { BsArrowReturnRight } from 'react-icons/bs';
import { UnstyledTable } from '../airspan/utils';
import { StaticStatusCircleIcon } from '../../icons/StatusIcon';
import { StatusConstants, StatusToColor } from '../../../data/constants';
import _ from 'lodash';
import { getComponentBgColor } from '../utils';
import { getStatusColor } from '../../../pages/CellOverview/hooks/useStatus';
import { FiLock, FiUnlock } from 'react-icons/fi';
import { MdOutlineHelp } from 'react-icons/md';

const LockStatusIcon = ({ isLocked }: { isLocked: boolean }) => {
  return isLocked ? <FiLock size={30} color="green" /> : <FiUnlock size={30} color="red" />;
};

const PtpCard = ({ ptpData }: { ptpData: any }) => {
  const [showPtpDetails, setShowPtpDetails] = useState(false);

  const RenderErrorDetails = ({ data }: { data: any }) => {
    const errorDetails: { [key: string]: JSX.Element } = {};
    const results = data.results;
    const clockStatus = results?.spec?.clockStatus;

    if (clockStatus) {
      if (clockStatus === 'NOT_IN_USE') {
        errorDetails['PTP Clock Status'] = <MdOutlineHelp size={30} color="gray" />;
      } else {
        errorDetails['PTP Clock Status'] = <LockStatusIcon isLocked={false} />;
      }
    }

    if (ptpData.reason) {
      errorDetails['Reason'] = (
        <Text fontWeight="bold" fontSize="sm" whiteSpace="initial" color="red.500" mr="4">
          {ptpData.reason}
        </Text>
      );
    }

    if (ptpData.cause) {
      errorDetails['Cause'] = (
        <Text fontWeight="bold" wordBreak="break-word" whiteSpace="normal" color="red.500">
          {ptpData.cause}
        </Text>
      );
    }

    if (ptpData.repairs && ptpData.repairs.filter((repair: string) => repair !== '').length > 0) {
      errorDetails['Repairs'] =
        ptpData.repairs.length > 1 ? (
          <UnorderedList ml={ptpData.repairs.length > 1 ? 4 : 0}>
            {ptpData.repairs.map((repair: any) =>
              _.isEmpty(repair) ? null : (
                <ListItem pl={ptpData.repairs.length > 1 ? 2 : 0} pr="2" whiteSpace="initial" key={repair}>
                  <Text ml={ptpData.repairs.length > 1 ? 2 : 0} wordBreak="break-word" whiteSpace="normal">
                    {repair}
                  </Text>
                </ListItem>
              )
            )}
          </UnorderedList>
        ) : (
          <Text ml="2" wordBreak="break-word" whiteSpace="normal">
            {ptpData.repairs[0]}
          </Text>
        );
    }

    return (
      <Flex wrap="wrap" ml="4" justifyContent="space-around">
        <UnstyledTable tableData={errorDetails} firstColWidth="15%" />
      </Flex>
    );
  };

  const RenderPtpData = ({ data }: { data: any }) => {
    const results = data.results;
    const clockStatus = results.spec.clockStatus;
    return (
      <Flex flexDir="column">
        <Flex>
          <Text ml="5" mt="2" mr="2">
            PTP Clock Status:{' '}
          </Text>
          <LockStatusIcon isLocked={clockStatus === 'LOCKED'} />
        </Flex>
      </Flex>
    );
  };
  const ptpStatusColor = getStatusColor(ptpData.status);
  const bgColor = getComponentBgColor(ptpStatusColor);
  return (
    <Card>
      <Stack divider={<StackDivider />} spacing="4">
        <CardHeader
          onClick={() => setShowPtpDetails(!showPtpDetails)}
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          _hover={{
            bg: bgColor,
            cursor: 'pointer',
            boxShadow: `0 0 12px ${bgColor}`,
          }}
          borderRadius="lg"
          data-testid="ptp-card-header"
          h="60px"
          minH="60px"
        >
          <Heading size="xs" fontWeight="500" display="flex" justifyContent="space-between" width="100%">
            <Flex alignItems="center" width="100%">
              <Icon as={BsArrowReturnRight} />
              <Text ml="2">PTP</Text>
            </Flex>
          </Heading>
          <StaticStatusCircleIcon size={40} color={StatusToColor[ptpData.status as keyof typeof StatusToColor]} />
        </CardHeader>
        {showPtpDetails && (
          <CardBody data-testid="ptp-card-body">
            <Stack divider={<StackDivider />} spacing="4">
              {ptpData.status === StatusConstants.OK && <RenderPtpData data={ptpData} />}
              {ptpData.status === StatusConstants.ERROR && <RenderErrorDetails data={ptpData} />}
            </Stack>
          </CardBody>
        )}
      </Stack>
    </Card>
  );
};

export default PtpCard;
