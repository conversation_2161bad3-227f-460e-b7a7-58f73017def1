import {
  <PERSON>,
  Card,
  CardBody,
  Card<PERSON><PERSON>er,
  Di<PERSON>r,
  <PERSON>lex,
  <PERSON>ing,
  Icon,
  Stack,
  StackDivider,
  Text,
} from '@chakra-ui/react';
import { useState } from 'react';
import { StatusToColor } from '../../../data/constants';
import { BsArrowReturnRight } from 'react-icons/bs';
import { StaticStatusCircleIcon } from '../../icons/StatusIcon';
import { UnstyledTable } from '../airspan/utils';
import { RenderErrorDetails, safeHasOwn } from './Helpers';

const NetworkCard = ({ networkData }: { networkData: any }) => {
  const [showNetworkDetails, setShowNetworkDetails] = useState(false);
  const networkResults = networkData.results;

  const renderTableData = (data: any) => {
    const { 'IO Counters': ioCounters, ...rest } = data;
    const keyValueArray = {
      ...rest,
      ...ioCounters,
    };

    return <UnstyledTable tableData={keyValueArray} fontSize="xs" rowBorderBottom={true} />;
  };

  return (
    <Card>
      <Stack divider={<StackDivider />} spacing="4">
        <CardHeader
          onClick={() => setShowNetworkDetails(!showNetworkDetails)}
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          _hover={{
            cursor: 'pointer',
          }}
          borderRadius="lg"
          data-testid="network-card-header"
        >
          <Heading size="xs" fontWeight="500" display="flex" justifyContent="space-between" width="100%">
            <Flex alignItems="center" width="100%">
              <Icon as={BsArrowReturnRight} />
              <Text ml="2">NETWORK</Text>
              {/* TODO: decide which data to be shown on card header */}
              {/* <Box flex="1" ml="10" display="flex" flexDirection="row" alignItems="center">
                <Text mr="2">Used</Text>
                <Box flex="1">
                  <UtilizationBar
                    percentage={parseFloat(
                      ((parseSize(networkResults.total.Used) / parseSize(networkResults.total.Size)) * 100).toFixed(2)
                    )}
                    usePadding={false}
                    threshold={90}
                  />
                </Box>
              </Box> */}
            </Flex>
          </Heading>
          <Box ml="5">
            <StaticStatusCircleIcon size={40} color={StatusToColor[networkData.status as keyof typeof StatusToColor]} />
          </Box>
        </CardHeader>
        {showNetworkDetails && (
          <CardBody data-testid="network-card-body">
            <Flex wrap="wrap">
              {safeHasOwn(networkData, 'status') && networkData.status !== 'OK' && (
                <>
                  <Flex ml="4" mb="4">
                    <RenderErrorDetails data={networkData} />
                  </Flex>
                  <Divider />
                </>
              )}
            </Flex>

            <Flex wrap="wrap" ml="4" justifyContent="center">
              <Flex wrap="wrap" justifyContent="flex-start" width="100%">
                {safeHasOwn(networkResults, 'Network Interfaces') &&
                  Object.entries(networkResults['Network Interfaces']).map(
                    ([key, networkData]: [string, any], index, array) => {
                      const isLastItem = index === array.length - 1;
                      const isOdd = array.length % 2 !== 0;
                      return (
                        <Box
                          key={key}
                          flexBasis="calc(50% - 8px)"
                          mb={8}
                          ml={isLastItem && isOdd ? '0' : '8px'} // Align last item to the left if odd
                        >
                          <Text fontWeight="bold" wordBreak="break-word" whiteSpace="normal">
                            {key}
                          </Text>
                          {renderTableData(networkData)}
                        </Box>
                      );
                    }
                  )}
              </Flex>
            </Flex>
          </CardBody>
        )}
      </Stack>
    </Card>
  );
};

export default NetworkCard;
