import {
  <PERSON>,
  Card,
  CardBody,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON>r,
  <PERSON>lex,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>ack,
  <PERSON>ackD<PERSON>ider,
  Text,
} from '@chakra-ui/react';
import { useState } from 'react';
import { StaticStatusCircleIcon } from '../../icons/StatusIcon';
import { BsArrowReturnRight } from 'react-icons/bs';
import { UtilizationBar } from '../common/UtilizationBar';
import { UnstyledTable } from '../airspan/utils';

import _ from 'lodash';
import { StatusToColor } from '../../../data/constants';
import { parseSize } from '../server/server/utils';
import { safeHasOwn } from './Helpers';
import { snakeCaseToTitleCase } from '../server/druid/utils';
import { getComponentBgColor } from '../utils';
import { getStatusColor } from '../../../pages/CellOverview/hooks/useStatus';

const convertSize = (size: number) => {
  const units = ['KB', 'MB', 'GB', 'TB'];
  let value = size;
  let unitIndex = 0;

  while (value > 1024 && unitIndex < units.length - 1) {
    value /= 1024;
    unitIndex++;
  }

  return { value, unit: units[unitIndex] };
};
import { RenderErrorDetails } from './Helpers';

const MemoryCard = ({ memoryData }: { memoryData: any }) => {
  const [showMemoryDetails, setShowMemoryDetails] = useState(false);
  const memoryResults = memoryData.results;

  const convertToKeyValueArray = (memKey: string, data: any) => {
    const keyValueArray: { key: string; value: any }[] = [];
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        const formattedKey = snakeCaseToTitleCase(key);
        // Calculate percentage before conversion to maintain accuracy
        let percentageValue;
        if (
          !_.isNull(data[key]) &&
          !_.isUndefined(data[key]) &&
          !_.isNaN(data[key]) &&
          key !== 'total' &&
          parseSize(data[key]) !== 0
        ) {
          percentageValue = parseFloat(((parseSize(data[key]) / parseSize(data.total)) * 100).toFixed(2));
        }

        if (_.isNull(data[key])) {
          keyValueArray.push({ key: formattedKey, value: 'N/A' });
        } else {
          // Convert size to appropriate unit
          const { value, unit } = convertSize(parseSize(data[key]));
          keyValueArray.push({ key: formattedKey, value: _.isNull(data[key]) ? 'N/A' : `${value.toFixed(2)}${unit}` });

          // Add utilization bar for memory usage, excluding 'total'
          if (!_.isUndefined(percentageValue)) {
            if (memKey === 'Swap' && key === 'free') {
              // we dont want to show Swap Free Memory percentage
              continue;
            }
            keyValueArray.push({
              key: formattedKey,
              value: (
                <UtilizationBar
                  percentage={percentageValue}
                  usePadding={false}
                  threshold={key.includes('free') || key.includes('available') ? 10 : 90}
                  inverted={key.includes('free') || key.includes('available')}
                />
              ),
            });
          }
        }
      }
    }
    return keyValueArray;
  };

  const renderTableData = (memKey: string, data: any) => {
    const keyValueArray = convertToKeyValueArray(memKey, data);
    return <UnstyledTable tableData={keyValueArray} fontSize="sm" rowBorderBottom={true} />;
  };
  const memoryStatusColor = getStatusColor(memoryData.status);
  const bgColor = getComponentBgColor(memoryStatusColor);

  return (
    <Card>
      <Stack divider={<StackDivider />} spacing="4">
        <CardHeader
          onClick={() => setShowMemoryDetails(!showMemoryDetails)}
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          _hover={{
            bg: bgColor,
            cursor: 'pointer',
            boxShadow: `0 0 12px ${bgColor}`,
          }}
          borderRadius="lg"
          data-testid="memory-card-header"
          h="60px"
          minH="60px"
        >
          <Heading size="xs" fontWeight="500" display="flex" justifyContent="space-between" width="100%">
            <Flex alignItems="center" width="100%">
              <Icon as={BsArrowReturnRight} />
              <Text ml="2">MEMORY</Text>
              {memoryResults && (
                <Box flex="1" ml="3" display="flex" flexDirection="row" alignItems="center">
                  <Text mr="2">Used</Text>
                  <UtilizationBar
                    percentage={parseFloat(
                      ((parseSize(memoryResults.Mem.used) / parseSize(memoryResults.Mem.total)) * 100).toFixed(2)
                    )}
                    usePadding={false}
                    threshold={90}
                  />
                </Box>
              )}
            </Flex>
          </Heading>
          <Box ml="5">
            <StaticStatusCircleIcon size={40} color={StatusToColor[memoryData.status as keyof typeof StatusToColor]} />
          </Box>
        </CardHeader>
        {showMemoryDetails && (
          <CardBody data-testid="memory-card-body">
            <Flex wrap="wrap">
              {safeHasOwn(memoryData, 'status') && memoryData.status !== 'OK' && (
                <>
                  <Flex ml="4" mb="4">
                    <RenderErrorDetails data={memoryData} />
                  </Flex>
                  <Divider />
                </>
              )}
            </Flex>

            <Flex ml="4" justifyContent="space-around" mt="4">
              {memoryResults &&
                Object.entries(memoryResults).map(([key, memDetails]: [string, any]) => {
                  if (memDetails.total === 0) {
                    return null;
                  }
                  return (
                    <Box key={key} flexBasis="calc(50% - 8px)" mb={4}>
                      <Text fontWeight="bold">{key}</Text>
                      {renderTableData(key, memDetails)}
                    </Box>
                  );
                })}
            </Flex>
          </CardBody>
        )}
      </Stack>
    </Card>
  );
};

export default MemoryCard;
