import {
  <PERSON>,
  <PERSON>,
  <PERSON>B<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>lex,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  StackDivider,
  Text,
} from '@chakra-ui/react';
import { useState } from 'react';
import { StatusToColor } from '../../../data/constants';
import { StaticStatusCircleIcon } from '../../icons/StatusIcon';
import { BsArrowReturnRight } from 'react-icons/bs';
import { UtilizationBar } from '../common/UtilizationBar';
import { UnstyledTable } from '../airspan/utils';
import { RenderErrorDetails, safeHasOwn } from './Helpers';
import _ from 'lodash';
import { snakeCaseToTitleCase } from '../server/druid/utils';
import { getComponentBgColor } from '../utils';
import { getStatusColor } from '../../../pages/CellOverview/hooks/useStatus';

const CpuCard = ({
  cpuData,
}: {
  cpuData: { results: any; status: string; cause: string; reason: string; repairs: any };
}) => {
  const [showCpuDetails, setShowCpuDetails] = useState(false);
  const cpuResults = cpuData.results;

  const convertToKeyValueArray = (data: any) => {
    const keyValueArray: { key: string; value: any }[] = [];
    for (const key in data) {
      if (key === 'CPU' || key === 'PM' || key === 'AM') {
        continue;
      }
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        const isIdle = key === '%idle';
        keyValueArray.push({
          key: snakeCaseToTitleCase(key.replace('%', '')),
          value: key.includes('%') ? (
            <UtilizationBar percentage={data[key]} usePadding={false} threshold={isIdle ? 10 : 90} inverted={isIdle} />
          ) : (
            data[key]
          ),
        });
      }
    }
    return keyValueArray;
  };

  const renderTableData = (data: any) => {
    const keyValueArray = convertToKeyValueArray(data);
    return <UnstyledTable tableData={keyValueArray} fontSize="sm" rowBorderBottom={true} firstColWidth="30%" />;
  };

  const RenderCPUDetails = ({ cpuResults }: { cpuResults: any }) => {
    if ('all' in cpuResults) {
      return (
        <Flex wrap="wrap" ml="4" justifyContent="center" mt="4">
          <Box key="all" flexBasis="calc(50% - 8px)" mb="4">
            <Text fontWeight="bold" wordBreak="break-word" whiteSpace="normal">
              All
            </Text>
            {renderTableData(cpuResults.all)}
          </Box>
          <Flex wrap="wrap" justifyContent="flex-start" width="100%">
            {Object.entries(cpuResults).map(([cpuName, cpuDetails]: [string, any], index, array) => {
              if (cpuName === 'all') return null;
              const isLastItem = index === array.length - 1;
              return (
                <Box
                  key={cpuName}
                  flexBasis="calc(50% - 8px)"
                  mb="8"
                  ml={isLastItem && array.length % 2 !== 0 ? '0' : '8px'} // Align last item to the left if odd
                >
                  <Text fontWeight="bold" wordBreak="break-word" whiteSpace="normal">
                    {cpuName}
                  </Text>
                  {renderTableData(cpuDetails)}
                </Box>
              );
            })}
          </Flex>
        </Flex>
      );
    } else if ('PM' in cpuResults || 'AM' in cpuResults) {
      return (
        <Flex wrap="wrap" ml="4" justifyContent="center">
          {'PM' in cpuResults && (
            <Box key="all" flexBasis="calc(50% - 8px)" mb="4">
              <Text fontWeight="bold" wordBreak="break-word" whiteSpace="normal">
                PM
              </Text>
              {renderTableData(cpuResults.PM)}
            </Box>
          )}
          {'AM' in cpuResults && (
            <Box key="all" flexBasis="calc(50% - 8px)" mb="4">
              <Text fontWeight="bold" wordBreak="break-word" whiteSpace="normal">
                AM
              </Text>
              {renderTableData(cpuResults.AM)}
            </Box>
          )}
          <Flex wrap="wrap" justifyContent="flex-start" width="100%">
            {Object.entries(cpuResults).map(([cpuName, cpuDetails]: [string, any], index, array) => {
              if (cpuName === 'PM' || cpuName === 'AM') return null;
              const isLastItem = index === array.length - 1;
              return (
                <Box
                  key={cpuName}
                  flexBasis="calc(50% - 8px)"
                  mb="8"
                  ml={isLastItem && array.length % 2 !== 0 ? '0' : '8px'} // Align last item to the left if odd
                >
                  <Text fontWeight="bold" wordBreak="break-word" whiteSpace="normal">
                    {cpuName}
                  </Text>
                  {renderTableData(cpuDetails)}
                </Box>
              );
            })}
          </Flex>
        </Flex>
      );
    } else {
      return <Text>Unidentified cpu data</Text>;
    }
  };
  const renderTotalUsedPercentageBar = (cpuResults: any) => {
    if (cpuResults.all || cpuResults.PM || cpuResults.AM) {
      const idlePercentage =
        _.isUndefined(cpuResults.all) || _.isNull(cpuResults.all)
          ? _.isUndefined(cpuResults.PM) || _.isNull(cpuResults.PM)
            ? _.isUndefined(cpuResults.AM) || _.isNull(cpuResults.AM)
              ? 0
              : cpuResults.AM['%idle']
            : cpuResults.PM['%idle']
          : cpuResults.all['%idle'];
      const usedPercentage = (100 - parseFloat(idlePercentage)).toFixed(2);
      return <UtilizationBar percentage={parseFloat(usedPercentage)} usePadding={false} threshold={90} />;
    }

    return null;
  };

  const cpuStatusColor = getStatusColor(cpuData.status);
  const bgColor = getComponentBgColor(cpuStatusColor);
  return (
    <Card>
      <Stack divider={<StackDivider />} spacing="4">
        <CardHeader
          onClick={() => setShowCpuDetails(!showCpuDetails)}
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          _hover={{
            bg: bgColor,
            cursor: 'pointer',
            boxShadow: `0 0 12px ${bgColor}`,
          }}
          borderRadius="lg"
          data-testid="cpu-card-header"
          h="60px"
          minH="60px"
        >
          <Heading size="xs" fontWeight="500" display="flex" justifyContent="space-between" width="100%">
            <Flex alignItems="center" width="100%">
              <Icon as={BsArrowReturnRight} />
              <Text ml="2">CPU</Text>
              {safeHasOwn(cpuData, 'status') &&
                (safeHasOwn(cpuResults, 'all') || safeHasOwn(cpuResults, 'PM') || safeHasOwn(cpuResults, 'AM')) && (
                  <Box flex="2" ml="10" display="flex" flexDirection="row" alignItems="center">
                    <Text mr="2">Used</Text>
                    {renderTotalUsedPercentageBar(cpuResults)}
                  </Box>
                )}
            </Flex>
          </Heading>
          <Box ml="5">
            <StaticStatusCircleIcon size={40} color={StatusToColor[cpuData.status as keyof typeof StatusToColor]} />
          </Box>
        </CardHeader>
        {showCpuDetails && (
          <CardBody data-testid="cpu-card-body">
            <Flex wrap="wrap">
              {safeHasOwn(cpuData, 'status') && cpuData.status !== 'OK' && (
                <>
                  <Flex ml="4" mb="4">
                    <RenderErrorDetails data={cpuData} />
                  </Flex>
                  <Divider />
                </>
              )}
            </Flex>

            <RenderCPUDetails cpuResults={cpuResults} />
          </CardBody>
        )}
      </Stack>
    </Card>
  );
};

export default CpuCard;
