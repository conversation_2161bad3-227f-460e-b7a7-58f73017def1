import { UnstyledTable } from '../airspan/utils';
import { Flex, ListItem, Text, UnorderedList } from '@chakra-ui/react';
import _ from 'lodash';

export const safeHasOwn = (obj: any, prop: string): boolean => {
  return obj != null && Object.hasOwn(obj, prop);
};

export const RenderErrorDetails = ({ data }: { data: any }) => {
  const errorDetails: { [key: string]: JSX.Element } = {};

  if (safeHasOwn(data, 'reason')) {
    errorDetails['Reason'] = (
      <Text fontWeight="bold" fontSize="sm" whiteSpace="initial" color="red.500" mr="4">
        {data.reason}
      </Text>
    );
  }

  if (safeHasOwn(data, 'cause')) {
    errorDetails['Cause'] = (
      <Text fontWeight="bold" wordBreak="break-word" whiteSpace="normal" color="red.500">
        {data.cause}
      </Text>
    );
  }

  if (safeHasOwn(data, 'repairs') && data.repairs.filter((repair: string) => repair !== '').length > 0) {
    errorDetails['Repairs'] =
      data.repairs.length > 1 ? (
        <UnorderedList ml={data.repairs.length > 1 ? 4 : 0}>
          {data.repairs.map((repair: any) =>
            _.isEmpty(repair) ? null : (
              <ListItem pl={data.repairs.length > 1 ? 2 : 0} pr="2" whiteSpace="initial" key={repair}>
                <Text ml={data.repairs.length > 1 ? 2 : 0} wordBreak="break-word" whiteSpace="normal">
                  {repair}
                </Text>
              </ListItem>
            )
          )}
        </UnorderedList>
      ) : (
        <Text ml="2" wordBreak="break-word" whiteSpace="normal">
          {data.repairs[0]}
        </Text>
      );
  }

  return <UnstyledTable tableData={errorDetails} firstColWidth="15%" />;
};
