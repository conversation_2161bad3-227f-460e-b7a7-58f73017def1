import {
  Card,
  CardBody,
  CardHeader,
  Flex,
  Heading,
  Icon,
  ListItem,
  Stack,
  StackDivider,
  Text,
  UnorderedList,
} from '@chakra-ui/react';
import { useState } from 'react';
import { BsArrowReturnRight } from 'react-icons/bs';
import { UnstyledTable } from '../airspan/utils';
import { MdCheckCircle, MdHighlightOff } from 'react-icons/md';
import { StaticStatusCircleIcon } from '../../icons/StatusIcon';
import { StatusConstants, StatusToColor } from '../../../data/constants';
import _ from 'lodash';
import { getComponentBgColor } from '../utils';
import { getStatusColor } from '../../../pages/CellOverview/hooks/useStatus';

const NtpCard = ({ ntpData }: { ntpData: any }) => {
  const [showNtpDetails, setShowNtpDetails] = useState(false);

  const convertToKeyValueArray = (data: any) => {
    const keyValueArray: { key: string; value: any }[] = [];
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        if (key === 'System_clock_synchronized') {
          keyValueArray.push({
            key: 'System clock synchronized',
            value:
              data[key] === 'yes' ? (
                <MdCheckCircle size="25" color="green" />
              ) : (
                <MdHighlightOff size="25" color="red" />
              ),
          });
        } else if (key === 'NTP_service') {
          keyValueArray.push({
            key: 'NTP service',
            value:
              data[key] === 'active' ? (
                <MdCheckCircle size="25" color="green" />
              ) : (
                <MdHighlightOff size="25" color="red" />
              ),
          });
        } else if (key === 'RTC_in_local_TZ') {
          keyValueArray.push({
            key: 'RTC in local TZ',
            value:
              data[key] === 'yes' ? (
                <MdCheckCircle size="25" color="red" />
              ) : (
                <MdHighlightOff size="25" color="green" />
              ),
          });
        } else {
          keyValueArray.push({ key: key.replace('_', ' '), value: data[key] });
        }
      }
    }
    return keyValueArray;
  };

  const RenderNtpData = (data: any) => {
    const keyValueArray = convertToKeyValueArray(data);
    return <UnstyledTable tableData={keyValueArray} firstColWidth="50%" />;
  };

  const RenderErrorDetails = () => {
    const errorDetails: { [key: string]: JSX.Element } = {};

    if (ntpData.reason) {
      errorDetails['Reason'] = (
        <Text fontWeight="bold" fontSize="sm" whiteSpace="initial" color="red.500" mr="4">
          {ntpData.reason}
        </Text>
      );
    }

    if (ntpData.cause) {
      errorDetails['Cause'] = (
        <Text fontWeight="bold" wordBreak="break-word" whiteSpace="normal" color="red.500">
          {ntpData.cause}
        </Text>
      );
    }

    if (ntpData.repairs && ntpData.repairs.filter((repair: string) => repair !== '').length > 0) {
      errorDetails['Repair'] =
        ntpData.repairs.length > 1 ? (
          <UnorderedList ml="4">
            {ntpData.repairs.map((repair: any) =>
              _.isEmpty(repair) ? null : (
                <ListItem pl="2" pr="2" whiteSpace="initial" key={repair}>
                  <Text ml="2" mb="2" wordBreak="break-word" whiteSpace="normal">
                    {repair}
                  </Text>
                </ListItem>
              )
            )}
          </UnorderedList>
        ) : (
          <Text ml="2" wordBreak="break-word" whiteSpace="normal">
            {ntpData.repairs[0]}
          </Text>
        );
    }

    return (
      <Flex wrap="wrap" ml="4" justifyContent="space-around">
        <UnstyledTable tableData={errorDetails} firstColWidth="15%" />
      </Flex>
    );
  };

  const ntpResults = ntpData.results;
  const ntpStatusColor = getStatusColor(ntpData.status);
  const bgColor = getComponentBgColor(ntpStatusColor);

  return (
    <Card>
      <Stack divider={<StackDivider />} spacing="4">
        <CardHeader
          onClick={() => setShowNtpDetails(!showNtpDetails)}
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          _hover={{
            bg: bgColor,
            cursor: 'pointer',
            boxShadow: `0 0 12px ${bgColor}`,
          }}
          borderRadius="lg"
          data-testid="ntp-card-header"
          h="60px"
          minH="60px"
        >
          <Heading size="xs" fontWeight="500" display="flex" justifyContent="space-between" width="100%">
            <Flex alignItems="center" width="100%">
              <Icon as={BsArrowReturnRight} />
              <Text ml="2">NTP</Text>
            </Flex>
          </Heading>
          <StaticStatusCircleIcon size={40} color={StatusToColor[ntpData.status as keyof typeof StatusToColor]} />
        </CardHeader>
        {showNtpDetails && (
          <CardBody data-testid="ntp-card-body">
            <Stack divider={<StackDivider />} spacing="4">
              <Flex wrap="wrap" ml="4" justifyContent="space-around">
                {RenderNtpData(ntpResults)}
              </Flex>
              {ntpData.status === StatusConstants.ERROR && <RenderErrorDetails />}
            </Stack>
          </CardBody>
        )}
      </Stack>
    </Card>
  );
};

export default NtpCard;
