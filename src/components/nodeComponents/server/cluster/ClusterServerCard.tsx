import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ack, <PERSON>ackDivider, Text } from '@chakra-ui/react';
import { useState } from 'react';
import { getStatusColor } from '../../../../pages/CellOverview/hooks/useStatus';
import StatusComponent from '../../../icons/StatusIcon';
import { BsArrowReturnRight } from 'react-icons/bs';
import { getComponentBgColor } from '../../utils';
import { ComponentTypeEnum } from '../../../../services/types';
import _ from 'lodash';
import SubClusterCard from './SubClusterCard';
import { MdCheckCircle, MdHighlightOff } from 'react-icons/md';

type PtpState = {
  spec?: {
    clockStatus?: string;
  };
};

type ClusterData = {
  name: string;
  status: string;
  nodes: any[];
  ptpstate?: PtpState;
};

type ClusterServerCardProps = {
  data: ClusterData;
};

const ClusterServerCard = ({ data }: ClusterServerCardProps) => {
  const [showContent, setShowContent] = useState(false);
  const statusColor = getStatusColor(data.status);
  const bgColor = getComponentBgColor(statusColor);
  const { name: clusterName, status: clusterStatus, nodes: clusterData = [], ptpstate = {} } = data;
  const clockStatus = ptpstate?.spec?.clockStatus || '';

  return (
    <Card width="100%" borderRadius="lg" data-testid="cluster-card">
      <CardHeader
        onClick={() => setShowContent(!showContent)}
        display="flex"
        alignItems="center"
        justifyContent="space-between"
        _hover={{
          bg: bgColor,
          cursor: 'pointer',
          boxShadow: `0 0 12px ${bgColor}`,
        }}
        transition="background-color 0.2s ease, box-shadow 0.2s ease"
        borderRadius="lg"
      >
        <Heading
          data-testid="cluster-card-heading"
          size="xs"
          fontWeight="500"
          display="flex"
          justifyContent="space-between"
        >
          <Flex alignItems="center">
            <Icon as={BsArrowReturnRight} />
            <Text ml="2">{clusterName}</Text>
          </Flex>
        </Heading>
        <StatusComponent
          dataTestId="cell-main-table-status-icon"
          status={clusterStatus}
          boxSize="sm"
          color={statusColor}
          component_type={ComponentTypeEnum.ANTHOS}
          component_id={clusterName}
        />
      </CardHeader>
      {showContent && (
        <CardBody data-testid="cluster-details-body" borderTop="1px solid #e2e2e2">
          <Stack divider={<StackDivider />} spacing="4">
            <Flex gap="4" flexDirection="column">
              {clockStatus && (
                <Flex alignItems="center" gap="2" mb="1">
                  <Text fontWeight="bold">PTP Clock Status :</Text>
                  <Text>{clockStatus}</Text>
                  {clockStatus === 'LOCKED' ? (
                    <MdCheckCircle size="25" color="green" />
                  ) : (
                    <MdHighlightOff size="25" color="red" />
                  )}
                </Flex>
              )}
              {clusterData.length > 0 ? (
                clusterData?.map((subCluster: any, index: number) => <SubClusterCard key={index} data={subCluster} />)
              ) : (
                <Text>Unknown Status - No data found for this Cluster Node</Text>
              )}
            </Flex>
          </Stack>
        </CardBody>
      )}
    </Card>
  );
};

export default ClusterServerCard;
