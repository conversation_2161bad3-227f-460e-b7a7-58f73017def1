import React, { useState } from 'react';
import {
  Badge,
  Box,
  Card,
  CardBody,
  CardHeader,
  Flex,
  Heading,
  Icon,
  <PERSON>ack,
  StackDivider,
  Text,
  useColorModeValue as mode,
} from '@chakra-ui/react';
import { getStatusColor } from '../../../../pages/CellOverview/hooks/useStatus';
import StatusComponent from '../../../icons/StatusIcon';
import { BsArrowReturnRight } from 'react-icons/bs';
import { getComponentBgColor } from '../../utils';
import { ComponentTypeEnum } from '../../../../services/types';
import { UnstyledTable } from '../../airspan/utils';
import { UtilizationBar } from '../../common/UtilizationBar';
import { MdCheckCircle, MdHighlightOff } from 'react-icons/md';
import { ChevronDownIcon, ChevronUpIcon } from '@chakra-ui/icons';
import { formatInReadableTimeDate } from '../../../../utils/formatInReadableTimeData';
import { NODE_TYPE } from '../../../../data/constants';
interface Container {
  container: string;
  cpu: {
    cpu_usage_percentage: number;
    cpu_usage_nano_cores: number;
  };
  memory_usage: {
    memory_usage_ki: number;
    memory_usage_percentage: number;
  };
}

interface Conditions {
  last_heartbeat_time: string;
  last_probe_time?: string | undefined;
  last_transition_time: string;
  message: string;
  reason: string;
  status: boolean;
  type: string;
}

interface KeyValuePair {
  key: string;
  value: any;
}

const convertContainersToKeyValuePairs = (container: Container): KeyValuePair[] => [
  {
    key: 'Cpu Usage',
    value: <UtilizationBar percentage={container.cpu.cpu_usage_percentage} usePadding={false} threshold={90} />,
  },
  {
    key: 'Cpu Nano Cores',
    value: container.cpu.cpu_usage_nano_cores,
  },
  {
    key: 'Memory Usage',
    value: (
      <UtilizationBar percentage={container.memory_usage.memory_usage_percentage} usePadding={false} threshold={90} />
    ),
  },
  {
    key: 'Memory Usage Ki',
    value: container.memory_usage.memory_usage_ki,
  },
];

const convertConditionsToKeyValuePairs = (conditions: Conditions): KeyValuePair[] => [
  {
    key: 'Last Heartbeat',
    value: conditions.last_heartbeat_time ? formatInReadableTimeDate(conditions.last_heartbeat_time) : 'N/A',
  },
  {
    key: 'Last Transition',
    value: conditions.last_transition_time ? formatInReadableTimeDate(conditions.last_transition_time) : 'N/A',
  },
  { key: 'Message', value: conditions.message },
  { key: 'Type', value: conditions.type },
];

const convertPodConditionsToKeyValuePairs = (conditions: Conditions): KeyValuePair[] => [
  {
    key: 'Last Probe',
    value: conditions.last_probe_time ? formatInReadableTimeDate(conditions.last_probe_time) : 'N/A',
  },
  {
    key: 'Last Transition',
    value: conditions.last_transition_time ? formatInReadableTimeDate(conditions.last_transition_time) : 'N/A',
  },
  { key: 'Message', value: conditions.message },
  { key: 'Reason', value: conditions.reason },
];

const renderTableData = (data: KeyValuePair[]) => (
  <UnstyledTable tableData={data} fontSize="xs" rowBorderBottom={true} firstColWidth="50%" />
);

export const RenderDetails = ({
  type,
  caller,
  heading,
  details,
}: {
  type?: string;
  caller?: string;
  heading: string;
  details: any[];
}) => {
  const [openIndices, setOpenIndices] = useState<number[]>([]);
  const hoverBackgroundColor = mode('gray.200', 'gray.700');
  const isPod = caller === 'pod';

  let formatTableData:
    | boolean
    | ((container: Container) => KeyValuePair[])
    | ((conditions: Conditions) => KeyValuePair[]);

  if (isPod) {
    formatTableData = heading === 'Conditions' && convertPodConditionsToKeyValuePairs;
  } else {
    formatTableData = heading === 'Containers' ? convertContainersToKeyValuePairs : convertConditionsToKeyValuePairs;
  }

  const handleToggleDetails = (index: number) => {
    setOpenIndices((prevState) =>
      prevState.includes(index) ? prevState.filter((i) => i !== index) : [...prevState, index]
    );
  };

  const getStatusIcon = (type: string, status: boolean) => {
    if (type === 'Ready') {
      return status ? <MdCheckCircle size="25" color="green" /> : <MdHighlightOff size="25" color="red" />;
    } else {
      return status ? <MdHighlightOff size="25" color="red" /> : <MdCheckCircle size="25" color="green" />;
    }
  };

  const getPodStatusIcon = (status: string) => {
    const normalizedStatus = status.toLowerCase() === 'true';
    return normalizedStatus ? <MdCheckCircle size="25" color="green" /> : <MdHighlightOff size="25" color="red" />;
  };

  return (
    <Flex wrap="wrap" justifyContent="flex-start" data-testid="sub-cluster-render-details-container">
      <Text
        fontWeight="bold"
        fontSize="medium"
        wordBreak="break-word"
        whiteSpace="normal"
        mb="4"
        data-testid={`sub-cluster-render-details-header-${heading}`}
      >
        {heading}
      </Text>

      <Flex wrap="wrap" justifyContent="flex-start" width="100%" data-testid="sub-cluster-render-details-body">
        {details.map((detail, index) => (
          <Box key={index} flexBasis={`calc(${isPod ? '20%' : '50%'} - 20px)`} mb="8" ml="4">
            {heading === 'Containers' ? (
              <Text fontWeight="bold" wordBreak="break-word" whiteSpace="normal">
                {detail.container}
              </Text>
            ) : null}
            {heading === 'Conditions' && type === NODE_TYPE.DU ? (
              <Flex
                alignItems="center"
                cursor="pointer"
                justifyContent="space-between"
                p="1"
                _hover={{ bg: hoverBackgroundColor }}
                onClick={() => handleToggleDetails(index)}
                borderBottom="2px"
                borderColor="gray.100"
              >
                <Flex alignItems="center" gap="2" width="100%">
                  <Text fontWeight="bold" wordBreak="break-word" whiteSpace="normal" flex="1">
                    {isPod ? detail.type : detail.reason}
                  </Text>
                  {isPod ? getPodStatusIcon(detail.status) : getStatusIcon(detail.type, detail.status)}
                </Flex>
                {openIndices.includes(index) ? <ChevronUpIcon boxSize="5" /> : <ChevronDownIcon boxSize="5" />}
              </Flex>
            ) : null}
            {heading === 'Conditions' && type === NODE_TYPE.CUUP ? (
              <Flex
                alignItems="center"
                cursor="pointer"
                justifyContent="space-between"
                p="1"
                _hover={{ bg: hoverBackgroundColor }}
                onClick={() => handleToggleDetails(index)}
                borderBottom="2px"
                borderColor="gray.100"
                width="500px"
              >
                <Text fontWeight="bold" wordBreak="break-word" whiteSpace="normal" flex="1">
                  {isPod ? detail.type : detail.reason}
                </Text>
                {isPod ? getPodStatusIcon(detail.status) : getStatusIcon(detail.type, detail.status)}

                {openIndices.includes(index) ? <ChevronUpIcon boxSize="5" /> : <ChevronDownIcon boxSize="5" />}
              </Flex>
            ) : null}
            {heading === 'Conditions' && type === NODE_TYPE.CUCP ? (
              <Flex
                alignItems="center"
                cursor="pointer"
                justifyContent="space-between"
                p="1"
                _hover={{ bg: hoverBackgroundColor }}
                onClick={() => handleToggleDetails(index)}
                borderBottom="2px"
                borderColor="gray.100"
                width="500px"
              >
                <Text fontWeight="bold" wordBreak="break-word" whiteSpace="normal" flex="1">
                  {isPod ? detail.type : detail.reason}
                </Text>
                {isPod ? getPodStatusIcon(detail.status) : getStatusIcon(detail.type, detail.status)}

                {openIndices.includes(index) ? <ChevronUpIcon boxSize="5" /> : <ChevronDownIcon boxSize="5" />}
              </Flex>
            ) : null}
            {(heading === 'Containers' || openIndices.includes(index)) &&
              typeof formatTableData === 'function' &&
              renderTableData(formatTableData(detail))}
          </Box>
        ))}
      </Flex>
    </Flex>
  );
};

const SubClusterCard = ({ data }: any) => {
  const [showContent, setShowContent] = useState(false);
  const statusColor = getStatusColor(data.status);
  const bgColor = getComponentBgColor(statusColor);
  const { node: subClusterName, status: subClusterStatus, containers, storage, conditions } = data;

  return (
    <Card width="100%" borderRadius="lg" data-testid="sub-cluster-details">
      <CardHeader
        onClick={() => setShowContent(!showContent)}
        display="flex"
        alignItems="center"
        justifyContent="space-between"
        _hover={{
          bg: bgColor,
          cursor: 'pointer',
          boxShadow: `0 0 12px ${bgColor}`,
        }}
        transition="background-color 0.2s ease, box-shadow 0.2s ease"
        borderRadius="lg"
      >
        <Heading
          data-testid="sub-cluster-heading"
          size="xs"
          fontWeight="500"
          display="flex"
          justifyContent="space-between"
        >
          <Flex alignItems="center">
            <Icon as={BsArrowReturnRight} />
            <Text ml="2">{subClusterName}</Text>
          </Flex>
        </Heading>
        <StatusComponent
          dataTestId="cell-main-table-status-icon"
          status={subClusterStatus}
          boxSize="sm"
          color={statusColor}
          component_type={ComponentTypeEnum.ANTHOS}
          component_id={subClusterName}
        />
      </CardHeader>
      {showContent && (
        <CardBody borderTop="1px solid #e2e2e2" data-testid="sub-cluster-details-body">
          <Stack divider={<StackDivider />} spacing="4">
            <Flex flexDirection="column">
              <Text fontWeight="bold" fontSize="medium" wordBreak="break-word" whiteSpace="normal" mb="4">
                Storage
              </Text>
              <UnstyledTable
                tableData={[
                  { key: 'Capacity Bytes', value: <Badge colorScheme="gray"> {storage.capacity_bytes} </Badge> },
                  { key: 'Allocated Bytes', value: <Badge colorScheme="gray"> {storage.allocatable_bytes} </Badge> },
                ]}
                firstColWidth="30%"
              />
            </Flex>
            <Flex gap="4" flexDirection="column" data-testid="sub-cluster-render-details">
              <RenderDetails heading="Conditions" details={conditions} />
              <RenderDetails heading="Containers" details={containers} />
            </Flex>
          </Stack>
        </CardBody>
      )}
    </Card>
  );
};

export default React.memo(SubClusterCard);
