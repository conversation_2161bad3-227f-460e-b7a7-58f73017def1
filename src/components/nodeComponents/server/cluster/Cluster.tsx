import { Box } from '@chakra-ui/react';
import { ErrorBoundary } from 'react-error-boundary';
import { ErrorBoundaryFallback, ErrorBoundaryLogError } from '../../../errorComponents/ErrorBoundaryFallback';
import NodeComponentErrorCard from '../../../errorComponents/NodeComponentErrorCard';
import ClusterServerCard from './ClusterServerCard';

type ClusterProps = {
  data: any;
  nodeId: string;
  compName: string;
};

const Cluster = ({ data, nodeId, compName }: ClusterProps) => {
  return (
    <Box width="50%" padding="1rem" data-testid="cluster">
      {data.error || data.error === null ? (
        <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
          <NodeComponentErrorCard
            id={nodeId}
            errorData={data}
            compName={compName}
            testId="node-comp-error-card"
            marginSpace="8"
          />
        </ErrorBoundary>
      ) : (
        <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
          <ClusterServerCard data={data} />
        </ErrorBoundary>
      )}
    </Box>
  );
};

export default Cluster;
