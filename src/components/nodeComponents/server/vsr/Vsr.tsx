import { Box, Card } from '@chakra-ui/react';
import { ErrorBoundary } from 'react-error-boundary';
import { NODE_COMPONENT_TITLES } from '../../../../data/constants';
import { ErrorBoundaryFallback, ErrorBoundaryLogError } from '../../../errorComponents/ErrorBoundaryFallback';
import NodeComponentErrorCard from '../../../errorComponents/NodeComponentErrorCard';
import VsrCard from './VsrCard';

type VsrProps = {
  data: any;
  nodeId: string;
};

const Vsr = ({ data, nodeId }: VsrProps) => {
  return (
    <Box width="50%" padding="1rem" data-testid="vsr-card">
      {data.error ? (
        <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
          <NodeComponentErrorCard
            id={nodeId}
            errorData={data.error}
            compName={NODE_COMPONENT_TITLES.VSR}
            testId="node-comp-error-card"
            marginSpace="8"
          />
        </ErrorBoundary>
      ) : (
        <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
          <VsrCard data={data} nodeId={nodeId} />
        </ErrorBoundary>
      )}
    </Box>
  );
};

export default Vsr;
