import { ArrowDownIcon, ArrowUpIcon } from '@chakra-ui/icons';
import {
  Box,
  Button,
  Card,
  CardBody,
  CardHeader,
  Flex,
  Heading,
  Icon,
  Stack,
  StackDivider,
  Text,
  useDisclosure,
} from '@chakra-ui/react';
import _ from 'lodash';
import { ColumnDef } from '@tanstack/react-table';
import { useMemo, useState } from 'react';
import { BsArrowReturnRight } from 'react-icons/bs';
import { NODE_COMPONENT_TITLES, StatusToColor } from '../../../../data/constants';
import { getStatusColor } from '../../../../pages/CellOverview/hooks/useStatus';
import { recursiveSearch } from '../../../../utils/recursiveSearch';
import StatusComponent, { StaticStatusCircleIcon } from '../../../icons/StatusIcon';
import { DynamicStats } from '../../common/DynamicStats';
import ModalDataTable from '../../common/ModalDataTable';
import { getComponentBgColor } from '../../utils';
import { formatISO8601Duration } from '../../../../utils/formatInReadableTimeData';
import {
  FastPath,
  Interface,
  FlattenedFastPath,
  FibroInterface,
  FibroClock,
} from '../../../../types/orchestrator.types';
import { ComponentTypeEnum } from '../../../../services/types';

const VsrCard = ({ data, nodeId }: any) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [showContent, setShowContent] = useState(false);

  const flattenFastPathData = (fastPathData: FastPath): FlattenedFastPath[] => {
    return fastPathData.flatMap((fastPathEntry) =>
      fastPathEntry['next-hop'].map((nextHop) => ({
        destination: fastPathEntry.destination,
        'next-hop': nextHop['next-hop'],
        interface: nextHop.interface,
        active: nextHop.active,
        uptime: nextHop.uptime,
      }))
    );
  };

  const flattenedFastPathData = useMemo(() => {
    return data.vsr_data?.fastpath ? flattenFastPathData(data.vsr_data.fastpath) : [];
  }, [data.vsr_data?.fastpath]);

  const vsrColumns = useMemo<ColumnDef<Interface | FlattenedFastPath | FibroInterface | FibroClock>[]>(
    () => [
      { header: 'Destination', accessorKey: 'destination' },
      { header: 'Next-Hop', accessorKey: 'next-hop' },
      { header: 'Interface', accessorKey: 'interface' },
      {
        header: 'Active',
        accessorKey: 'active',
        cell: ({ getValue }) => (
          <Box boxSize="5">
            {getValue() ? <ArrowUpIcon boxSize="5" color={'green'} /> : <ArrowDownIcon boxSize="5" color={'red'} />}
          </Box>
        ),
      },
      {
        header: 'Uptime',
        accessorKey: 'uptime',
        cell: ({ getValue }) => {
          const uptimeValue = getValue();
          if (_.isString(uptimeValue)) {
            const matches = uptimeValue.match(/^P(?:(\d+)D)?T(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?$/);
            // for P1DT41460S or T41085S
            if (matches) {
              return formatISO8601Duration(uptimeValue);
            }
          }
          return uptimeValue;
        },
      },
    ],
    []
  );
  const currentUrl = new URL(window.location.href);
  let DOCKER_NMS_serv_Grafana_URL = '';
  if (process.env.NODE_ENV === 'production') {
    if (currentUrl.host === '**********') {
      DOCKER_NMS_serv_Grafana_URL = 'http://***********:3000';
    } else {
      DOCKER_NMS_serv_Grafana_URL = 'http://*************:3000';
    }
  }
  const status = data?.vsr_data?.status || 'UNKNOWN';
  // const flattenedData: any = recursiveSearch(data);
  const statusColor = getStatusColor(status);
  const bgColor = getComponentBgColor(statusColor);
  return (
    <>
      <Card width="100%" borderRadius="lg">
        <CardHeader
          _hover={{
            bg: bgColor,
            cursor: 'pointer',
            boxShadow: `0 0 12px ${bgColor}`,
          }}
          transition="background-color 0.2s ease, box-shadow 0.2s ease"
          borderRadius="lg"
          onClick={() => setShowContent(!showContent)}
          display="flex"
          alignItems="center"
          justifyContent="space-between"
        >
          <Heading size="xs" fontWeight="500" display="flex" justifyContent="space-between">
            <Flex alignItems="center">
              <Icon as={BsArrowReturnRight} />
              <Text ml={2}> {NODE_COMPONENT_TITLES.VSR}</Text>
            </Flex>
          </Heading>

          <StatusComponent
            dataTestId="cell-main-table-status-icon"
            status={data?.vsr_data?.status}
            boxSize="sm"
            color={statusColor}
            component_type={ComponentTypeEnum.SIXWIND}
            component_id={data?.id}
          />
        </CardHeader>
        {showContent && (
          <CardBody borderTop={'1px solid #e2e2e2'}>
            <Stack divider={<StackDivider />} spacing="4">
              <DynamicStats data={data} componentId={data?.id} caller="vsr" />
              {data.vsr_data.fastpath.length > 0 && <Button onClick={onOpen}>More Data</Button>}
            </Stack>
          </CardBody>
        )}
      </Card>
      <ModalDataTable
        isOpen={isOpen}
        onClose={onClose}
        columns={vsrColumns}
        vsrData={flattenedFastPathData}
        caller="vsr"
      />
    </>
  );
};

export default VsrCard;
