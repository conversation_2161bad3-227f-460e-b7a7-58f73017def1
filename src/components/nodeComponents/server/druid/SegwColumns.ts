import { ColumnDef } from '@tanstack/react-table';
import {
  IpsecCertificate,
  IpsecChildConfig,
  IpsecChildConfigProposal,
  IpsecIkeConfigProposal,
  IpsecPeerConfig,
  IpsecPrivateKey,
  IpsecProposal,
  IpsecSecureAssociation,
  S1Client,
} from '../../../../types/orchestrator.types';
import { formatInReadableTimeDate } from '../../../../utils/formatInReadableTimeData';

const ipsecChildConfigColumns: ColumnDef<IpsecChildConfig>[] = [
  { header: 'ID', accessorKey: 'id' },
  { header: 'Name', accessorKey: 'name' },
  { header: 'Lifetime', accessorKey: 'lifetime' },
  { header: 'Rekeytime', accessorKey: 'rekeytime' },
  { header: 'Jitter', accessorKey: 'jitter' },
];

const ipsecChildConfigProposalColumns: ColumnDef<IpsecChildConfigProposal>[] = [
  { header: 'ID', accessorKey: 'id' },
  { header: 'Child Config', accessorKey: 'child_cfg' },
  { header: 'Priority', accessorKey: 'prio' },
  { header: 'Proposal', accessorKey: 'prop' },
];

const ipsecCertificateColumns: ColumnDef<IpsecCertificate>[] = [
  { header: 'Filename', accessorKey: 'filename' },
  { header: 'Subject', accessorKey: 'subject' },
  { header: 'Issuer', accessorKey: 'issuer' },
  {
    header: 'Expiry Date',
    accessorKey: 'expiry_date',
    cell: ({ getValue }) => formatInReadableTimeDate(getValue() as string),
  },
  {
    header: 'Start Date',
    accessorKey: 'start_date',
    cell: ({ getValue }) => formatInReadableTimeDate(getValue() as string),
  },
];

const ipsecPeerConfigColumns: ColumnDef<IpsecPeerConfig>[] = [
  { header: 'ID', accessorKey: 'id' },
  { header: 'Name', accessorKey: 'name' },
  { header: 'Type', accessorKey: 'type' },
  { header: 'IKE Version', accessorKey: 'ike_version' },
  { header: 'Rekeytime', accessorKey: 'rekeytime' },
  { header: 'Jitter', accessorKey: 'jitter' },
  { header: 'Pool', accessorKey: 'pool' },
];

const ipsecPrivateKeyColumns: ColumnDef<IpsecPrivateKey>[] = [
  { header: 'Type', accessorKey: 'type' },
  { header: 'Filename', accessorKey: 'filename' },
];

const ipsecSecureAssociationColumns: ColumnDef<IpsecSecureAssociation>[] = [
  { header: 'Name', accessorKey: 'name' },
  {
    header: 'Creation Time',
    accessorKey: 'creation_time',
    cell: ({ getValue }) => formatInReadableTimeDate(getValue() as string),
  },
  { header: 'Oper State', accessorKey: 'oper_state' },
  { header: 'Direction', accessorKey: 'direction' },
  { header: 'Local Address', accessorKey: 'local_addr' },
  { header: 'Local ID', accessorKey: 'local_id' },
  { header: 'Remote Address', accessorKey: 'remote_addr' },
  { header: 'Remote ID', accessorKey: 'remote_id' },
  { header: 'Integrity Algorithm', accessorKey: 'integrity_algorithm' },
  { header: 'Encryption Algorithm', accessorKey: 'encryption_algorithm' },
  { header: 'SA Bytes In', accessorKey: 'sa_bytes_in' },
  { header: 'SA Packets In', accessorKey: 'sa_packets_in' },
  { header: 'SA Bytes Out', accessorKey: 'sa_bytes_out' },
  { header: 'SA Packets Out', accessorKey: 'sa_packets_out' },
];

const s1ClientColumns: ColumnDef<S1Client>[] = [
  { header: 'Name', accessorKey: 'name' },
  { header: 'Oper State', accessorKey: 'oper_state' },
  { header: 'Admin State', accessorKey: 'admin_state' },
  { header: 'S1 Client Type', accessorKey: 's1_client_type' },
  { header: 'eNB Name', accessorKey: 'enb_name' },
  { header: 'PLMN ID', accessorKey: 'plmn_id' },
  { header: 'Cell Identity', accessorKey: 'cell_identity' },
  { header: 'Max Downlink Bandwidth', accessorKey: 'max_downlink_bandwidth' },
  { header: 'Max Uplink Bandwidth', accessorKey: 'max_uplink_bandwidth' },
];

const ipsecIkeConfigProposalColumns: ColumnDef<IpsecIkeConfigProposal>[] = [
  { header: 'ID', accessorKey: 'id' },
  { header: 'IKE Config', accessorKey: 'ike_cfg' },
  { header: 'Priority', accessorKey: 'prio' },
  { header: 'Proposal', accessorKey: 'prop' },
];

const ipsecProposalColumns: ColumnDef<IpsecProposal>[] = [
  { header: 'ID', accessorKey: 'id' },
  { header: 'Proposal', accessorKey: 'proposal' },
];

export {
  s1ClientColumns,
  ipsecChildConfigColumns,
  ipsecSecureAssociationColumns,
  ipsecPrivateKeyColumns,
  ipsecPeerConfigColumns,
  ipsecCertificateColumns,
  ipsecChildConfigProposalColumns,
  ipsecIkeConfigProposalColumns,
  ipsecProposalColumns,
};
