import {
  <PERSON><PERSON>,
  <PERSON>,
  CardB<PERSON>,
  <PERSON><PERSON><PERSON>er,
  <PERSON>lex,
  <PERSON>ing,
  <PERSON><PERSON>,
  <PERSON>ack,
  StackDivider,
  Text,
  useDisclosure,
} from '@chakra-ui/react';
import _ from 'lodash';
import { ColumnDef } from '@tanstack/react-table';
import { useMemo, useState } from 'react';
import { BsArrowReturnRight } from 'react-icons/bs';
import { NODE_COMPONENT_TITLES } from '../../../../data/constants';
import { getStatusColor } from '../../../../pages/CellOverview/hooks/useStatus';
import StatusComponent from '../../../icons/StatusIcon';
import ModalDataTable from '../../common/ModalDataTable';
import { getComponentBgColor } from '../../utils';
import { Interface, FlattenedFastPath, FibroInterface, FibroClock } from '../../../../types/orchestrator.types';
import { ComponentTypeEnum } from '../../../../services/types';
import { DynamicStatsV2 } from '../../common/DynamicStatsV2';
import {
  s1ClientColumns,
  // ipsecChildConfigColumns,
  ipsecSecureAssociationColumns,
  ipsecPrivateKeyColumns,
  // ipsecPeerConfigColumns,
  ipsecCertificateColumns,
  // ipsecChildConfigProposalColumns,
  // ipsecProposalColumns,
  // ipsecIkeConfigProposalColumns
} from './SegwColumns';
import { Dashboard } from './dashboard/Dashboard';
import { Druid } from '../../../../types/orchestrator.types';

const DruidCard = ({ data, nodeId }: { data: Druid | any; nodeId: string }) => {
  const { isOpen: isNetworkRoutesOpen, onOpen: onNetworkRoutesOpen, onClose: onNetworkRoutesClose } = useDisclosure();
  const { isOpen: isSeGWInfoOpen, onOpen: onSeGWInfoOpen, onClose: onSeGWInfoClose } = useDisclosure();
  const [showContent, setShowContent] = useState(false);

  const status = data?.status || 'UNKNOWN';

  const druidColumns = useMemo<ColumnDef<Interface | FlattenedFastPath | FibroInterface | FibroClock>[]>(
    () => [
      { header: 'ID', accessorKey: 'id' },
      { header: 'IPv4 Subnet', accessorKey: 'ipv4_subnet' },
      { header: 'IPv4 Subnet Mask', accessorKey: 'ipv4_subnetmask' },
      { header: 'Gateway IPv4', accessorKey: 'gateway_ipv4' },
      { header: 'Net Device', accessorKey: 'net_device' },
      { header: 'Metric', accessorKey: 'metric' },
      { header: 'Owner', accessorKey: 'owner' },
      { header: 'Raemis ID', accessorKey: 'raemis_id' },
      {
        header: 'DNM Managed',
        accessorKey: 'dnm_managed',
        cell: ({ getValue }) => {
          const value = getValue();
          return value ? 'True' : 'False';
        },
      },
    ],
    []
  );

  const statusColor = getStatusColor(status);
  const bgColor = getComponentBgColor(statusColor);

  return (
    <>
      <Card width="100%" borderRadius="lg">
        <CardHeader
          _hover={{
            bg: bgColor,
            cursor: 'pointer',
            boxShadow: `0 0 12px ${bgColor}`,
          }}
          transition="background-color 0.2s ease, box-shadow 0.2s ease"
          borderRadius="lg"
          onClick={() => setShowContent(!showContent)}
          display="flex"
          alignItems="center"
          justifyContent="space-between"
        >
          <Heading size="xs" fontWeight="500" display="flex" justifyContent="space-between">
            <Flex alignItems="center">
              <Icon as={BsArrowReturnRight} />
              <Text ml="2"> {NODE_COMPONENT_TITLES.DRUID}</Text>
            </Flex>
          </Heading>

          <StatusComponent
            dataTestId="cell-main-table-status-icon"
            status={data?.status}
            boxSize="sm"
            color={statusColor}
            component_type={ComponentTypeEnum.DRUID}
            component_id={data?.device_id}
          />
        </CardHeader>
        {showContent && (
          <CardBody borderTop="1px solid #e2e2e2">
            <Stack divider={<StackDivider />} spacing="4">
              <Dashboard data={data} />
              <DynamicStatsV2 data={data} componentId={data?.id} caller="druid" nodeId={nodeId} />
              <Flex justifyContent={data?.segw ? 'space-between' : 'flex-start'}>
                {data?.networks?.ip_route?.length > 0 && (
                  <Button flex={data?.segw ? '0 1 48%' : '1'} onClick={onNetworkRoutesOpen}>
                    View Network Routes
                  </Button>
                )}
                {data?.segw && Object.keys(data?.segw).length > 0 && (
                  <Button flex="0 1 48%" onClick={onSeGWInfoOpen}>
                    SeGW Information
                  </Button>
                )}
              </Flex>
            </Stack>
          </CardBody>
        )}
      </Card>

      <ModalDataTable
        isOpen={isNetworkRoutesOpen}
        onClose={onNetworkRoutesClose}
        columns={druidColumns}
        data={data.networks.ip_route}
        caller="druid"
      />

      <ModalDataTable
        isOpen={isSeGWInfoOpen}
        onClose={onSeGWInfoClose}
        data={data?.segw}
        caller="druidSEGW"
        isMultiple={true}
        multipleColumns={{
          // ipsec_child_config: ipsecChildConfigColumns,
          // ipsec_child_config_proposal: ipsecChildConfigProposalColumns,
          ipsec_certificate: ipsecCertificateColumns,
          // ipsec_peer_config: ipsecPeerConfigColumns,
          ipsec_private_key: ipsecPrivateKeyColumns,
          ipsec_secure_association: ipsecSecureAssociationColumns,
          s1client: s1ClientColumns,
          // ipsec_proposal: ipsecProposalColumns,
          // ipsec_ikeconfig_proposal: ipsecIkeConfigProposalColumns
        }}
      />
    </>
  );
};

export default DruidCard;
