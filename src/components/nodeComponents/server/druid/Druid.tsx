import { Box } from '@chakra-ui/react';
import { ErrorBoundary } from 'react-error-boundary';
import { NODE_COMPONENT_TITLES } from '../../../../data/constants';
import { ErrorBoundaryFallback, ErrorBoundaryLogError } from '../../../errorComponents/ErrorBoundaryFallback';
import NodeComponentErrorCard from '../../../errorComponents/NodeComponentErrorCard';
import DruidCard from './DruidCard';

type DruidProps = {
  data: any;
  nodeId: string;
};

const Druid = ({ data, nodeId }: DruidProps) => {
  return (
    <Box width="50%" padding="1rem" data-testid="druid-card">
      {data.error ? (
        <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
          <NodeComponentErrorCard
            id={nodeId}
            errorData={data.error}
            compName={NODE_COMPONENT_TITLES.DRUID}
            testId="node-comp-error-card"
            marginSpace="8"
          />
        </ErrorBoundary>
      ) : (
        <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
          <DruidCard data={data} nodeId={nodeId} />
        </ErrorBoundary>
      )}
    </Box>
  );
};

export default Druid;
