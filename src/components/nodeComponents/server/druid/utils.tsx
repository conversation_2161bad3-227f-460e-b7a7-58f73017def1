import { Text, Progress, Box } from '@chakra-ui/react';

export const LicenseProgress = ({ usedLicense, totalLicense }: { usedLicense: number; totalLicense: number }) => {
  const usagePercentage = (usedLicense / totalLicense) * 100;

  const getBarColor = (percentage: number) => {
    if (percentage > 100) return 'red';
    if (percentage === 100) return 'orange';
    return 'green';
  };

  return (
    <Box width="100%">
      <Box position="relative">
        <Progress colorScheme={getBarColor(usagePercentage)} height={6} value={usagePercentage} />
        <Text
          position="absolute"
          top="50%"
          left="50%"
          transform="translate(-50%, -50%)"
          fontWeight="bold"
          color={usagePercentage > 50 ? 'white' : 'black'}
        >
          {usedLicense} / {totalLicense}
        </Text>
      </Box>
    </Box>
  );
};

export const snakeCaseToTitleCase = (key: string): string => {
  return key
    .split('/') // Split by slashes first
    .map(
      (part) =>
        part
          .split('_') // Split by underscores within each part
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
          .join(' ') // Join without any delimiter
    )
    .join('/'); // Join parts with slashes
};
