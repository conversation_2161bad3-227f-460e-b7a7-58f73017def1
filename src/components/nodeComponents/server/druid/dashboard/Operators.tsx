import { Box, Card, CardBody, CardHeader, Flex, Icon, <PERSON>ing, <PERSON>ack, StackDivider, Text } from '@chakra-ui/react';
import { LicenseProgress } from '../utils';
import {
  BsArrowDownShort,
  BsArrowReturnRight,
  BsArrowUpShort,
  BsXCircleFill,
  BsCheckCircleFill,
  BsUnlock,
  BsLock,
} from 'react-icons/bs';
import { useState } from 'react';
import { UnstyledTable } from '../../../airspan/utils';
import _ from 'lodash';
import { snakeCaseToTitleCase } from '../utils';
import { S1Client } from '../../../../../types/orchestrator.types';
import { ReactElement } from 'react';

const convertToKeyValueArray = (data: S1Client) => {
  const keyValueArray: { key: string; value: string | number | ReactElement }[] = [];

  for (const key in data) {
    if (key === 'name') {
      continue;
    } else if (key === 'oper_state') {
      const value = data[key as keyof S1Client];
      keyValueArray.push({
        key: 'Operational State',
        value:
          value === 'enabled' ? <BsCheckCircleFill size={30} color="green" /> : <BsXCircleFill size={30} color="red" />,
      });
    } else if (key === 'admin_state') {
      keyValueArray.push({
        key: 'Admin State',
        value: data[key] === 'locked' ? <BsLock size={30} color="red" /> : <BsUnlock size={30} color="green" />,
      });
    } else if (key === 'plmn_id') {
      keyValueArray.unshift({ key: 'PLMN', value: data[key] });
    } else if (key === 'max_downlink_bandwidth') {
      let value = data[key as keyof S1Client];
      if (_.isNil(value) || _.isEmpty(value)) {
        value = '-- Mbps';
      }
      keyValueArray.push({
        key: 'Max Downlink Bandwidth',
        value: (
          <Flex alignItems="center" gap={1}>
            <BsArrowDownShort size={20} color="blue.500" />
            <Text>{value}</Text>
          </Flex>
        ),
      });
    } else if (key === 'max_uplink_bandwidth') {
      let value = data[key as keyof S1Client];
      if (_.isNil(value) || _.isEmpty(value)) {
        value = '-- Mbps';
      }
      keyValueArray.push({
        key: 'Max Uplink Bandwidth',
        value: (
          <Flex alignItems="center" gap={1}>
            <BsArrowUpShort size={20} color="blue.500" />
            <Text>{value}</Text>
          </Flex>
        ),
      });
    } else {
      const formattedKey = snakeCaseToTitleCase(key);
      keyValueArray.push({ key: formattedKey, value: data[key as keyof S1Client] });
    }
  }
  return keyValueArray;
};

export const Operators = ({
  s1clients,
  totalOperatorLicenses,
}: {
  s1clients: S1Client[];
  totalOperatorLicenses: number;
}) => {
  const [showOperatorDetails, setShowOperatorDetails] = useState(false);

  const renderTableData = (data: S1Client) => {
    const keyValueArray = convertToKeyValueArray(data);
    return <UnstyledTable tableData={keyValueArray} fontSize="xs" rowBorderBottom={true} />;
  };

  return (
    <Card>
      <Stack divider={<StackDivider />} spacing="4">
        <CardHeader
          onClick={() => setShowOperatorDetails(!showOperatorDetails)}
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          _hover={{
            cursor: 'pointer',
          }}
          borderRadius="lg"
        >
          <Heading size="xs" fontWeight="500" display="flex" justifyContent="space-between" width="100%">
            <Flex alignItems="center" width="100%">
              <Icon as={BsArrowReturnRight} />
              <Text ml="2">OPERATOR LICENSE</Text>
              <Box flex="1" ml="10" display="flex" flexDirection="row" alignItems="center">
                <LicenseProgress usedLicense={s1clients.length} totalLicense={totalOperatorLicenses} />
              </Box>
            </Flex>
          </Heading>
        </CardHeader>
        {showOperatorDetails && (
          <CardBody>
            <Flex wrap="wrap" ml="4" justifyContent="center">
              <Flex wrap="wrap" justifyContent="flex-start" width="100%" data-testid="operator-cards">
                {s1clients.map((s1client: S1Client, index: number) => {
                  const isLastItem = index === s1clients.length - 1;
                  return (
                    <Box
                      key={index}
                      flexBasis="calc(50% - 8px)"
                      mb="8"
                      ml={isLastItem && s1clients.length % 2 !== 0 ? '0' : '8px'} // Align last item to the left if odd
                      data-testid={`operator-card-${index}`}
                    >
                      <Text fontWeight="bold" wordBreak="break-word" whiteSpace="normal">
                        {s1client.name}
                      </Text>
                      {renderTableData(s1client)}
                    </Box>
                  );
                })}
              </Flex>
            </Flex>
          </CardBody>
        )}
      </Stack>
    </Card>
  );
};
