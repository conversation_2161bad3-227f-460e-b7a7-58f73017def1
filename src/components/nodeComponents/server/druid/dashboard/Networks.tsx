import { Box, Card, CardBody, CardHeader, Flex, Icon, <PERSON><PERSON>, <PERSON>ack, <PERSON>ackDivider, Text } from '@chakra-ui/react';
import { LicenseProgress } from '../utils';
import { BsArrowReturnRight } from 'react-icons/bs';
import { useState } from 'react';
import { UnstyledTable } from '../../../airspan/utils';
import { snakeCaseToTitleCase } from '../utils';
import { Pdn, TDruidNetworks } from '../../../../../types/orchestrator.types';
import { FaArrowUp, FaArrowDown } from 'react-icons/fa';
import { MdCheckCircle, MdHighlightOff } from 'react-icons/md';

const convertToKeyValueArray = (data: any) => {
  /* TODO: add more keys as per need */

  const keyValueArray: { key: string; value: string | number }[] = [];
  for (const key in data) {
    if (key === 'Apn') {
      continue;
    }
    const formattedKey = snakeCaseToTitleCase(key);
    keyValueArray.push({ key: formattedKey, value: data[key as keyof Pdn] });
  }

  return keyValueArray;
};

const findMatchingEntries = (data: any[], key: string, value: any) => {
  return data.filter((entry) => entry[key] === value);
};

export const Networks = ({
  networks,
  totalNetworkLicenses,
}: {
  networks: TDruidNetworks;
  totalNetworkLicenses: number;
}) => {
  const [showNetworkDetails, setShowNetworkDetails] = useState(false);

  const constructedEnrichedNetworkData = networks.pdn.map((pdn: Pdn) => {
    const devices = findMatchingEntries(networks.net_device, 'id', Number(pdn.ipv4_pool_id));
    const profiles = findMatchingEntries(networks.subscription_profile, 'apn', pdn.apn);
    const ipv4Pools = findMatchingEntries(networks.ipv4_pool, 'id', Number(pdn.ipv4_pool_id));
    const groups = findMatchingEntries(networks.group, 'description', pdn.apn);

    let slices = null;
    if (profiles.length > 0) {
      const profile = profiles[0];
      slices = findMatchingEntries(networks.network_slice, 'id', profile.network_slice_id);
    }

    // TODO: To be used in future
    // let mgws = null;
    // if (devices.length > 0) {
    //   const device = devices[0];
    //   mgws = findMatchingEntries(networks.mgw_endpoint, 'net_device', device.device);
    // }

    const firstHalf = {
      Apn: pdn.apn,
      'Network Port': devices.length > 0 ? devices[0].device : null,
      'Network Slice': slices && slices.length > 0 ? slices[0].name : null,
      'User IPv4 Assignment':
        ipv4Pools.length > 0
          ? `${ipv4Pools[0].name} ${ipv4Pools[0].name ? ' : ' : ''} ${ipv4Pools[0].first_ip} - ${ipv4Pools[0].last_ip}`
          : null,
      'User IPv6 Assignment': null,
      'Port IP': devices.length > 0 ? `${devices[0].ip}/${devices[0].cidr}` : null,
      'Primary DNS': pdn.primary_dns,
      'Primary IPv6 DNS': pdn.primary_ipv6_dns,
      'Secondary DNS': pdn.secondary_dns,
      'Secondary IPv6 DNS': pdn.secondary_ipv6_dns,
    };

    const secondHalf = {
      'Port Type': devices.length > 0 ? devices[0].device_type : null,
      // TODO: Dont know how is this calculated in druid instance, hardcoding it for now
      LAS: pdn.apn === '*' ? <MdCheckCircle size="30" color="green" /> : <MdHighlightOff size="30" color="red" />,
      'IPV4 Routes': devices.length > 0 ? `${devices[0].ip}/${devices[0].cidr}` : null,
      'MAC Mappings':
        pdn.use_mac_mappings === '0' ? (
          <MdHighlightOff size="30" color="red" />
        ) : (
          <MdCheckCircle size="30" color="green" />
        ),
      NAT:
        devices.length > 0 ? (
          devices[0].nat_enabled === '1' ? (
            <MdCheckCircle size="30" color="green" />
          ) : (
            <MdHighlightOff size="30" color="red" />
          )
        ) : null,
      VLAN: devices.length > 0 ? devices[0].vlan_id !== '0' ? devices[0].vlan_id : <Text> - </Text> : <Text> - </Text>,
      'Accept All Users':
        profiles.length > 0 ? (
          profiles[0].apply_to_all_subs === 0 ? (
            <MdHighlightOff size="30" color="red" />
          ) : (
            <MdCheckCircle size="30" color="green" />
          )
        ) : null,
      'User Groups': groups.length > 0 ? groups[0].description : null,
      // TODO: Dont know how is this calculated in druid instance, hardcoding it for now
      State: <FaArrowUp size="30" color="green" />,
      'Port IP Config': devices.length > 0 ? devices[0].bootproto : null,
    };

    return [firstHalf, secondHalf];
  });

  const renderTableData = (data: any) => {
    const keyValueArray = convertToKeyValueArray(data);
    return <UnstyledTable tableData={keyValueArray} fontSize="xs" rowBorderBottom={true} />;
  };

  return (
    <Card>
      <Stack divider={<StackDivider />} spacing="4">
        <CardHeader
          onClick={() => setShowNetworkDetails(!showNetworkDetails)}
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          _hover={{
            cursor: 'pointer',
          }}
          borderRadius="lg"
        >
          <Heading size="xs" fontWeight="500" display="flex" justifyContent="space-between" width="100%">
            <Flex alignItems="center" width="100%">
              <Icon as={BsArrowReturnRight} />
              <Text ml="2">NETWORK LICENSE</Text>
              <Box flex="1" ml="10" display="flex" flexDirection="row" alignItems="center">
                <LicenseProgress usedLicense={networks.pdn.length} totalLicense={totalNetworkLicenses} />
              </Box>
            </Flex>
          </Heading>
        </CardHeader>
        {showNetworkDetails && (
          <CardBody>
            <Flex wrap="wrap" ml="4" justifyContent="center">
              {constructedEnrichedNetworkData.map(([firstHalf, secondHalf], index) => {
                const isLastItem = index === networks.pdn.length - 1;
                return (
                  <Box
                    key={index}
                    mb="8"
                    ml={isLastItem && networks.pdn.length % 2 !== 0 ? '0' : '8px'}
                    data-testid={`network-card-${index}`}
                    width="100%"
                  >
                    <Text fontWeight="bold" wordBreak="break-word" whiteSpace="normal" fontSize="lg" mb="2">
                      {'Apn' in firstHalf ? firstHalf.Apn : ''}
                    </Text>

                    <Flex flexDirection="row" justifyContent="space-between" width="100%">
                      <Flex flex="1" justifyContent="space-between">
                        {renderTableData(firstHalf)}
                      </Flex>
                      <Flex flex="1" justifyContent="space-between">
                        {renderTableData(secondHalf)}
                      </Flex>
                    </Flex>
                  </Box>
                );
              })}
            </Flex>
          </CardBody>
        )}
      </Stack>
    </Card>
  );
};
