import { Box, Flex, Text, Divider } from '@chakra-ui/react';
import { BsXCircleFill, BsWifi, BsLock, BsUnlock } from 'react-icons/bs';
import { IconType } from 'react-icons';

interface CellStatusProps {
  label: string;
  value: number;
  total: number;
  icon: IconType;
  iconColor: string;
  barColor: string;
}

interface CellStatusProgressProps {
  connected_cells: number;
  disconnected_cells: number;
  locked_cells: number;
  unlocked_cells: number;
}

const CellStatusProgressBar = ({ label, value, total, icon: Icon, iconColor, barColor }: CellStatusProps) => {
  const width = total === 0 ? '0%' : `${(value / total) * 100}%`;

  return (
    <Flex align="center" gap={3} data-testid={`cell-status-${label}`}>
      <Text minWidth="100px" fontSize="sm" mr="2" ml="4">
        {label}
      </Text>
      <Icon size={40} color={iconColor} />
      <Box position="relative" width="100%" bg="gray.100" borderRadius="md" ml="4">
        <Box height="24px" bg={barColor} width={width} borderRadius="md" transition="width 0.3s ease-in-out" />
        <Text
          position="absolute"
          left="50%"
          top="50%"
          transform="translate(-50%, -50%)"
          color="black"
          fontSize="sm"
          fontWeight="bold"
        >
          {value}
        </Text>
      </Box>
    </Flex>
  );
};

export const CellStatus = ({
  connected_cells,
  disconnected_cells,
  locked_cells,
  unlocked_cells,
}: CellStatusProgressProps) => {
  const connectivityTotal = connected_cells + disconnected_cells;
  const lockTotal = locked_cells + unlocked_cells;

  return (
    <Flex direction="column" gap={3} width="100%">
      <Text fontWeight="bold" wordBreak="break-word" whiteSpace="normal" fontSize="lg" display="inline" mr="1">
        Physical Cell Status
      </Text>

      <CellStatusProgressBar
        label="Connected"
        value={connected_cells}
        total={connectivityTotal}
        icon={BsWifi}
        iconColor="green"
        barColor="green.500"
      />
      <CellStatusProgressBar
        label="Disconnected"
        value={disconnected_cells}
        total={connectivityTotal}
        icon={BsXCircleFill}
        iconColor="red"
        barColor="red.500"
      />

      <Divider borderColor="gray.500" />

      <CellStatusProgressBar
        label="Unlocked"
        value={unlocked_cells}
        total={lockTotal}
        icon={BsUnlock}
        iconColor="green"
        barColor="green.500"
      />
      <CellStatusProgressBar
        label="Locked"
        value={locked_cells}
        total={lockTotal}
        icon={BsLock}
        iconColor="red"
        barColor="red.500"
      />
    </Flex>
  );
};
