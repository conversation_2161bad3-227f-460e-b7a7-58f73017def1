import {
  ModalBody,
  ModalContent,
  ModalOverlay,
  Box,
  Button,
  CardBody,
  CardHeader,
  Flex,
  Heading,
  Modal,
  Text,
  useDisclosure,
  <PERSON>dal<PERSON><PERSON>er,
  Modal<PERSON>eader,
  ModalCloseButton,
  SimpleGrid,
  Tooltip,
} from '@chakra-ui/react';
import { Card } from '@chakra-ui/react';
import { Enodeb } from '../../../../../types/orchestrator.types';
import { StatusToColor } from '../../../../../data/constants';
import { getComponentBgColor } from '../../../utils';
import { KeyValuePair, UnstyledTable } from '../../../airspan/utils';
import { snakeCaseToTitleCase } from '../utils';
import { MdCheckCircle } from 'react-icons/md';
import { BsArrowReturnRight, BsLock, BsUnlock, BsXCircleFill } from 'react-icons/bs';
import { ColumnDef, Row } from '@tanstack/react-table';
import React from 'react';
import { DataTable } from '../../../../../pages/MetricsCollector/components/DataTable';
export type Cell = {
  id: number;
  oper_state: string;
  admin_state: string;
  name: string;
  tac: number;
  cell_id: number;
  enb_id: number;
};

export type EnodebTrx = {
  id: number;
  oper_state: string;
  admin_state: string;
  plmn_id: string;
  identity: number;
  name: string;
  sctp_address: string;
  last_inform_time: string | null;
  cell: Cell[];
};

type CellMappingsModalProps = {
  isOpen: boolean;
  onClose: () => void;
  enodebMap: { [key: number]: EnodebTrx };
};

const getInversedCellMappings = (cells: Enodeb[]) => {
  const enodebMap: { [key: number]: EnodebTrx } = {};

  cells.forEach((enodeb) => {
    if (!enodebMap[enodeb.enodeb.id]) {
      enodebMap[enodeb.enodeb.id] = {
        id: enodeb.enodeb.id,
        oper_state: enodeb.enodeb.oper_state,
        admin_state: enodeb.enodeb.admin_state,
        plmn_id: enodeb.enodeb.plmn_id,
        identity: enodeb.enodeb.identity,
        name: enodeb.enodeb.name,
        sctp_address: enodeb.enodeb.sctp_address,
        last_inform_time: enodeb.enodeb.last_inform_time,
        cell: [
          {
            id: enodeb.cell_id,
            cell_id: enodeb.cell_id,
            enb_id: enodeb.enb_id,
            oper_state: enodeb.oper_state,
            admin_state: enodeb.admin_state,
            name: enodeb.name,
            tac: enodeb.tac,
          },
        ],
      };
    } else {
      enodebMap[enodeb.enodeb.id].cell.push({
        id: enodeb.cell_id,
        cell_id: enodeb.cell_id,
        enb_id: enodeb.enb_id,
        oper_state: enodeb.oper_state,
        admin_state: enodeb.admin_state,
        name: enodeb.name,
        tac: enodeb.tac,
      });
    }
  });

  return enodebMap;
};

const convertToKeyValueArray = (data: EnodebTrx | Cell) => {
  const keyValueArray: KeyValuePair[] = [];
  for (const key in data) {
    if (Object.prototype.hasOwnProperty.call(data, key)) {
      if (key === 'oper_state') {
        keyValueArray.push({
          key: snakeCaseToTitleCase(key),
          value:
            data[key] === 'enabled' ? (
              <MdCheckCircle size="30" color="green" />
            ) : (
              <BsXCircleFill size="30" color="red" />
            ),
        });
      } else if (key === 'admin_state') {
        keyValueArray.push({
          key: snakeCaseToTitleCase(key),
          value: data[key] === 'locked' ? <BsLock size="30" color="red" /> : <BsUnlock size="30" color="green" />,
        });
      } else if (key === 'cell') {
        continue;
      } else {
        keyValueArray.push({
          key: snakeCaseToTitleCase(key),
          value: data[key as keyof (EnodebTrx | Cell)],
        });
      }
    }
  }
  return keyValueArray;
};

export const CellMappings = ({ cells }: { cells: Enodeb[] }) => {
  const enodebMap = getInversedCellMappings(cells);
  const { isOpen, onOpen, onClose } = useDisclosure();

  return (
    <Flex direction="column" gap="4">
      <Button variant="primary" onClick={onOpen}>
        View Cell Mappings
      </Button>
      <CellMappingsModal isOpen={isOpen} onClose={onClose} enodebMap={enodebMap} />
    </Flex>
  );
};

export const CellCard = ({ cell }: { cell: Cell }) => {
  const status = cell.oper_state === 'enabled' ? 'OK' : ('ERROR' as keyof typeof StatusToColor);
  const bgColor = getComponentBgColor(status);

  return (
    <Card key={cell.id}>
      <CardHeader
        display="flex"
        alignItems="center"
        justifyContent="space-between"
        _hover={{
          bg: bgColor,
          cursor: 'pointer',
          boxShadow: `0 0 12px ${bgColor}`,
        }}
        borderRadius="lg"
      >
        <Heading size="xs" fontWeight="500" display="flex" justifyContent="space-between" width="100%">
          <Flex alignItems="center" width="100%">
            <Box position="relative" display="inline-block" ml="2">
              <Text mr="1">{cell.cell_id}</Text>
            </Box>
          </Flex>
        </Heading>
      </CardHeader>

      <CardBody>
        <Box ml="4">
          <UnstyledTable tableData={convertToKeyValueArray(cell)} fontSize="xs" rowBorderBottom={true} />
        </Box>
      </CardBody>
    </Card>
  );
};

const CellMappingsModal = ({ isOpen, onClose, enodebMap }: CellMappingsModalProps) => {
  const renderSubComponent = React.useCallback(({ row }: { row: Row<EnodebTrx> }) => {
    return (
      <SimpleGrid columns={[1, 2, 3]} spacing="5" ml="6">
        {row.original.cell.map((cell) => (
          <CellCard key={cell.id} cell={cell} />
        ))}
      </SimpleGrid>
    );
  }, []);

  const columns = React.useMemo<ColumnDef<EnodebTrx>[]>(
    () => [
      {
        header: 'ID',
        accessorKey: 'id',
        id: 'id',
        cell: (props) => {
          const id = props.row.original.id as number;

          return (
            <>
              <Flex>
                <BsArrowReturnRight />
                <Text ml="2">{id}</Text>
              </Flex>
            </>
          );
        },
      },
      {
        header: 'Name',
        accessorKey: 'name',
        id: 'name',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        header: 'Operating State',
        accessorKey: 'oper_state',
        id: 'oper_state',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
        cell: ({ getValue }) => {
          const status = getValue() as string;
          let icon = null;
          let label = '';
          if (status === 'enabled') {
            icon = <MdCheckCircle color="green" size="30" />;
            label = 'enabled';
          } else if (status === 'disabled') {
            icon = <BsXCircleFill color="red" size="30" />;
            label = 'disabled';
          }
          return (
            <Box display="flex" justifyContent="center" alignItems="center">
              <Tooltip label={label} hasArrow>
                <span>{icon}</span>
              </Tooltip>
            </Box>
          );
        },
      },
      {
        header: 'Admin State',
        accessorKey: 'admin_state',
        id: 'admin_state',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
        cell: ({ getValue }) => {
          const status = getValue() as string;
          let icon = null;
          let label = '';
          if (status === 'locked') {
            icon = <BsLock color="red" size="30" />;
            label = 'locked';
          } else if (status === 'unlocked') {
            icon = <BsUnlock color="green" size="30" />;
            label = 'unlocked';
          }
          return (
            <Box display="flex" justifyContent="center" alignItems="center">
              <Tooltip label={label} hasArrow>
                <span>{icon}</span>
              </Tooltip>
            </Box>
          );
        },
      },
      {
        header: 'PLMN ID',
        accessorKey: 'plmn_id',
        id: 'plmn_id',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        header: 'Identity',
        accessorKey: 'identity',
        id: 'identity',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        header: 'SCTP Address',
        accessorKey: 'sctp_address',
        id: 'sctp_address',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        header: 'Last Inform Time',
        accessorKey: 'last_inform_time',
        id: 'last_inform_time',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
    ],
    []
  );

  const dataSortedAlphabetically = Object.values(enodebMap).sort((a, b) => a.name.localeCompare(b.name));

  return (
    <Modal isOpen={isOpen} onClose={onClose} size={'5xl'}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>Cell Mappings</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <DataTable
            isExpandable={true}
            enableFilter={true}
            columns={columns}
            data={dataSortedAlphabetically}
            pageSizeOptions={[100, 200, 300, 400, 500, 600, 700, 800, 900, 1000]}
            isLoading={false}
            defaultPageSize={100}
            hasEmptyResult={dataSortedAlphabetically.length === 0}
            renderSubComponent={renderSubComponent}
            getRowCanExpand={(row) => !!row.original.subRows}
            hiddenColumnsByDefault={{
              name: true,
              oper_state: true,
              admin_state: true,
              plmn_id: true,
              identity: true,
              sctp_address: true,
              last_inform_time: false,
            }}
            version={'v2'}
          />
        </ModalBody>
        <ModalFooter>
          <Button colorScheme="blue" mr={3} onClick={onClose}>
            Close
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};
