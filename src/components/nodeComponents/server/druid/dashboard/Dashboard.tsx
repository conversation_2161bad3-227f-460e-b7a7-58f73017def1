import { StackDivider, Text } from '@chakra-ui/react';
import { Stack } from '@chakra-ui/react';
import { Cells } from './Cells';
import { Networks } from './Networks';
import { Operators } from './Operators';
import { Users } from './Users';
import { Druid } from '../../../../../types/orchestrator.types';
import _ from 'lodash';

export const Dashboard = ({ data }: { data: Druid }) => {
  const is4GCell = !(data?.segw?.s1client?.length > 0);
  const totalCellLicenses = is4GCell
    ? Number(data?.details?.features?.max_enbs)
    : Number(data?.details?.features?.enbgw_max_enbs);

  return (
    <Stack divider={<StackDivider />} spacing="4">
      {!_.isEmpty(data?.aspects?.users_count) ? (
        <Users
          usedUserLicenses={data?.aspects?.users_count}
          totalUserLicenses={Number(
            data?.details?.features?.enbgw_max_active_subs || data?.details?.features?.max_nbr_of_subs
          )}
        />
      ) : (
        <Text>No users found</Text>
      )}
      {data?.details?.features?.max_s1_clients ? (
        <Operators
          s1clients={data?.segw?.s1client}
          totalOperatorLicenses={Number(data?.details?.features?.max_s1_clients)}
        />
      ) : _.isEmpty(data?.networks) ? (
        <Text>No networks found</Text>
      ) : (
        <Networks networks={data?.networks} totalNetworkLicenses={Number(data?.details?.features?.max_pdns)} />
      )}
      {!_.isEmpty(data?.enodebs) ? (
        <Cells cells={data?.enodebs} is4GCell={is4GCell} totalCellLicenses={totalCellLicenses} />
      ) : (
        <Text>No cells found</Text>
      )}
    </Stack>
  );
};
