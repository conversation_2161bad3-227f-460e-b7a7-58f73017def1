import { Box, Card, CardBody, CardHeader, Flex, Icon, <PERSON>ing, Stack, StackDivider, Text } from '@chakra-ui/react';
import { LicenseProgress } from '../utils';
import { BsArrowReturnRight } from 'react-icons/bs';
import { useState } from 'react';

export const Users = ({
  usedUserLicenses,
  totalUserLicenses,
}: {
  usedUserLicenses: number;
  totalUserLicenses: number;
}) => {
  const [showUserDetails, setShowUserDetails] = useState(false);

  return (
    <Card>
      <Stack divider={<StackDivider />} spacing="4">
        <CardHeader
          onClick={() => setShowUserDetails(!showUserDetails)}
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          _hover={{
            cursor: 'pointer',
          }}
          borderRadius="lg"
        >
          <Heading size="xs" fontWeight="500" display="flex" justifyContent="space-between" width="100%">
            <Flex alignItems="center" width="100%">
              <Icon as={BsArrowReturnRight} />
              <Text ml="2" mr="9">
                USER LICENSE
              </Text>
              <Box flex="1" ml="10" display="flex" flexDirection="row" alignItems="center">
                <LicenseProgress usedLicense={usedUserLicenses} totalLicense={totalUserLicenses} />
              </Box>
            </Flex>
          </Heading>
        </CardHeader>
        {showUserDetails && (
          <CardBody ml="10">
            <Text fontSize="md">Coming Soon</Text>
          </CardBody>
        )}
      </Stack>
    </Card>
  );
};
