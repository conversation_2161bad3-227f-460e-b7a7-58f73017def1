import {
  Box,
  Card,
  CardBody,
  CardHeader,
  Flex,
  Icon,
  <PERSON>ing,
  <PERSON>ack,
  StackDivider,
  Text,
  Divider,
} from '@chakra-ui/react';
import { LicenseProgress } from '../utils';
import { BsArrowReturnRight } from 'react-icons/bs';
import { useState } from 'react';
import { CellStatus } from './CellStatus';
import { Tooltip } from '@chakra-ui/react';
import { InfoIcon } from '@chakra-ui/icons';
import { CellMappings } from './CellMappings';
import { ReactComponent as FourGIcon } from '../../../../../assets/icons/4G.svg';
import { Enodeb } from '../../../../../types/orchestrator.types';

const getUniqueEnodebs = (array: Enodeb[]) => {
  // Create a Map to store unique enodebs using their id as key
  const enodebMap = new Map();

  array.forEach((item) => {
    if (item.enodeb && item.enodeb.id) {
      // Only add if this enodeb id hasn't been seen before
      if (!enodebMap.has(item.enodeb.id)) {
        enodebMap.set(item.enodeb.id, item.enodeb);
      }
    }
  });

  // Convert Map values back to array
  return Array.from(enodebMap.values());
};

export const Cells = ({
  cells,
  is4GCell,
  totalCellLicenses,
}: {
  cells: Enodeb[];
  is4GCell: boolean;
  totalCellLicenses: number;
}) => {
  const [showCellDetails, setShowCellDetails] = useState(false);
  const connectedCells = cells.filter((cell) => cell.oper_state === 'enabled');
  const uniqueEnodebs = getUniqueEnodebs(cells);

  return (
    <Card>
      <Stack divider={<StackDivider />} spacing="4">
        <CardHeader
          onClick={() => setShowCellDetails(!showCellDetails)}
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          _hover={{
            cursor: 'pointer',
          }}
          borderRadius="lg"
        >
          <Heading size="xs" fontWeight="500" display="flex" justifyContent="space-between" width="100%">
            {is4GCell ? (
              <Flex alignItems="center" width="100%">
                <Icon as={BsArrowReturnRight} />
                <FourGIcon width="30" height="30" />
                <Box position="relative" display="inline-block" ml="1">
                  <Text mr="1">CELL LICENSE</Text>
                  <Tooltip label={`Druid Enodeb Licenses`} placement="bottom">
                    <InfoIcon
                      position="absolute"
                      top="-0.5em"
                      right="-0.8em"
                      color="gray.500"
                      cursor="pointer"
                      boxSize="1em"
                    />
                  </Tooltip>
                </Box>
                <Box flex="1" ml="10" display="flex" flexDirection="row" alignItems="center">
                  <LicenseProgress
                    usedLicense={uniqueEnodebs.filter((enodeb) => enodeb.oper_state === 'enabled').length}
                    totalLicense={totalCellLicenses}
                  />
                </Box>
              </Flex>
            ) : (
              <Flex alignItems="center" width="100%">
                <Icon as={BsArrowReturnRight} />
                <Box position="relative" display="inline-block" ml="2">
                  <Text mr="1">CELL LICENSE</Text>
                  <Tooltip label={`Total Cell Licenses`} placement="bottom">
                    <InfoIcon
                      position="absolute"
                      top="-0.5em"
                      right="-0.8em"
                      color="gray.500"
                      cursor="pointer"
                      boxSize="1em"
                    />
                  </Tooltip>
                </Box>
                <Box flex="1" ml="20" display="flex" flexDirection="row" alignItems="center">
                  <LicenseProgress usedLicense={connectedCells.length} totalLicense={totalCellLicenses} />
                </Box>
              </Flex>
            )}
          </Heading>
        </CardHeader>
        {showCellDetails && (
          <CardBody>
            <Box ml="4">
              <CellStatus
                connected_cells={cells.filter((cell) => cell.oper_state === 'enabled').length}
                disconnected_cells={cells.filter((cell) => cell.oper_state === 'disabled').length}
                locked_cells={cells.filter((cell) => cell.admin_state === 'locked').length}
                unlocked_cells={cells.filter((cell) => cell.admin_state === 'unlocked').length}
              />
              <Divider />
              <Flex wrap="wrap" justifyContent="flex-start" width="100%" flexDirection="column" mt="4">
                <CellMappings cells={cells} />
              </Flex>
            </Box>
          </CardBody>
        )}
      </Stack>
    </Card>
  );
};
