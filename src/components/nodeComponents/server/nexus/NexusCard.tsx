import {
  <PERSON>ge,
  <PERSON><PERSON>,
  Card,
  CardBody,
  CardHeader,
  <PERSON><PERSON>r,
  Flex,
  Heading,
  Icon,
  Stack,
  StackDivider,
  Text,
  useDisclosure,
} from '@chakra-ui/react';
import { isEmpty } from 'lodash';
import { useState } from 'react';
import { BsArrowReturnRight } from 'react-icons/bs';
import { NODE_COMPONENT_TITLES } from '../../../../data/constants';
import { getStatusColor } from '../../../../pages/CellOverview/hooks/useStatus';
import StatusComponent from '../../../icons/StatusIcon';
import { getComponentBgColor } from '../../utils';
import { Nexus } from '../../../../types/orchestrator.types';
import { ComponentTypeEnum } from '../../../../services/types';
import { UnstyledTable } from '../../airspan/utils';
import CellOverviewAccordion from '../../accordian/CellOverviewAccordion';
import moment from 'moment';
import ComponentModal from '../../ComponentModal';
import { recursiveSearch } from '../../../../utils/recursiveSearch';

const NexusCard = ({ data, nodeId }: { data: Nexus | any; nodeId: string }) => {
  const [showContent, setShowContent] = useState(false);
  const { isOpen, onOpen, onClose } = useDisclosure();

  const status = data?.status || 'UNKNOWN';

  const statusColor = getStatusColor(status);
  const bgColor = getComponentBgColor(statusColor);
  const flattenedData: any = recursiveSearch(data);

  const formatActivityIntervals = (intervals: { interval: string; count: number }[]) => {
    if (!intervals || !Array.isArray(intervals)) return [];

    return intervals.map(({ interval, count }) => {
      const [start, end] = interval.split('-');
      const formattedRange = `${moment(start, 'YYYYMMDD').format('DD/MM/YYYY')} - ${moment(end, 'YYYYMMDD').format(
        'DD/MM/YYYY'
      )}`;

      return {
        key: formattedRange,
        value: <Badge colorScheme="gray">{count}</Badge>,
      };
    });
  };

  return (
    <>
      <Card width="100%" borderRadius="lg">
        <CardHeader
          _hover={{
            bg: bgColor,
            cursor: 'pointer',
            boxShadow: `0 0 12px ${bgColor}`,
          }}
          transition="background-color 0.2s ease, box-shadow 0.2s ease"
          borderRadius="lg"
          onClick={() => setShowContent(!showContent)}
          display="flex"
          alignItems="center"
          justifyContent="space-between"
        >
          <Heading size="xs" fontWeight="500" display="flex" justifyContent="space-between">
            <Flex alignItems="center">
              <Icon as={BsArrowReturnRight} />
              <Text ml="2"> {NODE_COMPONENT_TITLES.NEXUS}</Text>
            </Flex>
          </Heading>

          <StatusComponent
            dataTestId="cell-main-table-status-icon"
            status={data?.status}
            boxSize="sm"
            color={statusColor}
            component_type={ComponentTypeEnum.NEXUS}
            component_id={data?.device_id}
          />
        </CardHeader>

        {showContent ? (
          !isEmpty(data) ? (
            <CardBody borderTop="1px solid #e2e2e2">
              <Stack divider={<StackDivider />} spacing="4">
                <Flex justifyContent={data?.segw ? 'space-between' : 'flex-start'}>
                  <UnstyledTable
                    tableData={[
                      { key: 'IP Address', value: <Badge colorScheme="gray"> {data.ip_address} </Badge> },
                      {
                        key: 'Name',
                        value: (
                          <Badge colorScheme="gray" textTransform="none">
                            {' '}
                            {data.name}{' '}
                          </Badge>
                        ),
                      },
                      {
                        key: 'Description',
                        value: (
                          <Badge colorScheme="gray" textTransform="none">
                            {' '}
                            {data.description}{' '}
                          </Badge>
                        ),
                      },
                      {
                        key: 'Last Contacted',
                        value: (
                          <Badge colorScheme="gray">
                            {data.last_contacted ? moment(data.last_contacted).format('DD/MM/YYYY') : 'N/A'}
                          </Badge>
                        ),
                      },
                    ]}
                    firstColWidth="30%"
                  />
                </Flex>
                <StackDivider />
              </Stack>

              <Stack spacing="4">
                <Heading size="sm">Activity</Heading>
                {(['issued', 'expiring', 'revoked'] as const).map((key) => (
                  <CellOverviewAccordion key={key} title={key} data={data.activity[key]}>
                    <UnstyledTable
                      tableData={formatActivityIntervals(data.activity[key]?.intervals ?? [])}
                      firstColWidth="40%"
                    />
                  </CellOverviewAccordion>
                ))}
                <Divider />
                <Button onClick={onOpen}>More Data</Button>
                <StackDivider />
              </Stack>
            </CardBody>
          ) : (
            <Text fontSize="sm" color="gray.500">
              Not Available
            </Text>
          )
        ) : null}
      </Card>

      {/* Modal */}
      <ComponentModal isOpen={isOpen} onClose={onClose} flattenedData={flattenedData} />
    </>
  );
};

export default NexusCard;
