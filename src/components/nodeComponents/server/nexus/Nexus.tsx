import { Box } from '@chakra-ui/react';
import { ErrorBoundary } from 'react-error-boundary';
import { NODE_COMPONENT_TITLES } from '../../../../data/constants';
import { ErrorBoundaryFallback, ErrorBoundaryLogError } from '../../../errorComponents/ErrorBoundaryFallback';
import NodeComponentErrorCard from '../../../errorComponents/NodeComponentErrorCard';
import NexusCard from './NexusCard';

type NexusProps = {
  data: any;
  nodeId: string;
};

const Nexus = ({ data, nodeId }: NexusProps) => {
  return (
    <Box width="50%" padding="1rem" data-testid="druid-card">
      {data.error ? (
        <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
          <NodeComponentErrorCard
            id={nodeId}
            errorData={data.error}
            compName={NODE_COMPONENT_TITLES.DRUID}
            testId="node-comp-error-card"
            marginSpace="8"
          />
        </ErrorBoundary>
      ) : (
        <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
          <NexusCard data={data} nodeId={nodeId} />
        </ErrorBoundary>
      )}
    </Box>
  );
};

export default Nexus;
