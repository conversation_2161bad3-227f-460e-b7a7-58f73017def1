import { Box, Table, Tbody, Td, Text, Th, Thead, Tr } from '@chakra-ui/react';
import { MdAccessTime, MdWarning, MdHighlightOff, MdHelpOutline, MdCheckCircle, MdDirectionsRun } from 'react-icons/md';

type StatusIconType = {
  [key: string]: JSX.Element;
};

export const statusIcon: StatusIconType = {
  pending: <MdAccessTime color="orange" size="25" />,
  running: <MdDirectionsRun color="teal" size="25" />,
  completed: <MdCheckCircle color="green" size="25" />,
  failed: <MdWarning color="red" size="25" />,
  cancelled: <MdHighlightOff color="grey" size="25" />,
  unknown: <MdHelpOutline color="black" size="25" />,
};

interface Task {
  id: string;
  task_status: string;
  task_result: { reason: string };
  task_type: string;
  task_created_at: string;
  task_updated_at: string;
}

const ServerTask = ({ tasks }: { tasks: Task[] }) => {
  return (
    <>
      {tasks.length > 0 ? (
        <Box>
          <Text mb="2" ml="2" fontSize="l" fontWeight="bold">
            Recent Task Details
          </Text>

          <Table>
            <Thead>
              <Tr>
                <Th textAlign="left" p={2}>
                  Task Type
                </Th>
                <Th textAlign="left" p={2}>
                  Task Status
                </Th>
                <Th textAlign="left" p={2}>
                  Task Result
                </Th>
                <Th textAlign="left" p={2}>
                  Task Created At
                </Th>
                {/* <Th textAlign="left" p={2}>
                  Task Updated At
                </Th> */}
              </Tr>
            </Thead>
            <Tbody>
              {tasks?.map((task) => (
                <Tr key={task.id} justifyContent="space-around">
                  <Td p={1.5}>
                    <Text wordBreak="break-word">{task.task_type.toUpperCase().replace('_', '-')}</Text>
                  </Td>

                  <Td p={1.5}>
                    <Box display="flex" alignItems="center">
                      <Text wordBreak="break-word" mr="1">
                        {task.task_status.toUpperCase()}
                      </Text>
                      {statusIcon[task.task_status]}
                    </Box>
                  </Td>

                  <Td p={1.5} flex="1">
                    <Box overflowY="auto" maxHeight="100px">
                      <Text wordBreak="break-word">{task.task_result?.reason}</Text>
                    </Box>
                  </Td>

                  <Td p={1.5}>
                    <Text wordBreak="break-word">{task.task_created_at.replace('T', ' ').slice(0, 19)}</Text>
                  </Td>

                  {/* <Td p={1.5}>
                    <Text wordBreak="break-word">{task.task_updated_at.replace('T', ' ').slice(0, 19)}</Text>
                  </Td> */}
                </Tr>
              ))}
            </Tbody>
          </Table>
        </Box>
      ) : (
        <Text>No tasks have been run in the past.</Text>
      )}
    </>
  );
};

export default ServerTask;
