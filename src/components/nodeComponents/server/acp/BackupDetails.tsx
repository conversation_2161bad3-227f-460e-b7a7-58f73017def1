import { But<PERSON>, <PERSON>ack, Text } from '@chakra-ui/react';
import { Badge } from '@chakra-ui/react';
import { Box } from '@chakra-ui/react';
import cronstrue from 'cronstrue';
import moment from 'moment';
import { useEffect, useState } from 'react';

const CronFormatter = (cronExpression: string) => {
  const humanReadable = cronstrue.toString(cronExpression);
  return <div>{humanReadable}</div>;
};

const lastBackupRanAt = (path: string) => {
  const match = path.match(/(\d{4})_(\d{2})_(\d{2})_(\d{2})H(\d{2})M(\d{2})S(\d{3})/);

  if (match) {
    // Extracting the components from the regex match
    const [, year, month, day, hours, minutes, seconds, milliseconds] = match.map(Number); // convert strings to numbers

    // Creating the date object
    const date = new Date(year, month - 1, day, hours, minutes, seconds);

    // Adding milliseconds to the time
    date.setMilliseconds(milliseconds);
    return date;
  } else {
    console.log(`Unable to extract last backup time from ${path}`);
  }
};

const ElapsedTime = ({ startTime }: { startTime: any }) => {
  const [currentTime, setCurrentTime] = useState(() => moment());

  useEffect(() => {
    const tick = () => {
      const now = moment();
      setCurrentTime(now);
    };
    const timerID = setInterval(() => tick(), 1000);
    return () => clearInterval(timerID);
  }, []);

  const duration = moment.duration(currentTime.diff(moment(startTime)));
  let humanizedDuration = '';
  if (duration.years() > 0) humanizedDuration += `${duration.years()} years `;
  if (duration.months() > 0) humanizedDuration += `${duration.months()} months `;
  if (duration.days() > 0) humanizedDuration += `${duration.days()} days `;
  if (duration.hours() > 0) humanizedDuration += `${duration.hours()} hours `;
  if (duration.minutes() > 0) humanizedDuration += `${duration.minutes()} minutes `;
  if (duration.seconds() > 0) humanizedDuration += `${duration.seconds()} seconds `;

  return <Text>{humanizedDuration.trim()}</Text>;
};

export const BackupDetails = ({
  data,
  isRecentTaskRunningOrPending,
  handleClick,
  actionIcon,
}: {
  data: any;
  isRecentTaskRunningOrPending: boolean;
  handleClick: (taskType: string) => void;
  actionIcon: any;
}) => {
  return (
    <Box>
      {data.health_status_logs.details?.cron_active && (
        <Text pt="2" fontSize={['sm']} padding="0.1em" mb="2">
          Backup Frequency:{' '}
          <Badge pl="2" pr="2">
            {CronFormatter(data.health_status_logs.details.cron_freq)}
          </Badge>
        </Text>
      )}
      {data?.health_status_logs?.latest_backup ? (
        <Box mb="2">
          <Text pt="4" fontSize={['sm']} padding="0.1em">
            Last Backup was{' '}
            <Badge pl="4" pr="4" sx={{ textTransform: 'none' }} fontSize={['sm', 'md']}>
              {' '}
              <ElapsedTime startTime={lastBackupRanAt(data?.health_status_logs?.latest_backup)} />
            </Badge>{' '}
            ago
          </Text>
        </Box>
      ) : null}
      <Stack direction="row" mt="2">
        <Button
          isDisabled={!data?.acp_platform_agent_available || isRecentTaskRunningOrPending}
          colorScheme="teal"
          onClick={() => handleClick('snapshot')}
          size="sm"
        >
          <Text mr="1">Take Snapshot</Text>
          {actionIcon['takeSnapshot']}
        </Button>
        <Button
          isDisabled={!data?.acp_platform_agent_available || isRecentTaskRunningOrPending}
          colorScheme="green"
          onClick={() => handleClick('start_backup')}
          size="sm"
        >
          <Text>Start Backup</Text>
          {actionIcon['startBackup']}
        </Button>
        <Button
          isDisabled={
            !data?.acp_platform_agent_available ||
            !data.health_status_logs.details?.cron_active ||
            isRecentTaskRunningOrPending
          }
          bg="tomato"
          _hover={{
            bg: '#d9463e',
          }}
          onClick={() => handleClick('stop_backup')}
          size="sm"
        >
          <Text>Stop Backup</Text>
          {actionIcon['stopBackup']}
        </Button>
      </Stack>
    </Box>
  );
};
