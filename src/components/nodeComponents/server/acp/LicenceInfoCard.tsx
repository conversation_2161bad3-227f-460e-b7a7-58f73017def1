import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Text, Heading, Flex, CardBody, Box } from '@chakra-ui/react';
import { BsArrowReturnRight } from 'react-icons/bs';
import { useState } from 'react';
import { UnstyledTable } from '../../airspan/utils';
import { PascalCaseToSentenceCase } from '../server/utils';
import { TimeElapsed } from './AcpCard';

type LicenceInfoType = {
  id: number;
  licenseType: string;
  licenseId: string;
  licenseVersion: number;
  expiryDate?: string;
};

export const LicenceInfoCard = ({ licence_info }: { licence_info: LicenceInfoType[] }) => {
  const [showContent, setShowContent] = useState(false);

  return (
    <Card width="100%" borderRadius="lg" style={{ marginRight: '1rem' }} overflow="auto" data-testid="services-status">
      <CardHeader
        _hover={{
          cursor: 'pointer',
        }}
        transition="background-color 0.2s ease, box-shadow 0.2s ease"
        onClick={() => setShowContent(!showContent)}
        display="flex"
        alignItems="center"
        justifyContent="space-between"
        h="60px"
        minH="60px"
      >
        <Heading size="xs" fontWeight="500" display="flex" justifyContent="space-between">
          <Flex alignItems="center">
            <Icon as={BsArrowReturnRight} />
            <Text ml={2}>Licence Info</Text>
          </Flex>
        </Heading>
      </CardHeader>
      {showContent && (
        <CardBody borderTop={'1px solid #e2e2e2'}>
          <DisplayTable licence_info={licence_info} />
        </CardBody>
      )}
    </Card>
  );
};

const DisplayTable = ({ licence_info }: { licence_info: LicenceInfoType[] }) => {
  const convertToKeyValueArray = (data: LicenceInfoType) => {
    const keyValueArray: { key: string; value: any }[] = [];
    const orderedKeys = ['licenseType', 'licenseId', 'licenseVersion', 'expiryDate'];

    orderedKeys.forEach((key) => {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        if (key === 'expiryDate') {
          keyValueArray.push({
            key: 'Expired',
            value: <TimeElapsed initialUptime={data[key as keyof LicenceInfoType] as string} />,
          });
        } else {
          keyValueArray.push({
            key: PascalCaseToSentenceCase(key),
            value: data[key as keyof LicenceInfoType],
          });
        }
      }
    });
    return keyValueArray;
  };

  const renderTableData = (data: LicenceInfoType) => {
    const keyValueArray = convertToKeyValueArray(data);
    return <UnstyledTable tableData={keyValueArray} fontSize="xs" rowBorderBottom={true} />;
  };

  return (
    <Flex wrap="wrap" ml="4" justifyContent="center" mt="4">
      <Flex wrap="wrap" justify="flex-start" mx={-2}>
        {licence_info.map((detail, index) => (
          <Box key={detail.licenseId} flexBasis={{ base: '100%', md: 'calc(50% - 16px)' }} m={1} mb={8}>
            <Text fontWeight="bold" wordBreak="break-word" whiteSpace="normal">
              {detail.licenseId}
            </Text>
            {renderTableData(detail)}
          </Box>
        ))}
      </Flex>
    </Flex>
  );
};

export default LicenceInfoCard;
