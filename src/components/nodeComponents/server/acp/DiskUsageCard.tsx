import { Box, Icon, CardBody, Flex, Heading, Text, Card, CardHeader } from '@chakra-ui/react';

import { BsArrowReturnRight } from 'react-icons/bs';
import { StaticStatusCircleIcon } from '../../../icons/StatusIcon';
import { StatusConstants, StatusToColor } from '../../../../data/constants';
import { useState } from 'react';

import { getComponentBgColor } from '../../utils';
import { getStatusColor } from '../../../../pages/CellOverview/hooks/useStatus';
import { UtilizationBar } from '../../common/UtilizationBar';
import _ from 'lodash';
import { UnstyledTable } from '../../airspan/utils';
import { convertToBestReadable } from '../server/utils';

type DiskUsageProps = {
  StartTime: Date;
  EndTime: Date;
  DurationMinutes: number;
  ServerType: number;
  Disk: string;
  AvailableGb: number;
  TotalGb: number;
  UsagePercentage: number;
};

const DiskUsageStatusDeterminer = (diskUsage: number | undefined) => {
  if (diskUsage === undefined) {
    return StatusConstants.UNKNOWN;
  }

  if (diskUsage > 99) {
    return StatusConstants.CRITICAL;
  } else if (diskUsage > 95) {
    return StatusConstants.ERROR;
  } else if (diskUsage > 80) {
    return StatusConstants.WARNING;
  } else {
    return StatusConstants.OK;
  }
};

export const DiskUsageCard = ({ details }: { details: DiskUsageProps[] }) => {
  const [showContent, setShowContent] = useState(false);

  const rootDisk = details.find((detail) => detail.Disk === '/');
  const diskStatus = DiskUsageStatusDeterminer(rootDisk?.UsagePercentage);
  const diskStatusColor = getStatusColor(diskStatus);
  const bgColor = getComponentBgColor(diskStatusColor);

  return (
    <Card width="100%" borderRadius="lg" style={{ marginRight: '1rem' }} overflow="auto" data-testid="services-status">
      <CardHeader
        _hover={{
          bg: bgColor,
          cursor: 'pointer',
          boxShadow: `0 0 12px ${bgColor}`,
        }}
        transition="background-color 0.2s ease, box-shadow 0.2s ease"
        onClick={() => setShowContent(!showContent)}
        display="flex"
        alignItems="center"
        justifyContent="space-between"
        h="60px"
        minH="60px"
      >
        <Heading size="xs" fontWeight="500" display="flex" justifyContent="space-between" width="100%">
          <Flex alignItems="center" width="100%">
            <Icon as={BsArrowReturnRight} />
            <Text ml="2">DISK</Text>
            {rootDisk && (
              <Box flex="1" ml="10" display="flex" flexDirection="row" alignItems="center">
                <Text mr="2">Used</Text>
                <Box flex="1">
                  <UtilizationBar percentage={rootDisk.UsagePercentage} usePadding={false} threshold={95} />
                </Box>
              </Box>
            )}
          </Flex>
        </Heading>
        <Box ml="5">
          <StaticStatusCircleIcon size={40} color={StatusToColor[diskStatus as keyof typeof StatusToColor]} />
        </Box>
      </CardHeader>
      {showContent && (
        <CardBody borderTop={'1px solid #e2e2e2'}>
          <DisplayTable details={details} />
        </CardBody>
      )}
    </Card>
  );
};

const DisplayTable = ({ details }: { details: DiskUsageProps[] }) => {
  const convertToKeyValueArray = (data: DiskUsageProps) => {
    const keyValueArray: { key: string; value: any }[] = [];
    const orderedKeys = ['TotalGb', 'UsedGb', 'UsagePercentage', 'AvailableGb'];

    orderedKeys.forEach((key) => {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        if (key === 'UsagePercentage') {
          if (
            !_.isNull(data[key as keyof DiskUsageProps]) &&
            !_.isUndefined(data[key as keyof DiskUsageProps]) &&
            !_.isNaN(data[key as keyof DiskUsageProps])
          ) {
            keyValueArray.push({
              key: `Used`,
              value: <UtilizationBar percentage={data['UsagePercentage']} usePadding={false} threshold={90} />,
            });
          }
        } else {
          let newKey = key;
          if (key.includes('Gb')) {
            newKey = key.replace('Gb', '');
          }
          keyValueArray.push({ key: newKey, value: convertToBestReadable(data[key as keyof DiskUsageProps] + 'GB') });
        }
      }
    });

    // Explicitly add the calculated "Used" (TotalGb - AvailableGb)
    if (data.TotalGb !== undefined && data.AvailableGb !== undefined) {
      keyValueArray.splice(1, 0, {
        key: 'Used',
        value: convertToBestReadable((Number(data.TotalGb) - Number(data.AvailableGb)).toFixed(3) + 'GB'),
      });
    }

    return keyValueArray;
  };

  const renderTableData = (data: DiskUsageProps) => {
    const keyValueArray = convertToKeyValueArray(data);
    return <UnstyledTable tableData={keyValueArray} fontSize="sm" rowBorderBottom={true} />;
  };

  const rootDisk = details.find((detail) => detail.Disk === '/');
  const nonRootDisks = details.filter((detail) => detail.Disk !== '/');
  // sort otherDisks by disk_used in descending order
  const sortedNonRootDisks = nonRootDisks.sort((a, b) => b.UsagePercentage - a.UsagePercentage);

  return (
    <Flex direction="column" width="100%">
      <Box width="100%" mb="6">
        <Text fontSize="sm" mb="2">
          Metrics captured for {rootDisk?.DurationMinutes} minutes
          <Text>
            {' '}
            ({rootDisk?.StartTime.toLocaleString().replace('T', ' ')} -{' '}
            {rootDisk?.EndTime.toLocaleString().replace('T', ' ')})
          </Text>
        </Text>
      </Box>

      <Box width="100%">
        {rootDisk && (
          <Box key="total" width="50%" mx="auto" mb={8}>
            <Text fontWeight="bold" wordBreak="break-word" whiteSpace="normal">
              /
            </Text>
            {renderTableData(rootDisk)}
          </Box>
        )}
        <Flex wrap="wrap" justify="flex-start" mx={-2}>
          {sortedNonRootDisks.map((detail, index) => (
            <Box key={detail.Disk} flexBasis={{ base: '100%', md: 'calc(50% - 16px)' }} m={2} mb={8}>
              <Text fontWeight="bold" wordBreak="break-word" whiteSpace="normal">
                {detail.Disk}
              </Text>
              {renderTableData(detail)}
            </Box>
          ))}
        </Flex>
      </Box>
    </Flex>
  );
};

export default DiskUsageCard;
