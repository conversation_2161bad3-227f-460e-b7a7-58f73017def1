import { convertToBestReadable, PascalCaseToSentenceCase } from '../server/utils';
import { UnstyledTable } from '../../airspan/utils';
import { Badge, Box } from '@chakra-ui/react';
import { TimeElapsed } from './AcpCard';
import { MdWarning } from 'react-icons/md';
import { MdCheckCircle } from 'react-icons/md';

type BasicInfoType = {
  operatingSystem: string;
  databaseEngine: string;
  nmsSoftwareVersion: string;
  lastUpgradeTime: string;
  physicalMemory: string;
  countryVariant: string;
  machineName: string;
};

export const BasicInfo = ({ info, license_valid }: { info: BasicInfoType; license_valid?: boolean }) => {
  const {
    operatingSystem,
    databaseEngine,
    nmsSoftwareVersion,
    lastUpgradeTime,
    physicalMemory,
    countryVariant,
    machineName,
  } = info;

  const tableData = [
    { key: 'NMS Software Version', value: <Badge>{nmsSoftwareVersion}</Badge> },
    { key: 'Operating System', value: operatingSystem },
    { key: 'Time Since Last Upgrade', value: <TimeElapsed initialUptime={lastUpgradeTime} /> },
    { key: 'Physical Memory', value: convertToBestReadable(physicalMemory) },
    // { key: 'Country Variant', value: countryVariant },
    // { key: 'Machine Name', value: machineName },
    // { key: 'Database Engine', value: databaseEngine },
  ];

  if (license_valid !== undefined) {
    tableData.push({
      key: 'License Valid',
      value: license_valid ? <MdCheckCircle size="25" color="green" /> : <MdWarning size="25" color="red" />,
    });
  }

  return (
    <Box width="100%">
      <UnstyledTable tableData={tableData} firstColWidth="30%" />
    </Box>
  );
};
