import { Box, Icon, CardBody, Flex, Heading, Text, Card, CardHeader } from '@chakra-ui/react';

import { BsArrowReturnRight } from 'react-icons/bs';
import { StaticStatusCircleIcon } from '../../../icons/StatusIcon';
import { StatusConstants, StatusToColor } from '../../../../data/constants';
import { useState } from 'react';

import { getComponentBgColor } from '../../utils';
import { getStatusColor } from '../../../../pages/CellOverview/hooks/useStatus';
import { UtilizationBar } from '../../common/UtilizationBar';
import _ from 'lodash';
import { UnstyledTable } from '../../airspan/utils';
import { PascalCaseToSentenceCase } from '../server/utils';

type CpuUsageProps = {
  StartTime: Date;
  EndTime: Date;
  DurationMinutes: number;
  SystemCpu: number;
  NmsEventServiceCpu: number;
  NmsProvisioningServiceCpu: number;
  NmsDiscoveryServiceCpu: number;
  NmsSoftwareServiceCpu: number;
  NmsArchiveServiceCpu: number;
  NmsCbrsServiceCpu: number;
  NmsStatisticsServiceCpu: number;
  SqlCpu: number;
  WebServerCpu: number;
};

const CpuUsageStatusDeterminer = (cpuUsage: number | undefined) => {
  if (cpuUsage === undefined) {
    return StatusConstants.UNKNOWN;
  }

  if (cpuUsage > 99) {
    return StatusConstants.CRITICAL;
  } else if (cpuUsage > 95) {
    return StatusConstants.ERROR;
  } else if (cpuUsage > 80) {
    return StatusConstants.WARNING;
  } else {
    return StatusConstants.OK;
  }
};

export const CpuUsageCard = ({ details }: { details: CpuUsageProps }) => {
  const [showContent, setShowContent] = useState(false);

  const cpuStatus = CpuUsageStatusDeterminer(details.SystemCpu);
  const cpuStatusColor = getStatusColor(cpuStatus);
  const bgColor = getComponentBgColor(cpuStatusColor);

  return (
    <Card width="100%" borderRadius="lg" style={{ marginRight: '1rem' }} overflow="auto" data-testid="services-status">
      <CardHeader
        _hover={{
          bg: bgColor,
          cursor: 'pointer',
          boxShadow: `0 0 12px ${bgColor}`,
        }}
        transition="background-color 0.2s ease, box-shadow 0.2s ease"
        onClick={() => setShowContent(!showContent)}
        display="flex"
        alignItems="center"
        justifyContent="space-between"
        h="60px"
        minH="60px"
      >
        <Heading size="xs" fontWeight="500" display="flex" justifyContent="space-between" width="100%">
          <Flex alignItems="center" width="100%">
            <Icon as={BsArrowReturnRight} />
            <Text ml="2">CPU</Text>
            {details.SystemCpu && (
              <Box flex="1" ml="10" display="flex" flexDirection="row" alignItems="center">
                <Text mr="2">Used</Text>
                <Box flex="1">
                  <UtilizationBar percentage={details.SystemCpu} usePadding={false} threshold={95} />
                </Box>
              </Box>
            )}
          </Flex>
        </Heading>
        <Box ml="5">
          <StaticStatusCircleIcon size={40} color={StatusToColor[cpuStatus as keyof typeof StatusToColor]} />
        </Box>
      </CardHeader>
      {showContent && (
        <CardBody borderTop={'1px solid #e2e2e2'}>
          <DisplayTable details={details} />
        </CardBody>
      )}
    </Card>
  );
};

const DisplayTable = ({ details }: { details: CpuUsageProps }) => {
  const convertToKeyValueArray = (data: CpuUsageProps) => {
    const keyValueArray: { key: string; value: any }[] = [];
    const orderedKeys = [
      'SystemCpu',
      'NmsEventServiceCpu',
      'NmsProvisioningServiceCpu',
      'NmsDiscoveryServiceCpu',
      'NmsSoftwareServiceCpu',
      'NmsArchiveServiceCpu',
      'NmsCbrsServiceCpu',
      'NmsStatisticsServiceCpu',
      'SqlCpu',
      'WebServerCpu',
    ];

    orderedKeys.forEach((key) => {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        if (
          !_.isNull(data[key as keyof CpuUsageProps]) &&
          !_.isUndefined(data[key as keyof CpuUsageProps]) &&
          !_.isNaN(data[key as keyof CpuUsageProps])
        ) {
          keyValueArray.push({
            key: PascalCaseToSentenceCase(key),
            value: (
              <UtilizationBar percentage={Number(data[key as keyof CpuUsageProps])} usePadding={false} threshold={90} />
            ),
          });
        }
      }
    });

    // Explicitly add the calculated "Idle" (100 - SystemCpu)
    if (data.SystemCpu !== undefined) {
      const idlePercentage = 100 - Number(data.SystemCpu);
      keyValueArray.push({
        key: 'Idle',
        value: <UtilizationBar percentage={idlePercentage} usePadding={false} threshold={10} inverted={true} />,
      });
    }

    return keyValueArray;
  };

  const renderTableData = (data: CpuUsageProps) => {
    const keyValueArray = convertToKeyValueArray(data);
    return <UnstyledTable tableData={keyValueArray} fontSize="sm" rowBorderBottom={true} />;
  };

  return (
    <Flex wrap="wrap" justifyContent="center" width="100%" direction="column" alignItems="flex-start">
      <Box width="100%" mb="6">
        <Text fontSize="sm" mb="2">
          Metrics captured for {details.DurationMinutes} minutes
          <Text>
            {' '}
            ({details.StartTime.toLocaleString().replace('T', ' ')} -{' '}
            {details.EndTime.toLocaleString().replace('T', ' ')})
          </Text>
        </Text>
      </Box>

      <Box width="70%" alignSelf="center" justifyContent="center" alignItems="center">
        {renderTableData(details)}
      </Box>
    </Flex>
  );
};

export default CpuUsageCard;
