import { Box, Card } from '@chakra-ui/react';
import { ErrorBoundary } from 'react-error-boundary';
import { NODE_COMPONENT_TITLES } from '../../../../data/constants';
import { ErrorBoundaryFallback, ErrorBoundaryLogError } from '../../../errorComponents/ErrorBoundaryFallback';
import NodeComponentErrorCard from '../../../errorComponents/NodeComponentErrorCard';
import AcpCard from './AcpCard';

type AcpProps = {
  data: any;
  nodeId: string;
};

const Acp = ({ data, nodeId }: AcpProps) => {
  return (
    <Box width="50%" padding="1rem" data-testid="acp-component">
      {data.error || data.error === null ? (
        <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
          <NodeComponentErrorCard
            id={nodeId}
            errorData={data.error}
            compName={NODE_COMPONENT_TITLES.ACP}
            testId="node-comp-error-card"
            marginSpace="8"
          />
        </ErrorBoundary>
      ) : (
        <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
          <AcpCard data={data} nodeId={nodeId} />
        </ErrorBoundary>
      )}
    </Box>
  );
};

export default Acp;
