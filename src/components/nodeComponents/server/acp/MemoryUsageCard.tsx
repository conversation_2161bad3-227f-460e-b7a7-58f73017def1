import { Box, Icon, CardBody, Flex, Heading, Text, Card, CardHeader } from '@chakra-ui/react';

import { BsArrowReturnRight } from 'react-icons/bs';
import { StaticStatusCircleIcon } from '../../../icons/StatusIcon';
import { StatusConstants, StatusToColor } from '../../../../data/constants';
import { useState } from 'react';

import { getComponentBgColor } from '../../utils';
import { getStatusColor } from '../../../../pages/CellOverview/hooks/useStatus';
import { UtilizationBar } from '../../common/UtilizationBar';
import _ from 'lodash';
import { UnstyledTable } from '../../airspan/utils';
import { convertToBestReadable, PascalCaseToSentenceCase } from '../server/utils';

type MemoryUsageProps = {
  StartTime: Date;
  EndTime: Date;
  DurationMinutes: number;
  TotalSystemMemoryMb: number;
  UsedSystemMemoryMb: number;
  BuffCacheSystemMemoryMb: number;
  FreeSystemMemoryMb: number;
  NmsEventServiceMemoryMb: number;
  NmsProvisioningServiceMemoryMb: number;
  NmsDiscoveryServiceMemoryMb: number;
  NmsSoftwareServiceMemoryMb: number;
  NmsArchiveServiceMemoryMb: number;
  NmsCbrsServiceMemoryMb: number;
  NmsStatisticsServiceMemoryMb: number;
  SqlMemoryMb: number;
  WebServerMemoryMb: number;
};

const MemoryUsageStatusDeterminer = (memoryUsage: number | undefined) => {
  if (memoryUsage === undefined) {
    return StatusConstants.UNKNOWN;
  }

  if (memoryUsage > 99) {
    return StatusConstants.CRITICAL;
  } else if (memoryUsage > 95) {
    return StatusConstants.ERROR;
  } else if (memoryUsage > 80) {
    return StatusConstants.WARNING;
  } else {
    return StatusConstants.OK;
  }
};

export const MemoryUsageCard = ({ details }: { details: MemoryUsageProps }) => {
  const [showContent, setShowContent] = useState(false);

  const memoryStatus = MemoryUsageStatusDeterminer(details.UsedSystemMemoryMb / details.TotalSystemMemoryMb);
  const memoryStatusColor = getStatusColor(memoryStatus);
  const bgColor = getComponentBgColor(memoryStatusColor);

  return (
    <Card width="100%" borderRadius="lg" style={{ marginRight: '1rem' }} overflow="auto" data-testid="services-status">
      <CardHeader
        _hover={{
          bg: bgColor,
          cursor: 'pointer',
          boxShadow: `0 0 12px ${bgColor}`,
        }}
        transition="background-color 0.2s ease, box-shadow 0.2s ease"
        onClick={() => setShowContent(!showContent)}
        display="flex"
        alignItems="center"
        justifyContent="space-between"
        h="60px"
        minH="60px"
      >
        <Heading size="xs" fontWeight="500" display="flex" justifyContent="space-between" width="100%">
          <Flex alignItems="center" width="100%">
            <Icon as={BsArrowReturnRight} />
            <Text ml="2">Memory</Text>
            {details.TotalSystemMemoryMb && (
              <Box flex="1" ml="10" display="flex" flexDirection="row" alignItems="center">
                <Text mr="2">Used</Text>
                <Box flex="1">
                  <UtilizationBar
                    percentage={Number(((details.UsedSystemMemoryMb / details.TotalSystemMemoryMb) * 100).toFixed(2))}
                    usePadding={false}
                    threshold={95}
                  />
                </Box>
              </Box>
            )}
          </Flex>
        </Heading>
        <Box ml="5">
          <StaticStatusCircleIcon size={40} color={StatusToColor[memoryStatus as keyof typeof StatusToColor]} />
        </Box>
      </CardHeader>
      {showContent && (
        <CardBody borderTop={'1px solid #e2e2e2'}>
          <DisplayTable details={details} />
        </CardBody>
      )}
    </Card>
  );
};

const DisplayTable = ({ details }: { details: MemoryUsageProps }) => {
  const convertToKeyValueArray = (data: MemoryUsageProps) => {
    const keyValueArray: { key: string; value: any }[] = [];

    const orderedKeys = Object.keys(data).filter(
      (key) => key !== 'StartTime' && key !== 'EndTime' && key !== 'DurationMinutes'
    );
    // order the keys in descending order of memory usage
    orderedKeys.sort((a, b) => Number(data[b as keyof MemoryUsageProps]) - Number(data[a as keyof MemoryUsageProps]));

    orderedKeys.forEach((key) => {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        if (
          !_.isNull(data[key as keyof MemoryUsageProps]) &&
          !_.isUndefined(data[key as keyof MemoryUsageProps]) &&
          !_.isNaN(data[key as keyof MemoryUsageProps])
        ) {
          let newKey = key;
          if (key.includes('Mb')) {
            newKey = key.replace('Mb', '');
          }
          keyValueArray.push({
            key: PascalCaseToSentenceCase(newKey),
            value: convertToBestReadable(data[key as keyof MemoryUsageProps] + 'MB'),
          });
        }
      }
    });

    // Explicitly add the calculated "Idle" (100 - SystemCpu)
    if (data.TotalSystemMemoryMb !== undefined) {
      const idlePercentage = Number(
        (100 - (Number(data.UsedSystemMemoryMb) / Number(data.TotalSystemMemoryMb)) * 100).toFixed(2)
      );
      keyValueArray.push({
        key: 'Idle',
        value: <UtilizationBar percentage={idlePercentage} usePadding={false} threshold={10} inverted={true} />,
      });
    }

    return keyValueArray;
  };

  const renderTableData = (data: MemoryUsageProps) => {
    const keyValueArray = convertToKeyValueArray(data);
    return <UnstyledTable tableData={keyValueArray} fontSize="sm" rowBorderBottom={true} />;
  };

  return (
    <Flex wrap="wrap" justifyContent="center" width="100%" direction="column" alignItems="flex-start">
      <Box width="100%" mb="6">
        <Text fontSize="sm" mb="2">
          Metrics captured for {details.DurationMinutes} minutes
          <Text>
            {' '}
            ({details.StartTime.toLocaleString().replace('T', ' ')} -{' '}
            {details.EndTime.toLocaleString().replace('T', ' ')})
          </Text>
        </Text>
      </Box>

      <Box width="70%" alignSelf="center" justifyContent="center" alignItems="center">
        {renderTableData(details)}
      </Box>
    </Flex>
  );
};

export default MemoryUsageCard;
