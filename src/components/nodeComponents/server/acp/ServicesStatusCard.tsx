import {
  Icon,
  CardBody,
  Flex,
  <PERSON>ing,
  Text,
  Card,
  CardHeader,
  Tbody,
  Th,
  Tr,
  Thead,
  Table,
  TableContainer,
  Td,
} from '@chakra-ui/react';

import { BsArrowReturnRight } from 'react-icons/bs';
import { StaticStatusCircleIcon } from '../../../icons/StatusIcon';
import { StatusToColor } from '../../../../data/constants';
import { useState } from 'react';
import { MdDirectionsRun, MdHighlightOff, MdBlock, MdCheckCircle } from 'react-icons/md';
import { getComponentBgColor } from '../../utils';
import { getStatusColor } from '../../../../pages/CellOverview/hooks/useStatus';

type ServicesStatusProps = {
  name: string;
  status: string;
  isHealthy: boolean;
};

export const NoRunIcon = () => (
  <span style={{ position: 'relative', display: 'inline-block', lineHeight: 0 }}>
    <MdDirectionsRun size={25} color="teal" />
    <MdBlock
      size={25}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        color: 'rgba(255,0,0,0.6)',
        pointerEvents: 'none',
      }}
    />
  </span>
);

const DisplayTable = ({ services_status }: { services_status: ServicesStatusProps[] }) => {
  const nmsWebStatus = services_status.find((service: ServicesStatusProps) => service.name === 'nms-web');
  if (nmsWebStatus) {
    // TODO: Remove this once ACP fixes the issue
    nmsWebStatus.isHealthy = true;
  }
  return (
    <TableContainer>
      <Table variant="simple" size="sm">
        <Thead>
          <Tr>
            <Th borderBottom="1px" borderColor="gray.200">
              Service
            </Th>
            <Th borderBottom="1px" borderColor="gray.200">
              Status
            </Th>
            <Th borderBottom="1px" borderColor="gray.200">
              Health
            </Th>
          </Tr>
        </Thead>
        <Tbody>
          {services_status.map((service: ServicesStatusProps) => (
            <Tr key={service.name}>
              <Td borderBottom="none" fontWeight="medium" py={1}>
                {service.name}
              </Td>

              <Td borderBottom="none" py={1}>
                {service.status === 'Running' ? <MdDirectionsRun color="teal" size={25} /> : <NoRunIcon />}
              </Td>

              <Td borderBottom="none" py={1}>
                {service.isHealthy ? (
                  <MdCheckCircle color="green" size={25} />
                ) : (
                  <MdHighlightOff color="red" size={25} />
                )}
              </Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </TableContainer>
  );
};

export const ServicesStatusCard = ({ services_status }: { services_status: ServicesStatusProps[] }) => {
  const [showContent, setShowContent] = useState(false);
  const allServicesHealthy = services_status.every((service: ServicesStatusProps) => service.status === 'Running');
  const statusColor = getStatusColor(allServicesHealthy ? 'OK' : 'ERROR');
  const bgColor = getComponentBgColor(statusColor);

  return (
    <Card width="100%" borderRadius="lg" style={{ marginRight: '1rem' }} overflow="auto" data-testid="services-status">
      <CardHeader
        _hover={{
          bg: bgColor,
          cursor: 'pointer',
          boxShadow: `0 0 12px ${bgColor}`,
        }}
        transition="background-color 0.2s ease, box-shadow 0.2s ease"
        onClick={() => setShowContent(!showContent)}
        display="flex"
        alignItems="center"
        justifyContent="space-between"
        h="60px"
        minH="60px"
      >
        <Heading size="xs" fontWeight="500" display="flex" justifyContent="space-between">
          <Flex alignItems="center">
            <Icon as={BsArrowReturnRight} />
            <Text ml={2}>Services Health</Text>
          </Flex>
        </Heading>
        <StaticStatusCircleIcon size={40} color={StatusToColor[allServicesHealthy ? 'OK' : 'ERROR']} />
      </CardHeader>
      {showContent && (
        <CardBody borderTop={'1px solid #e2e2e2'}>
          <DisplayTable services_status={services_status} />
        </CardBody>
      )}
    </Card>
  );
};

export default ServicesStatusCard;
