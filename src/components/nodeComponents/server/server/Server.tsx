import { Box, Card } from '@chakra-ui/react';
import { ErrorBoundary } from 'react-error-boundary';
import { NODE_COMPONENT_TITLES } from '../../../../data/constants';
import { ErrorBoundaryFallback, ErrorBoundaryLogError } from '../../../errorComponents/ErrorBoundaryFallback';
import NodeComponentErrorCard from '../../../errorComponents/NodeComponentErrorCard';
import ServerCard from './ServerCard';

type ServerProps = {
  data: any;
  nodeId: string;
};

const Server = ({ data, nodeId }: ServerProps) => {
  return (
    // <Box width={'50%'} marginRight="4">
    //   <ErrorBoundary
    //     fallbackRender={ErrorBoundaryFallback}
    //     onError={ErrorBoundaryLogError}
    //   >
    //     <ServerCard data={data} nodeId={nodeId} />
    //   </ErrorBoundary>
    // </Box>
    <Box width="50%" padding="1rem">
      {data.error || data.error === null ? (
        <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
          <NodeComponentErrorCard
            id={nodeId}
            // errorData={data.subsystems[0]} // if not sub system, then data
            errorData={data}
            compName={NODE_COMPONENT_TITLES.SERVER}
            testId="node-comp-error-card"
            marginSpace="8"
          />
        </ErrorBoundary>
      ) : (
        <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
          <ServerCard data={data} nodeId={nodeId} />
        </ErrorBoundary>
      )}
    </Box>
  );
};

export default Server;
