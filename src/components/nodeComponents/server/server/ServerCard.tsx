import {
  Badge,
  Button,
  Card,
  CardBody,
  CardHeader,
  Flex,
  Heading,
  Icon,
  ListItem,
  Stack,
  StackDivider,
  Text,
  UnorderedList,
  useDisclosure,
} from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import { NODE_COMPONENT_TITLES, StatusConstants } from '../../../../data/constants';
import { getStatusColor } from '../../../../pages/CellOverview/hooks/useStatus';
import { recursiveSearch } from '../../../../utils/recursiveSearch';
import StatusComponent from '../../../icons/StatusIcon';
import ComponentModal from '../../ComponentModal';
import { BsArrowReturnRight } from 'react-icons/bs';
import { getComponentBgColor } from '../../utils';
import { ComponentTypeEnum } from '../../../../services/types';
import { UnstyledTable } from '../../airspan/utils';
import NtpCard from '../../metrics/NtpCard';
import MemoryCard from '../../metrics/MemoryCard';
import DiskCard from '../../metrics/DiskCard';
import CpuCard from '../../metrics/CpuCard';
import _ from 'lodash';
import { formatDate } from './utils';
import { TimeElapsed } from '../acp/AcpCard';
import PtpCard from '../../metrics/PtpCard';

const ServerCard = ({ data, nodeId }: any) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [showContent, setShowContent] = useState(false);

  // TODO: investigate why Its throwing Maximum call stack size exceeded for some scenarios
  const flattenedData: any = recursiveSearch(data);
  const statusColor = getStatusColor(data.status);
  const bgColor = getComponentBgColor(statusColor);
  const [lastContactedBadgeColor, setLastContactedBadgeColor] = useState('green');

  const ntpData = data.subsystems.find((subsystem: any) => subsystem.name === 'ntp');
  const ptpData = data.subsystems.find((subsystem: any) => subsystem.name === 'ptp');

  const diskData = data.subsystems.find((subsystem: any) => subsystem.name === 'disk');
  const cpuData = data.subsystems.find((subsystem: any) => subsystem.name === 'cpu');
  const memoryData = data.subsystems.find((subsystem: any) => subsystem.name === 'memory');
  const connectivity =
    !_.isNull(data['last_contacted']) && data['last_contacted'] === data['last_attempted'] ? true : false;

  useEffect(() => {
    if (data['last_contacted']) {
      const initialUptime = new Date(data['last_contacted']);
      const currentTime = new Date();
      const timeDifference = (currentTime.getTime() - initialUptime.getTime()) / 1000 / 60; // time difference in minutes

      if (timeDifference > 30) {
        setLastContactedBadgeColor('red');
      } else {
        setLastContactedBadgeColor('green');
      }
    } else {
      setLastContactedBadgeColor('red'); // Default to red if last_contacted is null
    }
  }, [data['last_contacted']]);

  const RenderErrorDetails = ({ data }: { data: any }) => {
    const errorDetails: { [key: string]: JSX.Element } = {};

    if (data.reason) {
      errorDetails['Reason'] = (
        <Text fontWeight="bold" fontSize="sm" whiteSpace="initial" color="red.500" mr="4">
          {data.reason}
        </Text>
      );
    }

    if (data.cause) {
      errorDetails['Cause'] = (
        <Text fontWeight="bold" wordBreak="break-word" whiteSpace="normal" color="red.500">
          {data.cause}
        </Text>
      );
    }

    if (data.repairs && data.repairs.filter((repair: string) => repair !== '').length > 0) {
      errorDetails['Repair'] =
        data.repairs.length > 1 ? (
          <UnorderedList ml="4">
            {data.repairs.map((repair: any) =>
              _.isEmpty(repair) ? null : (
                <ListItem pl="2" pr="2" whiteSpace="initial" key={repair}>
                  <Text ml="2" mb="2" wordBreak="break-word" whiteSpace="normal">
                    {repair}
                  </Text>
                </ListItem>
              )
            )}
          </UnorderedList>
        ) : (
          <Text ml="2" wordBreak="break-word" whiteSpace="normal">
            {data.repairs[0]}
          </Text>
        );
    }

    return (
      <Flex wrap="wrap" justifyContent="space-around">
        <UnstyledTable tableData={errorDetails} firstColWidth="30%" />
      </Flex>
    );
  };

  return (
    <>
      <Card width="100%" borderRadius="lg">
        <CardHeader
          onClick={() => setShowContent(!showContent)}
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          _hover={{
            bg: bgColor,
            cursor: 'pointer',
            boxShadow: `0 0 12px ${bgColor}`,
          }}
          transition="background-color 0.2s ease, box-shadow 0.2s ease"
          borderRadius="lg"
          data-testid="server-card-header"
        >
          <Heading size="xs" fontWeight="500" display="flex" justifyContent="space-between">
            <Flex alignItems="center">
              <Icon as={BsArrowReturnRight} />
              <Text ml="2"> {NODE_COMPONENT_TITLES.SERVER}</Text>
            </Flex>
          </Heading>
          <StatusComponent
            dataTestId="cell-main-table-status-icon"
            status={data.status}
            boxSize="sm"
            color={statusColor}
            component_type={ComponentTypeEnum.SERVER}
            component_id={data.server_id}
          />
        </CardHeader>
        {showContent && (
          <CardBody borderTop={'1px solid #e2e2e2'}>
            <Stack divider={<StackDivider />} spacing="4">
              <UnstyledTable
                tableData={[
                  { key: 'IP Address', value: <Badge colorScheme="gray"> {data.ip_address} </Badge> },
                  { key: 'Name', value: <Badge colorScheme="gray"> {data.name} </Badge> },
                  {
                    key: 'Connectivity',
                    value: connectivity,
                  },
                  { key: 'Last Attempted', value: formatDate(data['last_attempted']) },
                ]}
                firstColWidth="30%"
              />

              {data.status === StatusConstants.ERROR && <RenderErrorDetails data={data} />}

              {data.subsystems.length > 0 ? (
                <Flex gap="4" flexDirection={'column'}>
                  {_.isNull(data['last_contacted']) ? null : (
                    <Flex alignItems="center" wordBreak="break-word" whiteSpace="normal" mt="4">
                      <Text mr="1">Below details captured </Text>
                      <Badge colorScheme={lastContactedBadgeColor}>
                        {/* updated_at is the last time the details were captured and it is same for all subsystems */}
                        <TimeElapsed initialUptime={formatDate(diskData['updated_at'])} fontSize="lg" />
                      </Badge>
                      <Text ml="1">ago</Text>
                    </Flex>
                  )}

                  {ntpData && <NtpCard ntpData={ntpData} />}
                  {ptpData &&
                    (ptpData.status === 'UNKNOWN' && ptpData.reason === 'cluster is not available' ? null : (
                      <PtpCard ptpData={ptpData} />
                    ))}
                  {diskData && <DiskCard diskData={diskData} />}
                  {memoryData && <MemoryCard memoryData={memoryData} />}
                  {cpuData && <CpuCard cpuData={cpuData} />}
                </Flex>
              ) : null}

              {/* <Link
                textAlign="center"
                isExternal
                rel="noopener noreferrer"
                href={`/nms/grafana/login`}
              >
                Grafana Dashboard
                <ExternalLinkIcon mx={'2px'} />
              </Link> */}
              <Button onClick={onOpen}>More Data</Button>
            </Stack>
          </CardBody>
        )}
      </Card>
      <ComponentModal isOpen={isOpen} onClose={onClose} flattenedData={flattenedData} />
    </>
  );
};

export default ServerCard;
