export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

export const parseSize = (size: string): number => {
  const units: { [key: string]: number } = { KB: 1, MB: 1024, GB: 1024 * 1024, TB: 1024 * 1024 * 1024 };
  const match = size.match(/([\d.]+)([KMGTP]?B)/);
  if (match) {
    const value = parseFloat(match[1]);
    const unit = match[2] as keyof typeof units;
    return value * (units[unit] || 1);
  }
  return 0;
};

export const PascalCaseToSentenceCase = (str: string) => {
  return str.replace(/([A-Z])/g, ' $1').replace(/^./, function (str) {
    return str.toUpperCase();
  });
};

/**
 * Converts any byte size (large or small) into the most readable format.
 * - Large: "15000GB" → "14.65TB"
 * - Small: "0.004GB" → "4.1MB"
 *
 * @param {string} sizeString The input size string (e.g., "15000GB", "0.004GB").
 * @returns {string} The formatted size in the best unit (B, KB, MB, GB, TB, etc.).
 */
export function convertToBestReadable(sizeString: string): string {
  if (typeof sizeString !== 'string' || sizeString.trim() === '') {
    return 'Invalid input';
  }

  // Regex to extract number and unit (KB, MB, GB, etc., case-insensitive)
  const match = sizeString.trim().match(/^(\d*\.?\d+)\s*([KMGTPEZY]?B)$/i);

  if (!match) {
    return 'Invalid format (e.g., "10.5GB")';
  }

  let value = parseFloat(match[1]);
  let unit = match[2].toUpperCase();

  const units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  let unitIndex = units.indexOf(unit);

  if (unitIndex === -1) {
    return 'Invalid unit';
  }

  // Convert UP for large values (>= 1024)
  while (value >= 1024 && unitIndex < units.length - 1) {
    value /= 1024;
    unitIndex++;
    unit = units[unitIndex];
  }

  // Convert DOWN for small values (< 1)
  while (value < 1 && unitIndex > 0) {
    value *= 1024;
    unitIndex--;
    unit = units[unitIndex];
  }

  // Round to 1 decimal place, but remove ".0" for whole numbers
  const roundedValue = Math.round(value * 10) / 10;
  const formattedValue = roundedValue % 1 === 0 ? roundedValue.toFixed(0) : roundedValue.toFixed(1);

  return `${formattedValue}${unit}`;
}
