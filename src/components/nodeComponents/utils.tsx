import {
  TRAFFIC_ERROR_RED,
  TRAFFIC_OK_GREEN,
  TRAFFIC_SHUTDOWN_BLACK,
  TRAFFIC_UNKNOWN_GRAY,
  TRAFFIC_WARNING_ORANGE,
} from '../../data/constants';

export const getComponentBgColor = (statusColor: string) => {
  if (statusColor === TRAFFIC_OK_GREEN) {
    return 'rgba(56, 161, 105, 0.6)';
  } else if (statusColor === TRAFFIC_ERROR_RED) {
    return 'rgba(255, 0, 0, 0.4)';
  } else if (statusColor === TRAFFIC_SHUTDOWN_BLACK) {
    return 'rgba(0, 0 , 0, 0.4)';
  } else if (statusColor === TRAFFIC_UNKNOWN_GRAY) {
    return `${statusColor?.split('.')[0]}.200`;
  } else if (statusColor === TRAFFIC_WARNING_ORANGE) {
    return 'rgba(255, 165, 0, 0.4)';
  } else {
    return `${statusColor?.split('.')[0]}.200`;
  }
};
