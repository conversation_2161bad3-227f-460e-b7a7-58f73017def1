// StreetCellConfigModal.test.tsx
import { expect, vi } from 'vitest';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import NodeIpAddressModal from '../NodeIpAddressModal';

type ChildrenProps = {
  children: React.ReactNode;
};

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});
const wrapper = ({ children }: ChildrenProps) => (
  <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
);

describe('NodeIpAddressModal', () => {
  it('renders and fetches data correctly', async () => {
    render(<NodeIpAddressModal isOpen={true} nodeId="GBMARLNS000001" onClose={vi.fn()} onSubmit={vi.fn()} />, {
      wrapper,
    });
    // read the input field values]
    const bluwirelessInput = screen.getByLabelText('BluWireless');
    const fibrolanInput = screen.getByLabelText('FibroLAN');
    // Assert the input fields are empty
    expect(bluwirelessInput).toHaveValue('');
    expect(fibrolanInput).toHaveValue('');
    // Assert the input fields are populated with the correct values

    await waitFor(() => {
      const bluwireless = screen.getByTestId('bluwireless_ip_address') as HTMLInputElement;

      // expect(bluwireless.value).toEqual('***************');
    });

    await waitFor(() => {
      const fibrolan = screen.getByTestId('fibrolan_ip_address') as HTMLInputElement;
      // expect(fibrolan.value).toEqual('***************');
    });
  });

  it('submits the form correctly', async () => {
    const onSubmitMock = vi.fn().mockImplementation((data) => {
      return data;
    });

    render(<NodeIpAddressModal isOpen={true} nodeId="GBMARLNS000001" onClose={vi.fn()} onSubmit={onSubmitMock} />, {
      wrapper,
    });
    await waitFor(() => expect(screen.getAllByPlaceholderText('***************')));
    await waitFor(() => {
      expect(screen.getByLabelText('FibroLAN')).toBeInTheDocument();
    });
    await waitFor(() => {
      expect(screen.getByLabelText('BluWireless')).toBeInTheDocument();
    });
    // Update the value for the bluwireless input field
    const bluwirelessInput = screen.getByLabelText('BluWireless');
    fireEvent.change(bluwirelessInput, { target: { value: '***********' } });
    const fibrolanInput = screen.getByLabelText('FibroLAN');
    fireEvent.change(fibrolanInput, { target: { value: '***********' } });

    // Click the Confirm button to submit the form
    const submitButton = screen.getByText('Confirm');
    fireEvent.click(submitButton);
    await waitFor(() => {
      if (onSubmitMock.mock.calls.length > 0) {
        expect(onSubmitMock.mock.calls[0][0]).toEqual(
          expect.objectContaining({
            bluwireless_ip_address: '***********',
            fibrolan_ip_address: '***********',
          })
        );
      }
    });
  });
});
