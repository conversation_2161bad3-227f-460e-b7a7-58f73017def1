import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Row } from '@tanstack/react-table';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { ReactNode } from 'react';
import { MemoryRouter } from 'react-router-dom';
import { describe, it } from 'vitest';
import CellConfigMenu from '../../../../pages/CellOverview/CellConfigMenu';
import CellNodes from '../../../../pages/CellOverview/CellNodes';
import { getStatusColor } from '../../../../pages/CellOverview/hooks/useStatus';
import { DataTable } from '../../../../pages/MetricsCollector/components/DataTable';
import { getCellListMock } from '../../../../services/mocks/getCellListMock';
import StatusComponent from '../../../icons/StatusIcon';
//import { row } from '../row.json';

type ChildrenProps = {
  children: ReactNode;
};

// Wrapper for react query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});

const row = {
  cell_ref: 'GBMARLNS000001',
  oran_split: 'oran',
  site_name: 'GBMARLNS000001',
  orientation: 'orientation',
  region_name: 'region_name',
  country_code: 'country_code',
  lifecycle: 'lifecycle',
  status: 'status',
  row: {
    getIsExpanded: () => true,
    original: {
      cell_ref: 'some id',
    },
  },
};

const wrapper = ({ children }: ChildrenProps) => (
  <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
);

const columns = [
  {
    header: 'Cell Reference',
    accessorKey: 'cell_ref',
    id: 'cell_ref',
  },
  {
    header: 'Cell Type',
    accessorKey: 'oran_split',
    id: 'oran_split',
    filterFn: (row: Row<any>, id: string, value: string) => value.includes(row.getValue(id)),
  },
  {
    header: 'Site name',
    accessorKey: 'site_name',
    id: 'site_name',
    filterFn: (row: Row<any>, id: string, value: string) => value.includes(row.getValue(id)),
  },
  {
    header: 'Orientation',
    accessorKey: 'orientation',
    id: 'orientation',
    filterFn: (row: Row<any>, id: string, value: string) => value.includes(row.getValue(id)),
  },
  {
    header: 'Region Name',
    accessorKey: 'region_name',
    id: 'region_name',
    filterFn: (row: Row<any>, id: string, value: string) => value.includes(row.getValue(id)),
  },
  {
    header: 'Country code',
    accessorKey: 'country_code',
    id: 'country_code',
    filterFn: (row: Row<any>, id: string, value: string) => value.includes(row.getValue(id)),
  },
  {
    header: 'Lifecycle',
    accessorKey: 'lifecycle',
    id: 'lifecycle',
    filterFn: (row: Row<any>, id: string, value: string) => value.includes(row.getValue(id)),
  },
  {
    header: 'Status',
    accessorKey: 'status',
    id: 'status',
    cell: () => {
      const cellStatus = 'COMMISSIONING';
      const cell_ref = 'GBMLBKNS000001';
      return (
        <>
          <StatusComponent
            dataTestId="cell-main-table-status-icon"
            boxSize="sm"
            color={getStatusColor(cellStatus)}
            status={cellStatus}
          />{' '}
          <CellConfigMenu cellStatus={cellStatus} cell_ref={cell_ref} />
        </>
      );
    },
  },
];

// Debugging
// screen.debug();
// //This one in the function and before the issue
// screen.logTestingPlaygroundURL();

// TODO: need to rewrite this test as when opening node, we have to mock all the component that we need
describe('show content for a node components', () => {
  it('Show loading component', () => {
    render(
      <MemoryRouter>
        {/* <CellNodes
          nodeId="GBMARLNS000001"
          cellRef="GBMARLNS000001"
          isComponentOpen={true}
          checkForDevMode={false}
        /> */}
        <CellNodes row={row} />
      </MemoryRouter>,
      { wrapper }
    );
    expect(screen.getByText(/loading.../i));
  });

  // it('Show node', async () => {
  //   render(
  //     <MemoryRouter>
  //       {/* <DataTable
  //         isExpandable={true}
  //         columns={columns ?? []}
  //         data={getCellListMock}
  //         defaultPageSize={50}
  //         enableFilter={true}
  //       /> */}
  //       <CellNodes row={row} view="cell" handleTabsChange={handleTabsChange} />
  //     </MemoryRouter>,
  //     { wrapper }
  //   );
  //   await waitFor(() => {
  //     const node = screen.getByRole('cell', { name: /GBMARLNS000001/i });
  //   });

  //   //await screen.findByText(/MMWave/i);
  //   //click on node and find components below
  //   //fireEvent.click(node);
  //   // await waitFor(() => {
  //   //   screen.getByRole('cell', { name: /gbmlbkns000005/i });
  //   //   screen.getByRole('heading', {
  //   //     name: /5G CU User plane/i,
  //   //   });
  //   //   screen.getByRole('heading', {
  //   //     name: /5G DU/i,
  //   //   });
  //   //   screen.getByRole('heading', {
  //   //     name: /5G RU/i,
  //   //   });
  //   //   screen.getByRole('heading', {
  //   //     name: /MMWave/i,
  //   //   });
  //   //   screen.getByRole('heading', {
  //   //     name: /Controller board/i,
  //   //   });
  //   // });
  // });

  // it.skip('click on node comp to open it', async () => {
  //   render(
  //     <MemoryRouter>
  //       <DataTable isExpandable={true} columns={columns ?? []} data={getCellListMock} defaultPageSize={50} />
  //     </MemoryRouter>,
  //     { wrapper }
  //   );
  //   const nodeComp = screen.getByRole('heading', {
  //     name: /cu control plane/i,
  //   });
  //   fireEvent.click(nodeComp);
  //   await screen.findByText(/View in ACP/i);
  // });
});

function handleTabsChange(index: number, nodeId: string): void {
  console.log('handleTabsChange');
}
