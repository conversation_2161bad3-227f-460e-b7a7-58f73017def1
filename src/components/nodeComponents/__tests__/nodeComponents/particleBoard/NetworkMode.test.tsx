import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { fireEvent, render, screen } from '@testing-library/react';
import { ReactNode } from 'react';
import { describe, expect, it, vi } from 'vitest';
import { decodedJwtObject, encodedJwtString } from '../../../../../hooks/__tests__/useLogin.test';
import useLogin from '../../../../../hooks/useLogin';
import NetworkMode from '../../../particleboard/NetworkMode';

type ChildrenProps = {
  children: ReactNode;
};

// Wrapper for react query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});
const wrapper = ({ children }: ChildrenProps) => (
  <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
);

// Debugging
// screen.debug();
// //This one in the function and before the issue
// screen.logTestingPlaygroundURL();

describe('NetworkMode Component', () => {
  const mockNetworkModeData = {
    DHCP: false,
    IP: '***************',
    SUB: '**************',
    GW: '***************',
    DNS: '0.0.0.0',
  };
  const mockNodeId = 'AABA31700001';
  const mockSetApiState = vi.fn();

  //Make sure JWT in present in local storage
  beforeEach(async () => {
    localStorage.setItem('dw_authToken', encodedJwtString);
  });

  it('Switch to static', async () => {
    render(
      <NetworkMode
        networkModeData={mockNetworkModeData}
        nodeId={mockNodeId}
        setApiState={mockSetApiState}
        checkRoleAccess={true}
      />,
      { wrapper }
    );
    const staticRadioButton = screen.getAllByText(/Static/i);
    fireEvent.click(staticRadioButton[1]);
    expect(screen.getByText(/ip address:/i));
    expect(screen.getByText(/Subnet:/i));
    expect(screen.getByText(/DNS:/i));
    expect(screen.getByText(/Gateway:/i));
  });

  it('Switch to dhcp', async () => {
    render(
      <NetworkMode
        networkModeData={mockNetworkModeData}
        nodeId={mockNodeId}
        setApiState={mockSetApiState}
        checkRoleAccess={true}
      />,
      { wrapper }
    );

    const staticRadioButton = screen.getAllByText(/Static/i);
    const dhcpRadioButton = screen.getAllByText(/DHCP/i);
    fireEvent.click(staticRadioButton[1]);

    const ipAddress = screen.getByText(/ip address:/i);
    const subnet = screen.getByText(/Subnet:/i);
    const dns = screen.getByText(/DNS:/i);
    const gateway = screen.getByText(/Gateway:/i);

    expect(ipAddress);
    expect(subnet);
    expect(dns);
    expect(gateway);
    expect(screen.getByRole('button', { name: /apply static ip/i }));
    fireEvent.click(dhcpRadioButton[1]);
    expect(screen.getByRole('button', { name: /Apply DHCP mode/i }));
  });

  it('submit Static form correctly', async () => {
    render(
      <NetworkMode
        networkModeData={mockNetworkModeData}
        nodeId={mockNodeId}
        setApiState={mockSetApiState}
        checkRoleAccess={true}
      />,
      { wrapper }
    );
    const staticRadioButton = screen.getAllByText(/Static/i);
    fireEvent.click(staticRadioButton[1]);

    const ipAddressInput = screen.getByRole('textbox', {
      name: /ip address:/i,
    });
    const subnetInput = screen.getByRole('textbox', { name: /subnet:/i });
    const dnsInput = screen.getByRole('textbox', { name: /dns:/i });
    const gatewayInput = screen.getByRole('textbox', { name: /gateway:/i });

    fireEvent.change(ipAddressInput, { target: { value: '***********' } });
    fireEvent.change(subnetInput, { target: { value: '***********' } });
    fireEvent.change(dnsInput, { target: { value: '***********' } });
    fireEvent.change(gatewayInput, { target: { value: '***********' } });
    fireEvent.click(screen.getByRole('button', { name: /apply static ip/i }));
  });
});

// describe('show content for successful get request ', () => {
//   it('Render components details', async () => {
//     return true;
//   });
// });

// describe('show content for error in get request ', () => {
//   it('Render components details', async () => {
//     return true;
//   });
// });
