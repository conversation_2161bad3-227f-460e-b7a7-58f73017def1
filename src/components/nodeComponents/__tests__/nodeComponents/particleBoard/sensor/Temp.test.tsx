import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { render, screen } from '@testing-library/react';
import { ReactNode } from 'react';
import { describe, it, vi } from 'vitest';
import { encodedJwtString } from '../../../../../../hooks/__tests__/useLogin.test';
import Temp from '../../../../particleboard/sensor/Temp';

type ChildrenProps = {
  children: ReactNode;
};

// Wrapper for react query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});
const wrapper = ({ children }: ChildrenProps) => (
  <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
);

export const mockSetApiState = vi.fn();

// Debugging
// screen.debug();
// //This one in the function and before the issue
// screen.logTestingPlaygroundURL();

describe('show content for successful get request ', () => {
  beforeEach(async () => {
    localStorage.setItem('dw_authToken', encodedJwtString);
  });
  it('Render components details', async () => {
    render(<Temp nodeId="AABA35300403" overtemp={40} hysteresis={56} setApiState={mockSetApiState} />, {
      wrapper,
    });
    expect(screen.getAllByText(/overtemp/i));
    expect(screen.getAllByText(/hysteresis/i));
    expect(screen.getByText(/ot/i));
    expect(screen.getAllByText(/hy/i));
    expect(
      screen.getByRole('button', {
        name: /apply mode/i,
      })
    );
  });
});
