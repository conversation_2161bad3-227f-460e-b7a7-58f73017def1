import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { ReactNode } from 'react';
import { describe, expect, it } from 'vitest';
import NodeComponentErrorCard from '../../../../errorComponents/NodeComponentErrorCard';
import ParticleBoardCard from '../../../particleboard/ParticleBoardCard';
import { errorData, successData } from '../mockData/MockData';

type ChildrenProps = {
  children: ReactNode;
};

// Wrapper for react query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});
const wrapper = ({ children }: ChildrenProps) => (
  <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
);

describe('show content for successful get request ', () => {
  it('Render components details: Component id', async () => {
    render(
      <ParticleBoardCard
        particleData={successData.particle_board}
        cellRef={'GBR-MRLW-0001-A'}
        nodeId={'AABA31700001'}
      />,
      { wrapper }
    );
    const particleBoard = screen.getByRole('heading', {
      name: /Controller board/i,
    });
    fireEvent.click(particleBoard);
    await waitFor(() => expect(screen.getByText(/Component id/i)));
  });

  it('Render components details: Software version', async () => {
    render(
      <ParticleBoardCard
        particleData={successData.particle_board}
        nodeId={'AABA31700001'}
        cellRef={'GBR-MRLW-0001-A'}
      />,
      { wrapper }
    );
    const particleBoard = screen.getByRole('heading', {
      name: /Controller board/i,
    });
    fireEvent.click(particleBoard);
    expect(screen.getByText(/Software version/i));
  });

  it('Render components details: Current system mode', async () => {
    render(
      <ParticleBoardCard
        particleData={successData.particle_board}
        nodeId={'AABA31700001'}
        cellRef={'GBR-MRLW-0001-A'}
      />,
      { wrapper }
    );
    const particleBoard = screen.getByRole('heading', {
      name: /Controller board/i,
    });
    fireEvent.click(particleBoard);
    await waitFor(() => expect(screen.getByText(/Current system mode/i)));
  });
});

describe('show content for error in get request ', () => {
  it('Render error components details', async () => {
    render(
      <NodeComponentErrorCard
        id="1"
        errorData={errorData.airspan_node}
        compName="Controller board"
        testId="cell-comp-particle-board-card"
        linkText=""
        linkUrl=""
      />,
      {
        wrapper,
      }
    );
    const particleBoard = screen.getByRole('heading', {
      name: /Controller board/i,
    });
    fireEvent.click(particleBoard);
    await waitFor(() => expect(screen.getByText(/Error Code :/i)));
    await waitFor(() => expect(screen.getByText(/Reason :/i)));
  });
});
