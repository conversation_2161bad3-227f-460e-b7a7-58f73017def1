import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { render, screen } from '@testing-library/react';
import { ReactNode } from 'react';
import { describe, it, vi } from 'vitest';
import { encodedJwtString } from '../../../../../../hooks/__tests__/useLogin.test';
import System from '../../../../particleboard/mode/System';

type ChildrenProps = {
  children: ReactNode;
};

// Wrapper for react query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});
const wrapper = ({ children }: ChildrenProps) => (
  <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
);

export const mockSetApiState = vi.fn();

// Debugging
// screen.debug();
// //This one in the function and before the issue
// screen.logTestingPlaygroundURL();

describe('show content for successful get request ', () => {
  beforeEach(async () => {
    localStorage.setItem('dw_authToken', encodedJwtString);
  });
  it('Render components details', async () => {
    render(<System nodeId="AABA35300403" systemModeData="NORMAL_MODE" setApiState={mockSetApiState} />, {
      wrapper,
    });

    expect(screen.getAllByText(/system mode/i));
    expect(screen.getByText(/For R&D use only. Set the device mode to normal mode, test mode, or error mode./i));
    expect(screen.getAllByText(/normal mode/i));
    expect(screen.getAllByText(/test mode/i));
    expect(screen.getAllByText(/error mode/i));
    expect(screen.getByRole('button', { name: /apply mode/i }));
  });
});
