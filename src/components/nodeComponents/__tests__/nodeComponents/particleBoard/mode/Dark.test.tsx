import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { render, screen } from '@testing-library/react';
import { ReactNode } from 'react';
import { describe, it, vi } from 'vitest';
import { encodedJwtString } from '../../../../../../hooks/__tests__/useLogin.test';
import Dark, { DarkProps } from '../../../../particleboard/mode/Dark';

type ChildrenProps = {
  children: ReactNode;
};

// Wrapper for react query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});
const wrapper = ({ children }: ChildrenProps) => (
  <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
);

export const mockSetApiState = vi.fn();

describe('show content for successful get request ', () => {
  beforeEach(async () => {
    localStorage.setItem('dw_authToken', encodedJwtString);
  });
  it('Render components details', async () => {
    const props: DarkProps = {
      nodeId: 'AABA35300403',
      darkModeData: true,
      setApiState: mockSetApiState,
    };
    render(<Dark {...props} />, {
      wrapper,
    });
    expect(screen.getAllByText(/Dark mode/i));
    expect(
      screen.getByText(
        /Set the device to dark mode. When dark mode is on, the LED remains off unless the shroud is removed./i
      )
    );
    expect(screen.getAllByText(/on/i));
    expect(screen.getByRole('button', { name: /apply mode/i }));
  });
});
