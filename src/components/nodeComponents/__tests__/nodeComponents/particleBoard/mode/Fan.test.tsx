import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { render, screen } from '@testing-library/react';
import { ReactNode } from 'react';
import { describe, it, vi } from 'vitest';
import { encodedJwtString } from '../../../../../../hooks/__tests__/useLogin.test';
import Fan from '../../../../particleboard/mode/Fan';

type ChildrenProps = {
  children: ReactNode;
};

// Wrapper for react query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});
const wrapper = ({ children }: ChildrenProps) => (
  <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
);

export const mockSetApiState = vi.fn();

// Debugging
// screen.debug();
// //This one in the function and before the issue
// screen.logTestingPlaygroundURL();

describe('show content for successful get request ', () => {
  beforeEach(async () => {
    localStorage.setItem('dw_authToken', encodedJwtString);
  });
  it('Render components details', async () => {
    render(<Fan nodeId="AABA35300403" fanModeData="PID" setApiState={mockSetApiState} />, {
      wrapper,
    });
    expect(screen.getAllByText(/fan mode/i));
    expect(
      screen.getByText(
        /Set the fan mode. The fan is running in “temperature-driven” mode through a PID control algorithm by default. A linear control algorithm can also be chosen or it can be switched to “full on” mode./i
      )
    );
    expect(screen.getAllByText(/pid/i));
    expect(screen.getAllByText(/linear/i));
    expect(screen.getAllByText(/on/i));
    expect(screen.getByRole('button', { name: /apply mode/i }));
  });
});
