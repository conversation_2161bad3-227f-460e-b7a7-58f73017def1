import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { ReactNode } from 'react';
import { describe, it, vi } from 'vitest';
import { encodedJwtString } from '../../../../../hooks/__tests__/useLogin.test';
import { antennaPatternMock } from '../../../../../services/mocks/antennaPatternMock';
import NodeComponentErrorCard from '../../../../errorComponents/NodeComponentErrorCard';
import AntennaPattern from '../../../particleboard/AntennaPattern';
import { errorData } from '../mockData/MockData';

type ChildrenProps = {
  children: ReactNode;
};

// Wrapper for react query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});
const wrapper = ({ children }: ChildrenProps) => (
  <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
);

export const mockSetApiState = vi.fn();

// Debugging
// screen.debug();
// //This one in the function and before the issue
// screen.logTestingPlaygroundURL();

describe('show content for successful get request ', () => {
  beforeEach(async () => {
    localStorage.setItem('dw_authToken', encodedJwtString);
  });

  it('Render components details', async () => {
    render(
      <AntennaPattern antennaPatternData={antennaPatternMock} nodeId="AABA35300403" setApiState={mockSetApiState} />,
      { wrapper }
    );

    expect(screen.getByText(/configuration id/i));
    expect(screen.getAllByText(/Antenna Mode/i));
    expect(screen.getByRole('combobox'));
    expect(
      screen.getByText(
        /Control the switched-beam antenna configuration. StreetCell V1.1 has 12 antenna configuration patterns./i
      )
    );
    expect(
      screen.getByRole('img', {
        name: /antenna pattern config 6/i,
      })
    );
  });
});

describe('show content for error in get request ', () => {
  it('Render error components details', async () => {
    render(
      <NodeComponentErrorCard
        id="1"
        errorData={errorData.particle_board}
        compName="Controller board"
        testId="cell-comp-particle-board-card"
        linkText=""
        linkUrl=""
      />,
      {
        wrapper,
      }
    );

    const particleBoard = screen.getByRole('heading', {
      name: /Controller board/i,
    });
    fireEvent.click(particleBoard);
    await waitFor(() => expect(screen.getByText(/Error Code :/i)));
    await waitFor(() => expect(screen.getByText(/Reason :/i)));
  });
});
