import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { ReactNode } from 'react';
import { describe, expect, it } from 'vitest';
import NodeComponentErrorCard from '../../../../errorComponents/NodeComponentErrorCard';
import FibroLanSwitch from '../../../switch/FibroLanSwitch';

import { errorData, successData } from '../mockData/MockData';

type ChildrenProps = {
  children: ReactNode;
};

// Wrapper for react query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});
const wrapper = ({ children }: ChildrenProps) => (
  <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
);

describe('show content for successful get request ', () => {
  it('Render components details', async () => {
    render(
      <FibroLanSwitch
        data={successData.sc_v1.fibrolan_switch}
        id={'test'}
        // node={successData.sc_v1.airspan_node.nodeProperties}
      />,
      { wrapper }
    );
    const fibrolan = screen.getByRole('heading', {
      name: /Switch/i,
    });
    fireEvent.click(fibrolan);
    await waitFor(() => expect(screen.getByText(/7.2/i)));
  });

  it('Render components details', async () => {
    render(
      <FibroLanSwitch
        data={successData.sc_v1.fibrolan_switch}
        // node={successData.sc_v1.airspan_node.nodeProperties}
      />,
      { wrapper }
    );
    const fibrolan = screen.getByRole('heading', {
      name: /Switch/i,
    });
    fireEvent.click(fibrolan);
    screen.getByText(/Firmware version/i);
  });
});

describe('show content for error in get request ', () => {
  it('Render error components details', async () => {
    render(
      <NodeComponentErrorCard
        id="1"
        errorData={errorData.airspan_node}
        compName="Fibrolan"
        testId="cell-comp-fibrolan-card"
        linkText=""
        linkUrl=""
      />,
      {
        wrapper,
      }
    );
    const fibrolan = screen.getByRole('heading', {
      name: /Fibrolan/i,
    });
    fireEvent.click(fibrolan);
    await waitFor(() => expect(screen.getByText(/Error Code :/i)));
    await waitFor(() => expect(screen.getByText(/Reason :/i)));
  });
});
