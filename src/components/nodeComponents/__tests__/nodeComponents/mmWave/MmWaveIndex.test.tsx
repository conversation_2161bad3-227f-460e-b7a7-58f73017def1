import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { ReactNode } from 'react';
import { describe, it } from 'vitest';
import NodeComponentErrorCard from '../../../../errorComponents/NodeComponentErrorCard';
import MmWaveIndex from '../../../mmwave/MmWaveCard';
import { errorData, successData } from '../mockData/MockData';

type ChildrenProps = {
  children: ReactNode;
};

// Wrapper for react query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});
const wrapper = ({ children }: ChildrenProps) => (
  <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
);

describe('show content for successful get request ', () => {
  it('Render components details', async () => {
    render(
      <MmWaveIndex
        data={successData.sc_v1.bw_mmwave}
        bluwirelessInstanceAddress={'test'}

        // node={successData.sc_v1.airspan_node.nodeProperties}
      />,
      { wrapper }
    );
    const mmwave = screen.getByRole('heading', {
      name: /MmWave/i,
    });
    fireEvent.click(mmwave);
    await waitFor(() => expect(screen.getByText(/7.1/i)));
  });
});

describe('show content for error in get request ', () => {
  it('Render error components details', async () => {
    render(
      <NodeComponentErrorCard
        id="1"
        errorData={errorData.bw_mmwave}
        compName="MmWave"
        testId="cell-comp-bluwireless-card"
        linkText="BluWireless config server"
        linkUrl="http://***************/devices/"
      />,
      {
        wrapper,
      }
    );
    const mmwave = screen.getByRole('heading', {
      name: /MmWave/i,
    });
    fireEvent.click(mmwave);
    await waitFor(() => expect(screen.getByText(/Error Code :/i)));
    await waitFor(() => expect(screen.getByText(/Reason :/i)));
  });
});
