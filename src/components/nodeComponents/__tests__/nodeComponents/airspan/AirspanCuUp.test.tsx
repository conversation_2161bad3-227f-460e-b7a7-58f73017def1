import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { ReactNode } from 'react';
import { describe, expect, it } from 'vitest';
import NodeComponentErrorCard from '../../../../errorComponents/NodeComponentErrorCard';
import AirspanCuUpCard from '../../../airspan/userPlane/AirspanCuUpCard';

import { errorData, successData } from '../mockData/MockData';

type ChildrenProps = {
  children: ReactNode;
};

// Wrapper for react query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});
const wrapper = ({ children }: ChildrenProps) => (
  <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
);

describe('show content for successful get request ', () => {
  it('Render components details', async () => {
    render(
      <AirspanCuUpCard
        data={successData.sc_v1.airspan_node.gnbCuUp}
        node={successData.sc_v1.airspan_node.nodeProperties}
      />,
      { wrapper }
    );
    const airspanCuUp = screen.getByRole('heading', {
      name: /CU User plane/i,
    });
    fireEvent.click(airspanCuUp);
    await waitFor(() => expect(screen.getByText(/component id:/i)));
  });

  it('Render components details', async () => {
    render(
      <AirspanCuUpCard
        data={successData.sc_v1.airspan_node.gnbCuUp}
        node={successData.sc_v1.airspan_node.nodeProperties}
      />,
      { wrapper }
    );
    const airspanCuUp = screen.getByRole('heading', {
      name: /CU User plane/i,
    });
    fireEvent.click(airspanCuUp);
    await screen.findByRole('heading', { name: /Local Details/i });
    screen.getByText(/Local address:/i);
    screen.getByRole('link', {
      name: /view in acp/i,
    });
  });
});

describe('show content for error in get request ', () => {
  it('Render error components details', async () => {
    render(
      <NodeComponentErrorCard
        id="1"
        errorData={errorData.airspan_node}
        compName="5G CU User plane"
        testId="cell-comp-airspan-cuup-card"
        linkText="View in ACP"
        linkUrl="https://**************:8181/Management?DBID="
      />,
      {
        wrapper,
      }
    );
    const airspanCuUp = screen.getByRole('heading', {
      name: / CU User plane/i,
    });
    fireEvent.click(airspanCuUp);
    await waitFor(() => expect(screen.getByText(/Error Code :/i)));
    await waitFor(() => expect(screen.getByText(/Reason :/i)));
  });
});
