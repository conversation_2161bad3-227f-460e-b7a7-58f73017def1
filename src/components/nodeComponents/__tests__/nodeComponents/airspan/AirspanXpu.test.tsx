import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { ReactNode } from 'react';
import { describe, expect, it } from 'vitest';
import NodeComponentErrorCard from '../../../../errorComponents/NodeComponentErrorCard';

import { errorData, successData } from '../mockData/MockData';
import AirspanXpuCard from '../../../airspan/xPU/AirspanXpuCard';

type ChildrenProps = {
  children: ReactNode;
};

// Wrapper for react query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});
const wrapper = ({ children }: ChildrenProps) => (
  <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
);

describe('show content for successful get request ', () => {
  it('Render components details', async () => {
    render(<AirspanXpuCard airspanData={successData.sc_v1.airspan_node} />, { wrapper });
    const airspanXpu = screen.getByRole('heading', {
      name: /NODE & XPU/i,
    });
    fireEvent.click(airspanXpu);
    await screen.findByText('Node Details');
  });
});

describe('show content for error in get request ', () => {
  it('Render error components details', async () => {
    render(
      <NodeComponentErrorCard
        id="1"
        errorData={errorData.airspan_node}
        compName="NODE & XPU"
        testId="cell-comp-airspan-xpu-card"
        linkText="View in ACP"
        linkUrl="https://192.168.140.63:8181/Management?DBID="
      />,
      {
        wrapper,
      }
    );
    const airspanXpu = screen.getByRole('heading', {
      name: /NODE & XPU/i,
    });
    fireEvent.click(airspanXpu);
    await waitFor(() => expect(screen.getByText(/Error Code :/i)));
    await waitFor(() => expect(screen.getByText(/Reason :/i)));
  });
});
