import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { ReactNode } from 'react';
import { describe, expect, it } from 'vitest';
import NodeComponentErrorCard from '../../../../errorComponents/NodeComponentErrorCard';
import AirspanDuCard from '../../../airspan/distributedUnit/AirspanDuCard';

import { errorData, successData } from '../mockData/MockData';

type ChildrenProps = {
  children: ReactNode;
};

// Wrapper for react query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});
const wrapper = ({ children }: ChildrenProps) => (
  <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
);

describe('show content for successful get request ', () => {
  it('Render components details', async () => {
    render(
      <AirspanDuCard
        data={successData.sc_v1.airspan_node.gnbDu}
        node={successData.sc_v1.airspan_node.nodeProperties}
      />,
      { wrapper }
    );
    const airspanDu = screen.getByRole('heading', {
      name: /DU/i,
    });
    fireEvent.click(airspanDu);
    await waitFor(() => expect(screen.getByText(/component id:/i)));
  });

  it('Render components details', async () => {
    render(
      <AirspanDuCard
        data={successData.sc_v1.airspan_node.gnbDu}
        node={successData.sc_v1.airspan_node.nodeProperties}
      />,
      { wrapper }
    );
    const airspanDu = screen.getByRole('heading', {
      name: /DU/i,
    });
    fireEvent.click(airspanDu);
    await screen.findByRole('heading', { name: /CELL DETAILS/i });
    screen.getByText(/Cell nrPci:/i);
    screen.getByRole('link', {
      name: /view in acp/i,
    });
  });
});

describe('show content for error in get request ', () => {
  it('Render error components details', async () => {
    render(
      <NodeComponentErrorCard
        id="1"
        errorData={errorData.airspan_node}
        compName="5G DU"
        testId="cell-comp-airspan-du-card"
        linkText="View in ACP"
        linkUrl="https://192.168.140.63:8181/Management?DBID="
      />,
      {
        wrapper,
      }
    );
    const airspanDu = screen.getByRole('heading', {
      name: /DU/i,
    });
    fireEvent.click(airspanDu);
    await waitFor(() => expect(screen.getByText(/Error Code :/i)));
    await waitFor(() => expect(screen.getByText(/Reason :/i)));
  });
});
