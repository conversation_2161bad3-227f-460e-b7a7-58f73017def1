import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { ReactNode } from 'react';
import { describe, expect, it } from 'vitest';
import NodeComponentErrorCard from '../../../../errorComponents/NodeComponentErrorCard';
import AirspanRuCard from '../../../airspan/radioUnit/AirspanRuCard';

import { errorData, successData } from '../mockData/MockData';

type ChildrenProps = {
  children: ReactNode;
};

// Wrapper for react query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});
const wrapper = ({ children }: ChildrenProps) => (
  <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
);

describe('show content for successful get request ', () => {
  it('open component', async () => {
    render(
      <AirspanRuCard
        data={successData.sc_v1.airspan_node.gnbRu}
        node={successData.sc_v1.airspan_node.nodeProperties}
      />,
      { wrapper }
    );
    const airspanRu = screen.getByRole('heading', {
      name: /RU/i,
    });
    fireEvent.click(airspanRu);
    await waitFor(() => expect(screen.getByText(/component id:/i)));
  });

  it('Render component details', async () => {
    render(
      <AirspanRuCard
        data={successData.sc_v1.airspan_node.gnbRu}
        node={successData.sc_v1.airspan_node.nodeProperties}
      />,
      { wrapper }
    );

    const airspanRu = screen.getByRole('heading', { name: /RU/i });
    fireEvent.click(airspanRu);
    const resetButton = await screen.findByText('Reset');
    const bandsText = screen.getByText(/bands:/i);
    expect(resetButton).toBeInTheDocument();
    expect(bandsText).toBeInTheDocument();
  });
});

describe('show content for error in get request ', () => {
  it('Render error component details', async () => {
    render(
      <NodeComponentErrorCard
        id="1"
        errorData={errorData.airspan_node}
        compName="5G RU"
        testId="cell-comp-airspan-ru-card"
        linkText="View in ACP"
        linkUrl="https://192.168.140.63:8181/Management?DBID="
      />,
      { wrapper }
    );

    const airspanRu = screen.getByRole('heading', { name: /RU/i });
    fireEvent.click(airspanRu);

    const errorCodeText = await screen.findByText((content, element) => {
      return element !== null && element.tagName.toLowerCase() === 'div' && content.includes('Error Code :');
    });
    const reasonText = await screen.findByText((content, element) => {
      return element !== null && element.tagName.toLowerCase() === 'div' && content.includes('Reason :');
    });
    expect(errorCodeText).toBeInTheDocument();
    expect(reasonText).toBeInTheDocument();
  });
});
