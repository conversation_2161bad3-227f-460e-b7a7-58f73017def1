export const successData = {
  sc_v1: {
    id: 'GBR-MRLW-0001-A',
    type: 'Cell A',
    position: 'Root',
    airspan_node: {
      productType: 'AirSpeed 1900',
      productCode: 'AS19-F380-DSC1',
      gnbCuCp: [
        {
          id: 11,
          cuCpCellList: [
            {
              id: 9,
              cellIdentity: '000000171',
              cellNumber: 1,
              isEnabled: true,
              localCellId: 1,
              plmnInfoList: ['string'],
              defaultXncAllowed: true,
              neighbourManagementGroupId: 1,
              neighbourList: ['string'],
              externalNeighbourList: ['string'],
              securityConfig: {
                integrity: 'Optional',
                nullIntegrityPriority: 2,
                snow3GIntegrityPriority: 0,
                aesIntegrityPriority: 1,
                ciphering: 'Optional',
                nullCipheringPriority: 2,
                snow3GCipheringPriority: 0,
                aesCipheringPriority: 1,
              },
              mobilityConfig: {
                idleMode: {
                  commonReselection: {},
                },
                interRatIdleMode: {
                  epsFallback: {},
                },
              },
              endpointList: [
                {
                  index: 1,
                  isSameCloud: true,
                  localAddress: '*************',
                  localPort: 37412,
                  remoteId: 'AMF_Druid_VM',
                  remoteAddress: '*************',
                  remotePort: 38412,
                  type: 'F1-C',
                },
              ],
            },
          ],
          endpointList: [
            {
              type: 'NG-C (N2)',
              index: 1,
              remoteId: 'Cav-Druid',
              remoteAddress: '**************',
              remotePort: 38412,
            },
          ],
        },
      ],
      gnbCuUp: [
        {
          id: 42,
          endpointList: [
            {
              index: 1,
              isSameCloud: true,
              localAddress: '*************',
              localPort: 37412,
              remoteId: 'AMF_Druid_VM',
              remoteAddress: '*************',
              remotePort: 38412,
              type: 'F1-U',
            },
          ],
        },
      ],
      gnbDu: [
        {
          id: 39,
          duCellList: [
            {
              id: 9,
              cellIdentity: '000000171',
              srsCombineOffset: 0,
              srsSequenceId: 231,
              duCellConfiguration: {
                advancedConfiguration: {
                  downlink256Qam: 'Disabled',
                  reservedForOperatorUse: 'Disabled',
                  pMax: '33',
                  qRxMinLevel: -70,
                  qRxMinLevelSul: '-50',
                  qMinQuality: '-30',
                  sib2Periodicity: '32',
                  sib3Periodicity: '64',
                  sib4Periodicity: '64',
                  defaultPagingCycle: '64',
                  totalPagingFrames: '1/16T',
                  pagingFrameOffset: '0',
                  pagingOccasionsPerPagingFrame: 'One',
                  t300: '600',
                  t301: '600',
                  t310: '200',
                  n310: '1',
                  t311: '1000',
                  n311: '1',
                  t319: '200',
                  powerRampingStep: '2',
                  preambleInitialReceivedTargetPower: '-90',
                  preambleMaxTransmit: '10',
                  preambleFormat: 'B4',
                  rootSequenceType: 'L139',
                  prachConfigIndex: 148,
                  zeroCorrelationZoneConfig: 12,
                  msg1ScsKhz: '30',
                  ssbsPerRach: '1',
                  contentionBasedPreamblePerSsb: 12,
                  numberRachPreambles: 30,
                  msg1Fdm: '1',
                  msg1FrequencyStart: 12,
                  responseWindowSizeSubframes: 'sl20',
                  contentionResolutionTimer: 'sf40',
                  rsrpThresholdSsb: 20,
                  restrictedSet: 'Unrestricted',
                  msg3MaxTxThreshold: 1,
                  msg3TransformPrecoder: 'Enabled',
                  maxNumUeInheritFromGnb: 'Enabled',
                  maxNumUe: 32,
                  frameStructure: '70/20/10',
                  flexibleSlotStructure: '10D4G',
                },
              },
              cellNumber: 1,
              isEnabled: true,
              adminState: 'Locked',
              localCellId: 1,
              nrPci: 231,
              nrRsi: 0,
              nrTac: 0,
              band: 77,
              pdcchSubcarrierSpacingKhz: '30',
              ssbSubcarrierSpacingKhz: '30',
              nrArfcn: 657000,
              bandwidthMhz: '100',
              bwpSizeMhz: 100,
              bwpStartRb: 0,
              gscn: 8065,
              ssbPeriodicityMs: '20',
              ssbOffsetMs: 0,
              ssbDurationMs: 4,
              plmnInfoList: ['string'],
              inheritPlmnInfoListFromCuCell: true,
              cbrsEnabled: false,
              pciManagementGroupId: 1,
              excludeFromAutoPci: false,
              rachManagementGroupId: 1,
              excludeFromAutoRach: false,
              sectorCarrierProperties: {
                duMacAddress: 'string',
                ruSectorCarrierId: 1,
                maxTxPower: 10,
                sectorCarrierBandwidthMhz: '100',
                sectorCarrierNrArfcn: 0,
              },
            },
          ],
          endpointList: [
            {
              index: 1,
              isSameCloud: true,
              localAddress: '*************',
              localPort: 37412,
              remoteId: 'AMF_Druid_VM',
              remoteAddress: '*************',
              remotePort: 38412,
              type: 'F1-C',
            },
          ],
        },
      ],
      gnbRu: [
        {
          id: 40,
          sectorCarriers: [
            {
              centreFrequency: 3855000,
              duplexMode: 'TDD',
              bands: 'n77',
              duCell: 'DU-as1900-f0486900d99c-1 Cell 1',
              ruSectorConfiguration: {
                gnbRuSectorServiceManagerProperties: {
                  oRanFrontHaulIndicated: 'Enabled',
                  unitOverHeating: 'Enabled',
                  outOfSync: 'Enabled',
                },
              },
              sectorCarrierId: 1,
              nrArfcn: 657000,
              bandwidth: '100',
              inheritDuConfiguration: true,
            },
          ],
          ruConfiguration: {
            advancedConfiguration: {},
          },
        },
      ],
      gnbXpu: {
        id: 42,
        netconfProperties: {
          ipAddress: '**************',
          port: 830,
          userName: 'ems-Airspan',
          timeoutInSeconds: 5,
        },
        xpuConfiguration: {},
        status: 'OK',
        reason: 'Alarm Sync: ConnectionState=Comms',
      },
      nodeProperties: {
        id: 11,
        locationSource: 2,
        name: 'AS1900 - Ravi Lab',
        description: '**************',
        regionId: 1,
        siteId: 1,
        latitude: 51.573909,
        longitude: -0.759858,
        altitude: 85,
        isManaged: true,
        isNbifEventAlarmForwarding: true,
      },
      gnbProperties: {
        primaryPlmnInfo: {
          plmn: {
            mcc: '234',
            mnc: '30',
          },
          snssaiList: [
            {
              sst: 1,
              sd: true,
              sdString: '000000',
            },
          ],
        },
      },
      gnbType: 'Id32Bits',
      gnbId: 23,
      status: 'OK',
    },
    particle_board: {
      details: {
        version: 491,
        modeName: 'ERROR_MODE',
        darkMode: true,
        caseOpen: true,
        fanMode: 'ON',
        PID_BW: [180, 50, 12],
        PID_AS: [135, 40, 10],
        PID_SET: 40,
        LNR_SET: 40,
        LNR_P_BW: 360,
        LNR_P_AS: 270,
        overTemp: 40,
        hysteresis: 38,
        tempSensor1: 19.375,
        tempSensor2: 0,
        bwFanSpeed: 60,
        asFanSpeed: 0,
        antennaConfiguration: 1,
        antenna: {
          board1: {
            switch1: 'UNKNOWN',
            switch2: 'UNKNOWN',
            switch3: 'UNKNOWN',
            switch4: 'UNKNOWN',
            switch5: 'UNKNOWN',
            switch6: 'UNKNOWN',
          },
          board2: {
            switch1: 'UNKNOWN',
            switch2: 'UNKNOWN',
            switch3: 'UNKNOWN',
            switch4: 'UNKNOWN',
            switch5: 'UNKNOWN',
            switch6: 'UNKNOWN',
          },
        },
        networkConfiguration: {
          DHCP: false,
          IP: '**************',
          SUB: '*************',
          GW: '***************',
          DNS: '0.0.0.0',
        },
        alarmCodes: [9, 10],
      },
      updated: '2023-07-24T07:56:50.916436+00:00',
      created: '2023-07-24T07:56:50.916436+00:00',
      published_at: '2023-07-24T07:56:52.616313+00:00',
      manager: 'particle_interactions',
      status: 'ERROR',
      status_reasons: ['Antenna board 1 is not connected.', 'Antenna board 2 is not connected.'],
      coreid: '0a10aced202194944a0216bc',
    },
    bw_mmwave: {
      status: 'OK',
      firmware: '7.1',
    },
    fibrolan_switch: {
      status: 'OK',
      firmware: '7.2',
    },
  },
} as any;

export const errorData = {
  airspan_node: {
    utc_stamp: '2023-06-05T09:55:10.916035',
    client: 'http://***********:8055/api/acp/acp01/',
    error: {
      status_code: 502,
      message: '{"detail": "ACP credentials not found"}',
      url: '11',
    },
  },
  particle_board: {
    utc_stamp: '2024-04-25T11:29:21.865608',
    client: 'http://particle-interactions:8080/nms/particle/',
    error: {
      status_code: 500,
      message: {},
      url: '/device/0a10aced202194944a0219b4',
    },
  },
  bw_mmwave: {
    utc_stamp: '2024-04-25T11:29:21.865608',
    client: 'http://snmp-interactions:8080/nms/snmp/',
    error: {
      status_code: 404,
      message: {
        status: 404,
        message: 'Device not found: 68:1f:40:12:29:3a',
      },
      url: '/bluwireless/68:1f:40:12:29:3a',
    },
  },
  fibrolan_switch: {
    utc_stamp: '2024-04-25T11:29:21.865608',
    client: 'http://snmp-interactions:8080/nms/snmp/',
    error: {
      status_code: 404,
      message: {
        status: 404,
        message: 'Device not found: 7086PCB22100138',
      },
      url: '/fibrolan/7086PCB22100138',
    },
  },
};
