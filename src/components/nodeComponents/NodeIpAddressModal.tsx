import {
  Box,
  Button,
  Divider,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  GridItem,
  Input,
  InputGroup,
  InputLeftElement,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  <PERSON>dal<PERSON><PERSON>er,
  <PERSON>dal<PERSON>eader,
  ModalOverlay,
  SimpleGrid,
  Skeleton,
} from '@chakra-ui/react';
import { DevTool } from '@hookform/devtools';
import { zodResolver } from '@hookform/resolvers/zod';
import { useQuery } from '@tanstack/react-query';
import * as React from 'react';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';
import { MONITOR_STATE } from '../../data/constants';
import { getNodeComponentsListByNodeId } from '../../services/inventoryManager';
import { getNodeCompByNodeId } from '../../services/orchestrator';
import { CELL_ENTITY, ScV1 } from '../../types/orchestrator.types';
import { streetCellConfigurationSchema } from './schema/streetCellConfigurationSchema';
import { RadioButton, RadioButtonGroup } from '../form/RadioButtonGroup';
import { useEffect } from 'react';

type FormData = {
  bluwireless_ip_address?: string;
  fibrolan_ip_address?: string;
  bw_mmwave_monitored?: string;
  fibrolan_switch_monitored?: string;
};

type NodeIpAddressModalProps = {
  isOpen: boolean;
  onClose: () => void;
  //onSubmit: (data: FormData) => void;
  onSubmit: (nodeId: string, formData: FormData) => void;
  nodeId: string;
};

const NodeIpAddressModal: React.FC<NodeIpAddressModalProps> = ({ isOpen, onClose, onSubmit, nodeId }) => {
  const { data: deviceIpData, isLoading: deviceIpDataLoading } = useQuery({
    queryKey: ['getStreetCellConfiguration', nodeId],
    queryFn: () => getNodeComponentsListByNodeId(nodeId),
    enabled: !!nodeId && isOpen,
    select: (data) =>
      //loop through the data object and returns a new object with the values of the component_type and component_address properties.
      data?.reduce<{ [key: string]: string }>((acc, item) => {
        if (item.component_type === 'BLUWIRELESS' || item.component_type === 'FIBROLAN') {
          acc[item.component_type] = item.component_address;
        }
        return acc;
      }, {}),
  });
  const { isLoading: deviceMonitoredDataLoading, data: deviceMonitoredData } = useQuery(
    ['getNodeCompByNodeId', nodeId],
    () => getNodeCompByNodeId(nodeId),
    {
      enabled: !!nodeId && isOpen,
      select: (data) => ({
        bw_mmwave_monitored: data.sc_v1?.bw_mmwave ? data.sc_v1.bw_mmwave.monitored : null,
        fibrolan_switch_monitored: data.sc_v1?.fibrolan_switch ? data.sc_v1.fibrolan_switch.monitored : null,
      }),
      retry: false,
    }
  );
  // Combined isLoading state
  const isLoading = deviceIpDataLoading || deviceMonitoredDataLoading;

  // const colSpan = useBreakpointValue({ base: 2, md: 1 });
  const {
    handleSubmit,
    reset,
    control,
    setValue,
    watch,
    formState: { errors, dirtyFields, isDirty },
  } = useForm<FormData>({
    resolver: zodResolver(streetCellConfigurationSchema),
    defaultValues: {
      bluwireless_ip_address: '',
      fibrolan_ip_address: '',
      fibrolan_switch_monitored: '',
      bw_mmwave_monitored: '',
    },
  });

  const onSubmitHandler: SubmitHandler<FormData> = (formData) => {
    onSubmit(nodeId, formData);
  };

  useEffect(() => {
    if (deviceIpData) {
      reset({
        bluwireless_ip_address: deviceIpData?.BLUWIRELESS || '',
        fibrolan_ip_address: deviceIpData?.FIBROLAN || '',
        fibrolan_switch_monitored: deviceMonitoredData?.fibrolan_switch_monitored || '',
        bw_mmwave_monitored: deviceMonitoredData?.bw_mmwave_monitored || '',
      });
    }
  }, [deviceIpData, deviceMonitoredData, reset]);

  // useEffect(() => {
  //   if (watch('bluwireless_ip_address') !== '') {
  //     setValue('bw_mmwave_monitored', 'Y');
  //   }
  // }, [watch('bluwireless_ip_address'), setValue]);
  // useEffect(() => {
  //   if (watch('fibrolan_ip_address') !== '') {
  //     setValue('fibrolan_switch_monitored', 'Y');
  //   }
  // }, [watch('fibrolan_ip_address'), watch('bluwireless_ip_address'), setValue]);

  return (
    <Modal isOpen={isOpen} onClose={onClose} size={'2xl'}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>Node IP Address Management</ModalHeader>
        <Divider />
        <ModalHeader fontSize="sm">
          <Box fontWeight={'light'}>Node Identifier</Box>
          <Box>{nodeId}</Box>
        </ModalHeader>
        <ModalCloseButton />
        <ModalBody pb={6}>
          <form onSubmit={handleSubmit(onSubmitHandler)}>
            <SimpleGrid columns={2} columnGap={3} rowGap={6} w="full">
              <GridItem colSpan={1}>
                <FormControl isInvalid={!!errors.fibrolan_ip_address}>
                  <FormLabel>FibroLAN</FormLabel>
                  <Skeleton isLoaded={!isLoading}>
                    <Controller
                      name="fibrolan_ip_address"
                      control={control}
                      render={({ field }) => (
                        <InputGroup>
                          <InputLeftElement pointerEvents="none" color="gray.800" fontSize="1.2em">
                            IP
                          </InputLeftElement>
                          <Input
                            borderLeft={dirtyFields?.fibrolan_ip_address ? '5px solid' : ''}
                            data-testid="fibrolan_ip_address"
                            placeholder="***************"
                            {...field}
                          />
                        </InputGroup>
                      )}
                    />
                  </Skeleton>
                  <FormErrorMessage>{errors.fibrolan_ip_address?.message}</FormErrorMessage>
                </FormControl>
              </GridItem>
              <GridItem colSpan={1}>
                <FormControl isInvalid={!!errors.fibrolan_switch_monitored}>
                  <FormLabel>Monitor State</FormLabel>
                  <Skeleton isLoaded={!isLoading}>
                    <Controller
                      control={control}
                      name="fibrolan_switch_monitored"
                      data-testid="fibrolan_switch_monitored"
                      render={({ field }) => (
                        <RadioButtonGroup {...field} size={'md'}>
                          {Object.entries(MONITOR_STATE).map(([key, value]) => (
                            <RadioButton key={value} value={value}>
                              {key}
                            </RadioButton>
                          ))}
                        </RadioButtonGroup>
                      )}
                    />
                  </Skeleton>
                  <FormErrorMessage>{errors.fibrolan_switch_monitored?.message}</FormErrorMessage>
                </FormControl>
              </GridItem>
              <GridItem colSpan={1}>
                <FormControl isInvalid={!!errors.bluwireless_ip_address}>
                  <FormLabel>BluWireless</FormLabel>
                  <Skeleton isLoaded={!isLoading}>
                    <Controller
                      name="bluwireless_ip_address"
                      control={control}
                      render={({ field }) => (
                        <InputGroup>
                          <InputLeftElement pointerEvents="none" color="gray.800" fontSize="1.2em">
                            IP
                          </InputLeftElement>
                          <Input
                            borderLeft={dirtyFields?.bluwireless_ip_address ? '5px solid' : ''}
                            data-testid="bluwireless_ip_address"
                            placeholder="***************"
                            {...field}
                          />
                        </InputGroup>
                      )}
                    />
                  </Skeleton>
                  <FormErrorMessage>{errors.bluwireless_ip_address?.message}</FormErrorMessage>
                </FormControl>
              </GridItem>
              <GridItem colSpan={1}>
                <FormControl isInvalid={!!errors.bw_mmwave_monitored} mb={6}>
                  <FormLabel>Monitor State</FormLabel>
                  <Skeleton isLoaded={!isLoading}>
                    <Controller
                      name="bw_mmwave_monitored"
                      data-testid="bw_mmwave_monitored"
                      control={control}
                      render={({ field }) => (
                        <RadioButtonGroup {...field} size={'md'}>
                          {Object.entries(MONITOR_STATE).map(([key, value]) => (
                            <RadioButton key={value} value={value}>
                              {key}
                            </RadioButton>
                          ))}
                        </RadioButtonGroup>
                      )}
                    />
                  </Skeleton>
                  <FormErrorMessage>{errors.bw_mmwave_monitored?.message}</FormErrorMessage>
                </FormControl>
              </GridItem>
            </SimpleGrid>

            <Divider bg={'white'} />
            <ModalFooter px={0} pb={0}>
              <Flex width="100%">
                <Button
                  colorScheme="teal"
                  variant={'outline'}
                  border="none"
                  onClick={() => {
                    reset();
                  }}
                >
                  Reset
                </Button>
                <Button
                  mr={4}
                  marginLeft="auto"
                  colorScheme="teal"
                  variant={'outline'}
                  onClick={() => {
                    reset();
                    onClose();
                  }}
                >
                  Cancel
                </Button>
                <Button isDisabled={!isDirty} variant="primary" type="submit">
                  Confirm
                </Button>
              </Flex>
            </ModalFooter>
          </form>
        </ModalBody>
      </ModalContent>
      <DevTool control={control} />
    </Modal>
  );
};

export default NodeIpAddressModal;
