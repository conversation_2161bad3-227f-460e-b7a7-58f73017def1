import {
  Badge,
  Box,
  Button,
  Card,
  CardBody,
  CardHeader,
  Divider,
  Flex,
  Heading,
  HStack,
  Icon,
  Stack,
  StackDivider,
  Stat,
  StatGroup,
  StatHelpText,
  StatLabel,
  StatNumber,
  Text,
  ToastId,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import { useEffect, useRef, useState } from 'react';
import {
  AUTH_TOKEN_KEY,
  greenBoxShadowColor,
  NODE_COMPONENT_TITLES,
  READ_WRITE_ACCESS_ROLES,
} from '../../../data/constants';
import { getStatusColor } from '../../../pages/CellOverview/hooks/useStatus';
import { recursiveSearch } from '../../../utils/recursiveSearch';
import StatusComponent from '../../icons/StatusIcon';
import Loader from '../../loader/Loader';
import ComponentModal from '../ComponentModal';
import AntennaPattern from './AntennaPattern';
import Dark from './mode/Dark';
import Fan from './mode/Fan';
import System from './mode/System';
import NetworkMode from './NetworkMode';
import ResetDevice from './reset/ResetDevice';
import Temp from './sensor/Temp';
import TimeSeriesGraph from '../common/TimeSeriesGraph';
import useLogin from '../../../hooks/useLogin';
import { BsArrowReturnRight } from 'react-icons/bs';
import { getComponentBgColor } from '../utils';
import { ComponentTypeEnum } from '../../../services/types';

export type ApiStateObject = {
  status?: string;
  message?: string;
};

const ParticleBoardCard = ({ particleData, cellId, nodeId, id }: any) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [showContent, setShowContent] = useState(false);

  const [apiState, setApiState] = useState<ApiStateObject>({
    status: '',
    message: '',
  });

  useEffect(() => {
    if (apiState.status === 'success') {
      setShowContent(!showContent);
    }
  }, [apiState]);

  const ignore = ['details'];
  const flattenedData: any = recursiveSearch(particleData, ignore);

  //NOTE: pass this to all components instead of calling it everywhere
  const { checkApplicationAccess } = useLogin(AUTH_TOKEN_KEY);
  const checkRoleAccess = checkApplicationAccess(READ_WRITE_ACCESS_ROLES);

  const statusColor = getStatusColor(particleData?.status);
  // const bgColor = statusColor === 'green.500' ? `${statusColor.split('.')[0]}.50` : `${statusColor.split('.')[0]}.900`;
  const bgColor = getComponentBgColor(statusColor);
  return (
    <>
      <Card width="100%" border={'0.1em grey'} borderRadius="lg">
        <CardHeader
          _hover={{
            bg: bgColor,
            cursor: 'pointer',
            boxShadow: `0 0 12px ${bgColor}`,
          }}
          transition="background-color 0.2s ease, box-shadow 0.2s ease"
          borderRadius="lg"
          onClick={() => setShowContent(!showContent)}
          display="flex"
          alignItems="center"
          justifyContent="space-between"
        >
          <Heading size="xs" fontWeight="500" display="flex" justifyContent="space-between">
            <Flex alignItems="center">
              <Icon as={BsArrowReturnRight} />
              <Text ml={2}>{NODE_COMPONENT_TITLES.CONTROLLER_BOARD}</Text>
            </Flex>
          </Heading>

          <StatusComponent
            dataTestId="cell-main-table-status-icon"
            status={particleData?.status}
            boxSize="sm"
            color={statusColor}
            component_type={ComponentTypeEnum.PARTICLE}
            component_id={id}
          />
        </CardHeader>

        {showContent && apiState?.status === 'loading' && <Loader />}

        {showContent && apiState?.status === 'isSubmitting' && <Loader />}

        {showContent && apiState?.status !== 'loading' && (
          <CardBody borderTop="1px solid #e2e2e2">
            {/* <DynamicStats data={particleData} id={id ?? ''} /> */}
            <StatGroup mt="4" mb="4" textAlign="center">
              <Stat>
                <StatLabel>Component id</StatLabel>
                <StatNumber fontSize={'md'}>{id}</StatNumber>
              </Stat>
              <Stat>
                <StatLabel>Software version</StatLabel>
                <StatNumber fontSize={'md'}>{particleData?.details?.version}</StatNumber>
              </Stat>
              <Stat>
                <StatLabel>Current system mode</StatLabel>
                <StatNumber fontSize={'md'}>{particleData?.details?.modeName}</StatNumber>
              </Stat>
            </StatGroup>
            <Divider pb="2" />
            {particleData?.status !== 'SHUTDOWN' && (
              <>
                <HStack divider={<StackDivider />} pt="6" pb="6" justifyContent="space-between">
                  {/* Dark */}
                  <Dark nodeId={nodeId} darkModeData={particleData?.details?.darkMode} setApiState={setApiState} />
                  {/* Fan */}
                  <Fan nodeId={nodeId} fanModeData={particleData?.details?.fanMode} setApiState={setApiState} />
                </HStack>
                <HStack pt="6" pb="6" divider={<StackDivider />} justifyContent="space-between">
                  {/* System */}
                  <System nodeId={nodeId} systemModeData={particleData?.details?.modeName} setApiState={setApiState} />
                  {/* Temp */}
                  <Temp
                    nodeId={nodeId}
                    overtemp={particleData?.details?.overtemp}
                    hysteresis={particleData?.details?.hysteresis}
                    setApiState={setApiState}
                  />
                </HStack>
                <HStack divider={<StackDivider />} pt="6" pb="6" justifyContent="space-between">
                  {/* Antenna 3 */}
                  <AntennaPattern
                    antennaPatternData={particleData?.details}
                    nodeId={nodeId}
                    setApiState={setApiState}
                  />
                  <Box mb="2" ml={'3'}>
                    {/* Network */}
                    <NetworkMode
                      networkModeData={particleData?.details?.networkConfiguration}
                      nodeId={nodeId}
                      setApiState={setApiState}
                      checkRoleAccess={checkRoleAccess}
                    />
                    {/* Reset device */}
                    <Box mt={'4rem'}>
                      <ResetDevice nodeId={nodeId} checkRoleAccess={checkRoleAccess} />
                    </Box>
                  </Box>
                </HStack>
                <HStack
                  divider={<StackDivider />}
                  pt="6"
                  pb="6"
                  justifyContent={'space-around'}
                  alignItems={'self-start'}
                >
                  {/* Time Series Graph */}
                  <TimeSeriesGraph coreid={particleData?.coreid} />
                </HStack>
              </>
            )}
            {/* More Data */}
            <Box>
              <Button onClick={onOpen} width="100%">
                More Data
              </Button>
            </Box>
          </CardBody>
        )}
      </Card>

      {/* Modal */}
      <ComponentModal isOpen={isOpen} onClose={onClose} flattenedData={flattenedData} />
    </>
  );
};

export default ParticleBoardCard;
