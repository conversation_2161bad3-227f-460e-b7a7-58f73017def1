import {
  Badge,
  Box,
  Button,
  Divider,
  FormControl,
  FormErrorMessage,
  Heading,
  Image,
  Select,
  Stat,
  StatGroup,
  StatHelpText,
  StatLabel,
  StatNumber,
  Text,
  useToast,
} from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { z } from 'zod';
import { AUTH_TOKEN_KEY, READ_WRITE_ACCESS_ROLES } from '../../../data/constants';
import useLogin from '../../../hooks/useLogin';
import { putAntennaPattern } from '../../../services/orchestrator';
import { ANTENNA_MODES } from './antenna/data/antennaModes';

type AntennaPatternObject = {
  configId?: number;
  antennaMode?: string;
  imgUrl?: string;
  fileName?: string;
};

type AntennaPatternProps = {
  antennaPatternData: any;
  nodeId: string;
  setApiState: any;
};

type FormData = {
  configId: number;
};

const schema = z.object({
  configId: z.string().min(1).max(2),
});

const AntennaPattern = ({ antennaPatternData, nodeId, setApiState }: AntennaPatternProps) => {
  const { checkApplicationAccess } = useLogin(AUTH_TOKEN_KEY);
  const checkRoleAccess = checkApplicationAccess(READ_WRITE_ACCESS_ROLES);
  const [antennaConfig, setAntennaConfig] = useState<AntennaPatternObject | undefined>();
  const [disabledButton, setDisabledButton] = useState<boolean>(true);
  const [initialAntennaMode, setInitialAntennaMode] = useState<string | undefined>('');

  const createAntennaModeObj = (initialConfigId: number) => {
    const initialConfig = ANTENNA_MODES.find((config) => config.configId === initialConfigId);
    if (initialConfig) {
      return initialConfig;
    }
  };

  useEffect(() => {
    const initialAntennaModeObj = createAntennaModeObj(antennaPatternData?.antennaConfiguration);
    setAntennaConfig(initialAntennaModeObj);
    setInitialAntennaMode(initialAntennaModeObj?.antennaMode);
  }, []);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FormData>({ mode: 'all', resolver: zodResolver(schema) });

  const toast = useToast();

  const handleAntennaModeSelect = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedConfig = ANTENNA_MODES.find((config) => config.configId === parseInt(event.target.value));
    if (selectedConfig) {
      setAntennaConfig(selectedConfig);
    }
    if (initialAntennaMode !== selectedConfig?.antennaMode) {
      setDisabledButton(false);
    } else {
      setDisabledButton(true);
    }
  };

  // Querying
  const antennaMutation = useMutation(['putAntennaPattern', '/cell/antenna'], putAntennaPattern);

  const handlePutAntennaPattern = async (nodeId: string, configId: number) => {
    try {
      setApiState({
        status: 'loading',
        message: 'Form is being submitted',
      });

      const newAntennaModeObj = createAntennaModeObj(configId);
      setInitialAntennaMode(newAntennaModeObj?.antennaMode);

      const response = await antennaMutation.mutateAsync({ nodeId, configId });
      setApiState({
        status: 'success',
        message: `${response.message} for antenna pattern`,
      });
      toast({
        title: response.message.toUpperCase(),
        description: `Antenna pattern has changed from ${initialAntennaMode} to ${response.object.antennaMode}`,
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    } catch (error: any) {
      setApiState({ status: 'error', message: error });
      console.error('AntennaPattern - handlePutAntennaPattern ----- ', error);
      toast({
        title: `${AntennaPattern.name} - ${error.message.toUpperCase()}`,
        description: `Code: ${error.code} | response status: ${error.response.status}`,
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    }
  };

  if (antennaMutation.isSuccess) {
    //setApiState('loading');
    //Get the latest data from the API
    // const queryClient = useQueryClient();
    // queryClient.invalidateQueries({ queryKey: ['getCellByRef'] });
    // const { status, isLoading, error, data } = useQuery({
    //   queryKey: ['getCellByRef', nodeId],
    //   queryFn: () => getCellByRef(nodeId),
    // });
  }

  const onSubmit: SubmitHandler<FormData> = (data) => {
    const configId = data.configId.toString();
    handlePutAntennaPattern(nodeId, parseInt(configId));
  };

  return (
    <Box mb="2" mr={'3'} width={'50%'}>
      <Heading size="sm" textTransform={'uppercase'} display="flex" justifyContent="center" mb="1">
        Antenna Pattern
      </Heading>
      {antennaConfig?.configId && antennaConfig?.antennaMode && (
        <StatGroup mt="4" mb="4" textAlign="center">
          <Stat textAlign="center">
            <StatLabel as={'span'}>Configuration ID - </StatLabel>
            <StatNumber fontSize={'md'} as={'span'}>
              {antennaPatternData.antennaConfiguration}
            </StatNumber>
          </Stat>
          <Stat textAlign="center">
            <StatLabel as={'span'}>Antenna Mode - </StatLabel>
            <StatNumber fontSize={'md'} as={'span'}>
              {initialAntennaMode}
            </StatNumber>
          </Stat>
        </StatGroup>
      )}
      <Text mt="4" mb="4" size="sm" whiteSpace="normal">
        Control the switched-beam antenna configuration. StreetCell V1.1 has 12 antenna configuration patterns.
      </Text>
      <form onSubmit={handleSubmit(onSubmit)}>
        <FormControl isInvalid={!!errors.configId}>
          {/* <label htmlFor="antennaPatternData">Select Antenna pattern</label> */}
          {checkRoleAccess && (
            <Select mt={4} mb={4} {...register('configId')} onChange={handleAntennaModeSelect} id="antennaPattern">
              {ANTENNA_MODES.map((mode) => (
                <option key={mode.configId} value={mode.configId}>
                  {mode.antennaMode}
                </option>
              ))}
            </Select>
          )}
          {errors.configId && checkRoleAccess && (
            <Text bg={'red.400'} p="1">
              {String(errors.configId.message)} for Antenna pattern
            </Text>
          )}
          <FormErrorMessage>{errors?.configId && errors?.configId?.message}</FormErrorMessage>
          <>
            <Box display="flex" justifyContent="center">
              <Image
                src={antennaConfig?.imgUrl ? antennaConfig?.imgUrl : antennaPatternData?.antennaConfiguration}
                alt={`Antenna pattern config ${antennaConfig?.configId}`}
              />
            </Box>
            {checkRoleAccess && (
              <Button
                width="100%"
                marginY={4}
                colorScheme="blue"
                type="submit"
                isDisabled={disabledButton}
              >{`Change Antenna mode to ${antennaConfig?.antennaMode}`}</Button>
            )}
          </>
          {/* )} */}
        </FormControl>
      </form>
      <Divider />
    </Box>
  );
};

export default AntennaPattern;
