import { Box } from '@chakra-ui/react';
import { <PERSON><PERSON><PERSON> } from 'lodash';
import { ErrorBoundary } from 'react-error-boundary';
import { NODE_COMPONENT_TITLES } from '../../../data/constants';
import ComingSoon from '../../comingSoon/ComingSoon';
import { ErrorBoundaryFallback, ErrorBoundaryLogError } from '../../errorComponents/ErrorBoundaryFallback';
import NodeComponentErrorCard from '../../errorComponents/NodeComponentErrorCard';
import ParticleBoardCard from './ParticleBoardCard';

type ParticleBoardProps = {
  nodeComponentsData: any;
  nodeListComponentsData: any;
  acpInstanceAddress: string | Falsey;
  particleBoardHaveData: boolean;
};

const ParticleBoard = ({
  nodeComponentsData,
  nodeListComponentsData,
  acpInstanceAddress,
  particleBoardHaveData,
}: ParticleBoardProps) => {
  return (
    <Box width="100%">
      {nodeComponentsData?.particle_board?.error || nodeComponentsData?.particle_board?.error === null ? (
        <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
          <NodeComponentErrorCard
            id={nodeListComponentsData?.find((item: any) => item.component_type === 'CONTROLLER_BOARD')?.component_id}
            errorData={nodeComponentsData?.particle_board}
            compName={NODE_COMPONENT_TITLES.CONTROLLER_BOARD}
            testId="node-comp-error-card"
            marginSpace="8"
          />
        </ErrorBoundary>
      ) : (
        <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
          {particleBoardHaveData ? (
            <ParticleBoardCard
              particleData={nodeComponentsData.particle_board}
              cellRef={nodeComponentsData?.id}
              nodeId={nodeComponentsData?.id}
              id={nodeComponentsData?.particle_board?.coreid}
            />
          ) : (
            <ComingSoon componentName="Particle board" />
          )}
        </ErrorBoundary>
      )}
    </Box>
  );
};

export default ParticleBoard;
