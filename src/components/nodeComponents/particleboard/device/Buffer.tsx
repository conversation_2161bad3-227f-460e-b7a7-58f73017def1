import { <PERSON><PERSON>, <PERSON>, Button, Heading, Text } from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

const schema = z.object({
  ipAddress: z.string().nonempty().ip(),
  subnet: z.string().nonempty().ip(),
  dns: z.string().nonempty().ip(),
  gateway: z.string().nonempty().ip(),
});
// TODO: NORMAL_MODE, TEST_MODE, ERROR_MODE

const Buffer = ({ modeNameData, setApiState }: any) => {
  // const handleModeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
  const handleModeChange = () => {
    console.log('handleModeChange');
  };

  // Submit form
  const {
    handleSubmit,
    register,
    formState: { errors, isDirty, isValid },
  } = useForm({
    mode: 'all',
    resolver: zodResolver(schema),
  });
  const onSubmit = (data: any) => {
    // Perform form submission or data handling here
    handleModeChange();
  };

  return (
    <Box mt="4" mb="2">
      <Heading size="sm" textTransform={'uppercase'} display="flex" justifyContent="center" mb="4">
        Buffer
      </Heading>
      <Text mt="2" mb="4" fontSize={'md'}>
        Buffer data - <Badge>---More information needed ---</Badge>
      </Text>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Text>Buffer data</Text>

        <Button type="submit" width="100%" marginY={4} colorScheme="blue" isDisabled={!isDirty || !isValid}>
          Apply mode
        </Button>
      </form>
    </Box>
  );
};

export default Buffer;
