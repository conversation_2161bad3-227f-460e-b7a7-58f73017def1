import { <PERSON>ge, Box, Button, Heading, Text } from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
const schema = z.object({
  ipAddress: z.string().nonempty().ip(),
  subnet: z.string().nonempty().ip(),
  dns: z.string().nonempty().ip(),
  gateway: z.string().nonempty().ip(),
});

const Info = ({ setApiState }: any) => {
  const handleInfoChange = () => {
    console.log('handleInfoChange');
  };

  // Submit form
  const {
    handleSubmit,
    register,
    formState: { errors, isDirty, isValid },
  } = useForm({
    mode: 'all',
    resolver: zodResolver(schema),
  });
  const onSubmit = (data: any) => {
    // Perform form submission or data handling here
    handleInfoChange();
  };
  return (
    <Box mt="4" mb="2">
      <Heading size="sm" textTransform={'uppercase'} display="flex" justifyContent="center" mb="4">
        Info
      </Heading>
      <Text mt="2" mb="4" fontSize={'md'}>
        Info data - <Badge>---more information needed---</Badge>
      </Text>
      <Text mt="4" mb="4" size="sm">
        Get particle device information variable of DeviceID, mode, dark mode state, and antenna state.
      </Text>
      {/* Think this will return an object that is just meant to be shown to the user. Format it like in a code editor */}
      {/*NOTE: roles to show this */}
      <form onSubmit={handleSubmit(onSubmit)}>
        <Text>Buffer data</Text>

        <Button type="submit" width="100%" marginY={4} colorScheme="blue" isDisabled={!isDirty || !isValid}>
          Apply mode
        </Button>
      </form>
    </Box>
  );
};

export default Info;
