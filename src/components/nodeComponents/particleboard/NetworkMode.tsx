import {
  Box,
  Button,
  Center,
  Divider,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Heading,
  Input,
  Stat,
  StatGroup,
  StatLabel,
  StatNumber,
  Text,
  useToast,
} from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import React from 'react';
import { Controller, useForm } from 'react-hook-form';
import { z } from 'zod';
import { putNetworkUpdate } from '../../../services/orchestrator';
import { NetworkConfiguration } from '../../../types/orchestrator.types';
import { RadioButton, RadioButtonGroup } from '../../form/RadioButtonGroup';
import { ApiStateObject } from './ParticleBoardCard';

const schema = z.object({
  dhcp: z.boolean().optional(),
  address: z.union([z.literal(''), z.string().ip()]).optional(),
  netmask: z.union([z.literal(''), z.string().ip()]).optional(),
  dns: z.union([z.literal(''), z.string().ip()]).optional(),
  gateway: z.union([z.literal(''), z.string().ip()]).optional(),
});
type NetworkUpdateRequestObject = z.infer<typeof schema>;

type NetworkModeProps = {
  networkModeData: Required<NetworkConfiguration>;
  nodeId: string;
  setApiState: React.Dispatch<React.SetStateAction<ApiStateObject>>;
  checkRoleAccess: boolean;
};
const NetworkMode = ({
  networkModeData = { DHCP: true, IP: '', SUB: '', GW: '', DNS: '' },
  nodeId,
  setApiState,
  checkRoleAccess,
}: NetworkModeProps) => {
  // Setup form & validation
  const {
    handleSubmit,
    register,
    setValue,
    watch,
    control,
    formState: { errors, isDirty },
  } = useForm<NetworkUpdateRequestObject>({
    mode: 'all',
    resolver: zodResolver(schema),
    defaultValues: {
      dhcp: networkModeData.DHCP,
      address: networkModeData.IP,
      netmask: networkModeData.SUB,
      dns: networkModeData.DNS,
      gateway: networkModeData.GW,
    },
  });
  const mode = watch('dhcp') ? 'DHCP' : 'Static';

  const isDHCP = watch('dhcp') === true ? true : false;
  const isStatic = !isDHCP;

  const toast = useToast();

  // Submit form
  const onSubmit = async (networkConfig: NetworkUpdateRequestObject) => {
    await networkUpdateMutation.mutateAsync({
      nodeId,
      networkConfig,
    });
  };
  // Querying
  const networkUpdateMutation = useMutation(['putNetworkUpdate', '/cell/network'], putNetworkUpdate, {
    onMutate: async () => {
      setApiState({
        status: 'loading',
        message: 'Form is being submitted',
      });
    },
    onSuccess: (data) => {
      setApiState({
        status: 'success',
        message: ` for network mode`,
      });
      toast({
        title: data.message.toUpperCase(),
        description: `Network changes will take effect shortly.`,
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: (error: any) => {
      setApiState({ status: 'error', message: error });
      console.error('NetworkMode - darkModeUpdateMutation ----- ', error);
      toast({
        title: `${NetworkMode.name} - ${error.message.toUpperCase()}`,
        description: `Code: ${error.code} | response status: ${error.response.status}`,
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
  });

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Heading size="sm" textTransform={'uppercase'} display="flex" justifyContent="center" mb="1">
          Network mode
        </Heading>
        <StatGroup mt="4" mb="4" textAlign="center">
          <Stat textAlign="center">
            <StatLabel as={'span'}>Network mode - </StatLabel>
            <StatNumber fontSize={'md'} as={'span'}>
              {mode}
            </StatNumber>
          </Stat>
        </StatGroup>
        <Text mt="4" mb="4" size="sm">
          Configure Particle network, such as DHCP, static IP, gateway IP, subnet mask, and DNS.
        </Text>
        {/*NOTE: roles to show this */}
        {checkRoleAccess && (
          <Controller
            control={control}
            name="dhcp"
            render={({ field }) => (
              <RadioButtonGroup
                {...field}
                size={'md'}
                width="100%"
                justifyContent="center"
                mb="4"
                onChange={(value) => {
                  field.onChange(value === 'true');
                  setValue('dhcp', value === 'true');
                }}
                value={watch('dhcp')?.toString()}
              >
                <RadioButton data-testid={'dhcp'} value={'true'}>
                  DHCP
                </RadioButton>
                <RadioButton data-testid={'static'} value={'false'}>
                  STATIC
                </RadioButton>
              </RadioButtonGroup>
            )}
          />
        )}

        {(isStatic || isDHCP) && checkRoleAccess && (
          <>
            <FormControl isInvalid={!!errors.address}>
              <Box mb="4">
                <Flex mb="2">
                  <Center w="25%" alignItems="center" justifyContent="flex-start">
                    <FormLabel htmlFor="address">IP address: </FormLabel>
                  </Center>
                  <Center width={'75%'} alignItems="center" justifyContent="flex-start">
                    <Input
                      id="address"
                      placeholder="***************"
                      data-testid={'address'}
                      isDisabled={isDHCP}
                      {...register('address', { required: true })}
                    />
                  </Center>
                </Flex>
                <FormErrorMessage p="1" display={'block'}>
                  {String(errors?.address?.message)} for IP address
                </FormErrorMessage>
              </Box>
            </FormControl>
            <FormControl isInvalid={!!errors.netmask}>
              <Box mb="4">
                <Flex mb="2">
                  <Center w="25%" alignItems="center" justifyContent="flex-start">
                    <label htmlFor="netmask">Subnet: </label>
                  </Center>
                  <Center width={'75%'} alignItems="center" justifyContent="flex-start">
                    <Input
                      id="netmask"
                      placeholder="***************"
                      isDisabled={isDHCP}
                      {...register('netmask', { required: true })}
                    />
                  </Center>
                </Flex>
                <FormErrorMessage p="1" display={'block'}>
                  {String(errors?.netmask?.message)} for Subnet
                </FormErrorMessage>
              </Box>
            </FormControl>
            <FormControl isInvalid={!!errors.dns}>
              <Box mb="4">
                <Flex mb="2">
                  <Center w="25%" alignItems="center" justifyContent="flex-start">
                    <label htmlFor="dns">DNS: </label>
                  </Center>
                  <Center width={'75%'} alignItems="center" justifyContent="flex-start">
                    <Input
                      id="dns"
                      placeholder="***************"
                      isDisabled={isDHCP}
                      {...register('dns', { required: true })}
                    />
                  </Center>
                </Flex>
                <FormErrorMessage p="1" display={'block'}>
                  {String(errors?.dns?.message)} for Subnet
                </FormErrorMessage>
              </Box>
            </FormControl>
            <FormControl isInvalid={!!errors.gateway}>
              <Box mb="4">
                <Flex mb="2">
                  <Center w="25%" alignItems="center" justifyContent="flex-start">
                    <label htmlFor="gateway">Gateway: </label>
                  </Center>
                  <Center width={'75%'} alignItems="center" justifyContent="flex-start">
                    <Input
                      id="gateway"
                      placeholder="***************"
                      isDisabled={isDHCP}
                      {...register('gateway', { required: true })}
                    />
                  </Center>
                </Flex>
                <FormErrorMessage p="1" display={'block'}>
                  {String(errors?.gateway?.message)} for Subnet
                </FormErrorMessage>
              </Box>
            </FormControl>
          </>
        )}

        <Button type="submit" width="100%" marginY={4} colorScheme="blue" isDisabled={!isDirty}>
          {isDHCP ? 'Apply DHCP mode' : 'Apply static IP'}
        </Button>
        <Divider />
      </form>
    </>
  );
};

export default NetworkMode;
