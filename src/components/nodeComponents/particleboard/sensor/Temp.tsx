import {
  Box,
  Button,
  Divider,
  FormControl,
  FormErrorMessage,
  Heading,
  Input,
  InputGroup,
  InputLeftAddon,
  InputRightAddon,
  Stack,
  Stat,
  StatGroup,
  StatLabel,
  StatNumber,
  Text,
  Tooltip,
  useToast,
} from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import React from 'react';
import { FieldValues, useForm } from 'react-hook-form';
import { z, ZodIssue } from 'zod';
import { AUTH_TOKEN_KEY, READ_WRITE_ACCESS_ROLES } from '../../../../data/constants';
import useLogin from '../../../../hooks/useLogin';
import { putTempSensor } from '../../../../services/orchestrator';
import { TempSensorUpdateObject } from '../../../../types/orchestrator.types';
import { ApiStateObject } from '../ParticleBoardCard';

type TempProps = {
  nodeId: string;
  overtemp: number;
  hysteresis: number;
  setApiState: React.Dispatch<React.SetStateAction<ApiStateObject>>;
};

// TODO:  {
//   "OT": 40,
//   "HY": 35.5
// }
const schema = z
  .object({
    OT: z
      .number()
      .gte(-55)
      .lte(125)
      .refine((value) => value % 0.5 === 0, {
        message: 'OT must be in 0.5 increments',
        path: ['OT'],
      }),
    HY: z
      .number()
      .gte(-55)
      .lte(125)
      .refine((value) => value % 0.5 === 0, {
        message: 'HY must be in 0.5 increments',
        path: ['HY'],
      }),
  })
  .superRefine((data, ctx) => {
    if (data.HY > data.OT) {
      const customError: ZodIssue = {
        code: 'custom',
        message: 'HY cannot be greater than OT',
        path: [...ctx.path, 'HY'],
      };
      ctx.addIssue(customError);
    }
  })
  .refine((value) => value.OT % 0.5 === 0, {
    message: 'OT must be in 0.5 increments',
    path: ['OT'],
  })
  .refine((value) => value.HY % 0.5 === 0, {
    message: 'HY must be in 0.5 increments',
    path: ['HY'],
  });

const Temp = ({ nodeId, overtemp, hysteresis, setApiState }: TempProps) => {
  const { checkApplicationAccess } = useLogin(AUTH_TOKEN_KEY);
  const checkRoleAccess = checkApplicationAccess(READ_WRITE_ACCESS_ROLES);
  // Setup form & validation
  const {
    handleSubmit,
    register,
    formState: { errors, isDirty, isValid },
  } = useForm({
    mode: 'all',
    resolver: zodResolver(schema),
    defaultValues: { OT: '', HY: '' },
  });

  const toast = useToast();

  const handleTempSensorChange = async (nodeId: string, tempSensorUpdateObject: TempSensorUpdateObject) => {
    try {
      setApiState({
        status: 'loading',
        message: 'Form is being submitted',
      });
      const response = await tempSensorUpdateMutation.mutateAsync({
        nodeId,
        tempSensorUpdateObject,
      });
      setApiState({
        status: 'success',
        message: `${response.message} for temp sensor`,
      });
      toast({
        title: response.message.toUpperCase(),
        description: `Temp sensor changed from OT-${overtemp} & HY-${hysteresis} to OT-${response.object.OT} & HY-${response.object.HY}`,
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    } catch (error: any) {
      setApiState({ status: 'error', message: error });
      console.error('Temp - tempSensorUpdateMutation ----- ', error);
      toast({
        title: `${Temp.name} - ${error.message.toUpperCase()}`,
        description: `Code: ${error.code} | response status: ${error.response.status}`,
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    }
  };

  // Submit form
  const onSubmit = async (values: FieldValues) => {
    const tempSensorUpdateObject: TempSensorUpdateObject = {
      OT: values.OT as number,
      HY: values.HY as number,
    };
    await handleTempSensorChange(nodeId, tempSensorUpdateObject);
  };

  // Querying
  const tempSensorUpdateMutation = useMutation(['putTempSensor', '/cell/sensor/temp'], putTempSensor);

  return (
    <Box mb="2" ml={'3'} width={'50%'}>
      <Heading size="sm" textTransform={'uppercase'} display="flex" justifyContent="center" mb="1">
        Temp
      </Heading>
      <StatGroup mt="4" mb="4" textAlign="center">
        <Stat textAlign="center">
          <StatLabel as={'span'}>Overtemp - </StatLabel>
          <StatNumber fontSize={'md'} as={'span'}>
            {overtemp} &#8451;
          </StatNumber>
        </Stat>
        <Stat textAlign="center">
          <StatLabel as={'span'}>Hysteresis - </StatLabel>
          <StatNumber fontSize={'md'} as={'span'}>
            {hysteresis} &#8451;
          </StatNumber>
        </Stat>
      </StatGroup>

      <Text mt="4" mb="6" size="sm" whiteSpace="normal">
        Set the over-temperature and hysteresis value in Degrees Celsius. The over-temperature value must be larger than
        hysteresis value. The resolution is 0.5C. Over temperature alarms are set at the higher threshold and cleared at
        the lower threshold.
      </Text>
      {checkRoleAccess && (
        <form onSubmit={handleSubmit(onSubmit)}>
          <Stack spacing={8} direction={'row'} justifyContent={'space-around'}>
            <InputGroup>
              <FormControl isInvalid={!!errors.OT}>
                <Box display="flex">
                  <InputLeftAddon>OT</InputLeftAddon>
                  <Input
                    type="number"
                    {...register('OT', { valueAsNumber: true })}
                    min={-55}
                    max={125}
                    step={0.5}
                    placeholder={overtemp?.toString()}
                  />
                  <Tooltip
                    label="OT - Over temperature value in ℃. The top boundary of the
                    temperature threshold"
                    fontSize={'md'}
                  >
                    <InputRightAddon>&#8451;</InputRightAddon>
                  </Tooltip>
                </Box>
                <FormErrorMessage>{errors?.OT && errors?.OT?.message}</FormErrorMessage>
              </FormControl>

              <FormControl isInvalid={!!errors.HY}>
                <Box display="flex">
                  <InputLeftAddon>HY</InputLeftAddon>
                  <Input
                    type="number"
                    {...register('HY', { valueAsNumber: true })}
                    min={-55}
                    max={125}
                    step={0.5}
                    placeholder={hysteresis?.toString()}
                  />
                  <Tooltip
                    label="HY - Hysteresis value in ℃. The bottom boundary of the
                temperature threshold"
                    fontSize={'md'}
                  >
                    <InputRightAddon>&#8451;</InputRightAddon>
                  </Tooltip>
                </Box>
                <FormErrorMessage>{errors?.HY && errors?.HY?.message}</FormErrorMessage>
              </FormControl>
            </InputGroup>
          </Stack>

          <Button type="submit" width="100%" marginY={4} colorScheme="blue" isDisabled={!isDirty || !isValid} mt="7">
            Apply mode
          </Button>
        </form>
      )}
      <Divider />
    </Box>
  );
};

export default Temp;
