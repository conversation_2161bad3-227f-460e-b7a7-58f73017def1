import {
  Box,
  Button,
  Divider,
  FormControl,
  FormErrorMessage,
  Heading,
  Radio,
  RadioGroup,
  Stack,
  Stat,
  StatLabel,
  StatNumber,
  Text,
  useToast,
} from '@chakra-ui/react';
import { DevTool } from '@hookform/devtools';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import React, { useState } from 'react';
import { FieldValues, useForm } from 'react-hook-form';
import { z } from 'zod';
import { AUTH_TOKEN_KEY, READ_WRITE_ACCESS_ROLES } from '../../../../data/constants';
import useLogin from '../../../../hooks/useLogin';
import { putDarkMode } from '../../../../services/orchestrator';
import { DarkModeUpdateObject } from '../../../../types/orchestrator.types';
import { ApiStateObject } from '../ParticleBoardCard';

export enum DARKMODE {
  ON = 'ON',
  OFF = 'OFF',
}

export type DarkProps = {
  nodeId: string;
  darkModeData: string | boolean | (() => boolean);
  setApiState: React.Dispatch<React.SetStateAction<ApiStateObject>>;
};

const schema = z.object({
  DarkMode: z.enum(['ON', 'OFF']).refine((value) => value === 'ON' || value === 'OFF', {
    message: 'Please select a mode.',
    path: ['DarkMode'],
  }),
});

const Dark = ({ nodeId, darkModeData, setApiState }: DarkProps) => {
  const [disableButton, setDisableButton] = useState(true);
  // const [currentDmMode, setCurrentDmMode] = useState<string | undefined>(
  //   darkModeData ? DARKMODE.ON : DARKMODE.OFF
  // );

  const currentDmMode = darkModeData ? DARKMODE.ON : DARKMODE.OFF;
  const { checkApplicationAccess } = useLogin(AUTH_TOKEN_KEY);
  const checkRoleAccess = checkApplicationAccess(READ_WRITE_ACCESS_ROLES);

  const {
    handleSubmit,
    register,
    control,
    setValue,
    formState: { errors, isDirty, isValid },
  } = useForm({
    mode: 'all',
    resolver: zodResolver(schema),
    defaultValues: { DarkMode: '' },
  });

  //const { handleDarkModeChange } = useDarkModeUpdateMutation(setApiState);
  const darkModeUpdateMutation = useMutation(['putDarkMode', '/cell/mode/dark'], putDarkMode);
  const toast = useToast();
  const queryClient = useQueryClient();
  const handleDarkModeChange = async (nodeId: string, darkModeUpdateObject: DarkModeUpdateObject) => {
    try {
      setApiState({
        status: 'loading',
        message: '',
      });
      const response = await darkModeUpdateMutation.mutateAsync({
        nodeId,
        darkModeUpdateObject,
      });

      queryClient.invalidateQueries({ queryKey: ['getNodeCompByNodeId'] });
      if (response.status === 'SUCCESS') {
        queryClient.invalidateQueries({ queryKey: ['getNodeCompByNodeId'] });
        setTimeout(() => {
          setApiState({
            status: 'success',
            message: '',
          });
          toast({
            title: response.message.toUpperCase(),
            description: `Dark mode changed from ${darkModeData.toString()} to ${response.object.Cmd}`,
            status: 'success',
            duration: 9000,
            isClosable: true,
            position: 'top',
          });
        }, 2000);
      }
    } catch (error: any) {
      setApiState({
        status: error.status,
        message: '',
      });
      toast({
        title: `${Dark.name} - ${error.message.toUpperCase()}`,
        description: `Code: ${error.code} | response status: ${error.response.status}`,
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    }
  };

  const onSubmit = async (values: FieldValues) => {
    const darkModeUpdateObject: DarkModeUpdateObject = {
      Cmd: values.DarkMode as string,
    };
    await handleDarkModeChange(nodeId, darkModeUpdateObject);
  };

  const handleDisableButton = (selectedValue: string) => {
    setDisableButton(selectedValue === currentDmMode ? true : false);
    setValue('DarkMode', selectedValue);
  };

  return (
    <Box mb="2" mr={'3'} width={'50%'}>
      <Heading size="sm" textTransform={'uppercase'} textAlign="center" mb="1">
        Dark
      </Heading>
      <Stat mt="4" mb="4" textAlign="center">
        <StatLabel as={'span'}>Dark mode - </StatLabel>
        <StatNumber fontSize={'md'} as={'span'}>
          {currentDmMode}
        </StatNumber>
      </Stat>
      <Text mt="4" mb="4" size="sm" whiteSpace="normal">
        Set the device to dark mode. When dark mode is on, the LED remains off unless the shroud is removed.
      </Text>
      {checkRoleAccess && (
        <form onSubmit={handleSubmit(onSubmit)}>
          <RadioGroup defaultValue={currentDmMode} mb={4}>
            <Stack spacing={8} direction={'row'} justifyContent={'space-around'} textAlign="center">
              <FormControl isInvalid={!!errors.DarkMode}>
                <Radio
                  colorScheme="green"
                  value="ON"
                  {...register('DarkMode')}
                  onChange={() => handleDisableButton('ON')}
                >
                  ON
                </Radio>
                <FormErrorMessage>{errors?.DarkMode && errors?.DarkMode?.message}</FormErrorMessage>
              </FormControl>
              <FormControl isInvalid={!!errors.DarkMode}>
                <Radio
                  colorScheme="green"
                  value="OFF"
                  {...register('DarkMode')}
                  onChange={() => handleDisableButton('OFF')}
                >
                  OFF
                </Radio>
                <FormErrorMessage>{errors?.DarkMode && errors?.DarkMode?.message}</FormErrorMessage>
              </FormControl>
            </Stack>
          </RadioGroup>
          <Button
            type="submit"
            width="100%"
            marginY={4}
            colorScheme="blue"
            //isDisabled={disableButton && (!isDirty || !isValid)}
            //isDisabled={!isDirty || !isValid}
            isDisabled={disableButton}
          >
            Apply mode
          </Button>
        </form>
      )}
      <DevTool control={control} /> {/* set up the dev tool */}
      <Divider />
    </Box>
  );
};

export default Dark;
