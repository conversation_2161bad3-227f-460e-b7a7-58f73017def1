import {
  Box,
  Button,
  Divider,
  FormControl,
  FormErrorMessage,
  Heading,
  Radio,
  RadioGroup,
  Stack,
  Stat,
  StatLabel,
  StatNumber,
  Text,
  useToast,
} from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import React, { useState } from 'react';
import { FieldValues, useForm } from 'react-hook-form';
import { z } from 'zod';
import { AUTH_TOKEN_KEY, READ_WRITE_ACCESS_ROLES } from '../../../../data/constants';
import useLogin from '../../../../hooks/useLogin';
import { putSystemMode } from '../../../../services/orchestrator';
import { SystemModeUpdateObject } from '../../../../types/orchestrator.types';
import { ApiStateObject } from '../ParticleBoardCard';

type SystemProps = {
  nodeId: string;
  systemModeData: string;
  setApiState: React.Dispatch<React.SetStateAction<ApiStateObject>>;
};

//NOTE:  NORMAL_MODE, TEST_MODE, ERROR_MODE
const schema = z.object({
  Cmd: z
    .enum(['NORMAL_MODE', 'TEST_MODE', 'ERROR_MODE'])
    .refine((value) => value === 'NORMAL_MODE' || value === 'TEST_MODE' || value === 'ERROR_MODE', {
      message: 'Please select a mode.',
      path: ['Cmd'],
    }),
});

const System = ({ nodeId, systemModeData, setApiState }: SystemProps) => {
  const [disableButton, setDisableButton] = useState<boolean>(false);

  const { checkApplicationAccess } = useLogin(AUTH_TOKEN_KEY);
  const checkRoleAccess = checkApplicationAccess(READ_WRITE_ACCESS_ROLES);

  // Setup form & validation
  const {
    handleSubmit,
    register,
    setValue,
    formState: { errors, isDirty, isValid },
  } = useForm({
    mode: 'all',
    resolver: zodResolver(schema),
    defaultValues: { Cmd: '' },
  });

  const toast = useToast();

  const handleDisableButton = (selectedValue: string) => {
    setDisableButton(selectedValue === systemModeData ? false : true);
    setValue('Cmd', selectedValue);
  };

  const handleSystemModeChange = async (nodeId: string, systemModeUpdateObject: SystemModeUpdateObject) => {
    try {
      setApiState({
        status: 'loading',
        message: 'Form is being submitted',
      });
      const response = await systemModeUpdateMutation.mutateAsync({
        nodeId,
        systemModeUpdateObject,
      });
      setApiState({
        status: 'success',
        message: `${response.message} for system mode`,
      });
      toast({
        title: response.message.toUpperCase(),
        description: `System mode changed from ${systemModeData.toString()} to ${response.object.Cmd}`,
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    } catch (error: any) {
      setApiState({ status: 'error', message: error });
      console.error('System - systemModeUpdateMutation ----- ', error);
      toast({
        title: `${System.name} - ${error.message.toUpperCase()}`,
        description: `Code: ${error.code} | response status: ${error.response.status}`,
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    }
  };

  // Submit form
  const onSubmit = async (values: FieldValues) => {
    const systemModeUpdateObject: SystemModeUpdateObject = {
      Cmd: values.Cmd as string,
    };
    await handleSystemModeChange(nodeId, systemModeUpdateObject);
  };

  // Querying
  const systemModeUpdateMutation = useMutation(['putSystemMode', '/cell/mode/system'], putSystemMode);

  return (
    <Box mb="2" mr={'3'} width={'50%'}>
      <Heading size="sm" textTransform={'uppercase'} display="flex" justifyContent="center" mb="1">
        System
      </Heading>
      <Stat mt="4" mb="4" textAlign="center">
        <StatLabel as={'span'}>System mode - </StatLabel>
        <StatNumber fontSize={'md'} as={'span'}>
          {systemModeData && systemModeData.toString()}
        </StatNumber>
      </Stat>
      <Text mt="4" mb={'20'} size="sm" whiteSpace="normal">
        For R&D use only. Set the device mode to normal mode, test mode, or error mode.
      </Text>
      {checkRoleAccess && (
        <form onSubmit={handleSubmit(onSubmit)}>
          <RadioGroup defaultValue={systemModeData && systemModeData.toString()} mb={4}>
            <Stack spacing={8} direction={'row'} justifyContent={'space-around'}>
              <FormControl isInvalid={!!errors.Cmd}>
                <Radio
                  colorScheme="green"
                  value="NORMAL_MODE"
                  {...register('Cmd', { value: 'NORMAL_MODE' })}
                  onChange={() => handleDisableButton('NORMAL_MODE')}
                >
                  Normal Mode
                </Radio>
                <FormErrorMessage>{errors?.Cmd && errors?.Cmd?.message}</FormErrorMessage>
              </FormControl>
              <FormControl isInvalid={!!errors.Cmd}>
                <Radio
                  colorScheme="green"
                  value="TEST_MODE"
                  {...register('Cmd', { value: 'TEST_MODE' })}
                  onChange={() => handleDisableButton('TEST_MODE')}
                >
                  Test Mode
                </Radio>
                <FormErrorMessage>{errors?.Cmd && errors?.Cmd?.message}</FormErrorMessage>
              </FormControl>
              <FormControl isInvalid={!!errors.Cmd}>
                <Radio
                  colorScheme="green"
                  value="ERROR_MODE"
                  {...register('Cmd', { value: 'ERROR_MODE' })}
                  onChange={() => handleDisableButton('ERROR_MODE')}
                >
                  Error Mode
                </Radio>
                <FormErrorMessage>{errors?.Cmd && errors?.Cmd?.message}</FormErrorMessage>
              </FormControl>
            </Stack>
          </RadioGroup>

          <Button
            type="submit"
            width="100%"
            marginY={4}
            colorScheme="blue"
            isDisabled={!disableButton && (!isDirty || !isValid)}
          >
            Apply mode
          </Button>
        </form>
      )}
      <Divider />
    </Box>
  );
};

export default System;
