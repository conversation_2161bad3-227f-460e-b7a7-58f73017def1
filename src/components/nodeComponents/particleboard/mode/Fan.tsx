import {
  Box,
  Button,
  Divider,
  FormControl,
  FormErrorMessage,
  Heading,
  Radio,
  RadioGroup,
  Stack,
  Stat,
  StatLabel,
  StatNumber,
  Text,
  useToast,
} from '@chakra-ui/react';
import { DevTool } from '@hookform/devtools';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import React, { useState } from 'react';
import { FieldValues, useForm } from 'react-hook-form';
import { z } from 'zod';
import { AUTH_TOKEN_KEY, READ_WRITE_ACCESS_ROLES } from '../../../../data/constants';
import useLogin from '../../../../hooks/useLogin';
import { putFanMode } from '../../../../services/orchestrator';
import { FanModeUpdateObject } from '../../../../types/orchestrator.types';
import { ApiStateObject } from '../ParticleBoardCard';

type FanProps = {
  nodeId: string;
  fanModeData: string;
  setApiState: React.Dispatch<React.SetStateAction<ApiStateObject>>;
};

const schema = z.object({
  Cmd: z.enum(['PID', 'LINEAR', 'ON']).refine((value) => value === 'ON' || value === 'LINEAR' || value === 'PID', {
    message: 'Please select a mode.',
    path: ['Cmd'],
  }),
});

//NOTE:  PID, LINEAR, ON
const Fan = ({ nodeId, fanModeData, setApiState }: FanProps) => {
  const [disableButton, setDisableButton] = useState<boolean>(false);

  const { checkApplicationAccess } = useLogin(AUTH_TOKEN_KEY);
  const checkRoleAccess = checkApplicationAccess(READ_WRITE_ACCESS_ROLES);

  // Setup form & validation
  const {
    handleSubmit,
    register,
    control,
    setValue,
    formState: { errors, isDirty, isValid },
  } = useForm({
    mode: 'all',
    resolver: zodResolver(schema),
    defaultValues: { Cmd: '' },
  });
  const toast = useToast();

  const handleDisableButton = (selectedValue: string) => {
    setDisableButton(selectedValue === fanModeData ? false : true);
    setValue('Cmd', selectedValue);
  };

  const handleFanModeChange = async (nodeId: string, fanModeUpdateObject: FanModeUpdateObject) => {
    try {
      setApiState({
        status: 'loading',
        message: 'Form is being submitted',
      });
      const response = await fanModeUpdateMutation.mutateAsync({
        nodeId,
        fanModeUpdateObject,
      });
      setApiState({
        status: 'success',
        message: `${response.message} for fan mode`,
      });
      toast({
        title: response.message.toUpperCase(),
        description: `Fan mode changed from ${fanModeData.toString()} to ${response.object.Cmd}`,
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    } catch (error: any) {
      setApiState({ status: 'error', message: error });
      console.error('Fan - fanModeUpdateMutation ----- ', error);
      toast({
        title: `${Fan.name} - ${error.message.toUpperCase()}`,
        description: `Code: ${error.code} | response status: ${error.response.status}`,
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    }
  };

  // Submit form
  const onSubmit = async (values: FieldValues) => {
    const fanModeUpdateObject: FanModeUpdateObject = {
      Cmd: values.Cmd as string,
    };
    await handleFanModeChange(nodeId, fanModeUpdateObject);
  };

  // Querying
  const fanModeUpdateMutation = useMutation(['putFanMode', '/cell/mode/fan'], putFanMode);

  return (
    <Box mb="2" ml={'3'} width={'50%'}>
      <Heading size="sm" textTransform={'uppercase'} textAlign="center" mb="1">
        Fan
      </Heading>
      <Stat mt="4" mb="4" textAlign="center">
        <StatLabel as={'span'}>Fan mode - </StatLabel>
        <StatNumber fontSize={'md'} as={'span'}>
          {fanModeData && fanModeData.toString()}
        </StatNumber>
      </Stat>
      <Text mt="4" mb="4" whiteSpace="normal">
        Set the fan mode. The fan is running in &ldquo;temperature-driven&rdquo; mode through a PID control algorithm by
        default. A linear control algorithm can also be chosen or it can be switched to &ldquo;full on&rdquo; mode.
      </Text>
      {checkRoleAccess && (
        <form onSubmit={handleSubmit(onSubmit)}>
          <RadioGroup defaultValue={fanModeData && fanModeData.toString()} mb={4}>
            <Stack spacing={8} direction={'row'} justifyContent={'space-around'}>
              <FormControl isInvalid={!!errors.Cmd}>
                <Radio
                  colorScheme="green"
                  value="PID"
                  {...register('Cmd', { value: 'PID' })}
                  onChange={() => handleDisableButton('PID')}
                >
                  PID
                </Radio>
                <FormErrorMessage>{errors?.Cmd && errors?.Cmd?.message}</FormErrorMessage>
              </FormControl>
              <FormControl isInvalid={!!errors.Cmd}>
                <Radio
                  colorScheme="green"
                  value="LINEAR"
                  {...register('Cmd', { value: 'LINEAR' })}
                  onChange={() => handleDisableButton('LINEAR')}
                >
                  LINEAR
                </Radio>
                <FormErrorMessage>{errors?.Cmd && errors?.Cmd?.message}</FormErrorMessage>
              </FormControl>
              <FormControl isInvalid={!!errors.Cmd}>
                <Radio
                  colorScheme="green"
                  value="ON"
                  {...register('Cmd', { value: 'ON' })}
                  onChange={() => handleDisableButton('ON')}
                >
                  ON
                </Radio>
                <FormErrorMessage>{errors?.Cmd && errors?.Cmd?.message}</FormErrorMessage>
              </FormControl>
            </Stack>
          </RadioGroup>

          <Button
            type="submit"
            width="100%"
            marginY={4}
            colorScheme="blue"
            isDisabled={!disableButton && (!isDirty || !isValid)}
          >
            Apply mode
          </Button>
        </form>
      )}
      <DevTool control={control} /> {/* set up the dev tool */}
      <Divider />
    </Box>
  );
};

export default Fan;
