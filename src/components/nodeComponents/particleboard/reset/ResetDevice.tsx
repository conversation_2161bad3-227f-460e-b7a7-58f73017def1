import {
  Button,
  Divider,
  Heading,
  Stat,
  StatGroup,
  StatLabel,
  StatNumber,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import { useMutation } from '@tanstack/react-query';
import { NodeResetByNodeId } from '../../../../services/orchestrator';
import Loader from '../../../loader/Loader';
import { CustomModal } from '../../../modal';
// import { NodeResetByNodeId } from '../../../../../services/orchestrator';
// import Loader from '../../../../loader/Loader';
// import { CustomModal } from '../../../../modal';

type ResetDeviceProps = {
  nodeId: string;
  checkRoleAccess: boolean;
};

const ResetDevice = ({ nodeId, checkRoleAccess }: ResetDeviceProps) => {
  const { isOpen, onOpen, onClose } = useDisclosure();

  const toast = useToast();

  // Mutation
  const { mutate: ResetDevice, isLoading } = useMutation(NodeResetByNodeId, {
    onSuccess(data) {
      toast({
        title: data.message.toUpperCase(),
        description: `Changes will take effect shortly.`,
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
      onClose();
    },
    onError: (error: any) => {
      toast({
        title: `${error.message.toUpperCase()}`,
        description: `Code: ${error.code} | response status: ${error.response.status}`,
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
  });

  return (
    <>
      <Heading size="sm" textTransform={'uppercase'} display="flex" justifyContent="center" mb="1">
        System Reset
      </Heading>
      <StatGroup mt="4" mb="4" textAlign="center">
        <Stat textAlign="center">
          <StatLabel>Node</StatLabel>
          <StatNumber>{nodeId}</StatNumber>
        </Stat>
      </StatGroup>

      {checkRoleAccess && (
        <Button
          type="submit"
          width="100%"
          marginY={4}
          colorScheme="blue"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            onOpen();
          }}
        >
          Reset node
        </Button>
      )}
      <Divider />
      <CustomModal
        isOpen={isOpen}
        onClose={onClose}
        title={'Reset node'}
        submitText={'Confirm'}
        size={'xl'}
        onSubmit={() => {
          ResetDevice(nodeId);
        }}
      >
        {isLoading ? (
          <Loader />
        ) : (
          <p>
            Are you sure you want to reset the <strong>{nodeId}</strong> node?
          </p>
        )}
        <Divider mt={'2rem'} />
      </CustomModal>
    </>
  );
};

export default ResetDevice;
