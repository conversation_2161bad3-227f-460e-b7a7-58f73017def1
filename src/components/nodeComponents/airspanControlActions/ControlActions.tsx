import { ChevronDownIcon } from '@chakra-ui/icons';
import { AlertDialogFooter, Box, Button, Icon, MenuItem, MenuList, useDisclosure, useToast } from '@chakra-ui/react';

import { MenuButton } from '@chakra-ui/react';

import { Menu, Text } from '@chakra-ui/react';

import { AlertDialogBody, Flex } from '@chakra-ui/react';

import { AlertDialog, AlertDialogContent, AlertDialogHeader, AlertDialogOverlay } from '@chakra-ui/react';
import React, { useState, useRef, useEffect, useCallback } from 'react';
import { AUTH_TOKEN_KEY, READ_ONLY_ACCESS_ROLES } from '../../../data/constants';
import { MdAutorenew, MdLock, MdLockOpen } from 'react-icons/md';
import {
  ServerActionType,
  useGetActionApiCall,
  usePostActionRadioApiCall,
} from '../../../pages/CellOverview/hooks/services/use_Orc_ServerTasks';
import _ from 'lodash';
import { ServerActionGetDataType, ServerActionPostDataType } from '../../../types/orchestrator.types';
import useLogin from '../../../hooks/useLogin';
import { ActionPhase } from '../../../pages/CellOverview/hooks/services/use_Orc_ServerTasks';
import { UsePostActionParams } from '../du-cuManager/RadioCard';
import { statusIcon } from '../server/acp/ServerTasks';

type ActionColorType = Record<ServerActionType, string>;
type ActionIconType = Record<ServerActionType, JSX.Element>;

const actionButtonColor: ActionColorType = {
  [ServerActionType.RESET]: 'red',
  [ServerActionType.LOCK]: 'orange',
  [ServerActionType.UNLOCK]: 'green',
};
const actionIcons: ActionIconType = {
  [ServerActionType.RESET]: <MdAutorenew size="25" />,
  [ServerActionType.LOCK]: <MdLock size="25" />,
  [ServerActionType.UNLOCK]: <MdLockOpen size="25" />,
};

const TaskStatus = {
  CREATED: 'Created inactive',
  WAITING: 'Active, not yet running',
  BLOCKED: 'Unable to proceed',
  RUNNING: 'Active and running',
  RESULTS: 'Results available',
};

const getTaskStatus = (operation: ServerActionGetDataType[]) => {
  const tasks = operation[0].info;
  if (typeof tasks === 'string') {
    return statusIcon['pending'];
  } else if (_.isEmpty(tasks)) {
    return statusIcon['pending'];
  } else {
    const sortedTasks = tasks.sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime());
    for (const task of sortedTasks) {
      if (task.progress === TaskStatus.RESULTS) {
        if (!_.isString(task.data) && task.data.detail && task.data?.detail.includes('successfully')) {
          return statusIcon['completed'];
        } else {
          return (
            <Flex justifyContent="space-between">
              <Flex>{statusIcon['failed']}</Flex>
              <Flex>
                <Text whiteSpace="normal" ml="3" color="red">
                  {typeof task.data === 'string' ? task.data : task.data?.detail}
                </Text>
              </Flex>
            </Flex>
          );
        }
      } else if (task.progress === TaskStatus.RUNNING) {
        return statusIcon['running'];
      } else {
        return statusIcon['pending'];
      }
    }
  }
};

const usePostAction = ({ local_node_id, selectedOption, actionType }: UsePostActionParams) => {
  const [triggerPostAction, setTriggerPostAction] = useState<boolean>(false);
  const {
    status: postActionStatus,
    isLoading: isPostActionExecuting,
    data: postActionData,
    error: postActionError,
  }: {
    status: 'error' | 'success' | 'loading';
    isLoading: boolean;
    data: ServerActionPostDataType[] | undefined;
    error: any;
  } = usePostActionRadioApiCall(local_node_id, selectedOption, triggerPostAction, actionType as ServerActionType);

  useEffect(() => {
    if (triggerPostAction) {
      setTriggerPostAction(false);
    }
  }, [triggerPostAction]);

  const initiatePostAction = useCallback(() => {
    if (selectedOption && actionType) {
      setTriggerPostAction(true);
    }
  }, [selectedOption, actionType]);

  return { initiatePostAction, postActionData, postActionError };
};

const ActionButton = ({
  actionType,
  onClick,
  isDisabled,
  dataTestId,
}: {
  actionType: ServerActionType;
  onClick: () => void;
  isDisabled: boolean;
  dataTestId: string;
}) => {
  return (
    <Button
      colorScheme={actionButtonColor[actionType]}
      variant="solid"
      onClick={onClick}
      size="sm"
      isDisabled={isDisabled}
      data-testid={dataTestId}
    >
      <Text>{actionType}</Text>
      {actionIcons[actionType]}
    </Button>
  );
};

const ControlActions = ({ local_node_id }: { local_node_id: string }) => {
  const { checkApplicationAccess } = useLogin(AUTH_TOKEN_KEY);
  const toast = useToast();
  const cancelRef = useRef<HTMLButtonElement>(null);
  const isReadOnlyAccess = checkApplicationAccess(READ_ONLY_ACCESS_ROLES);
  const [actionType, setActionType] = useState<ServerActionType | ''>('');
  const [triggerGetAction, setTriggerGetAction] = useState<boolean>(false);
  const [selectedOption, setSelectedOption] = useState<ActionPhase | ''>('');
  // it is absolutely very necessary to hold postActionHookData in a useState variable,
  // reason being when AlertDialog is opened then all the data returned by useQuery are set to undefined
  // dont know why but this is the only way to make it work
  const [postActionData, setPostActionData] = useState<ServerActionPostDataType[] | null>(null);

  const {
    initiatePostAction,
    postActionData: postActionHookData,
    postActionError,
  } = usePostAction({
    local_node_id,
    selectedOption,
    actionType: actionType as ServerActionType,
    toast,
  });

  const {
    isOpen: isSelectReasonAlertOpen,
    onOpen: onSelectReasonAlertOpen,
    onClose: onSelectReasonAlertClose,
  } = useDisclosure();

  const {
    status: getActionStatus,
    isLoading: isGetActionExecuting,
    data: getActionData,
  } = useGetActionApiCall(postActionData, triggerGetAction);

  useEffect(() => {
    if (!_.isUndefined(postActionHookData && postActionHookData[0]?.uid)) {
      setTriggerGetAction(true);
      toast({
        title: `${postActionHookData && postActionHookData[0].type} Action created successfully.`,
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
      setPostActionData(() => postActionHookData || null);
    }
  }, [postActionHookData, toast]);

  useEffect(() => {
    if (postActionError) {
      toast({
        title: 'Error',
        description: postActionError.message,
        status: 'error',
        duration: 9000,
        isClosable: true,
      });
    }
  }, [postActionError, toast]);

  useEffect(() => {
    if (isSelectReasonAlertOpen) {
      setSelectedOption('');
    }
  }, [isSelectReasonAlertOpen]);

  const handleSelect = (value: string) => {
    if (Object.values(ActionPhase).includes(value as ActionPhase)) {
      setSelectedOption((prevState) => (prevState === value ? '' : (value as ActionPhase)));
    }
  };

  const handleActionButtonClick = (actionType: ServerActionType) => {
    onSelectReasonAlertOpen();
    setActionType(actionType);
  };

  const handleConfirmClick = () => {
    onSelectReasonAlertClose();
    initiatePostAction();
  };

  const handleCancelClick = () => {
    onSelectReasonAlertClose();
  };

  return (
    <>
      <Box width="100%" display="flex" flexDirection="row" justifyContent="space-between" alignItems="center" gap={4}>
        <ActionButton
          actionType={ServerActionType.RESET}
          onClick={() => handleActionButtonClick(ServerActionType.RESET)}
          isDisabled={isReadOnlyAccess}
          dataTestId="reset-button"
        />
        <ActionButton
          actionType={ServerActionType.LOCK}
          onClick={() => handleActionButtonClick(ServerActionType.LOCK)}
          isDisabled={isReadOnlyAccess}
          dataTestId="lock-button"
        />
        <ActionButton
          actionType={ServerActionType.UNLOCK}
          onClick={() => handleActionButtonClick(ServerActionType.UNLOCK)}
          isDisabled={isReadOnlyAccess}
          dataTestId="unlock-button"
        />
      </Box>

      {postActionData && (
        <Box display="flex" flexDirection="row" justifyContent="flex-start" mt="4">
          <Text display="flex" justifyContent="space-between" alignItems="center" colorScheme="red" size="sm" mt="1">
            {postActionData[0].type}
          </Text>
          <Box color={actionButtonColor[postActionData[0].type as ServerActionType]}>
            {actionIcons[postActionData[0].type as ServerActionType]}
          </Box>
          <Text ml="2" mr="2" mt="2">
            Action
          </Text>
          <Box mt="1">{getActionData && getTaskStatus(getActionData)}</Box>
        </Box>
      )}

      <AlertDialog
        isOpen={isSelectReasonAlertOpen}
        onClose={onSelectReasonAlertClose}
        isCentered
        leastDestructiveRef={cancelRef}
        size="lg"
      >
        <AlertDialogOverlay>
          <AlertDialogContent
          // if you want customise dimensions
          // sx={{
          //   height: '350px',
          //   width: '500px',
          //   maxWidth: '100%',
          // }}
          >
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              <Flex flexDirection="row">Select a reason for the {actionType} action</Flex>
            </AlertDialogHeader>
            <AlertDialogBody>
              <Menu>
                <MenuButton
                  as={Button}
                  rightIcon={<Icon as={ChevronDownIcon} />}
                  variant="outline"
                  colorScheme={actionButtonColor[actionType as keyof typeof actionButtonColor]}
                  size="sm"
                >
                  {selectedOption || 'Click to select an option'}
                </MenuButton>
                <MenuList>
                  {Object.entries(ActionPhase).map(([key, value]) => (
                    <MenuItem
                      key={key}
                      onClick={() => handleSelect(value)}
                      _hover={{
                        backgroundColor: `${actionButtonColor[actionType as keyof typeof actionButtonColor]}.400`,
                        color: 'white',
                      }}
                    >
                      {value}
                    </MenuItem>
                  ))}
                </MenuList>
              </Menu>
            </AlertDialogBody>
            <AlertDialogFooter>
              <Button onClick={handleCancelClick} colorScheme="blue" ref={cancelRef} data-testid="cancel-button">
                Cancel
              </Button>
              <Button
                colorScheme={actionButtonColor[actionType as keyof typeof actionButtonColor]}
                onClick={handleConfirmClick}
                isDisabled={_.isEmpty(selectedOption)}
                ml={3}
                data-testid="confirm-button"
              >
                Confirm
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
    </>
  );
};

export default ControlActions;
