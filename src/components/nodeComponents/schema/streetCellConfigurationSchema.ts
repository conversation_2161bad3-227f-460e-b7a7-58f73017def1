import { z } from 'zod';
import { MONITOR_STATE } from '../../../data/constants';

const stateSchema = z
  .nativeEnum(MONITOR_STATE, {
    errorMap: () => {
      return { message: 'Please select an option' };
    },
  })
  .or(z.literal(''))
  .optional();
export const isValidIP = (str: string) => {
  const regex =
    /^(25[0-5]|2[0-4][0-9]|[0-1]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[0-1]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[0-1]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[0-1]?[0-9][0-9]?)$/;
  return regex.test(str);
};
export const streetCellConfigurationSchema = z
  .object({
    bluwireless_ip_address: z
      .union([
        z.literal(''), // Accepts an empty string
        z.string().ip(), // Accepts a valid IP address
      ])
      .optional(),
    fibrolan_ip_address: z.union([z.literal(''), z.string().ip()]).optional(),
    fibrolan_switch_monitored: stateSchema,
    bw_mmwave_monitored: stateSchema,
  })
  .refine(
    (data) => {
      if (data.fibrolan_switch_monitored && (!data.fibrolan_ip_address || !isValidIP(data.fibrolan_ip_address))) {
        return false;
      }
      return true;
    },
    {
      path: ['fibrolan_ip_address'],
      message: 'If monitored field is selected, the respective IP address field is mandatory and should be a valid IP.',
    }
  )
  .refine(
    (data) => {
      // If bw_mmwave_monitored is selected, then bluwireless_ip_address should be a valid IP
      if (data.bw_mmwave_monitored && (!data.bluwireless_ip_address || !isValidIP(data.bluwireless_ip_address))) {
        return false;
      }
      return true;
    },
    {
      path: ['bluwireless_ip_address'],
      message: 'If monitored field is selected, the respective IP address field is mandatory and should be a valid IP.',
    }
  )
  .refine(
    (data) => {
      if (data.fibrolan_ip_address && isValidIP(data.fibrolan_ip_address) && !data.fibrolan_switch_monitored) {
        return false;
      }
      return true;
    },
    {
      path: ['fibrolan_switch_monitored'],
      message: 'If IP address is provided and valid, the monitored field is mandatory.',
    }
  )
  .refine(
    (data) => {
      // If bluwireless_ip_address is not empty and is a valid IP, then bw_mmwave_monitored should not be empty
      if (data.bluwireless_ip_address && isValidIP(data.bluwireless_ip_address) && !data.bw_mmwave_monitored) {
        return false;
      }
      return true;
    },
    {
      path: ['bw_mmwave_monitored'],
      message: 'If IP address is provided and valid, the monitored field is mandatory.',
    }
  );
