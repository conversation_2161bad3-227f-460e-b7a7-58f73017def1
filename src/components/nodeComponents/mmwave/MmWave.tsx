import { ErrorBoundary } from 'react-error-boundary';
import { Box, Card } from '@chakra-ui/react';
import MmWaveCard from './MmWaveCard';
import { ErrorBoundaryFallback, ErrorBoundaryLogError } from '../../errorComponents/ErrorBoundaryFallback';
import NodeComponentErrorCard from '../../errorComponents/NodeComponentErrorCard';
import { NODE_COMPONENT_TITLES } from '../../../data/constants';
import ComingSoon from '../../comingSoon/ComingSoon';
import { Falsey } from 'lodash';

type MmWaveProps = {
  nodeComponentsData: any;
  nodeListComponentsData: any;
  bluwirelessInstanceAddress: string | Falsey;
  mmWaveHaveData: boolean;
};

const MmWave = ({
  nodeComponentsData,
  nodeListComponentsData,
  bluwirelessInstanceAddress,
  mmWaveHaveData,
}: MmWaveProps) => {
  return (
    <Box width={'50%'}>
      {nodeComponentsData.sc_v1?.bw_mmwave.error || nodeComponentsData.sc_v1?.bw_mmwave.error === null ? (
        <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
          <NodeComponentErrorCard
            id={nodeListComponentsData?.find((item: any) => item.component_type === 'MMWAVE')?.component_id}
            errorData={nodeComponentsData.sc_v1?.bw_mmwave}
            compName={NODE_COMPONENT_TITLES.MMWAVE}
            testId="node-comp-error-card"
            linkText="BluWireless config server"
            linkUrl={`${bluwirelessInstanceAddress}/Management?DBID=`}
            marginSpace="8"
          />
        </ErrorBoundary>
      ) : (
        <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
          {mmWaveHaveData ? (
            <MmWaveCard
              data={nodeComponentsData.sc_v1?.bw_mmwave}
              id={nodeComponentsData.sc_v1?.bw_mmwave.component_id}
              bluwirelessInstanceAddress={bluwirelessInstanceAddress}
            />
          ) : (
            <ComingSoon componentName="mmWave" />
          )}
        </ErrorBoundary>
      )}
    </Box>
  );
};

export default MmWave;
