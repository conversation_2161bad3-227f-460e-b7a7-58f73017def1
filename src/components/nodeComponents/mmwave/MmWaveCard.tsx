import { ArrowDownIcon, ArrowUpIcon, ExternalLinkIcon } from '@chakra-ui/icons';
import {
  Box,
  Button,
  Card,
  CardBody,
  CardHeader,
  Heading,
  Link,
  Stack,
  StackDivider,
  Tooltip,
  useDisclosure,
  Text,
  Flex,
  Icon,
} from '@chakra-ui/react';
import { ColumnDef } from '@tanstack/react-table';
import React, { useState } from 'react';
import { NODE_COMPONENT_TITLES, greenBoxShadowColor } from '../../../data/constants';
import { getStatusColor } from '../../../pages/CellOverview/hooks/useStatus';
import { BWMmwave, FlattenedFastPath, Interface, FibroInterface, FibroClock } from '../../../types/orchestrator.types';
import { bitsToOptimal } from '../../../utils/helpers';
import StatusComponent from '../../icons/StatusIcon';
import { DynamicStats } from '../common/DynamicStats';
import ModalDataTable from '../common/ModalDataTable';
import { Falsey } from 'lodash';
import { BsArrowReturnRight } from 'react-icons/bs';
import { getComponentBgColor } from '../utils';
import { ComponentTypeEnum } from '../../../services/types';

interface MmWaveIndexProps {
  data: BWMmwave;
  id?: string | number;
  bluwirelessInstanceAddress: string | Falsey;
}
const MmWaveIndex: React.FC<MmWaveIndexProps> = ({ data, id, bluwirelessInstanceAddress }) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [showContent, setShowContent] = useState(false);
  const statusColor = getStatusColor(data.status ?? 'unknown');
  const bgColor = getComponentBgColor(statusColor);
  //Just for testing
  const nodeId = 'fc8514fc-63bf-4831-a85b-c5c89ad0c868';
  const columns = React.useMemo<ColumnDef<Interface | FlattenedFastPath | FibroClock | FibroInterface>[]>(
    () => [
      {
        header: 'Index',
        accessorKey: 'ifIndex',
        id: 'ifIndex',
      },
      {
        header: 'Description',
        accessorKey: 'ifDescr',
        id: 'ifDescr',
        cell: ({ getValue }) => {
          const value = getValue() as string;
          const shouldShowTooltip = value && value.length > 20;
          const content = (
            <Box isTruncated maxWidth="20ch">
              {value}
            </Box>
          );
          return shouldShowTooltip ? <Tooltip label={value}>{content}</Tooltip> : content;
        },
      },
      {
        header: 'Type',
        accessorKey: 'ifType',
        id: 'ifType',
      },
      {
        header: 'MTU',
        accessorKey: 'ifMtu',
        id: 'ifMtu',
      },
      {
        header: 'Speed',
        accessorKey: 'ifSpeed',
        id: 'ifSpeed',
        cell: ({ getValue }) => <>{bitsToOptimal(Number(getValue() ?? 0))}</>,
      },
      {
        header: 'Physical Address',
        accessorKey: 'ifPhysAddress',
        id: 'ifPhysAddress',
      },
      {
        header: 'inbound',
        accessorKey: 'ifIn1SecRate',
        id: 'ifIn1SecRate',
        cell: ({ getValue }) => {
          const value = Number(getValue()) || 0;
          return <>{value === 0 ? value + ' Bytes' : value + ' Mbps'}</>;
        },
      },
      {
        header: 'outbound',
        accessorKey: 'ifOut1SecRate',
        id: 'ifOut1SecRate',
        cell: ({ getValue }) => {
          const value = Number(getValue()) || 0;
          return <>{value === 0 ? value + ' Bytes' : value + ' Mbps'}</>;
        },
      },
      {
        header: 'Admin Status',
        accessorKey: 'ifAdminStatus',
        id: 'ifAdminStatus',
        cell: ({ getValue }) => {
          if (getValue() === null) return '-';
          return (
            <Box boxSize="5">
              {getValue() === 'up' ? (
                <ArrowUpIcon boxSize="5" color={'green'}>
                  getValue()
                </ArrowUpIcon>
              ) : (
                <ArrowDownIcon boxSize="5" color={'red'}>
                  getValue()
                </ArrowDownIcon>
              )}
            </Box>
          );
        },
      },
      {
        header: 'Operational Status',
        accessorKey: 'ifOperStatus',
        id: 'ifOperStatus',
        cell: ({ getValue }) => {
          if (getValue() === null) return '-';
          return (
            <Box boxSize="5">
              {getValue() === 'up' ? (
                <ArrowUpIcon boxSize="5" color={'green'}>
                  getValue()
                </ArrowUpIcon>
              ) : (
                <ArrowDownIcon boxSize="5" color={'red'}>
                  getValue()
                </ArrowDownIcon>
              )}
            </Box>
          );
        },
      },
    ],
    []
  );
  return (
    <>
      <Card width="100%" borderRadius="lg">
        <CardHeader
          _hover={{
            bg: bgColor,
            cursor: 'pointer',
            boxShadow: `0 0 12px ${bgColor}`,
          }}
          transition="background-color 0.2s ease, box-shadow 0.2s ease"
          borderRadius="lg"
          onClick={() => setShowContent(!showContent)}
          display="flex"
          alignItems="center"
          justifyContent="space-between"
        >
          <Heading size="xs" fontWeight="500" display="flex" justifyContent="space-between">
            <Flex alignItems="center">
              <Icon as={BsArrowReturnRight} />
              <Text ml={2}>{NODE_COMPONENT_TITLES.MMWAVE}</Text>
            </Flex>
          </Heading>
          <StatusComponent
            dataTestId="cell-main-table-status-icon"
            status={data.status}
            boxSize="sm"
            color={statusColor}
            component_type={ComponentTypeEnum.BLUWIRELESS}
            component_id={id}
          />
        </CardHeader>
        {showContent && (
          <CardBody borderTop={'1px solid #e2e2e2'}>
            <Stack divider={<StackDivider />} spacing="4">
              <DynamicStats data={data} componentId={id ?? ''} />
              {data?.interfaces?.length && <Button onClick={onOpen}>More Data</Button>}
              {bluwirelessInstanceAddress ? (
                <Link
                  textAlign="center"
                  isExternal
                  rel="noopener noreferrer"
                  href={`http://${bluwirelessInstanceAddress}/devices/${nodeId}`}
                >
                  BluWireless config server
                  <ExternalLinkIcon mx={'2px'} />
                </Link>
              ) : (
                <Text>No manger Url</Text>
              )}
              {/* <Link
                textAlign="center"
                isExternal
                rel="noopener noreferrer"
                href={`http://${bluwirelessInstanceAddress}/devices/${nodeId}`}
              >
                BluWireless config server
                <ExternalLinkIcon mx={'2px'} />
              </Link> */}
            </Stack>
          </CardBody>
        )}
      </Card>

      {/* Modal */}
      <ModalDataTable isOpen={isOpen} onClose={onClose} data={data?.interfaces ?? []} columns={columns} />
    </>
  );
};

export default MmWaveIndex;
