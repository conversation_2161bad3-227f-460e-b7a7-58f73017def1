import { ErrorBoundary } from 'react-error-boundary';
import { Box } from '@chakra-ui/react';
import { ErrorBoundaryFallback, ErrorBoundaryLogError } from '../../errorComponents/ErrorBoundaryFallback';
import NodeComponentErrorCard from '../../errorComponents/NodeComponentErrorCard';
import { NODE_COMPONENT_TITLES } from '../../../data/constants';
import ComingSoon from '../../comingSoon/ComingSoon';
import FibroLanSwitch from './FibroLanSwitch';
import Loader from '../../loader/Loader';
import useGetSwitchNodeDetailsByNodeId from '../../../pages/CellOverview/hooks/services/use_GetSwitchNodeDetailsByNodeId';
import { Falsey } from 'lodash';
import JuniperSwitch from './JuniperSwitch';
import MosolabsSwitch from './MosolabSwitch.tsx';
import { isEmpty } from 'lodash';
// import { FibrolanSwitchDataMock } from '../../../services/mocks/fibrolanDataMock';
// import { SwitchNodeMockData } from '../../../services/mocks/juniperSwitchBoxMock';

type SwitchProps = {
  nodeComponentsData?: any | null;
  nodeListComponentsData?: any | null | undefined;
  bluwirelessInstanceAddress: string | Falsey;
  switchHaveData: boolean;
  queryNodeId?: string;
  caller?: string;
  nodeType: string;
};

const Switch = ({
  nodeComponentsData,
  nodeListComponentsData,
  bluwirelessInstanceAddress,
  switchHaveData,
  queryNodeId,
  caller,
  nodeType,
}: SwitchProps) => {
  // to restrict SwitchNode query to trigger only when this returns true
  const shouldFetchData =
    nodeType === 'Switch' ||
    (nodeType === 'StreetCell' && (caller === 'Nodes' || caller === 'Cells') && !nodeComponentsData);

  const { data: switchNodeData, isLoading: isLoadingSwitchNode } = useGetSwitchNodeDetailsByNodeId(
    queryNodeId || '',
    shouldFetchData
  );

  if (isLoadingSwitchNode) return <Loader />;
  const showErrorCard =
    nodeComponentsData?.fibrolan_switch?.error === null ||
    nodeComponentsData?.fibrolan_switch?.error ||
    switchNodeData?.fibrolan_switch?.error ||
    switchNodeData?.fibrolan_switch?.error === null ||
    switchNodeData?.juniper_switch?.error ||
    switchNodeData?.juniper_switch?.error === null ||
    switchNodeData?.mosolabs_switch?.error ||
    switchNodeData?.mosolabs_switch?.error === null;

  const boxWidth = nodeType === 'Switch' || (nodeType === 'StreetCell' && caller === 'Nodes') ? '100%' : '50%';

  const isJuniperSwitch = nodeType === 'Switch' && !isEmpty(switchNodeData?.juniper_switch);
  const isFibroLanSwitch = nodeType === 'Switch' && !isEmpty(switchNodeData?.fibrolan_switch);
  const isMosoblabsSwitch = nodeType === 'Switch' && !isEmpty(switchNodeData?.mosolabs_switch);

  return (
    <>
      <Box width={boxWidth} marginRight="4" data-testid="cells-node-SwitchComponents">
        {showErrorCard ? (
          <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
            <NodeComponentErrorCard
              id={nodeListComponentsData?.find((item: any) => item.component_type === 'SWITCH')?.component_id}
              errorData={
                switchNodeData?.fibrolan_switch?.error ||
                switchNodeData?.juniper_switch?.error ||
                switchNodeData?.mosolabs_switch?.error
              }
              compName={NODE_COMPONENT_TITLES.SWITCH}
              testId="node-comp-error-card"
              linkText="BluWireless config server"
              linkUrl={`${bluwirelessInstanceAddress}` ? `/Management?DBID=}` : null}
              marginSpace="8"
            />
          </ErrorBoundary>
        ) : (
          <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
            {switchHaveData && isFibroLanSwitch ? (
              <FibroLanSwitch
                caller={caller}
                data={nodeComponentsData ? nodeComponentsData?.fibrolan_switch : switchNodeData?.fibrolan_switch}
                // data={FibrolanSwitchDataMock}
                // id={nodeComponentsData.sc_v1?.fibrolan_switch.component_id}
                id={queryNodeId}
                marginSpace="8"
              />
            ) : isJuniperSwitch ? (
              <JuniperSwitch data={switchNodeData?.juniper_switch} caller={caller} />
            ) : isMosoblabsSwitch ? (
              <MosolabsSwitch data={switchNodeData?.mosolabs_switch} caller={caller} />
            ) : (
              <ComingSoon componentName="SWITCH" />
            )}
          </ErrorBoundary>
        )}
      </Box>
    </>
  );
};

export default Switch;
