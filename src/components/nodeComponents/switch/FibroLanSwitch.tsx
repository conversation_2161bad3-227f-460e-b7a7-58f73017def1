import {
  Box,
  <PERSON>ton,
  Card,
  CardBody,
  CardHeader,
  <PERSON>lex,
  <PERSON><PERSON>,
  <PERSON>ack,
  <PERSON>ackD<PERSON>ider,
  Tooltip,
  useDisclosure,
  Text,
  Icon,
} from '@chakra-ui/react';
import StatusComponent from '../../icons/StatusIcon';
import { ArrowDownIcon, ArrowUpIcon } from '@chakra-ui/icons';
import { ColumnDef } from '@tanstack/react-table';
import React, { useState } from 'react';
import { DynamicStats } from '../common/DynamicStats';
import ModalDataTable from '../common/ModalDataTable';
import {
  FibroClock,
  FastPath,
  FlattenedFastPath,
  FibroInterface,
  FibrolanSwitch,
  Interface,
} from '../../../types/orchestrator.types';
import { NODE_COMPONENT_TITLES } from '../../../data/constants';
import { bitsToOptimal } from '../../../utils/helpers';
import { getStatusColor } from '../../../pages/CellOverview/hooks/useStatus';
import { BsArrowReturnRight } from 'react-icons/bs';
import { getComponentBgColor } from '../utils';
import { ComponentTypeEnum } from '../../../services/types';
import { FaChartLine } from 'react-icons/fa';

interface FibroLanSwitchProps {
  data: FibrolanSwitch;
  id?: string | number;
  marginSpace?: string | number;
  caller?: string;
  nodeType?: string;
}

const FibroLanSwitch: React.FC<FibroLanSwitchProps> = ({ data, id: queryNodeId, marginSpace, caller, nodeType }) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [hasError, setHasError] = useState<boolean>(false);
  // when to show the header based on caller and nodeType
  const showHeader = !(caller === 'Nodes' || (undefined && nodeType === 'Switch'));
  // show content if header is not being shown
  const [showContent, setShowContent] = useState(!showHeader);
  const columns = React.useMemo<ColumnDef<Interface | FlattenedFastPath | FibroInterface | FibroClock>[]>(
    () => [
      {
        header: 'Index',
        accessorKey: 'ifIndex',
        id: 'ifIndex',
      },
      {
        header: 'Description',
        accessorKey: 'ifDescr',
        id: 'ifDescr',
        cell: ({ getValue }) => {
          const value = getValue() as string;
          const shouldShowTooltip = value && value.length > 20;
          const content = (
            <Box isTruncated maxWidth="20ch">
              {value}
            </Box>
          );
          return shouldShowTooltip ? <Tooltip label={value}>{content}</Tooltip> : content;
        },
      },
      {
        header: 'Type',
        accessorKey: 'ifType',
        id: 'ifType',
      },
      {
        header: 'MTU',
        accessorKey: 'ifMtu',
        id: 'ifMtu',
      },
      {
        header: 'Speed',
        accessorKey: 'ifSpeed',
        id: 'ifSpeed',
        cell: ({ getValue }) => {
          return <>{bitsToOptimal(Number(getValue()))}</>;
        },
      },
      {
        header: 'Physical Address',
        accessorKey: 'ifPhysAddress',
        id: 'ifPhysAddress',
      },
      {
        header: 'inbound',
        accessorKey: 'ifIn1SecRate',
        id: 'ifIn1SecRate',
        cell: ({ getValue }) => {
          const value = Number(getValue()) || 0;
          return <>{value === 0 ? value + ' Bytes' : value + ' Mbps'}</>;
        },
      },
      {
        header: 'outbound',
        accessorKey: 'ifOut1SecRate',
        id: 'ifOut1SecRate',
        cell: ({ getValue }) => {
          const value = Number(getValue()) || 0;
          return <>{value === 0 ? value + ' Bytes' : value + ' Mbps'}</>;
        },
      },
      {
        header: () => (
          <Box textAlign="center">
            Admin
            <br />
            Status
          </Box>
        ),
        accessorKey: 'ifAdminStatus',
        id: 'ifAdminStatus',
        cell: ({ getValue }) => {
          if (getValue() === null) return '-';
          return (
            <Box boxSize="5">
              {getValue() === 'up' ? (
                <ArrowUpIcon boxSize="5" color="green" data-testid="arrowUp_grren">
                  getValue()
                </ArrowUpIcon>
              ) : (
                <ArrowDownIcon boxSize="5" color="red" data-testid="arrowDown_red">
                  getValue()
                </ArrowDownIcon>
              )}
            </Box>
          );
        },
      },
      {
        header: () => (
          <Box textAlign="center">
            Oper
            <br />
            Status
          </Box>
        ),
        accessorKey: 'ifOperStatus',
        id: 'ifOperStatus',
        cell: ({ getValue }) => {
          if (getValue() === null) return '-';
          return (
            <Box boxSize="5">
              {getValue() === 'up' ? (
                <ArrowUpIcon boxSize="5" color={'green'}>
                  getValue()
                </ArrowUpIcon>
              ) : (
                <ArrowDownIcon boxSize="5" color={'red'}>
                  getValue()
                </ArrowDownIcon>
              )}
            </Box>
          );
        },
      },
      {
        header: '',
        accessorKey: 'chart',
        id: 'chart',
        cell: ({ row }) => {
          const data = row.original;

          if ('ifOperStatus' in data && 'ifAdminStatus' in data) {
            const ifOperStatus = data.ifOperStatus;
            const ifAdminStatus = data.ifAdminStatus;
            const isUp = ifOperStatus === 'up' && ifAdminStatus === 'up';

            return (
              <Box boxSize="10" p="2">
                <Icon as={FaChartLine} color={isUp ? 'currentColor' : 'gray.400'} w="7" h="7" />
              </Box>
            );
          }
        },
        enableSorting: false,
      },
    ],
    []
  );
  const statusColor = getStatusColor(data?.status ?? 'unknown');
  const bgColor = getComponentBgColor(statusColor);
  return (
    <>
      <Card width="100%" marginRight={marginSpace} borderRadius="lg">
        {showHeader && (
          <CardHeader
            onClick={() => setShowContent(!showContent)}
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            _hover={{
              bg: bgColor,
              cursor: 'pointer',
              boxShadow: `0 0 12px ${bgColor}`,
            }}
            transition="background-color 0.2s ease, box-shadow 0.2s ease"
            borderRadius="lg"
          >
            <Heading size="xs" fontWeight="500" display="flex" justifyContent="space-between">
              <Flex alignItems="center">
                <Icon as={BsArrowReturnRight} />
                <Text ml={2}>{NODE_COMPONENT_TITLES.SWITCH}</Text>
              </Flex>
            </Heading>
            <StatusComponent
              dataTestId="cell-main-table-status-icon"
              status={data?.status}
              boxSize="sm"
              color={statusColor}
              component_type={ComponentTypeEnum.FIBROLAN}
              component_id={data.component_id}
            />
          </CardHeader>
        )}
        {showContent && (
          <CardBody borderTop={'1px solid #e2e2e2'}>
            <Stack spacing="4">
              <Text fontSize="x-large" my="4">
                Fibrolan Switch
              </Text>
              <DynamicStats
                data={data}
                setHasError={setHasError}
                componentId={data?.component_id}
                clockData={data?.sync?.clocks}
              />
              {data?.interfaces?.length && !data?.error && !hasError ? (
                <Button onClick={onOpen}>Interface Details</Button>
              ) : null}
            </Stack>
          </CardBody>
        )}
      </Card>

      {/* Modal */}
      <ModalDataTable
        isOpen={isOpen}
        onClose={onClose}
        columns={columns}
        data={data?.interfaces ?? []}
        caller={'interface'}
        subComponentProps={{
          component_id: data?.component_id as string,
          component_type: ComponentTypeEnum.FIBROLAN,
        }}
      />
    </>
  );
};

export default FibroLanSwitch;
