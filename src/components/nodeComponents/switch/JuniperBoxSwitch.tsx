import { Box, Card, CardBody, Flex, Table, Tbody, Td, Text, Th, Thead, Tr } from '@chakra-ui/react';
import React, { Fragment, useState } from 'react';
import _ from 'lodash';
import { DynamicStatsV2 } from '../common/DynamicStatsV2';

interface JuniperBoxSwitchProps {
  data?: any;
}

const JuniperBoxSwitch: React.FC<JuniperBoxSwitchProps> = ({ data }) => {
  const [hasError, setHasError] = useState<boolean>(false);
  // New state to track which row's contents are being displayed
  const [expandedRow, setExpandedRow] = useState<number | null>(null);
  const [contentIndex, setContentIndex] = useState<number | null>(null);

  const columns = React.useMemo(
    () => [
      {
        header: 'Containers Description',
        accessorKey: 'jnxContainersDescr',
        id: 'jnxContainersDescr',
      },
      {
        header: 'Containers View',
        accessorKey: 'jnxContainersView',
        id: 'jnxContainersView',
      },
      {
        header: 'Contents Description',
        accessorKey: 'jnxContentsDescr',
        id: 'jnxContentsDescr',
      },
    ],
    []
  );

  return (
    <>
      <DynamicStatsV2 data={data} setHasError={setHasError} isJuniperBox={true} />
      {!hasError && (
        <Box padding="10px" width="96.1%">
          <Table variant="unstyled" size="sm">
            <Thead>
              <Tr>
                {columns.map((column) => (
                  <Th key={column.id} background="gray.50">
                    <Flex>
                      <Box ml="2">{column.header}</Box>
                    </Flex>
                  </Th>
                ))}
              </Tr>
            </Thead>
            <Tbody>
              {data?.containers?.map((rowData: any, rowIndex: number) => (
                <Fragment key={rowIndex}>
                  <Tr key={rowIndex}>
                    <Td
                      width="10%"
                      p="4"
                      textAlign="left"
                      borderRight="1px solid rgba(0, 0, 0, 0.1)"
                      _last={{ borderRight: 'none' }}
                      data-testid={`cell-jnxContainersDescr-${rowIndex}`}
                    >
                      <Text>{rowData.jnxContainersDescr}</Text>
                    </Td>
                    <Td
                      width="10%"
                      p="4"
                      textAlign="left"
                      borderRight="1px solid rgba(0, 0, 0, 0.1)"
                      _last={{ borderRight: 'none' }}
                    >
                      <Text>
                        {rowData.jnxContainersView?.length > 1
                          ? rowData.jnxContainersView.join(' and ')
                          : rowData.jnxContainersView[0]}
                      </Text>
                    </Td>
                    <Td width="10%" p="4" textAlign="left" _last={{ borderRight: 'none' }}>
                      {rowData?.contents?.map((content: any, index: number) => {
                        const isExpand = expandedRow === rowIndex && contentIndex === index;
                        return (
                          <Fragment key={index}>
                            <Box
                              key={index}
                              _hover={{
                                background: isExpand ? '' : 'brand.300',
                                cursor: 'pointer',
                              }}
                              as="span"
                              width="fit-content"
                              color={isExpand ? 'brand.300' : ''}
                              textDecoration="underline"
                              onClick={() => (setExpandedRow(isExpand ? null : rowIndex), setContentIndex(index))}
                            >
                              {content?.detail?.jnxContentsDescr || content?.detail?.jnxContentsChassisDescr}
                            </Box>
                            {index < rowData?.contents?.length - 1 ? ', ' : ''}
                          </Fragment>
                        );
                      })}
                    </Td>
                  </Tr>
                  {expandedRow === rowIndex && !_.isNil(contentIndex) && (
                    <Tr key={`sub-row-${rowIndex}`}>
                      <Td paddingX="28" paddingY="4" colSpan={3} data-testid={`expanded-content-${rowIndex}`}>
                        <Card width="100%" borderRadius="lg">
                          <CardBody borderTop="1px solid #e2e2e2">
                            <DynamicStatsV2
                              data={rowData.contents[contentIndex]}
                              setHasError={setHasError}
                              isJuniperBoxContents={true}
                            />
                          </CardBody>
                        </Card>
                      </Td>
                    </Tr>
                  )}
                </Fragment>
              ))}
            </Tbody>
          </Table>
        </Box>
      )}
    </>
  );
};

export default JuniperBoxSwitch;
