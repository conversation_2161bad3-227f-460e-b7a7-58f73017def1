import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>lex, <PERSON><PERSON>, StackDivider, Text } from '@chakra-ui/react';
import { MosolabsSystemNtp } from '../../../../types/orchestrator.types';
import { UnstyledTable } from '../../airspan/utils';

interface NtpCardProps {
  ntp: MosolabsSystemNtp | undefined;
  showContent: boolean;
  toggleContent: () => void;
}

export const MosolabSystemNtp = ({ ntp, showContent, toggleContent }: NtpCardProps) => {
  if (!ntp) return null;

  const tableData = {
    'NTP Time Mode': ntp?.ntpTimeMode,
    'NTP Main NTP Server': ntp?.ntpMainNtpServer,
    'NTP Backup NTP Server': ntp?.ntpBackupNtpServer,
    'NTP Trusted Server': ntp?.ntpTrustedServer,
    'NTP Sync Interval': ntp?.ntpSyncInterval,
    'NTP Show Time Date': ntp?.ntpShowTimeDate,
  };

  return (
    <Card>
      <Stack divider={<StackDivider />} spacing="0">
        <CardHeader
          _hover={{
            bg: 'gray.100',
            cursor: 'pointer',
            boxShadow: `0 0 12px gray.100`,
          }}
          transition="background-color 0.2s ease, box-shadow 0.2s ease"
          borderRadius="lg"
          onClick={toggleContent}
          bg="gray.100"
        >
          <Flex width="100%" position="relative" height="20px" alignItems="center">
            <Text fontSize="lg" position="absolute" left="0">
              NTP
            </Text>
            <Text fontSize="lg" position="absolute" left="50%" transform="translateX(-50%)">
              Server: {ntp?.ntpMainNtpServer}
            </Text>
          </Flex>
        </CardHeader>
        {showContent && (
          <CardBody>
            <Flex wrap="wrap">
              <UnstyledTable tableData={tableData} />
            </Flex>
          </CardBody>
        )}
      </Stack>
    </Card>
  );
};
