import { UnstyledTable } from '../../airspan/utils';

import {
  <PERSON>lex,
  Card,
  CardHeader,
  Text,
  CardBody,
  useDisclosure,
  Button,
  ModalHeader,
  ModalCloseButton,
  Modal,
  ModalContent,
  ModalOverlay,
  ModalBody,
  Box,
  StackDivider,
  Stack,
} from '@chakra-ui/react';
import { MosolabsSystemPtp } from '../../../../types/orchestrator.types';
import { SyncEnabledIcon, SyncDisabledIcon } from './Icon';

const ptpMode = [
  {
    label: 'Grand Master',
    value: 'grandmaster',
  },
  {
    label: 'Boundary Clock',
    value: 'boundaryClock',
  },
  {
    label: 'Ordinary Clock',
    value: 'ordinaryClock',
  },
  {
    label: 'Transparent Clock',
    value: 'transparentClock',
  },
];

const qualityTimesource = [
  {
    label: 'Atomic Clock',
    value: 'atomicClock',
  },
  {
    label: 'Gps',
    value: 'gps',
  },
  {
    label: 'Terrestrial Radio',
    value: 'terrestrialRadio',
  },
  {
    label: 'PTP',
    value: 'ptp',
  },
  {
    label: 'NTP',
    value: 'ntp',
  },
  {
    label: 'HandSet',
    value: 'handSet',
  },
  {
    label: 'Other',
    value: 'other',
  },
  {
    label: 'Internal Oscillator',
    value: 'internalOscillator',
  },
];

interface PtpCardProps {
  ptp: MosolabsSystemPtp | undefined;
  showContent: boolean;
  toggleContent: () => void;
}

export const MosolabSystemPtp = ({ ptp, showContent, toggleContent }: PtpCardProps) => {
  const { isOpen, onOpen, onClose } = useDisclosure();

  if (!ptp) return null;

  const tableData = {
    'PTP Enabled': ptp?.ptpEnabled,
    'PTP Profile': ptp?.ptpProfile,
    'PTP GM Quality Timesource': qualityTimesource.find(
      (timesource) => timesource.value === ptp?.ptpGrandMasterQualityTimesource
    )?.label,
    'GNSS Status': (
      <Box display="flex" alignItems="center">
        {ptp?.ptpGrandMasterAPTSStatusGNSSStatus}
        {ptp?.ptpGrandMasterAPTSStatusGNSSStatus === 'sync' ? <SyncEnabledIcon /> : <SyncDisabledIcon />}
      </Box>
    ),
    'GNSS DateTime': ptp?.ptpGrandMasterAPTSStatusGNSSDateTime,
    'PTP Status': (
      <Box display="flex" alignItems="center">
        {ptp?.ptpGrandMasterAPTSStatusPTPStatus}
        {ptp?.ptpGrandMasterAPTSStatusPTPStatus === 'sync' ? <SyncEnabledIcon /> : <SyncDisabledIcon />}
      </Box>
    ),
    'PTP DateTime': ptp?.ptpGrandMasterAPTSStatusPTPDateTime,
  };

  const modalTableData = {
    'PTP Grand Master Info Domain Number': ptp?.ptpGrandMasterInfoDomainNumber,
    'PTP Grand Master Info Port Identity': ptp?.ptpGrandMasterInfoPortIdentity,
    'PTP Grand Master Info Clock Identity': ptp?.ptpGrandMasterInfoClockIdentity,
    'PTP Grand Master Info Number of Ports': ptp?.ptpGrandMasterInfoNumberOfPorts,
    'PTP Grand Master Quality Class': ptp?.ptpGrandMasterQualityClass,
    'PTP Grand Master Quality Accuracy': ptp?.ptpGrandMasterQualityAccuracy,
    'PTP Grand Master Quality Offset Scaled Log Variance': ptp?.ptpGrandMasterQualityOffsetScaledLogVariance,
    'PTP Grand Master Quality Offset': ptp?.ptpGrandMasterQualityOffset,
    'PTP Grand Master Clock Error Offset 1': ptp?.ptpGrandMasterClockErrorOffset1,
    'PTP Grand Master Clock Error Offset 2': ptp?.ptpGrandMasterClockErrorOffset2,
    'PTP Grand Master Clock Error Fractional Frequency Offset': ptp?.ptpGrandMasterClockErrorFractionalFrequencyOffset,

    'PTP Domain Number': ptp?.ptpDomainNumber,
    'PTP Delay Mechanism': ptp?.ptpDelayMechanism,
    'PTP Master Announce Rate': ptp?.ptpMasterAnnouceRate,
    'PTP Master Announce Timeout': ptp?.ptpMasterAnnouceTimeout,
    'PTP Master Sync Rate': ptp?.ptpMasterSyncRate,
    'PTP Master Sync Limit': ptp?.ptpMasterSyncLimit,
    'PTP Master Encaspsulation': ptp?.ptpMasterEncaspsulation,
    'PTP Master Transmission': ptp?.ptpMasterTransmission,
    'PTP Master Peer Delay Request Rate': ptp?.ptpMasterPeerDelayRequestRate,
    'PTP Slave Encaspsulation': ptp?.ptpSlaveEncaspsulation,
    'PTP Slave Transmission': ptp?.ptpSlaveTransmission,
    'PTP Clock Step': ptp?.ptpClockStep,
  };

  return (
    <>
      <Card>
        <Stack divider={<StackDivider />} spacing="0">
          <CardHeader
            _hover={{
              bg: 'gray.100',
              cursor: 'pointer',
              boxShadow: `0 0 12px gray.100`,
            }}
            transition="background-color 0.2s ease, box-shadow 0.2s ease"
            borderRadius="lg"
            onClick={toggleContent}
            bg="gray.100"
          >
            <Flex width="100%" position="relative" height="20px" alignItems="center">
              <Text fontSize="lg" position="absolute" left="0">
                PTP
              </Text>

              <Text fontSize="lg" position="absolute" left="50%" transform="translateX(-50%)">
                Mode: {ptpMode.find((mode) => mode.value === ptp?.ptpMode)?.label}
              </Text>
            </Flex>
          </CardHeader>
          {showContent && (
            <CardBody>
              <Box width="100%">
                <UnstyledTable tableData={tableData} />
              </Box>
              <Box width="100%" textAlign="center" mt="4">
                <Button onClick={isOpen ? onClose : onOpen}>{'More Data'}</Button>
              </Box>
            </CardBody>
          )}
        </Stack>
      </Card>

      <Modal isOpen={isOpen} onClose={onClose} size="3xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>PTP</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <UnstyledTable tableData={modalTableData} />
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
};
