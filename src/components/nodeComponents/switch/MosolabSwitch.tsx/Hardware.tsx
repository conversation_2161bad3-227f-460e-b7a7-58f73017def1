import { Box, Card, CardBody, CardHeader, <PERSON>lex, <PERSON>ack, Stack<PERSON>ivider, Text, Divider, VStack } from '@chakra-ui/react';

import { MosolabsSystemInfo } from '../../../../types/orchestrator.types';
import { UnstyledTable } from '../../airspan/utils';
import { AmpereIcon, TemperatureIcon, VoltIcon, WattIcon } from './Icon';

import { UtilizationBar } from '../../common/UtilizationBar';

interface HardwareCardProps {
  systemInfo: MosolabsSystemInfo | undefined;
  showContent: boolean;
  toggleContent: () => void;
}

export const DeviceHardwareMonitor = ({ systemInfo, showContent, toggleContent }: HardwareCardProps) => {
  if (!systemInfo) return null;

  const table1Data = {
    'Redundant 1 Current': (
      <Box display="flex" alignItems="center">
        <Text> {systemInfo?.deviceHardwareMonitorPowerRedundant1Current} A</Text>
        <AmpereIcon size={30} color="green" />
      </Box>
    ),
    'Redundant 1 Consumption': (
      <Box display="flex" alignItems="center">
        <Text> {systemInfo?.deviceHardwareMonitorPowerRedundant1Consumption} W</Text>
        <WattIcon size={30} color="red" />
      </Box>
    ),
    'Redundant 1 Temperature': (
      <Box display="flex" alignItems="center">
        <Text> {systemInfo?.deviceHardwareMonitorPowerRedundant1Temperature} C</Text>
        <TemperatureIcon size="30" color="red" />
      </Box>
    ),
    'Redundant 1 Voltage': (
      <Box display="flex" alignItems="center">
        <Text> {systemInfo?.deviceHardwareMonitorPowerRedundant1Voltage} V</Text>
        <VoltIcon size={30} color="orange" />
      </Box>
    ),
  };

  const table2Data = {
    'Redundant 2 Current': (
      <Box display="flex" alignItems="center">
        <Text> {systemInfo?.deviceHardwareMonitorPowerRedundant2Current} A</Text>
        <AmpereIcon size={30} color="green" />
      </Box>
    ),
    'Redundant 2 Consumption': (
      <Box display="flex" alignItems="center">
        <Text> {systemInfo?.deviceHardwareMonitorPowerRedundant2Consumption} W</Text>
        <WattIcon size={30} color="red" />
      </Box>
    ),
    'Redundant 2 Temperature': (
      <Box display="flex" alignItems="center">
        <Text> {systemInfo?.deviceHardwareMonitorPowerRedundant2Temperature} C</Text>
        <TemperatureIcon size="30" color="red" />
      </Box>
    ),
    'Redundant 2 Voltage': (
      <Box display="flex" alignItems="center">
        <Text> {systemInfo?.deviceHardwareMonitorPowerRedundant2Voltage} V</Text>
        <VoltIcon size={30} color="orange" />
      </Box>
    ),
  };

  const fanUtilizationData = {
    'Fan 1 Utilization': <UtilizationBar percentage={systemInfo?.deviceHardwareMonitorFan1Utilization} />,
    'Fan 2 Utilization': <UtilizationBar percentage={systemInfo?.deviceHardwareMonitorFan2Utilization} />,
    'Fan 3 Utilization': <UtilizationBar percentage={systemInfo?.deviceHardwareMonitorFan3Utilization} />,
  };

  return (
    <Card>
      <Stack divider={<StackDivider />} spacing="0">
        <CardHeader
          _hover={{
            bg: 'gray.100',
            cursor: 'pointer',
            boxShadow: `0 0 12px gray.100`,
          }}
          transition="background-color 0.2s ease, box-shadow 0.2s ease"
          borderRadius="lg"
          onClick={toggleContent}
          bg="gray.100"
        >
          <Flex justifyContent="space-between" height="20px" width="100%">
            <Box display="flex" alignItems="center">
              <Text fontSize="lg">Device Hardware</Text>
            </Box>

            <Flex>
              <Box display="flex" alignItems="center">
                <Text fontSize="lg">Power: {systemInfo?.deviceHardwareMonitorPower} W</Text>
                <WattIcon size={30} color="red" />
              </Box>
            </Flex>
            <Flex>
              <Box display="flex" alignItems="center">
                <Text fontSize="lg">Temperature: {systemInfo?.deviceHardwareMonitorTemperature} C</Text>
                <TemperatureIcon size="30" color="red" />
              </Box>
            </Flex>
          </Flex>
        </CardHeader>
        {showContent && (
          <CardBody>
            <Flex flexDirection="column" gap="4">
              <Flex gap="4">
                <UnstyledTable tableData={table1Data} />
                <VStack align="stretch" borderLeft="2px solid" borderColor="gray.300"></VStack>
                <UnstyledTable tableData={table2Data} />
              </Flex>
              <Divider mb="1" />
              <Flex gap="4">
                <UnstyledTable tableData={fanUtilizationData} />
              </Flex>
            </Flex>
          </CardBody>
        )}
      </Stack>
    </Card>
  );
};
