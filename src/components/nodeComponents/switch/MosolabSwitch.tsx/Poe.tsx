import { AmpereIcon, VoltIcon } from './Icon';
import { WattIcon } from './Icon';
import { Card<PERSON>eader, Flex, CardBody, Text, StackDivider, Stack, Heading, Divider } from '@chakra-ui/react';
import { MosolabsSystemPoe } from '../../../../types/orchestrator.types';
import { Box } from '@chakra-ui/react';
import { UtilizationBar } from '../../common/UtilizationBar';
import { Card } from '@chakra-ui/react';
import { UnstyledTable } from '../../airspan/utils';

interface PoeCardProps {
  poe: MosolabsSystemPoe | undefined;
  showContent: boolean;
  toggleContent: () => void;
}

export const MosolabPoe = ({ poe, showContent, toggleContent }: PoeCardProps) => {
  if (!poe) return null;

  const tableData = {
    'Max Available Power': (
      <Box display="flex" alignItems="center">
        <Text> {poe?.poeMaxAvailablePower} W</Text>
        <WattIcon size={30} color="red" />
      </Box>
    ),
    'Total Power Consumption': (
      <Box display="flex" alignItems="center">
        <Text> {poe?.poeTotalPowerConsumption} W</Text>
        <WattIcon size={30} color="red" />
      </Box>
    ),
    'Main Current': (
      <Box display="flex" alignItems="center">
        <Text> {poe?.poeMainCurrent} A</Text>
        <AmpereIcon size={30} color="green" />
      </Box>
    ),
    'Main Voltage': (
      <Box display="flex" alignItems="center">
        <Text> {poe?.poeMainVoltage} V</Text>
        <VoltIcon size={30} color="orange" />
      </Box>
    ),
  };

  const powerUtilizationPercentage = ((poe?.poeTotalPowerConsumption / poe?.poeMaxAvailablePower) * 100).toFixed(2);

  return (
    <Card>
      <Stack divider={<StackDivider />} spacing="0">
        <CardHeader
          _hover={{
            bg: 'gray.100',
            cursor: 'pointer',
            boxShadow: `0 0 12px gray.100`,
          }}
          transition="background-color 0.2s ease, box-shadow 0.2s ease"
          borderRadius="lg"
          onClick={toggleContent}
          bg="gray.50"
        >
          <Flex justifyContent="space-between" width="100%" position="relative" height="20px" alignItems="center">
            <Text fontSize="lg" mr="4">
              POE
            </Text>

            <Box width="85%" display="flex" alignItems="center">
              <Text fontSize="lg" mr="4" mb="1">
                Power Consumption
              </Text>
              <Box width="100%" mt="3">
                <UtilizationBar percentage={Number(powerUtilizationPercentage)} />
              </Box>
            </Box>
          </Flex>
        </CardHeader>
        {showContent && (
          <CardBody>
            <Flex wrap="wrap">
              <UnstyledTable tableData={tableData} />
            </Flex>
          </CardBody>
        )}
      </Stack>
    </Card>
  );
};
