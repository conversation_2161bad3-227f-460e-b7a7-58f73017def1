import { MosolabsPort } from '../../../../../types/orchestrator.types';
import { Box, Text, Flex, Menu, MenuButton, MenuList, MenuItem, But<PERSON> } from '@chakra-ui/react';
import { DataTable } from '../../../../../pages/MetricsCollector/components/DataTable';
import { Row } from '@tanstack/react-table';
import { columns } from './Columns';
import { PortUsage } from './PortUsage';
import { PortPoe } from './PortPoe';
import { PortConfig } from './PortConfig';
import { useState, useMemo } from 'react';
import { FaChevronDown } from 'react-icons/fa';

const powerUtilizationPercentage = (poe: MosolabsPort['poe']) => {
  return poe?.poePortStatusOutputPower && poe?.poePortStatusPowerConsumption
    ? ((poe.poePortStatusOutputPower / poe.poePortStatusPowerConsumption) * 100).toFixed(2)
    : '0';
};

type PortStatus = 'Active' | 'All';
const PORT_STATUS_OPTIONS: PortStatus[] = ['Active', 'All'];

export const MosolabPort = ({ ports }: { ports: Record<string, MosolabsPort> }) => {
  const [statusFilter, setStatusFilter] = useState<PortStatus>('Active');

  const filteredPorts = useMemo(() => {
    if (!ports) return {};

    if (statusFilter === 'All') {
      return ports;
    }

    return Object.entries(ports).reduce<Record<string, MosolabsPort>>((filtered, [portKey, portData]) => {
      if (portData.status !== 'SHUTDOWN') {
        filtered[portKey] = portData;
      }
      return filtered;
    }, {});
  }, [ports, statusFilter]);

  const renderSubComponent = ({ row }: { row: Row<MosolabsPort> }) => {
    return (
      <Flex gap="4" width="100%" p="4">
        <PortConfig config={row.original.config} />
        <PortPoe poe={row.original.poe} />
        <PortUsage usage={row.original.usage} />
      </Flex>
    );
  };

  function transformObjectToArray<T>(obj: Record<string, MosolabsPort>): Array<MosolabsPort & { id: number }> {
    return Object.entries(obj).map(([key, value]) => ({
      id: Number(key),
      portsLEDsStatusLinkIsLit: value.led.portsLEDsStatusLinkIsLit,
      portsLEDsStatusSpeedIsLit: value.led.portsLEDsStatusSpeedIsLit,
      portsLEDsStatusPoEIsLit: value.led.portsLEDsStatusPoEIsLit,
      portConfigPortOperation: value.config.portConfigPortOperation,
      portStatusLinkUp: value.config.portStatusLinkUp,
      name: value.config.portConfigAlias,
      poeConfigEnabled: value.poe?.poeConfigEnabled,
      poePortStatusCondition: value.poe?.poePortStatusCondition,
      poePortStatusPowerConsumption: powerUtilizationPercentage(value.poe),
      portStatusSpeedUsed: value.config.portStatusSpeedUsed,
      ...value,
    }));
  }

  const data = transformObjectToArray(filteredPorts);

  return (
    <Box>
      <Box
        p="2"
        display="flex"
        alignItems="center"
        justifyContent="space-between"
        width="100%"
        borderTop="1px solid teal"
        borderLeft="1px solid teal"
        borderRight="1px solid teal"
        borderRadius="md"
        data-testid="port-table-header"
      >
        <Box textAlign="center" flex="1" borderRadius="md">
          <Text fontSize="x-large" my="4">
            Ports
          </Text>
        </Box>
        <Box justifyContent="flex-end" mr="4" width="10%">
          <Menu>
            <MenuButton as={Button} colorScheme="blue" gap="2">
              <Flex alignItems="center" gap="2">
                <Text>{statusFilter === 'All' ? 'All Ports' : `${statusFilter} Ports`}</Text>
                <FaChevronDown />
              </Flex>
            </MenuButton>
            <Flex overflow="overflow-x" position="relative">
              <MenuList py="2">
                {PORT_STATUS_OPTIONS.map((status) => (
                  <MenuItem
                    key={status}
                    onClick={() => setStatusFilter(status)}
                    fontWeight={statusFilter === status ? 'bold' : 'normal'}
                    py="2"
                    px="4"
                  >
                    {status}
                  </MenuItem>
                ))}
              </MenuList>
            </Flex>
          </Menu>
        </Box>
      </Box>

      {Object.entries(filteredPorts).length > 0 ? (
        <Box
          border="1px solid teal"
          borderTop="none"
          p="4"
          borderRadius="md"
          sx={{
            '& thead tr th': {
              height: '50px',
              py: 1,
              fontSize: 'sm',
            },
            '& tbody tr td': {
              height: '28px',
              py: 1,
              fontSize: 'sm',
            },
          }}
          data-testid="port-table-body"
        >
          <DataTable
            columns={columns}
            data={data}
            isExpandable={true}
            renderSubComponent={renderSubComponent}
            showSearch={false}
            size="xs"
          />
        </Box>
      ) : (
        <Text color="gray.500" textAlign="center" py="4" fontSize="large">
          No ports with status &quot;{statusFilter}&quot; found
        </Text>
      )}
    </Box>
  );
};
