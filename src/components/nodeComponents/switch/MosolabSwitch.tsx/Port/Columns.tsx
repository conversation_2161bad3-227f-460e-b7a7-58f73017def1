import { MosolabsPort } from '../../../../../types/orchestrator.types';
import { ColumnDef, Row } from '@tanstack/react-table';
import { StaticStatusCircleIcon } from '../../../../icons/StatusIcon';
import { StatusToColor } from '../../../../../data/constants';
import { LedOnIcon, LedOffIcon } from '../Icon';
import { Box, Flex, Text } from '@chakra-ui/react';
import { BsArrowReturnRight } from 'react-icons/bs';
import { MdHighlightOff } from 'react-icons/md';
import { MdCheckCircle } from 'react-icons/md';
import { UtilizationBar } from '../../../common/UtilizationBar';

export const columns: ColumnDef<MosolabsPort>[] = [
  {
    header: 'Id',
    accessorKey: 'id',
    id: 'id',
    cell: (props) => {
      const id = (props.row.original as MosolabsPort & { id: number }).id;

      return (
        <Flex>
          <BsArrowReturnRight />
          <Text ml="2">{id}</Text>
        </Flex>
      );
    },
  },
  {
    header: 'Name',
    accessorKey: 'name',
  },
  {
    header: 'Port Operation',
    accessorKey: 'portConfigPortOperation',
    cell: ({ getValue }) => {
      return getValue() === true ? <MdCheckCircle size="25" color="green" /> : <MdHighlightOff size="25" color="red" />;
    },
  },
  {
    header: 'Link Up',
    accessorKey: 'portStatusLinkUp',
    cell: ({ getValue }) => {
      return getValue() === true ? <MdCheckCircle size="25" color="green" /> : <MdHighlightOff size="25" color="red" />;
    },
  },
  {
    header: 'POE Enabled',
    accessorKey: 'poeConfigEnabled',
    cell: ({ getValue }) => {
      return getValue() === true ? (
        <MdCheckCircle size="25" color="green" />
      ) : getValue() === false ? (
        <MdHighlightOff size="25" color="red" />
      ) : (
        <Text>N/A</Text>
      );
    },
  },
  {
    header: 'POE Condition',
    accessorKey: 'poePortStatusCondition',
  },
  {
    header: 'POE Power Consumption',
    accessorKey: 'poePortStatusPowerConsumption',
    cell: ({ getValue }) => {
      return (
        <Box width="80%">
          <UtilizationBar percentage={Number(getValue())} usePadding={false} />
        </Box>
      );
    },
  },
  {
    header: 'Speed Used',
    accessorKey: 'portStatusSpeedUsed',
  },
  {
    header: 'LED Status Link | POE | Speed',
    accessorKey: 'portsLEDsStatusLinkIsLit',
    cell: (props) => {
      const status_link = (props.row.original as MosolabsPort & { portsLEDsStatusLinkIsLit: boolean })
        .portsLEDsStatusLinkIsLit;
      const status_poe = (props.row.original as MosolabsPort & { portsLEDsStatusPoEIsLit: boolean })
        .portsLEDsStatusPoEIsLit;
      const status_speed = (props.row.original as MosolabsPort & { portsLEDsStatusSpeedIsLit: boolean })
        .portsLEDsStatusSpeedIsLit;
      return (
        <Box width="90%">
          <Flex justifyContent="space-evenly">
            {status_link === true ? <LedOnIcon /> : status_link === false ? <LedOffIcon /> : <Text>N/A</Text>}
            {status_poe === true ? <LedOnIcon /> : status_poe === false ? <LedOffIcon /> : <Text>N/A</Text>}
            {status_speed === true ? <LedOnIcon /> : status_speed === false ? <LedOffIcon /> : <Text>N/A</Text>}
          </Flex>
        </Box>
      );
    },
  },
  {
    header: 'Status',
    accessorKey: 'status',
    cell: ({ getValue }) => {
      return <StaticStatusCircleIcon size={40} color={StatusToColor[getValue() as keyof typeof StatusToColor]} />;
    },
    filterFn: (row: Row<any>, id: string, value: string) => value.includes(row.getValue(id)),
  },
];
