import { Box, Stack, StackDivider } from '@chakra-ui/react';
import { MosolabsPort } from '../../../../../types/orchestrator.types';
import { UnstyledTable } from '../../../airspan/utils';
import { Text } from '@chakra-ui/react';

export const PortConfig = ({ config }: { config: MosolabsPort['config'] }) => {
  const tableData = {
    'Port Config Alias': config?.portConfigAlias,
    'Port Config Port Operation': config?.portConfigPortOperation,
    'Port Config Speed': config?.portConfigSpeed,
    'Port Config Flow Control': config?.portConfigFlowControl,
    'Port Status Link Up': config?.portStatusLinkUp,
    'Port Status Last Link Change': config?.portStatusLastLinkChange,
    'Port Status Link State': config?.portStatusLinkState,
    'Port Status Speed Used': config?.portStatusSpeedUsed,
    'Port Status Flowcontrol Used': config?.portStatusFlowcontrolUsed,
    'PTP Enabled': config?.ptpPortPtpEnabled,
    'PTP Role': config?.ptpPortRole,
  };

  return (
    <Box border="0.5px solid teal" p="4">
      <Stack divider={<StackDivider />}>
        <Text fontSize="lg">Port Config</Text>
        <Box p="4">
          <UnstyledTable tableData={tableData} />
        </Box>
      </Stack>
    </Box>
  );
};
