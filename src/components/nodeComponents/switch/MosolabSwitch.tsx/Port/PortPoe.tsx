import { UnstyledTable } from '../../../airspan/utils';
import { Box, Stack, StackDivider, Text } from '@chakra-ui/react';
import { MosolabsPort } from '../../../../../types/orchestrator.types';
import { AmpereIcon, VoltIcon, WattIcon } from '../Icon';
import { UtilizationBar } from '../../../common/UtilizationBar';

export const PortPoe = ({ poe }: { poe: MosolabsPort['poe'] }) => {
  const powerUtilizationPercentage =
    poe?.poePortStatusOutputPower && poe?.poePortStatusPowerConsumption
      ? ((poe.poePortStatusOutputPower / poe.poePortStatusPowerConsumption) * 100).toFixed(2)
      : '0';

  const tableData = {
    'POE Config Enabled': poe?.poeConfigEnabled,
    'POE Config Mode': poe?.poeConfigMode,
    'POE Config Priority': poe?.poeConfigPriority,
    'POE Port Determined Class': poe?.poePortStatusDeterminedClass,
    'POE Port Condition': poe?.poePortStatusCondition,
    'POE Port Priority': poe?.poePortStatusPriority,
    'POE Port Output Current': (
      <Box display="flex" alignItems="center">
        <Text> {poe?.poePortStatusOutputCurrent} A</Text>
        <AmpereIcon size={30} color="green" />
      </Box>
    ),
    'POE Port Output Voltage': (
      <Box display="flex" alignItems="center">
        <Text> {poe?.poePortStatusOutputVoltage} V</Text>
        <VoltIcon size={30} color="orange" />
      </Box>
    ),
    'POE Port Total Power Consumption': (
      <Box display="flex" alignItems="center">
        <Text> {poe?.poePortStatusPowerConsumption} W</Text>
        <WattIcon size={30} color="red" />
      </Box>
    ),
    'POE Port Output Power': (
      <Box display="flex" alignItems="center">
        <Text> {poe?.poePortStatusOutputPower} W</Text>
        <WattIcon size={30} color="red" />
      </Box>
    ),
    'POE Port Output Power Percentage': (
      <Box display="flex" alignItems="center">
        <UtilizationBar percentage={Number(powerUtilizationPercentage)} />
      </Box>
    ),
  };

  return (
    <Box border="0.5px solid teal" p="4">
      <Stack divider={<StackDivider />}>
        <Text fontSize="lg">POE</Text>
        <Box p="4">
          <UnstyledTable tableData={tableData} />
        </Box>
      </Stack>
    </Box>
  );
};
