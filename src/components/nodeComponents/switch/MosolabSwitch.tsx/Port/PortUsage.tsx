import { MosolabsPort } from '../../../../../types/orchestrator.types';
import { UnstyledTable } from '../../../airspan/utils';
import { Box, Stack, StackDivider, Text } from '@chakra-ui/react';

export const PortUsage = ({ usage }: { usage: MosolabsPort['usage'] }) => {
  const tableData = {
    'In 1 Sec Rate': usage?.ifIn1SecRate ? `${usage?.ifIn1SecRate} Mbps` : '-',
    'Out 1 Sec Rate': usage?.ifOut1SecRate ? `${usage?.ifOut1SecRate} Mbps` : '-',
    'In Discard Delta': usage?.inDiscardDelta ?? '-',
    'Out Discard Delta': usage?.outDiscardDelta ?? '-',
    'In Error Delta': usage?.inErrorDelta ?? '-',
    'Out Error Delta': usage?.outErrorDelta ?? '-',
  };

  return (
    <Box border="0.5px solid teal" p="4">
      <Stack divider={<StackDivider />}>
        <Text fontSize="lg">Usage</Text>
        <Box p="4">
          <UnstyledTable tableData={tableData} />
        </Box>
      </Stack>
    </Box>
  );
};
