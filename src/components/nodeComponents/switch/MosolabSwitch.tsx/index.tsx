import { MosolabsResponse } from '../../../../types/orchestrator.types';
import { Card, CardBody, Stack, Text, CardHeader, Flex, Box, Button, HStack } from '@chakra-ui/react';
import { LED, MosolabSystemInfo } from './SystemInfo';
import { MosolabSystemNtp } from './Ntp';
import { MosolabSystemPtp } from './Ptp';
import { MosolabPort } from './Port';
import { MosolabPoe } from './Poe';
import { DeviceHardwareMonitor } from './Hardware';
import { useState, useMemo } from 'react';

// Custom useMap hook implementation
function useMap<K, V>(initialMap = new Map<K, V>()) {
  const [map, setMap] = useState(initialMap);

  const actions = useMemo(
    () => ({
      set: (key: K, value: V) => {
        setMap((prev) => {
          const newMap = new Map(prev);
          newMap.set(key, value);
          return newMap;
        });
      },
      delete: (key: K) => {
        setMap((prev) => {
          const newMap = new Map(prev);
          newMap.delete(key);
          return newMap;
        });
      },
      clear: () => {
        setMap(new Map<K, V>());
      },
      setAll: (entries: [K, V][]) => {
        setMap(new Map(entries));
      },
    }),
    []
  );

  return [map, actions] as const;
}

interface MosolabsSwitchProps {
  data?: MosolabsResponse;
  id?: string | number;
  marginSpace?: string | number;
  caller?: string;
  nodeType?: string;
}

// Card keys type for type safety
type CardKey = 'systemInfo' | 'led' | 'poe' | 'ptp' | 'ntp' | 'hardware';

const MosolabsSwitch: React.FC<MosolabsSwitchProps> = ({ data, marginSpace, caller }) => {
  const systemInfo = data?.system?.info;
  const poe = data?.system?.poe;
  const ntp = data?.system?.ntp;
  const ptp = data?.system?.ptp;
  const ports = data?.ports;

  // Initial card states
  const initialCardStates: [CardKey, boolean][] = [
    ['systemInfo', true],
    ['led', true],
    ['poe', true],
    ['ptp', true],
    ['ntp', true],
    ['hardware', true],
  ];

  // Centralized state using useMap
  const [expandedCards, cardActions] = useMap<CardKey, boolean>(new Map(initialCardStates));

  // Toggle a specific card's expanded state
  const toggleCard = (cardName: CardKey) => {
    cardActions.set(cardName, !expandedCards.get(cardName));
  };

  // Expand or collapse all cards
  const expandAll = () => {
    initialCardStates.forEach(([key]) => {
      cardActions.set(key, true);
    });
  };

  const collapseAll = () => {
    initialCardStates.forEach(([key]) => {
      cardActions.set(key, false);
    });
  };

  // Check if all cards are expanded or collapsed
  const allExpanded = Array.from(expandedCards.values()).every((value) => value);
  const allCollapsed = Array.from(expandedCards.values()).every((value) => !value);

  return (
    <>
      <Card width="100%" marginRight={marginSpace} borderRadius="lg">
        <CardHeader>
          <Flex justifyContent="space-between" alignItems="center">
            <Text fontSize="x-large" my="4">
              Mosolabs Switch
            </Text>
            <HStack spacing="2">
              <Button size="sm" colorScheme={allExpanded ? 'teal' : 'gray'} onClick={expandAll}>
                Expand All
              </Button>
              <Button size="sm" colorScheme={allCollapsed ? 'teal' : 'gray'} onClick={collapseAll}>
                Collapse All
              </Button>
            </HStack>
          </Flex>
        </CardHeader>
        <CardBody borderTop="1px solid #e2e2e2">
          <Stack spacing="4" width="100%">
            <Flex flexDirection="column" gap="4" width="100%">
              <Flex flexDirection="row" gap="4" flexWrap="wrap" width="100%">
                <Box flex="1" height="fit-content">
                  {systemInfo && (
                    <MosolabSystemInfo
                      systemInfo={systemInfo}
                      showContent={expandedCards.get('systemInfo') ?? true}
                      toggleContent={() => toggleCard('systemInfo')}
                    />
                  )}
                </Box>
                <Box flex="1" maxWidth="20%" height="fit-content">
                  {systemInfo && (
                    <LED
                      systemInfo={systemInfo}
                      showContent={expandedCards.get('led') ?? true}
                      toggleContent={() => toggleCard('led')}
                    />
                  )}
                </Box>
                <Box flex="1" height="fit-content">
                  {poe && (
                    <MosolabPoe
                      poe={poe}
                      showContent={expandedCards.get('poe') ?? true}
                      toggleContent={() => toggleCard('poe')}
                    />
                  )}
                </Box>
              </Flex>
              <Flex flexDirection="row" gap="4" flexWrap="wrap" width="100%" alignItems="flex-start">
                <Box flex="1" maxWidth="31%" height="fit-content">
                  {ptp && (
                    <MosolabSystemPtp
                      ptp={ptp}
                      showContent={expandedCards.get('ptp') ?? true}
                      toggleContent={() => toggleCard('ptp')}
                    />
                  )}
                </Box>
                <Box flex="1" maxWidth="25%" height="fit-content">
                  {ntp && (
                    <MosolabSystemNtp
                      ntp={ntp}
                      showContent={expandedCards.get('ntp') ?? true}
                      toggleContent={() => toggleCard('ntp')}
                    />
                  )}
                </Box>
                <Box flex="1" maxWidth="44%" height="fit-content">
                  {systemInfo && (
                    <DeviceHardwareMonitor
                      systemInfo={systemInfo}
                      showContent={expandedCards.get('hardware') ?? true}
                      toggleContent={() => toggleCard('hardware')}
                    />
                  )}
                </Box>
              </Flex>
            </Flex>
            <Box width="100%" mt="0">
              {ports && <MosolabPort ports={ports} />}
            </Box>
          </Stack>
        </CardBody>
      </Card>
    </>
  );
};

export default MosolabsSwitch;
