import { GiElectric } from 'react-icons/gi';
import { IoMdBatteryCharging } from 'react-icons/io';
import { LiaTemperatureHighSolid } from 'react-icons/lia';
import { LuLightbulb } from 'react-icons/lu';
import { Md<PERSON>heckCircle, MdHighlightOff, Md<PERSON>ower, MdSync, MdSyncDisabled } from 'react-icons/md';
import { TbBulbFilled } from 'react-icons/tb';

export const AmpereIcon = ({ size = 30, color = 'green' }: { size?: number; color?: string }) => {
  return <GiElectric size={size} color={color} />;
};

export const VoltIcon = ({ size = 30, color = 'orange' }: { size?: number; color?: string }) => {
  return <IoMdBatteryCharging size={size} color={color} />;
};

export const WattIcon = ({ size = 30, color = 'red' }: { size?: number; color?: string }) => {
  return <MdPower size={size} color={color} />;
};

export const LedOffIcon = ({ size = '25', color = 'gray' }: { size?: string; color?: string }) => {
  return <LuLightbulb size={size} color={color} />;
};

export const LedOnIcon = ({ size = '30', color = 'green' }: { size?: string; color?: string }) => {
  return <TbBulbFilled size={size} color={color} />;
};

export const TemperatureIcon = ({ size = '30', color = 'red' }: { size?: string; color?: string }) => {
  return <LiaTemperatureHighSolid size={size} color={color} />;
};

export const SyncEnabledIcon = ({ size = '30', color = 'green' }: { size?: string; color?: string }) => {
  return <MdSync size={size} color={color} />;
};

export const SyncDisabledIcon = ({ size = '30', color = 'red' }: { size?: string; color?: string }) => {
  return <MdSyncDisabled size={size} color={color} />;
};

export const EnabledIcon = ({ size = '30', color = 'green' }: { size?: string; color?: string }) => {
  return <MdCheckCircle size={size} color={color} />;
};

export const DisabledIcon = ({ size = '30', color = 'red' }: { size?: string; color?: string }) => {
  return <MdHighlightOff size={size} color={color} />;
};
