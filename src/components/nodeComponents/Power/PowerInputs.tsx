import { Box, Divider, Flex, Table, Tbody, Td, Text, Th, Thead, Tr } from '@chakra-ui/react';
import React, { Fragment } from 'react';
import _ from 'lodash';

interface PwerInputsProps {
  data?: any;
}

const units: any = {
  voltage: 'V',
  current: 'A',
  power: 'W',
  frequency: 'Hz',
};

const PowerInputs: React.FC<PwerInputsProps> = ({ data }) => {
  const columns = React.useMemo(
    () => [
      {
        header: 'Index',
        accessorKey: 'index',
        id: 'index',
      },
      {
        header: 'Voltage',
        accessorKey: 'voltage',
        id: 'voltage',
      },
      {
        header: 'Current',
        accessorKey: 'current',
        id: 'current',
      },
      {
        header: 'Power',
        accessorKey: 'power',
        id: 'power',
      },
      {
        header: 'Frequency',
        accessorKey: 'frequency',
        id: 'frequency',
      },
    ],
    []
  );
  return (
    <>
      <Box
        width="100%"
        mt="5"
        mb="5"
        display="flex"
        alignItems="center"
        data-testid="stats-header-box"
        key={`header-input`}
      >
        <Text mb="2" fontWeight="bold" fontSize="md" data-testid="stats-header">
          Input
        </Text>
        <Divider flex="1" mb="4" ml="2" borderColor="gray.300" borderWidth="2px" />
      </Box>

      <Box padding="10px" width="96.1%">
        <Table variant="unstyled" size="sm">
          <Thead>
            <Tr>
              {columns.map((column) => (
                <Th key={column.id} background="gray.50">
                  <Flex>
                    <Box ml="2">{column.header}</Box>
                  </Flex>
                </Th>
              ))}
            </Tr>
          </Thead>
          <Tbody>
            {data?.inputs?.map((rowData: any, rowIndex: number) => (
              <Fragment key={rowIndex}>
                <Tr key={rowIndex}>
                  {Object.entries(rowData).map(([key, value], index) => (
                    <Td
                      width="10%"
                      key={key}
                      p="4"
                      textAlign="left"
                      borderRight="1px solid rgba(0, 0, 0, 0.1)"
                      _last={{ borderRight: 'none' }}
                      data-testid={`cell-powerInput-${rowIndex}-${index}`}
                    >
                      <Text>
                        {value} {units[key]}
                      </Text>
                    </Td>
                  ))}
                </Tr>
              </Fragment>
            ))}
          </Tbody>
        </Table>
      </Box>
    </>
  );
};

export default PowerInputs;
