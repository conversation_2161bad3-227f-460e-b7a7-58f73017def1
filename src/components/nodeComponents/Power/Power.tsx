import { ErrorBoundary } from 'react-error-boundary';
import { Box } from '@chakra-ui/react';
import { ErrorBoundaryFallback, ErrorBoundaryLogError } from '../../errorComponents/ErrorBoundaryFallback';
import NodeComponentErrorCard from '../../errorComponents/NodeComponentErrorCard';
import { NODE_COMPONENT_TITLES } from '../../../data/constants';
import Loader from '../../loader/Loader';
import useGetPowerNodeByNodeId from '../../../pages/CellOverview/hooks/services/use_GetPowerNodeByNodeId';
import PowerUPS from './PowerUPS';

type PowerProps = {
  queryNodeId?: string;
};

const Power = ({ queryNodeId }: PowerProps) => {
  const { data: powerNodeData, isLoading: isLoadingPowerNode, error } = useGetPowerNodeByNodeId(queryNodeId!);

  if (isLoadingPowerNode) return <Loader />;
  const showErrorCard = powerNodeData?.ups?.error || error;

  return (
    <>
      <Box width="100%" marginRight="4" data-testid="cells-node-PowerComponents">
        {showErrorCard ? (
          <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError as any}>
            <NodeComponentErrorCard
              id="power"
              errorData={powerNodeData?.ups?.error || error}
              compName={NODE_COMPONENT_TITLES.POWER}
              testId="node-comp-error-card"
              marginSpace="8"
            />
          </ErrorBoundary>
        ) : (
          <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError as any}>
            <PowerUPS data={powerNodeData?.ups} />
          </ErrorBoundary>
        )}
      </Box>
    </>
  );
};

export default Power;
