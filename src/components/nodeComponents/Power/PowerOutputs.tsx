import {
  Box,
  Divider,
  Flex,
  Stat,
  StatLabel,
  StatNumber,
  Table,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  Tr,
} from '@chakra-ui/react';
import React, { Fragment } from 'react';
import _ from 'lodash';

interface PwerOutputsProps {
  data?: any;
}

const units: any = {
  voltage: 'V',
  current: 'A',
  power: 'W',
  percent_load: '%',
};

const PowerOutputs: React.FC<PwerOutputsProps> = ({ data }) => {
  const columns = React.useMemo(
    () => [
      {
        header: 'Index',
        accessorKey: 'index',
        id: 'index',
      },
      {
        header: 'Voltage',
        accessorKey: 'voltage',
        id: 'voltage',
      },
      {
        header: 'Current',
        accessorKey: 'current',
        id: 'current',
      },
      {
        header: 'Power',
        accessorKey: 'power',
        id: 'power',
      },
      {
        header: 'Percentage',
        accessorKey: 'percent_load',
        id: 'percent_load',
      },
    ],
    []
  );
  return (
    <>
      <Box
        width="100%"
        mt="5"
        mb="5"
        display="flex"
        alignItems="center"
        data-testid="stats-header-box"
        key={`header-output`}
      >
        <Text mb="2" fontWeight="bold" fontSize="md" data-testid="stats-header">
          Output
        </Text>
        <Divider flex="1" mb="4" ml="2" borderColor="gray.300" borderWidth="2px" />
      </Box>
      <Box data-testid="stats-label-box" display="flex" mb="3" key={`stat-pair-outputs`} width="100%">
        {Object.entries(data).map(
          ([key, value]) =>
            key !== 'outputs' && (
              <Stat width="calc(50% - 9px)" height={['40px']} key={`stat-output-${key}`}>
                <>
                  <StatLabel data-testid="stats-label">{_.capitalize(key)}</StatLabel>
                  <StatNumber fontSize="md" isTruncated maxWidth="30ch">
                    <Box mb="1">
                      {key === 'frequency' && !_.isNil(value) ? (
                        <Text>{value as string} Hz</Text>
                      ) : (
                        <Text as="span">{value as string} </Text>
                      )}
                    </Box>
                  </StatNumber>
                </>
              </Stat>
            )
        )}
      </Box>

      <Box padding="10px" width="96.1%">
        <Table variant="unstyled" size="sm">
          <Thead>
            <Tr>
              {columns.map((column) => (
                <Th key={column.id} background="gray.50">
                  <Flex>
                    <Box ml="2">{column.header}</Box>
                  </Flex>
                </Th>
              ))}
            </Tr>
          </Thead>
          <Tbody>
            {data?.outputs?.map((rowData: any, rowIndex: number) => (
              <Fragment key={rowIndex}>
                <Tr key={rowIndex}>
                  {Object.entries(rowData).map(([key, value], index) => (
                    <Td
                      width="10%"
                      key={key}
                      p="4"
                      textAlign="left"
                      borderRight="1px solid rgba(0, 0, 0, 0.1)"
                      _last={{ borderRight: 'none' }}
                      data-testid={`cell-powerOutput-${rowIndex}-${index}`}
                    >
                      <Text>
                        {value} {units[key]}
                      </Text>
                    </Td>
                  ))}
                </Tr>
              </Fragment>
            ))}
          </Tbody>
        </Table>
      </Box>
    </>
  );
};

export default PowerOutputs;
