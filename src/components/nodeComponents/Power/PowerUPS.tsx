import { Card, CardBody, Stack, Text } from '@chakra-ui/react';
import React, { useState } from 'react';
import { DynamicStatsV2 } from '../common/DynamicStatsV2';
import { PowerUPSType } from '../../../types/orchestrator.types';
import PowerInputs from './PowerInputs';
import PowerOutputs from './PowerOutputs';

interface PowerUPSProps {
  data?: PowerUPSType;
  id?: string | number;
  marginSpace?: string | number;
  caller?: string;
  nodeType?: string;
}

const PowerUPS: React.FC<PowerUPSProps> = ({ data, marginSpace }) => {
  const [hasError, setHasError] = useState<boolean>(false);

  return (
    <>
      <Card width="100%" marginRight={marginSpace} borderRadius="lg">
        <CardBody borderTop="1px solid #e2e2e2">
          <Stack spacing="4">
            <Text fontSize="x-large" my="4">
              UPS
            </Text>
            <DynamicStatsV2 data={data} setHasError={setHasError} componentId={data?.component_id} isPowerUPS={true} />
            {!hasError && (
              <>
                {data?.input && <PowerInputs data={data?.input} />}
                {data?.output && <PowerOutputs data={data?.output} />}
              </>
            )}
          </Stack>
        </CardBody>
      </Card>
    </>
  );
};

export default PowerUPS;
