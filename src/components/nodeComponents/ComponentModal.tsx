import {
  Button,
  <PERSON>dal,
  <PERSON>dalBody,
  Modal<PERSON>lose<PERSON>utton,
  Modal<PERSON>ontent,
  Modal<PERSON>ooter,
  ModalHeader,
  ModalOverlay,
  Table,
  Tbody,
  Td,
  Th,
  Tr,
  Text,
} from '@chakra-ui/react';

export type ComponentModalProps = {
  isOpen: boolean;
  onClose: () => void | boolean;
  flattenedData?: any;
};

const ComponentModal = ({ isOpen, onClose, flattenedData }: ComponentModalProps) => {
  return (
    <Modal isOpen={isOpen} onClose={onClose} size="5xl">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>Component data</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <Table>
            <Tbody>
              {flattenedData &&
                Object.entries(flattenedData).map(([key, val]: any) => (
                  <Tr key={key}>
                    <Th style={{ textAlign: 'left', paddingRight: '1em' }}>
                      <Text>{key}</Text>
                    </Th>
                    <Td>
                      <Text wordBreak="break-word" whiteSpace="normal">
                        {val?.toString?.()}
                      </Text>
                    </Td>
                  </Tr>
                ))}
            </Tbody>
          </Table>
        </ModalBody>
        <ModalFooter>
          <Button colorScheme="blue" mr={3} onClick={onClose}>
            Close
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default ComponentModal;
