import { ChevronDownIcon, ChevronUpIcon } from '@chakra-ui/icons';
import { Card, Stack, CardHeader, Heading, Flex, Icon, Text, CardBody } from '@chakra-ui/react';
import React, { useState } from 'react';
import { BsArrowReturnRight } from 'react-icons/bs';
import { StatusToColor } from '../../../data/constants';
import { StaticStatusCircleIcon } from '../../icons/StatusIcon';

type CellOverviewAccordionProps = {
  title: string;
  data: any;
  status?: string;
  children?: React.ReactNode;
};

const CellOverviewAccordion: React.FC<CellOverviewAccordionProps> = ({ title, data, status, children }) => {
  const [showAccordionDetails, setShowAccordionDetails] = useState(false);

  return (
    <Card>
      <Stack spacing="4">
        <CardHeader
          onClick={() => setShowAccordionDetails(!showAccordionDetails)}
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          _hover={{
            cursor: 'pointer',
          }}
          borderRadius="lg"
          data-testid={`${title}-card-header`}
        >
          <Heading size="xs" fontWeight="500" display="flex" justifyContent="space-between" width="100%">
            <Flex alignItems="center" width="100%">
              <Icon as={BsArrowReturnRight} />
              <Text ml="2">{title}</Text>
            </Flex>
          </Heading>
          {status ? (
            <StaticStatusCircleIcon size={40} color={StatusToColor[data.status as keyof typeof StatusToColor]} />
          ) : (
            <Icon boxSize="4" as={showAccordionDetails ? ChevronUpIcon : ChevronDownIcon} />
          )}
        </CardHeader>

        {showAccordionDetails && (
          <CardBody data-testid={`${title}-card-body`}>
            <Stack spacing="4">{children}</Stack>
          </CardBody>
        )}
      </Stack>
    </Card>
  );
};

export default CellOverviewAccordion;
