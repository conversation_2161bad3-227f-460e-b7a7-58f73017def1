import { ErrorBoundary } from 'react-error-boundary';
import { Box, Text } from '@chakra-ui/react';
import { ErrorBoundaryFallback, ErrorBoundaryLogError } from '../../errorComponents/ErrorBoundaryFallback';
import NodeComponentErrorCard from '../../errorComponents/NodeComponentErrorCard';
import { NODE_COMPONENT_TITLES } from '../../../data/constants';
import Loader from '../../loader/Loader';
import { Falsey } from 'lodash';
import JuniperFirewall from './JuniperFirewall';
import useGetFirewallNodeByNodeId from '../../../pages/CellOverview/hooks/services/use_GetFirewallNodeByNodeId';
import FortinetFirewall from './FortinetFirewall';
import { FortinetSwitchType, JuniperSwitchType } from '../../../types/orchestrator.types';

type FirewallProps = {
  queryNodeId?: string;
  instanceAddress: string | Falsey;
};

const Firewall = ({ queryNodeId, instanceAddress }: FirewallProps) => {
  const { data: fireWallNodeData, isLoading: isLoadingFirewallNode } = useGetFirewallNodeByNodeId(queryNodeId || '');

  const availableFirewall = fireWallNodeData?.juniper_firewall || fireWallNodeData?.fortigate_firewall;

  if (isLoadingFirewallNode) return <Loader />;

  const showJuniperErrorCard =
    fireWallNodeData?.juniper_firewall?.error || fireWallNodeData?.juniper_firewall?.error === null;

  const showFortigateErrorCard =
    fireWallNodeData?.fortigate_firewall?.error || fireWallNodeData?.fortigate_firewall?.error === null;

  const boxWidth = '100%';

  return (
    <>
      <Box width={boxWidth} marginRight="4" data-testid="cells-node-FirewallComponents">
        {showFortigateErrorCard || showJuniperErrorCard ? (
          <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
            <NodeComponentErrorCard
              id="juniper/fortigate_firewall"
              errorData={availableFirewall}
              compName={NODE_COMPONENT_TITLES.FIREWALL}
              testId="node-comp-error-card"
              linkText="Instance"
              linkUrl={`${availableFirewall?.error?.url}` ? `${availableFirewall?.error?.url}` : null}
              marginSpace="8"
            />
          </ErrorBoundary>
        ) : (
          <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
            {fireWallNodeData?.juniper_firewall ? (
              <JuniperFirewall data={availableFirewall as JuniperSwitchType} />
            ) : fireWallNodeData?.fortigate_firewall ? (
              <FortinetFirewall data={availableFirewall as FortinetSwitchType} />
            ) : (
              <Text fontSize="sm" color="gray.500">
                No Firewall Available
              </Text>
            )}
          </ErrorBoundary>
        )}
      </Box>
    </>
  );
};

export default Firewall;
