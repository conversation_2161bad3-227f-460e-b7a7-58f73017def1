import { <PERSON>, <PERSON>ton, Card, CardBody, Stack, Text, Tooltip, useDisclosure, Icon } from '@chakra-ui/react';
import { ArrowDownIcon, ArrowUpIcon } from '@chakra-ui/icons';
import { ColumnDef } from '@tanstack/react-table';
import React, { useState } from 'react';
import ModalDataTable from '../common/ModalDataTable';
import {
  FibroClock,
  FlattenedFastPath,
  FibroInterface,
  Interface,
  JuniperSwitchType,
} from '../../../types/orchestrator.types';
import { bitsToOptimal } from '../../../utils/helpers';
import { DynamicStatsV2 } from '../common/DynamicStatsV2';
import JuniperBoxFirewall from './JuniperBoxFirewall';
import { FaChartLine } from 'react-icons/fa';
import { ComponentTypeEnum } from '../../../services/types';

interface JuniperFirewallProps {
  data?: JuniperSwitchType;
  id?: string | number;
  marginSpace?: string | number;
  caller?: string;
  nodeType?: string;
}

const JuniperFirewall: React.FC<JuniperFirewallProps> = ({ data, marginSpace }) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [hasError, setHasError] = useState<boolean>(false);

  const columns = React.useMemo<ColumnDef<Interface | FlattenedFastPath | FibroInterface | FibroClock>[]>(
    () => [
      {
        header: 'Index',
        accessorKey: 'ifIndex',
        id: 'ifIndex',
      },
      {
        header: 'Description',
        accessorKey: 'ifDescr',
        id: 'ifDescr',
        cell: ({ getValue }) => {
          const value = getValue() as string;
          const shouldShowTooltip = value && value.length > 20;
          const content = (
            <Box isTruncated maxWidth="20ch">
              {value}
            </Box>
          );
          return shouldShowTooltip ? <Tooltip label={value}>{content}</Tooltip> : content;
        },
      },
      {
        header: 'Type',
        accessorKey: 'ifType',
        id: 'ifType',
      },
      {
        header: 'MTU',
        accessorKey: 'ifMtu',
        id: 'ifMtu',
      },
      {
        header: 'Speed',
        accessorKey: 'ifSpeed',
        id: 'ifSpeed',
        cell: ({ getValue }) => {
          return <>{bitsToOptimal(Number(getValue()))}</>;
        },
      },
      {
        header: 'Physical Address',
        accessorKey: 'ifPhysAddress',
        id: 'ifPhysAddress',
      },
      {
        header: 'inbound',
        accessorKey: 'ifIn1SecRate',
        id: 'ifIn1SecRate',
        cell: ({ getValue }) => {
          const value = Number(getValue()) || 0;
          return <>{value === 0 ? value + ' Bytes' : value + ' Mbps'}</>;
        },
      },
      {
        header: 'outbound',
        accessorKey: 'ifOut1SecRate',
        id: 'ifOut1SecRate',
        cell: ({ getValue }) => {
          const value = Number(getValue()) || 0;
          return <>{value === 0 ? value + ' Bytes' : value + ' Mbps'}</>;
        },
      },
      {
        header: () => (
          <Box textAlign="center">
            Admin
            <br />
            Status
          </Box>
        ),
        accessorKey: 'ifAdminStatus',
        id: 'ifAdminStatus',
        cell: ({ getValue }) => {
          if (getValue() === null) return '-';
          return (
            <Box boxSize="5">
              {getValue() === 'up' ? (
                <ArrowUpIcon boxSize="5" color="green" data-testid="arrowUp_grren">
                  getValue()
                </ArrowUpIcon>
              ) : (
                <ArrowDownIcon boxSize="5" color="red" data-testid="arrowDown_red">
                  getValue()
                </ArrowDownIcon>
              )}
            </Box>
          );
        },
      },
      {
        header: () => (
          <Box textAlign="center">
            Oper
            <br />
            Status
          </Box>
        ),
        accessorKey: 'ifOperStatus',
        id: 'ifOperStatus',
        cell: ({ getValue }) => {
          if (getValue() === null) return '-';
          return (
            <Box boxSize="5">
              {getValue() === 'up' ? (
                <ArrowUpIcon boxSize="5" color={'green'}>
                  getValue()
                </ArrowUpIcon>
              ) : (
                <ArrowDownIcon boxSize="5" color={'red'}>
                  getValue()
                </ArrowDownIcon>
              )}
            </Box>
          );
        },
      },
      {
        header: '',
        accessorKey: 'chart',
        id: 'chart',
        cell: ({ row }) => {
          const data = row.original;

          if ('ifOperStatus' in data && 'ifAdminStatus' in data) {
            const ifOperStatus = data.ifOperStatus;
            const ifAdminStatus = data.ifAdminStatus;
            const isUp = ifOperStatus === 'up' && ifAdminStatus === 'up';

            return (
              <Box boxSize="10" p="2">
                <Icon as={FaChartLine} color={isUp ? 'currentColor' : 'gray.400'} w="7" h="7" />
              </Box>
            );
          }
        },
      },
    ],
    []
  );

  return (
    <>
      <Card width="100%" marginRight={marginSpace} borderRadius="lg">
        <CardBody borderTop="1px solid #e2e2e2">
          <Stack spacing="4">
            <Text fontSize="x-large" my="4">
              Juniper Firewall
            </Text>
            <DynamicStatsV2 data={data} setHasError={setHasError} componentId={data?.component_id} />
            {/* Juniper Box component*/}
            {!hasError && <JuniperBoxFirewall data={data?.box} vpnInfoData={data?.vpninfo} />}
            {data?.interfaces?.length && !data?.error && !hasError ? (
              <Button onClick={onOpen}>Interface Details</Button>
            ) : null}
          </Stack>
        </CardBody>
      </Card>

      {/* Modal */}
      <ModalDataTable<Interface | FlattenedFastPath | FibroInterface | FibroClock>
        isOpen={isOpen}
        onClose={onClose}
        columns={columns}
        data={data?.interfaces || []}
        caller="interface"
        subComponentProps={{
          component_id: data?.component_id as string,
          component_type: ComponentTypeEnum.JUNIPER,
        }}
      />
    </>
  );
};

export default JuniperFirewall;
