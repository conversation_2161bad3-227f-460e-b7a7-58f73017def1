import { ChevronDownIcon } from '@chakra-ui/icons';
import {
  AlertDialog,
  AlertDialogBody,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogOverlay,
  Badge,
  Box,
  Button,
  Card,
  CardBody,
  CardHeader,
  Flex,
  Heading,
  Icon,
  Menu,
  MenuButton,
  MenuItem,
  MenuList,
  Stack,
  StackDivider,
  Text,
  useDisclosure,
} from '@chakra-ui/react';
import _ from 'lodash';
import { useCallback, useRef, useState } from 'react';
import { AUTH_TOKEN_KEY, READ_ONLY_ACCESS_ROLES } from '../../../data/constants';
import useLogin from '../../../hooks/useLogin';
import { usePostActionOranRuApiCall } from '../../../pages/CellOverview/hooks/services/use_Orc_OranRuTasks';
import {
  ActionPhase,
  ServerActionType,
  useGetActionApiCall,
} from '../../../pages/CellOverview/hooks/services/use_Orc_ServerTasks';

import { recursiveSearch } from '../../../utils/recursiveSearch';
import Loader from '../../loader/Loader';
import ComponentModal from '../ComponentModal';
import { getIconForAction, getIconForActionButton } from './actionIcons';
import { actionButtonColor, OranRuActionIconType, OranRuActionType } from './OranRuActionTypes';
import { getTaskStatus } from './taskStatus';
import NetworkCard from '../metrics/NetworkCard';
import MemoryCard from '../metrics/MemoryCard';
import CpuCard from '../metrics/CpuCard';
import DiskCard from '../metrics/DiskCard';
import UpgradeModal from './UpgradeModal';

export type UsePostActionParams = {
  local_node_id: string;
  selectedOption: ActionPhase | '';
  actionType: OranRuActionType | ServerActionType | '';
  setTriggerGetAction?: (value: boolean) => void;
  setPostActionData?: (value: any) => void;
  toast?: any;
};

export const formatActionText = (text: string) => _.startCase(text);

export const TaskStatus = {
  CREATED: 'Created inactive',
  WAITING: 'Active, not yet running',
  BLOCKED: 'Unable to proceed',
  RUNNING: 'Active and running',
  RESULTS: 'Results available',
};

const usePostAction = ({
  local_node_id,
  selectedOption,
  actionType,
  setTriggerGetAction,
  setPostActionData,
}: UsePostActionParams) => {
  const { triggerPostAction, isMutating } = usePostActionOranRuApiCall(
    local_node_id,
    selectedOption,
    actionType as OranRuActionType,
    setTriggerGetAction ?? (() => undefined),
    setPostActionData ?? (() => undefined)
  );

  const initiatePostAction = useCallback(() => {
    if (selectedOption && actionType) {
      triggerPostAction(); // Directly trigger the mutation
    }
  }, [selectedOption, actionType, triggerPostAction]);

  return { initiatePostAction, isMutating };
};

const ActionButton = ({
  actionType,
  onClick,
  isDisabled,
}: {
  actionType: OranRuActionType;
  onClick: () => void;
  isDisabled: boolean;
}) => {
  const buttonIcon = actionType && getIconForActionButton(actionType);
  const buttonColor = actionButtonColor[actionType];
  return (
    <Button
      bg={buttonColor}
      color="white"
      variant="solid"
      size="sm"
      mr="2"
      onClick={onClick}
      _hover={{ bg: buttonColor, opacity: 0.9 }}
      _disabled={{ bg: buttonColor, opacity: 0.3, cursor: 'not-allowed' }}
      _focus={{ boxShadow: 'none' }}
      isDisabled={isDisabled}
    >
      <Text>{formatActionText(buttonIcon || '')}</Text>
      <Box ml="1">{buttonIcon ? getIconForAction(buttonIcon) : null}</Box>
    </Button>
  );
};

const RadioCard = ({
  nodeOpen,
  queryNodeId,
  type,
  data,
  isLoading,
  error,
}: {
  nodeOpen: boolean;
  queryNodeId: string;
  type: string;
  data: any;
  isLoading: boolean;
  error: any;
}) => {
  const { ru, id } = data || {};

  const { device_info, health_status } = ru || {};
  const local_node_id = data?.id;
  const flattenedData: any = recursiveSearch(ru);
  const [actionType, setActionType] = useState<OranRuActionType | ''>('');
  const [selectedOption, setSelectedOption] = useState<ActionPhase | ''>('');
  const [triggerGetAction, setTriggerGetAction] = useState<boolean>(false);
  const [postActionData, setPostActionData] = useState<any[] | null>(null);
  const { checkApplicationAccess } = useLogin(AUTH_TOKEN_KEY);
  const isReadOnlyAccess = checkApplicationAccess(READ_ONLY_ACCESS_ROLES);
  const cancelRef = useRef<HTMLButtonElement>(null);

  const cpuData = health_status?.cpu;
  const memoryData = health_status?.memory;
  const diskData = health_status?.disk;
  const networkData = health_status?.network;

  const deviceId = ru?.device_info?.DeviceID;
  const macAddress = ru?.device_info?.EthernetInfo?.MACAddress;
  const ModelNumber = ru?.device_info?.ModelNumber;

  const { isOpen, onOpen, onClose } = useDisclosure();
  const {
    isOpen: isSelectReasonAlertOpen,
    onOpen: onSelectReasonAlertOpen,
    onClose: onSelectReasonAlertClose,
  } = useDisclosure();
  const { isOpen: isUpgradeModalOpen, onOpen: onUpgradeModalOpen, onClose: onUpgradeModalClose } = useDisclosure();

  const { initiatePostAction, isMutating } = usePostAction({
    local_node_id,
    selectedOption,
    actionType: actionType as OranRuActionType,
    setTriggerGetAction,
    setPostActionData,
  });

  const handleActionButtonClick = (actionType: OranRuActionType) => {
    onSelectReasonAlertOpen();
    setActionType(actionType);
  };

  const handleSelect = (value: string) => {
    if (Object.values(ActionPhase).includes(value as ActionPhase)) {
      setSelectedOption((prevState) => (prevState === value ? '' : (value as ActionPhase)));
    }
  };

  const handleConfirmClick = () => {
    onSelectReasonAlertClose();
    initiatePostAction();
  };

  const handleCancelClick = () => {
    onSelectReasonAlertClose();
  };

  const {
    status: getActionStatus,
    isLoading: isGetActionExecuting,
    data: getActionData,
  } = useGetActionApiCall(postActionData, triggerGetAction);

  const renderActionType = postActionData ? postActionData[0]?.type : null;

  if (isLoading) {
    return <Loader />;
  }

  if (error) {
    return <div>Error loading data</div>;
  }

  if (!data) {
    return <div>No data available</div>;
  }

  return (
    <>
      <Card width="100%" marginRight="4" borderRadius="lg">
        <CardHeader>
          <Stack divider={<StackDivider />} spacing="4">
            <Box display="flex" justifyContent="space-between" mb="4">
              <Text fontSize="x-large" mb="4">
                RU
              </Text>
              <Box>
                <ActionButton
                  actionType={OranRuActionType.REBOOT}
                  onClick={() => handleActionButtonClick(OranRuActionType.REBOOT)}
                  isDisabled={isReadOnlyAccess || isMutating}
                />
                <ActionButton
                  actionType={OranRuActionType.FACTORY_RECOVERY}
                  onClick={() => handleActionButtonClick(OranRuActionType.FACTORY_RECOVERY)}
                  isDisabled={isReadOnlyAccess || isMutating}
                />
                <ActionButton
                  actionType={OranRuActionType.TRIGGER_DIAGNOSTICS}
                  onClick={() => handleActionButtonClick(OranRuActionType.TRIGGER_DIAGNOSTICS)}
                  isDisabled={isReadOnlyAccess || isMutating}
                />
                <ActionButton
                  actionType={OranRuActionType.TRIGGER_RU_CRASH_LOGS_UPLOAD}
                  onClick={() => handleActionButtonClick(OranRuActionType.TRIGGER_RU_CRASH_LOGS_UPLOAD)}
                  isDisabled={isReadOnlyAccess || isMutating}
                />
                <Button onClick={onUpgradeModalOpen} colorScheme="blue" size="sm" ml={2}>
                  Upgrade
                </Button>
              </Box>
            </Box>

            {renderActionType && actionType && (
              <Box display="flex" flexDirection="row" justifyContent="center" alignItems="center">
                <Text size="sm" mr="1">
                  {formatActionText(renderActionType)}
                </Text>
                <Box color={actionButtonColor[actionType]}>
                  {getIconForAction(renderActionType as OranRuActionIconType)}
                </Box>
                <Text ml="2" mr="2">
                  Action
                </Text>
                {getActionData && getTaskStatus(getActionData)}
              </Box>
            )}
          </Stack>
        </CardHeader>
        <CardBody borderTop="1px solid #e2e2e2">
          <Stack spacing="4">
            <Stack divider={<StackDivider />} spacing="4">
              <Box>
                <Heading size="xs" textTransform="uppercase">
                  Device Info
                </Heading>

                <Box display="flex" flexWrap="wrap">
                  <Text pt="2" pl="2" fontSize="sm">
                    ModelNumber:{' '}
                    <Badge pl="2" pr="2">
                      {device_info?.ModelNumber}
                    </Badge>
                  </Text>
                </Box>
                <Box display="flex" flexWrap="wrap">
                  <Text pt="2" pl="2" fontSize="sm">
                    Device id:{' '}
                    <Badge pl="2" pr="2">
                      {device_info?.DeviceID}
                    </Badge>
                  </Text>
                </Box>
                <Box display="flex" flexWrap="wrap">
                  <Text pt="2" pl="2" fontSize="sm">
                    Temp:{' '}
                    <Badge pl="2" pr="2">
                      {device_info?.Temperature}&deg;
                    </Badge>
                  </Text>
                </Box>
                <Box display="flex" flexWrap="wrap">
                  <Text pt="2" pl="2" fontSize="sm">
                    Firmware version :{' '}
                    <Badge pl="2" pr="2">
                      {device_info?.FirmwareVersion}
                    </Badge>
                  </Text>
                </Box>
                <Box display="flex" flexWrap="wrap">
                  <Text pt="2" pl="2" fontSize="sm">
                    Current boot time :{' '}
                    <Badge pl="2" pr="2">
                      {device_info?.CurrentBootTime}
                    </Badge>
                  </Text>
                </Box>
              </Box>

              <Box>
                <Heading size="xs" textTransform="uppercase">
                  OS
                </Heading>

                <Box display="flex" flexWrap="wrap">
                  <Text pt="2" pl="2" fontSize="sm">
                    ModelNumber:{' '}
                    <Badge pl="2" pr="2">
                      {device_info?.OS?.Name}
                    </Badge>
                  </Text>
                </Box>
                <Box display="flex" flexWrap="wrap">
                  <Text pt="2" pl="2" fontSize="sm">
                    Device id:{' '}
                    <Badge pl="2" pr="2">
                      {device_info?.OS?.Version}
                    </Badge>
                  </Text>
                </Box>
              </Box>

              <Box>
                <Heading size="xs" textTransform="uppercase">
                  Hardware Info
                </Heading>

                <Box display="flex" flexWrap="wrap">
                  <Text pt="2" pl="2" fontSize="sm">
                    CPU:{' '}
                    <Badge pl="2" pr="2">
                      {device_info?.HardwareInfo?.CPU}
                    </Badge>
                  </Text>
                </Box>
                <Box display="flex" flexWrap="wrap">
                  <Text pt="2" pl="2" fontSize="sm">
                    RAM:{' '}
                    <Badge pl="2" pr="2">
                      {device_info?.HardwareInfo?.RAM}
                    </Badge>
                  </Text>
                </Box>
                <Box display="flex" flexWrap="wrap">
                  <Text pt="2" pl="2" fontSize="sm">
                    Storage:{' '}
                    <Badge pl="2" pr="2">
                      {device_info?.HardwareInfo?.Storage}
                    </Badge>
                  </Text>
                </Box>
              </Box>

              <Box>
                <Heading size="xs" textTransform="uppercase">
                  Ethernet Info
                </Heading>

                <Box display="flex" flexWrap="wrap">
                  <Text pt="2" pl="2" fontSize="sm">
                    Gateway:{' '}
                    <Badge pl="2" pr="2">
                      {device_info?.EthernetInfo?.Gateway}
                    </Badge>
                  </Text>
                </Box>
                <Box display="flex" flexWrap="wrap">
                  <Text pt="2" pl="2" fontSize="sm">
                    IP Address:{' '}
                    <Badge pl="2" pr="2">
                      {device_info?.EthernetInfo?.IPAddress}
                    </Badge>
                  </Text>
                </Box>
                <Box display="flex" flexWrap="wrap">
                  <Text pt="2" pl="2" fontSize="sm">
                    MAC Address:{' '}
                    <Badge pl="2" pr="2">
                      {device_info?.EthernetInfo?.MACAddress}
                    </Badge>
                  </Text>
                </Box>
                <Box display="flex" flexWrap="wrap">
                  <Text pt="2" pl="2" fontSize="sm">
                    SubnetMask:{' '}
                    <Badge pl="2" pr="2">
                      {device_info?.EthernetInfo?.SubnetMask}
                    </Badge>
                  </Text>
                </Box>
              </Box>

              <Box>
                <Heading size="xs" textTransform="uppercase">
                  Timing status
                </Heading>

                <Box display="flex" flexWrap="wrap">
                  <Text pt="2" pl="2" fontSize="sm">
                    Clock source status:{' '}
                    <Badge pl="2" pr="2">
                      {device_info?.TimingStatus?.clockSourceStatus}
                    </Badge>
                  </Text>
                </Box>
                <Box display="flex" flexWrap="wrap">
                  <Text pt="2" pl="2" fontSize="sm">
                    Clock synchronized:{' '}
                    <Badge pl="2" pr="2">
                      {device_info?.TimingStatus?.clockSynchronized.toString()}
                    </Badge>
                  </Text>
                </Box>
                <Box display="flex" flexWrap="wrap">
                  <Text pt="2" pl="2" fontSize="sm">
                    Holdover status:{' '}
                    <Badge pl="2" pr="2">
                      {device_info?.TimingStatus?.holdoverStatus}
                    </Badge>
                  </Text>
                </Box>
                <Box display="flex" flexWrap="wrap">
                  <Text pt="2" pl="2" fontSize="sm">
                    Time source:{' '}
                    <Badge pl="2" pr="2">
                      {device_info?.TimingStatus?.timeSource}
                    </Badge>
                  </Text>
                </Box>
              </Box>

              <Flex flexDirection="column" gap={4}>
                {/* First Row */}
                <Flex justifyContent="space-between" gap={4}>
                  <Box flex="1">
                    <CpuCard cpuData={cpuData} />
                  </Box>
                  <Box flex="1">
                    <MemoryCard memoryData={memoryData} />
                  </Box>
                </Flex>

                {/* Second Row */}
                <Flex justifyContent="space-between" gap={4}>
                  <Box flex="1">
                    <DiskCard diskData={diskData} />
                  </Box>
                  <Box flex="1">
                    <NetworkCard networkData={networkData} />
                  </Box>
                </Flex>
              </Flex>

              <Button onClick={onOpen}>RU Data</Button>
            </Stack>
          </Stack>
        </CardBody>
      </Card>

      {/* Component modal */}
      <ComponentModal isOpen={isOpen} onClose={onClose} flattenedData={flattenedData} />

      {/* E500 upgrade modal */}
      <UpgradeModal
        isOpen={isUpgradeModalOpen}
        onClose={onUpgradeModalClose}
        deviceId={deviceId}
        macAddress={macAddress}
        ModelNumber={ModelNumber}
      />

      {/* Action button dialogue */}
      <AlertDialog
        isOpen={isSelectReasonAlertOpen}
        onClose={onSelectReasonAlertClose}
        isCentered
        leastDestructiveRef={cancelRef}
        size="lg"
      >
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              <Flex flexDirection="row">Select a reason for the {actionType} action</Flex>
            </AlertDialogHeader>
            <AlertDialogBody>
              <Menu>
                <MenuButton
                  as={Button}
                  rightIcon={<Icon as={ChevronDownIcon} />}
                  variant="outline"
                  colorScheme={actionButtonColor[actionType as keyof typeof actionButtonColor]}
                  size="sm"
                >
                  {selectedOption || 'Click to select an option'}
                </MenuButton>
                <MenuList>
                  {Object.entries(ActionPhase).map(([key, value]) => (
                    <MenuItem
                      key={key}
                      onClick={() => handleSelect(value)}
                      _hover={{
                        backgroundColor:
                          actionType && actionButtonColor[actionType] ? actionButtonColor[actionType] : 'gray.500',
                        color: 'white',
                      }}
                    >
                      {value}
                    </MenuItem>
                  ))}
                </MenuList>
              </Menu>
            </AlertDialogBody>
            <AlertDialogFooter>
              <Button onClick={handleCancelClick} colorScheme="blue" ref={cancelRef}>
                Cancel
              </Button>
              <Button
                //colorScheme={actionButtonColor[actionType as keyof typeof actionButtonColor]}
                bg={actionType && actionButtonColor[actionType] ? actionButtonColor[actionType] : 'gray.500'}
                color="white"
                _hover={{
                  bg: actionType && actionButtonColor[actionType] ? actionButtonColor[actionType] : 'gray.500',
                  opacity: 0.9,
                }}
                onClick={handleConfirmClick}
                isDisabled={_.isEmpty(selectedOption)}
                _disabled={{
                  bg: actionType && actionButtonColor[actionType] ? actionButtonColor[actionType] : 'gray.500',
                  opacity: 0.3,
                  cursor: 'not-allowed',
                }}
                ml={3}
              >
                Confirm
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
    </>
  );
};

export default RadioCard;
