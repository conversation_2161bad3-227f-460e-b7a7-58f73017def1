import _ from 'lodash';
import { useEffect, useState } from 'react';

type MetadataLabels = {
  site?: string;
  [key: string]: any;
};

type Deployment = {
  du_site_name?: string;
  cucp_site_name?: string;
  cuup_site_name?: string;
  deployment_name: string;
};

type DeploymentInfo = {
  siteName: string | null;
  deploymentName: string | null;
};

const useDeploymentFinder = (crs: any, allDeployments: Deployment[], nodeType: string) => {
  const [deploymentInfo, setDeploymentInfo] = useState<DeploymentInfo>({
    siteName: null,
    deploymentName: null,
  });

  const findAllMetaDataLabels = (obj: any): MetadataLabels[] => {
    const labelsList: MetadataLabels[] = [];
    const searchLabels = (currentObj: any) => {
      if (typeof currentObj !== 'object' || currentObj === null) return;

      if (currentObj.metadata?.labels) {
        labelsList.push(currentObj.metadata.labels);
      }

      for (const key in currentObj) {
        if (Object.prototype.hasOwnProperty.call(currentObj, key)) {
          searchLabels(currentObj[key]);
        }
      }
    };

    searchLabels(obj);
    return labelsList;
  };

  const getDeploymentNameBySite = (siteName: string | null, deployments: Deployment[]) => {
    if (!siteName || _.isEmpty(deployments)) return null;

    for (const deployment of deployments) {
      if (
        deployment.du_site_name === siteName ||
        deployment.cucp_site_name === siteName ||
        deployment.cuup_site_name === siteName
      ) {
        return deployment.deployment_name;
      }
    }
    return null;
  };

  useEffect(() => {
    if (_.isEmpty(crs) || _.isEmpty(allDeployments)) {
      setDeploymentInfo({ siteName: null, deploymentName: null });
      return;
    }

    const labels = findAllMetaDataLabels(crs);
    const siteName = labels.length > 0 ? labels[0]?.site ?? null : null;

    const deploymentName =
      nodeType === 'DU'
        ? allDeployments[0]?.deployment_name ?? null
        : getDeploymentNameBySite(siteName, allDeployments);

    setDeploymentInfo({ siteName, deploymentName });
  }, [crs, allDeployments, nodeType]);

  return deploymentInfo;
};

export default useDeploymentFinder;
