import {
  TRAFFIC_ERROR_RED,
  TRAFFIC_OK_GRE<PERSON>,
  TRAFFIC_SHUTDOWN_BLACK,
  TRAFFIC_WARNING_ORANGE,
} from '../../../data/constants';

export enum OranRuActionType {
  RESTART = 'restart',
  REBOOT = 'reboot',
  LOCK = 'Lock',
  UNLOCK = 'Unlock',
  TRIGGER_DIAGNOSTICS = 'trigger-diagnostics',
  FACTORY_RECOVERY = 'factory-recovery',
  TRIGGER_RU_CRASH_LOGS_UPLOAD = 'trigger-ru-crash-logs-upload',
}

export enum OranRuActionIconType {
  RESTART = 'restart',
  REBOOT = 'Reboot',
  LOCK = 'Lock',
  UNLOCK = 'Unlock',
  TRIGGER_DIAGNOSTICS = 'TriggerDiagnostics',
  FACTORY_RECOVERY = 'FactoryRecovery',
  TRIGGER_RU_CRASH_LOGS_UPLOAD = 'TriggerCrashLogsUpload',
}

type ActionColorType = Record<OranRuActionType, string>;

// export const actionButtonColor: ActionColorType = {
//   [OranRuActionType.RESTART]: 'orange',
//   [OranRuActionType.REBOOT]: 'red',
//   [OranRuActionType.LOCK]: 'green',
//   [OranRuActionType.UNLOCK]: 'red',
//   [OranRuActionType.TRIGGER_RU_CRASH_LOGS_UPLOAD]: 'pink',
//   [OranRuActionType.TRIGGER_DIAGNOSTICS]: 'orange',
//   [OranRuActionType.FACTORY_RECOVERY]: 'green',
// };

export const actionButtonColor: ActionColorType = {
  [OranRuActionType.RESTART]: TRAFFIC_WARNING_ORANGE,
  [OranRuActionType.REBOOT]: TRAFFIC_ERROR_RED,
  [OranRuActionType.LOCK]: TRAFFIC_OK_GREEN,
  [OranRuActionType.UNLOCK]: TRAFFIC_ERROR_RED,
  [OranRuActionType.TRIGGER_RU_CRASH_LOGS_UPLOAD]: TRAFFIC_SHUTDOWN_BLACK,
  [OranRuActionType.TRIGGER_DIAGNOSTICS]: TRAFFIC_WARNING_ORANGE,
  [OranRuActionType.FACTORY_RECOVERY]: TRAFFIC_OK_GREEN,
};

export const actionButtonIconMapping: Record<OranRuActionType, OranRuActionIconType> = {
  [OranRuActionType.RESTART]: OranRuActionIconType.RESTART,
  [OranRuActionType.REBOOT]: OranRuActionIconType.REBOOT,
  [OranRuActionType.LOCK]: OranRuActionIconType.LOCK,
  [OranRuActionType.UNLOCK]: OranRuActionIconType.UNLOCK,
  [OranRuActionType.TRIGGER_DIAGNOSTICS]: OranRuActionIconType.TRIGGER_DIAGNOSTICS,
  [OranRuActionType.FACTORY_RECOVERY]: OranRuActionIconType.FACTORY_RECOVERY,
  [OranRuActionType.TRIGGER_RU_CRASH_LOGS_UPLOAD]: OranRuActionIconType.TRIGGER_RU_CRASH_LOGS_UPLOAD,
};

export const actionIconMapping = {
  FactoryRecovery: OranRuActionIconType.FACTORY_RECOVERY,
  TriggerDiagnostics: OranRuActionIconType.TRIGGER_DIAGNOSTICS,
  restart: OranRuActionIconType.RESTART,
  Reboot: OranRuActionIconType.REBOOT,
  Lock: OranRuActionIconType.LOCK,
  Unlock: OranRuActionIconType.UNLOCK,
  TriggerCrashLogsUpload: OranRuActionIconType.TRIGGER_RU_CRASH_LOGS_UPLOAD,
};
