import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>dalHeader,
  ModalOverlay,
  Box,
  Select,
  Checkbox,
  VStack,
  FormControl,
  FormLabel,
} from '@chakra-ui/react';
import useE500 from '../../../pages/CellOverview/hooks/services/use_Orc_E500';
import Loader from '../../loader/Loader';
import { useState, useMemo, useEffect } from 'react';

export type ComponentModalProps = {
  isOpen: boolean;
  onClose: () => void | boolean;
  deviceId: string;
  macAddress: string;
  ModelNumber: string;
};

type SoftwareVersion = {
  name: string;
  size: number;
  modified: string;
  display_name: string;
  development_build: boolean;
};

type DeploymentStatus = {
  devices: Array<{
    id: string;
    hostname: string;
    mac: string;
    device_type: string;
    status: string;
    created_ts: string;
    updated_ts: string;
  }>;
};

const DEPLOYMENT_STATUS = {
  DOWNLOADING: 'downloading',
  REBOOTING: 'rebooting',
  SUCCESS: 'success',
  ERROR: 'error',
} as const;

const UpgradeModal = ({ isOpen, onClose, deviceId, macAddress, ModelNumber }: ComponentModalProps) => {
  const { getE500SoftwareVersions, updateE500SoftwareVersion, getE500SoftwareUpdateStatus } = useE500();
  const { isLoading, error, data } = getE500SoftwareVersions(isOpen);

  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [showDevelopmentBuilds, setShowDevelopmentBuilds] = useState(false);
  const [selectedVersionName, setSelectedVersionName] = useState<string>('');
  const [deploymentId, setDeploymentId] = useState<string>('');

  const {
    data: deploymentStatus,
    isLoading: isStatusLoading,
    refetch: refetchStatus,
  } = getE500SoftwareUpdateStatus(!!deploymentId, deploymentId);

  useEffect(() => {
    let pollInterval: NodeJS.Timeout | null = null;

    if (deploymentId) {
      pollInterval = setInterval(() => {
        refetchStatus();
      }, 5000);

      if (
        deploymentStatus?.devices?.[0]?.status === DEPLOYMENT_STATUS.SUCCESS ||
        deploymentStatus?.devices?.[0]?.status === DEPLOYMENT_STATUS.ERROR
      ) {
        if (pollInterval) {
          clearInterval(pollInterval);
        }
      }
    }

    return () => {
      if (pollInterval) {
        clearInterval(pollInterval);
      }
    };
  }, [deploymentId, deploymentStatus?.devices?.[0]?.status, refetchStatus]);

  const categories = useMemo(() => {
    if (!data) return [];
    return Object.keys(data);
  }, [data]);

  const filteredVersions = useMemo(() => {
    if (!data || !selectedCategory) return [];
    const versions = data[selectedCategory] as SoftwareVersion[];
    return versions.filter((version) =>
      showDevelopmentBuilds ? version.development_build : !version.development_build
    );
  }, [data, selectedCategory, showDevelopmentBuilds]);

  const selectedVersion = useMemo(() => {
    return filteredVersions.find((v) => v.name === selectedVersionName);
  }, [filteredVersions, selectedVersionName]);

  const mutation = updateE500SoftwareVersion();

  const handleSubmit = () => {
    if (!selectedVersion) return;

    mutation.mutate(
      {
        release: selectedVersion.name,
        mac_address: macAddress,
        serial_number: deviceId,
      },
      {
        onSuccess: (response: any) => {
          if (response?.deployment_id) {
            setDeploymentId(response.deployment_id);
          }
        },
      }
    );
  };

  const isUpgradeDisabled = useMemo(() => {
    if (deploymentId) {
      if (mutation.isLoading || isStatusLoading) {
        return true;
      }
      if (deploymentStatus?.devices?.length > 0) {
        return deploymentStatus.devices[0].status !== 'success';
      }
      return true;
    }
    return !selectedVersionName;
  }, [deploymentId, selectedVersionName, mutation.isLoading, isStatusLoading, deploymentStatus]);

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="5xl">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>{ModelNumber} Upgrade</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          {isLoading ? (
            <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
              <Loader />
            </Box>
          ) : (
            <VStack spacing={4} align="stretch">
              <FormControl>
                <FormLabel>Category</FormLabel>
                <Select
                  placeholder="Select category"
                  value={selectedCategory}
                  onChange={(e) => {
                    setSelectedCategory(e.target.value);
                    setSelectedVersionName('');
                  }}
                  isDisabled={!!deploymentId && deploymentStatus?.devices?.[0]?.status !== DEPLOYMENT_STATUS.SUCCESS}
                >
                  {categories.map((category) => (
                    <option key={category} value={category}>
                      {category.replace(/_/g, ' ').toUpperCase()}
                    </option>
                  ))}
                </Select>
              </FormControl>

              <FormControl>
                <Checkbox
                  isChecked={showDevelopmentBuilds}
                  onChange={(e) => setShowDevelopmentBuilds(e.target.checked)}
                  isDisabled={!!deploymentId && deploymentStatus?.devices?.[0]?.status !== DEPLOYMENT_STATUS.SUCCESS}
                >
                  Show Development Builds
                </Checkbox>
              </FormControl>

              <FormControl>
                <FormLabel>Version</FormLabel>
                <Select
                  placeholder="Select version"
                  isDisabled={
                    !selectedCategory ||
                    (!!deploymentId && deploymentStatus?.devices?.[0]?.status !== DEPLOYMENT_STATUS.SUCCESS)
                  }
                  value={selectedVersionName}
                  onChange={(e) => setSelectedVersionName(e.target.value)}
                >
                  {filteredVersions.map((version) => (
                    <option key={version.name} value={version.name}>
                      {version.display_name}
                    </option>
                  ))}
                </Select>
              </FormControl>
            </VStack>
          )}
        </ModalBody>
        <ModalFooter>
          <Button colorScheme="teal" mr={3} onClick={onClose}>
            Close
          </Button>
          <Button
            colorScheme="blue"
            onClick={handleSubmit}
            isDisabled={isUpgradeDisabled}
            isLoading={
              mutation.isLoading ||
              (!!deploymentId && deploymentStatus?.devices?.[0]?.status !== DEPLOYMENT_STATUS.SUCCESS)
            }
            loadingText={
              deploymentId
                ? deploymentStatus?.devices?.length > 0
                  ? deploymentStatus.devices[0].status === DEPLOYMENT_STATUS.DOWNLOADING
                    ? 'Downloading software...'
                    : deploymentStatus.devices[0].status === DEPLOYMENT_STATUS.REBOOTING
                    ? 'Rebooting device...'
                    : deploymentStatus.devices[0].status === DEPLOYMENT_STATUS.SUCCESS
                    ? 'Upgrade completed!'
                    : deploymentStatus.devices[0].status === DEPLOYMENT_STATUS.ERROR
                    ? 'Error during upgrade'
                    : 'Upgrading...'
                  : 'Initializing upgrade...'
                : 'Upgrading...'
            }
          >
            {!deploymentId
              ? 'Upgrade'
              : deploymentStatus?.devices?.[0]?.status === DEPLOYMENT_STATUS.SUCCESS
              ? 'Upgrade Complete!'
              : 'Upgrading...'}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default UpgradeModal;
