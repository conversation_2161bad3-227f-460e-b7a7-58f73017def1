import { Md<PERSON><PERSON>renew, MdLock, MdLock<PERSON>pen } from 'react-icons/md';
import { LuServer<PERSON>rash } from 'react-icons/lu';
import { RiDeviceRecoverLine } from 'react-icons/ri';
import { LiaDiagnosesSolid } from 'react-icons/lia';

export const actionIcons: Record<OranRuActionIconType, JSX.Element> = {
  [OranRuActionIconType.RESTART]: <MdAutorenew size="25" />,
  [OranRuActionIconType.REBOOT]: <MdAutorenew size="25" />,
  [OranRuActionIconType.LOCK]: <MdLock size="25" />,
  [OranRuActionIconType.UNLOCK]: <MdLockOpen size="25" />,
  [OranRuActionIconType.TRIGGER_DIAGNOSTICS]: <LiaDiagnosesSolid size="25" />,
  [OranRuActionIconType.FACTORY_RECOVERY]: <RiDeviceRecoverLine size="25" />,
  [OranRuActionIconType.TRIGGER_RU_CRASH_LOGS_UPLOAD]: <LuServerCrash size="25" />,
};

import {
  OranRuActionType,
  OranRuActionIconType,
  actionIconMapping,
  actionButtonIconMapping,
} from './OranRuActionTypes';

export const getIconForAction = (actionType: OranRuActionIconType): JSX.Element | null => {
  const iconType = actionIconMapping[actionType];
  return iconType ? actionIcons[iconType] : null;
};

export const getIconForActionButton = (actionType: OranRuActionType): OranRuActionIconType | undefined => {
  return actionButtonIconMapping[actionType];
};
