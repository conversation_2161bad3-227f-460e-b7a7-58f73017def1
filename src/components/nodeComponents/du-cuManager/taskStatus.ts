import _ from 'lodash';
import { statusIcon } from '../server/acp/ServerTasks';
import { TaskStatus } from './RadioCard';

export const getTaskStatus = (operation: { info: any }[]) => {
  const tasks = operation[0]?.info;
  if (typeof tasks === 'string' || _.isEmpty(tasks)) {
    return statusIcon['pending'];
  }

  const sortedTasks = tasks.sort(
    (a: { updated_at: string }, b: { updated_at: string }) =>
      new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime()
  );

  for (const task of sortedTasks) {
    if (task.progress === TaskStatus.RESULTS) {
      // Handle success or failure
    } else if (task.progress === TaskStatus.RUNNING) {
      return statusIcon['running'];
    } else {
      return statusIcon['pending'];
    }
  }
};
