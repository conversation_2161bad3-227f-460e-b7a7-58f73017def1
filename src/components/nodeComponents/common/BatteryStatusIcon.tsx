import React from 'react';
import { WarningIcon, CheckIcon, WarningTwoIcon } from '@chakra-ui/icons';
import { Box, Text, Tooltip } from '@chakra-ui/react';

const statusIconMap = {
  low: { icon: <WarningIcon color="yellow.500" />, label: 'low' },
  normal: { icon: <CheckIcon color="green.500" />, label: 'Normal' },
  depleted: { icon: <WarningTwoIcon color="red.500" />, label: 'Depleted' },
  unknown: { icon: <WarningTwoIcon color="gray.500" />, label: 'Depleted' },
};

export type statusType = 'low' | 'normal' | 'depleted' | 'unknown';

export interface StatusIconProps {
  status: statusType;
}

const BatteryStatusIcon: React.FC<StatusIconProps> = ({ status }) => {
  const statusInfo = statusIconMap[status];

  return (
    <Box display="flex" alignItems="center">
      <Tooltip label={status} hasArrow>
        {statusInfo?.icon || null}
      </Tooltip>
    </Box>
  );
};

export default BatteryStatusIcon;
