import { Icon } from '@chakra-ui/icons';
import { Box, Tooltip, useColorModeValue } from '@chakra-ui/react';
import ReactECharts from 'echarts-for-react';
import * as echarts from 'echarts/core';
import { BsInfoCircle } from 'react-icons/bs';
import useParticleMetrics from '../../../pages/MetricsCollector/hooks/useParticleMetrics';
import Loader from '../../loader/Loader';
// import useParticleMetrics from '../../../../pages/MetricsCollector/hooks/useParticleMetrics';
// import Loader from '../../../loader/Loader';

echarts.registerTheme('dark', {
  backgroundColor: 'rgba(41,52,65,1)',
  title: {
    textStyle: {
      color: '#ffffff',
    },
    subtextStyle: {
      color: '#dddddd',
    },
  },
  valueAxis: {
    axisLine: {
      lineStyle: {
        color: '#d2d2d2',
      },
    },
    axisLabel: {
      color: '#e1e2eb',
    },
  },
  categoryAxis: {
    axisLine: {
      lineStyle: {
        color: '#d2d2d2',
      },
    },
    axisLabel: {
      color: '#e1e2eb',
    },
  },
  legend: {
    textStyle: {
      color: '#ffffff',
    },
  },
});

interface TimeSeriesGraphProps {
  coreid: string;
}

const TimeSeriesGraph: React.FC<TimeSeriesGraphProps> = ({ coreid }) => {
  const themeMode = useColorModeValue('light', 'dark');
  const { data, isLoading, error } = useParticleMetrics(coreid);
  if (isLoading) return <Loader />;
  if (error) return <p>Error :</p>;

  const bwFanSpeedMax = Math.max(...data['bwFanSpeed']);
  const asFanSpeedMax = Math.max(...data['asFanSpeed']);
  const globalFanMax = Math.max(bwFanSpeedMax, asFanSpeedMax);
  // const globalFanMin = Math.min(bwFanSpeedMax, asFanSpeedMax);
  const temperatureMax = Math.max(...data['temperature']);
  const temperatureMin = Math.min(...data['temperature']);

  const options = {
    title: {
      text: 'Temperature and Fan Speed',
    },
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      data: ['Temperature', 'MW Fan Speed', '5G Fan Speed'],
    },
    toolbox: {
      show: true,
      showTitle: false,
      feature: {
        saveAsImage: {
          show: true,
          title: 'Save As Image',
        },
        dataView: {
          show: true,
          title: 'Data View',
          buttonColor: '#578DA8',
        },
        myTool2: {
          show: true,
          title: 'Open in new tab (coming soon)',
          icon: 'M432,320H400a16,16,0,0,0-16,16V448H64V128H208a16,16,0,0,0,16-16V80a16,16,0,0,0-16-16H48A48,48,0,0,0,0,112V464a48,48,0,0,0,48,48H400a48,48,0,0,0,48-48V336A16,16,0,0,0,432,320ZM488,0h-128c-21.37,0-32.05,25.91-17,41l35.73,35.73L135,320.37a24,24,0,0,0,0,34L157.67,377a24,24,0,0,0,34,0L435.28,133.32,471,169c15,15,41,4.5,41-17V24A24,24,0,0,0,488,0Z',
          onclick: function () {
            // window.open('/');
          },
        },
      },
      tooltip: {
        show: true,
        formatter: function (param: { title: string }) {
          return '<Box>' + param.title + '</Box>';
        },
        textStyle: {
          fontSize: 12,
        },
        extraCssText: 'box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);',
      },
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: data?.time,
      axisLabel: {
        rotate: 45, // Optional: rotate labels if they overlap or aren't easily readable
        interval: 55, // Optional: show every label. Adjust if labels overlap
      },
    },

    yAxis: [
      {
        type: 'value',
        name: 'Temperature (°C)',
        // min: -55,
        // max: 125,
        min: () => {
          return temperatureMin - 5;
        },
        max: () => {
          return temperatureMax + 5;
        },
        position: 'left',
      },
      {
        type: 'value',
        name: 'Fan Speed (RPM)',
        min: 0,
        // max: 9200,
        // min: () => {
        //   return globalFanMin; // or any buffer you want to add
        // },
        max: () => {
          return globalFanMax + 200; // or any buffer you want to add
        },
        position: 'right',
      },
    ],
    series: [
      {
        name: 'Temperature',
        type: 'line',
        data: data?.temperature,
        yAxisIndex: 0,
      },
      {
        name: 'MW Fan Speed',
        type: 'line',
        data: data?.bwFanSpeed,
        yAxisIndex: 1,
      },
      {
        name: '5G Fan Speed',
        type: 'line',
        data: data?.asFanSpeed,
        yAxisIndex: 1,
      },
    ],
  };
  if (data?.time.length === 0) return <Box>No data available for graph</Box>;
  return (
    <Box width={'full'}>
      <Tooltip
        border={'1px'}
        borderColor={'gray.300'}
        p={4}
        label="Data is up to 1 hour old because the Particle agent sends updates to the Metrics Collector only once every hour."
        fontSize="md"
        bg="white"
        color="gray"
      >
        <Box alignContent="flex-end" mb={'10px'} width={'1px'}>
          <Icon as={BsInfoCircle} boxSize={4} />
        </Box>
      </Tooltip>
      <ReactECharts option={options} echarts={echarts} style={{ height: '400px', width: '100%' }} theme={themeMode} />
    </Box>
  );
};

export default TimeSeriesGraph;
