import { Box, Select, Text, useColorModeValue } from '@chakra-ui/react';
import React, { useState } from 'react';
import ReactECharts from 'echarts-for-react';
import * as echarts from 'echarts/core';
import useTransportMetrics from '../../../pages/MetricsCollector/hooks/useTransportMetrics';
import { TransporteMetricsSearchParams } from '../../../types/metricCollector.type';
import { formatInReadableTimeDate } from '../../../utils/formatInReadableTimeData';
import Loader from '../../loader/Loader';

echarts.registerTheme('dark', {
  backgroundColor: 'rgba(41,52,65,1)',
  title: {
    textStyle: {
      color: '#ffffff',
    },
    subtextStyle: {
      color: '#dddddd',
    },
  },
  valueAxis: {
    axisLine: {
      lineStyle: {
        color: '#d2d2d2',
      },
    },
    axisLabel: {
      color: '#e1e2eb',
    },
  },
  categoryAxis: {
    axisLine: {
      lineStyle: {
        color: '#d2d2d2',
      },
    },
    axisLabel: {
      color: '#e1e2eb',
    },
  },
  legend: {
    textStyle: {
      color: '#ffffff',
    },
  },
});

const timeOptions = [
  { label: 'Last 30 Minutes', value: 'Last30Mins' },
  { label: 'Last Hour', value: 'LastHour' },
  { label: 'Last 3 Hours', value: 'Last3Hours' },
  { label: 'Last 24 Hours', value: 'Last24Hours' },
  { label: 'Last 7 Days', value: 'Last7Days' },
  { label: 'Last 30 Days', value: 'Last30Days' },
];

const NetworkInterfaceGraph = (params: TransporteMetricsSearchParams) => {
  const themeMode = useColorModeValue('light', 'dark');
  const [timeFilter, setTimeFilter] = useState('Last24Hours');

  const updatedParams = { ...params, time_filter: timeFilter };

  const { data: { events: data = [] } = {}, isLoading, error } = useTransportMetrics(updatedParams);

  const handleTimeFilterChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setTimeFilter(event.target.value);
  };

  if (isLoading) return <Loader />;
  if (error) return <p>Error :</p>;

  const ifDescr = params?.interfaces[0] || '';
  const componentId = params?.component_id || '';
  const interfaceName = data[0]?.interface_name || '';

  const chartTitle = `${componentId} ${ifDescr} ${interfaceName ? ` - ${interfaceName}` : ''}`;

  // Define colors for consistency
  const throughputInColor = '#00b0f0';
  const throughputOutColor = '#00b050';
  const outDiscardsColor = '#00008b';
  const outErrorsColor = '#b22222';
  const inDiscardsColor = '#7030a0';
  const inErrorsColor = '#ff0000';

  const options = {
    title: {
      text: chartTitle,
      left: 'center',
      textStyle: {
        padding: [0, 0, 20, 0],
      },
    },
    tooltip: {
      trigger: 'axis',
      formatter: function (params: any[]) {
        const date = params[0].axisValue; // x-axis value
        const formattedDate = formatInReadableTimeDate(date);
        let tooltipText = `${formattedDate}<br/>`;

        // Loop through series to format tooltip content
        params.forEach((param) => {
          tooltipText += `${param.marker} ${param.seriesName}: ${param.value}<br/>`;
        });

        return tooltipText;
      },
    },
    legend: {
      data: ['Throughput In (Mbps)', 'Throughput Out (Mbps)', 'Out Discards', 'Out Errors', 'In Discards', 'In Errors'],
      bottom: 0,
    },
    grid: {
      bottom: 200,
    },
    toolbox: {
      show: true,
      showTitle: false,
      feature: {
        saveAsImage: {
          show: true,
          title: 'Save As Image',
        },
      },
    },
    dataZoom: [
      {
        type: 'slider',
        xAxisIndex: 0,
        start: 0,
        end: 100,
        bottom: 50,
      },
      {
        type: 'inside',
        xAxisIndex: 0,
        start: 0,
        end: 100,
      },
    ],
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: data.map((event) => event.date),
      axisLabel: {
        rotate: 45,
        formatter: (value: string) => formatInReadableTimeDate(value),
      },
    },
    yAxis: [
      {
        type: 'value',
        name: 'Throughput (Mbps)',
        position: 'left',
        axisLabel: {
          formatter: '{value} Mbps',
        },
      },
      {
        type: 'value',
        name: 'Error/Discard (packets)',
        position: 'right',
        axisLabel: {
          formatter: '{value} packets',
        },
        splitLine: {
          show: false,
        },
      },
    ],
    series: [
      {
        name: 'Throughput In (Mbps)',
        type: 'line',
        data: data.map((event) => event?.ifIn1SecRate),
        yAxisIndex: 0,
        lineStyle: {
          color: throughputInColor,
        },
        itemStyle: {
          color: throughputInColor,
        },
      },
      {
        name: 'Throughput Out (Mbps)',
        type: 'line',
        data: data.map((event) => event?.ifOut1SecRate),
        yAxisIndex: 0,
        lineStyle: {
          color: throughputOutColor,
        },
        itemStyle: {
          color: throughputOutColor,
        },
      },
      {
        name: 'Out Discards',
        type: 'bar',
        data: data.map((event) => event?.outDiscardDelta),
        yAxisIndex: 1,
        itemStyle: {
          color: outDiscardsColor,
        },
      },
      {
        name: 'Out Errors',
        type: 'bar',
        data: data.map((event) => event?.outErrorDelta),
        yAxisIndex: 1,
        itemStyle: {
          color: outErrorsColor,
        },
      },
      {
        name: 'In Discards',
        type: 'bar',
        data: data.map((event) => event?.inDiscardDelta),
        yAxisIndex: 1,
        itemStyle: {
          color: inDiscardsColor,
        },
      },
      {
        name: 'In Errors',
        type: 'bar',
        data: data.map((event) => event?.inErrorDelta),
        yAxisIndex: 1,
        itemStyle: {
          color: inErrorsColor,
        },
      },
    ],
  };

  if (data.length === 0) return <Box>No data available for graph</Box>;

  return (
    <Box width="full" my="10" alignItems="center" data-testid="sub-component-networkGrpah">
      <Box mb="10" gap="4" display="flex" alignItems="center" justifyContent="center" data-testid="selectRange">
        <Text fontSize="md" fontWeight="500">
          Select Time Range
        </Text>
        <Select value={timeFilter} onChange={handleTimeFilterChange} width="200px">
          {timeOptions.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </Select>
      </Box>
      <ReactECharts option={options} echarts={echarts} style={{ height: '600px', width: '100%' }} theme={themeMode} />
    </Box>
  );
};

export default NetworkInterfaceGraph;
