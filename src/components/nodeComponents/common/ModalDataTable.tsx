import {
  <PERSON><PERSON>,
  <PERSON>dal,
  <PERSON>dal<PERSON>ody,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  ModalOverlay,
  Text,
  Divider,
  Flex,
  Progress,
} from '@chakra-ui/react';
import { ColumnDef } from '@tanstack/react-table';
import React, { useState } from 'react';
import { DataTable } from '../../../pages/MetricsCollector/components/DataTable';
import { SegwDataType } from '../../../types/orchestrator.types';
import NetworkInterfaceGraph from './NetworkInterfaceGraph';

const RenderNodes = ({ row, subComponentProps }: any) => {
  const params = {
    ...subComponentProps,
    interfaces: [row?.row?.original?.ifDescr],
    time_filter: 'Last24Hours',
  };
  return <NetworkInterfaceGraph {...params} />;
};

export type ModalDataTableProps<TData> = {
  isOpen: boolean;
  onClose: () => void | boolean;
  data?: TData[];
  columns?: ColumnDef<TData>[];
  multipleColumns?: Partial<Record<keyof SegwDataType, ColumnDef<any>[]>>;
  isMultiple?: boolean;
  vsrData?: any;
  caller?: string | undefined;
  subComponentProps?: Record<string, string>;
};

const ModalDataTable = <TData,>({
  isOpen,
  onClose,
  data,
  columns,
  isMultiple = false,
  vsrData,
  caller,
  multipleColumns,
  subComponentProps,
}: ModalDataTableProps<TData>) => {
  const [step, setStep] = useState(0);

  const getSteps = () => {
    if (isMultiple && data && typeof data === 'object' && multipleColumns) {
      return Object.keys(multipleColumns)
        .filter(
          (key) =>
            key in data &&
            Array.isArray((data as SegwDataType)[key as keyof SegwDataType]) &&
            ((data as SegwDataType)[key as keyof SegwDataType]?.length ?? 0) > 0
        )
        .map((key) => ({
          key: key
            .split('_')
            ?.map((word) => word.charAt(0).toUpperCase() + word.slice(1))
            ?.join(' '),
          tableData: (data as SegwDataType)[key as keyof SegwDataType],
          tableColumns: multipleColumns[key as keyof SegwDataType],
        }));
    }
    return [];
  };

  const steps = getSteps();

  const allEmpty = steps.length === 0;
  const isLastStep = step === steps.length - 1;
  const isFirstStep = step === 0;

  const handleNext = () => {
    if (!isLastStep) {
      setStep((prevStep) => prevStep + 1);
    }
  };

  const handleBack = () => {
    if (!isFirstStep) {
      setStep((prevStep) => prevStep - 1);
    }
  };

  const renderTables = () => {
    if (isMultiple && !allEmpty)
      return (
        <>
          <Flex key={steps[step].key} mb="4" gap="2" flexDirection="column">
            <Text mt="4" mb="6" textAlign="center" fontSize="lg" fontWeight="bold" data-testid="modal-table-title">
              {steps[step].key}
            </Text>
            <DataTable
              columns={steps[step]?.tableColumns || []}
              enableFilter={true}
              data={steps[step]?.tableData || []}
              isExpandable={false}
              defaultPageSize={50}
              hasEmptyResult={false}
              size="sm"
            />
            <Divider mt="4" />
            <Progress value={((step + 1) / steps.length) * 100} size="xs" colorScheme="teal" mt="4" mb="4" />
            <Text textAlign="center" fontSize="sm">
              IPSec Settings {step + 1} of {steps.length}
            </Text>
          </Flex>
        </>
      );

    return (
      <Text mt="4" textAlign="center">
        There is no data present currently. Please try again later.
      </Text>
    );
  };

  const tableData = React.useMemo(() => (caller === 'vsr' ? vsrData : data ?? []), [vsrData, data, caller]);
  return (
    <Modal isOpen={isOpen} onClose={onClose} size={'6xl'}>
      <ModalOverlay />
      <ModalContent data-testid="modal-content">
        <ModalHeader data-testid="modal-header">
          {caller === 'vsr'
            ? 'VSR Routes'
            : caller === 'switch'
            ? 'Clocks'
            : caller === 'druid'
            ? 'Network Routes'
            : caller === 'druidSEGW'
            ? 'SeGW Information'
            : caller === 'interface'
            ? 'Interface Details'
            : 'Component Data'}
        </ModalHeader>

        <ModalCloseButton />
        <ModalBody>
          {isMultiple ? (
            renderTables()
          ) : tableData.length > 0 ? (
            <DataTable
              columns={columns ?? []}
              enableFilter={true}
              data={tableData}
              isExpandable={caller === 'interface' ? true : false}
              caller={caller}
              defaultPageSize={50}
              hasEmptyResult={false}
              size="sm"
              renderSubComponent={(row) => <RenderNodes row={row} subComponentProps={subComponentProps} />}
            />
          ) : (
            <Text mt="4" textAlign="center">
              There is no data present currently. Please try again later.
            </Text>
          )}
        </ModalBody>
        <ModalFooter>
          {isMultiple && steps.length > 1 ? (
            <Flex justifyContent="space-between" width="100%">
              <Button onClick={handleBack} isDisabled={isFirstStep} data-testid="table-back">
                Back
              </Button>
              <Button colorScheme="teal" onClick={handleNext} isDisabled={isLastStep} data-testid="table-next">
                Next
              </Button>
            </Flex>
          ) : (
            <Button colorScheme="blue" mr="3" onClick={onClose}>
              Close
            </Button>
          )}
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default ModalDataTable;
