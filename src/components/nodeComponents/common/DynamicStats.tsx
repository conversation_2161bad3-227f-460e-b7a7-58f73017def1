import _ from 'lodash';
import {
  StatGroup,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Link,
  Tooltip,
  Divider,
  Text,
  VStack,
  Box,
  Button,
  useDisclosure,
} from '@chakra-ui/react';
import {
  BWMmwave,
  FibroClock,
  FibroInterface,
  FibrolanSwitch,
  Interface,
  Vsr,
  FlattenedFastPath,
} from '../../../types/orchestrator.types';
import { constructStatsData, timeticksToTime } from '../../../utils/helpers';
import { UtilizationBar } from './UtilizationBar';
import { formatInReadableTimeDate } from '../../../utils/formatInReadableTimeData';
import ModalDataTable from './ModalDataTable';
import React, { Fragment, useEffect } from 'react';
import { ColumnDef } from '@tanstack/table-core';
import { ExternalLinkIcon } from '@chakra-ui/icons';

interface StatData {
  label: string;
  value: React.ReactNode;
  optionalvalue?: React.ReactNode;
  helpText?: string;
  renderAs?: 'utilizationBar';
  arrow?: 'increase' | 'decrease';
  SectionText?: string;
  isHeader?: boolean;
}
interface DynamicStatsProps {
  data: FibrolanSwitch | BWMmwave | Vsr;
  componentId: string | number | undefined;
  caller?: string | undefined;
  clockData?: Array<any> | null;
  setHasError?: React.Dispatch<React.SetStateAction<boolean>>;
}
type Stat = { value: string | null | undefined };

export const DynamicStats: React.FC<DynamicStatsProps> = ({ data, componentId, caller, clockData, setHasError }) => {
  const statsData = constructStatsData(data, componentId, caller);
  const hasError = statsData.some((stat) => stat.label === 'Last Error' && stat.value && stat.value !== 'N/A');

  function sectionHeader(stats: any[]): any[] {
    let currentSection: string | undefined = undefined;
    let headerIndex = -1;
    stats.forEach((stat, i) => {
      if (stat.SectionText) {
        if (currentSection !== stat.SectionText && headerIndex !== -1) {
          stats[headerIndex].isHeader = stats
            .slice(headerIndex + 1, i)
            .some(({ value }) => value !== null && value !== undefined && value !== 'N/A');
        }
        currentSection = stat.SectionText;
        headerIndex = i;
      } else if (headerIndex !== -1 && (stat.value === null || stat.value === undefined || stat.value === 'N/A')) {
        stats.splice(i, 1);
        i--;
      }
    });
    // last section header
    if (currentSection && headerIndex !== -1) {
      stats[headerIndex].isHeader = stats
        .slice(headerIndex + 1)
        .some(({ value }) => value !== null && value !== undefined && value !== 'N/A');
    }

    return stats.filter((stat) => stat);
  }
  const updatedStatsData = sectionHeader(statsData);
  useEffect(() => {
    if (setHasError) {
      if (hasError) setHasError(true);
      else setHasError(false);
    }
  }, [hasError, setHasError]);

  let filteredStatsData = [];
  if (hasError) {
    let deviceSectionStarted = false;
    filteredStatsData = updatedStatsData.filter((stat) => {
      if (stat.SectionText === 'Device') {
        deviceSectionStarted = true;
      }
      if (stat.SectionText === 'Error') {
        deviceSectionStarted = false;
      }
      return !deviceSectionStarted;
    });
  } else {
    for (const stat of statsData) {
      if (stat.SectionText === 'Error') {
        break;
      }
      filteredStatsData.push(stat);
    }
  }

  filteredStatsData = filteredStatsData.filter(
    (stat) => stat.value !== null && stat.value !== undefined && stat.value !== 'N/A'
  );

  const groupedStats: StatData[][] = [];
  const { isOpen, onOpen, onClose } = useDisclosure();
  const columns = React.useMemo<ColumnDef<Interface | FibroClock | FlattenedFastPath | FibroInterface>[]>(
    () => [
      {
        header: 'Clock ID',
        accessorKey: 'flSyncCenterClockInputId',
        id: 'flSyncCenterClockInputId',
      },
      {
        header: 'Input Type',
        accessorKey: 'flSyncCenterClockInputType',
        id: 'flSyncCenterClockInputType',
      },
      {
        header: 'State',
        accessorKey: 'flSyncCenterClockInputState',
        id: 'flSyncCenterClockInputState',
      },
      {
        header: 'Since',
        accessorKey: 'flSyncCenterClockInputStateLastChange',
        id: 'flSyncCenterClockInputStateLastChange',
        cell: ({ getValue }) => {
          return <>{timeticksToTime(Number(getValue()))}</>;
        },
      },
      {
        header: 'Quality',
        accessorKey: 'flSyncCenterClockInputQuality',
        id: 'flSyncCenterClockInputQuality',
      },
      {
        header: 'Since',
        accessorKey: 'flSyncCenterClockInputQualityLastChange',
        id: 'flSyncCenterClockInputQualityLastChange',
        cell: ({ getValue }) => {
          return <>{timeticksToTime(Number(getValue()))}</>;
        },
      },
    ],
    []
  );
  let i = 0;
  while (i < filteredStatsData.length) {
    const group: any = [];
    const currentStat = filteredStatsData[i] as StatData;
    if (currentStat?.isHeader === false) {
      i++;
      continue;
    }
    group.push(currentStat);

    if (currentStat.isHeader) {
      if (i + 1 < filteredStatsData.length) {
        group.push(filteredStatsData[i + 1]);
      }
      if (i + 2 < filteredStatsData.length) {
        group.push(filteredStatsData[i + 2]);
      }
      i += 3;
    } else {
      if (i + 1 < filteredStatsData.length) {
        group.push(filteredStatsData[i + 1]);
      }
      i += 2;
    }

    groupedStats.push(group);
  }
  return (
    <>
      {groupedStats.map((group, index) => (
        <StatGroup mb={3} key={index} display="flex">
          {group.map((stat, sIndex) => {
            let displayValue = stat.value;
            if (['Uptime', 'License Expiry', 'Updated'].includes(stat.label) && _.isString(stat.value)) {
              displayValue = formatInReadableTimeDate(stat.value);
            }
            return (
              <Fragment key={sIndex}>
                {stat.isHeader && stat.SectionText !== 'GPS Module' && (
                  <Box width="100%" mt={5} mb={5} display="flex" alignItems="center">
                    <Text mb={2} fontWeight="bold" fontSize="md">
                      {stat.SectionText}
                    </Text>
                    <Divider flex={1} mb={4} ml={2} borderColor="gray.300" borderWidth="2px" />
                  </Box>
                )}
                {!stat.isHeader && (
                  <Stat
                    width={'calc(50% - 9px)'}
                    // width={statWidth}
                    height={['40px']}
                    key={sIndex}
                  >
                    <StatLabel>{stat.label}</StatLabel>
                    <Tooltip label={_.isString(displayValue) && displayValue.length > 30 ? displayValue : ''}>
                      <StatNumber fontSize={'md'} isTruncated maxWidth="30ch">
                        {stat.renderAs === 'utilizationBar' ? (
                          <UtilizationBar percentage={Number(stat.value)} />
                        ) : stat.renderAs === 'location' ? (
                          <Link
                            textAlign="left"
                            isExternal
                            rel="noopener noreferrer"
                            href={`http://www.google.com/maps/place/${stat?.value},${stat.optionalvalue}`}
                          >
                            <Box display="flex" alignItems="center">
                              <Box flex="0" display="flex" flexDirection="column">
                                <Text as="span">{stat?.value}</Text>
                                <Text as="span">{stat?.optionalvalue}</Text>
                              </Box>
                              <ExternalLinkIcon ml="0.25" />
                            </Box>
                          </Link>
                        ) : stat.label === 'Selected Clock ID' ? (
                          <Box display="flex" alignItems="center" gap="10px">
                            <Text>{displayValue}</Text>
                            <Text onClick={onOpen} color="blue.500" cursor="pointer">
                              show clocks
                            </Text>
                            <ModalDataTable
                              isOpen={isOpen}
                              onClose={onClose}
                              columns={columns}
                              data={clockData ?? []}
                              caller={'switch'}
                            />
                          </Box>
                        ) : (
                          displayValue
                        )}
                      </StatNumber>
                    </Tooltip>
                    {stat.helpText && <StatHelpText>{stat.helpText}</StatHelpText>}
                  </Stat>
                )}
              </Fragment>
            );
          })}
        </StatGroup>
      ))}
    </>
  );
};
