import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Button,
  Box,
  Flex,
  Text,
  useDisclosure,
  useToast,
  AlertDialog,
  AlertDialogBody,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogOverlay,
  Menu,
  MenuButton,
  MenuItem,
  MenuList,
  Icon,
} from '@chakra-ui/react';
import { MdRefresh } from 'react-icons/md';
import { ChevronDownIcon } from '@chakra-ui/icons';
import {
  ActionPhase,
  useGetActionApiCall,
  usePostActionDruidApiCall,
} from '../../../pages/CellOverview/hooks/services/use_Orc_ServerTasks';
import { ServerActionPostDataType, ServerActionGetDataType, Task } from '../../../types/orchestrator.types';
import { statusIcon } from '../server/acp/ServerTasks';
import _ from 'lodash';
import { AUTH_TOKEN_KEY, READ_ONLY_ACCESS_ROLES } from '../../../data/constants';
import useLogin from '../../../hooks/useLogin';

interface RestartButtonProps {
  nodeId?: string;
}

const RestartButton: React.FC<RestartButtonProps> = ({ nodeId }) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const cancelRef = useRef<HTMLButtonElement>(null);
  const toast = useToast();
  const [triggerGetAction, setTriggerGetAction] = useState<boolean>(false);
  const [selectedOption, setSelectedOption] = useState<ActionPhase | ''>('');
  const [postActionData, setPostActionData] = useState<ServerActionPostDataType[] | null>(null);
  const [isButtonDisabled, setIsButtonDisabled] = useState<boolean>(false);
  const { checkApplicationAccess } = useLogin(AUTH_TOKEN_KEY);
  const isReadOnlyAccess = checkApplicationAccess(READ_ONLY_ACCESS_ROLES);

  const usePostAction = ({ nodeId, selectedOption }: { nodeId: string | undefined; selectedOption: ActionPhase }) => {
    const [triggerPostAction, setTriggerPostAction] = useState<boolean>(false);
    const {
      status: postActionStatus,
      isLoading: isPostActionExecuting,
      data: postActionData,
    }: {
      status: 'error' | 'success' | 'loading';
      isLoading: boolean;
      data: ServerActionPostDataType[] | undefined;
    } = usePostActionDruidApiCall(nodeId ?? '', selectedOption, triggerPostAction);

    useEffect(() => {
      if (triggerPostAction) {
        setTriggerPostAction(false);
      }
    }, [triggerPostAction]);

    const initiatePostAction = useCallback(() => {
      if (selectedOption) {
        setTriggerPostAction(true);
        setIsButtonDisabled(true); // Disable the button when action is initiated
      }
    }, [selectedOption]);

    return { initiatePostAction, postActionData };
  };

  const { initiatePostAction, postActionData: postActionHookData } = usePostAction({
    nodeId,
    selectedOption: selectedOption as ActionPhase,
  });

  const {
    status: getActionStatus,
    isLoading: isGetActionExecuting,
    data: getActionData,
  } = useGetActionApiCall(postActionData, triggerGetAction);

  useEffect(() => {
    if (!_.isUndefined(postActionHookData && _.get(postActionHookData, '[0].uid'))) {
      setTriggerGetAction(true);
      toast({
        title: `${_.get(postActionHookData, '[0].type')} Action created successfully.`,
        status: 'success',
        duration: 5000,
        isClosable: true,
        position: 'top',
      });
      setPostActionData(() => postActionHookData || null);
    }
  }, [postActionHookData, toast]);

  useEffect(() => {
    if (isOpen) {
      setSelectedOption('');
    }
  }, [isOpen]);

  useEffect(() => {
    if (getActionData) {
      const { progress } = getTaskProgressAndTask(getActionData);
      if (_.includes(['completed', 'failed'], progress)) {
        setIsButtonDisabled(false); // Enable the button when task is completed or failed
      }
    }
  }, [getActionData]);

  const handleSelect = (value: string) => {
    if (_.includes(Object.values(ActionPhase), value as ActionPhase)) {
      setSelectedOption((prevState) => (prevState === value ? '' : (value as ActionPhase)));
    }
  };

  const handleActionButtonClick = () => {
    onOpen();
  };

  const handleConfirmClick = () => {
    onClose();
    initiatePostAction();
  };

  const handleCancelClick = () => {
    onClose();
  };

  const TaskStatus = {
    CREATED: 'Created inactive',
    WAITING: 'Active, not yet running',
    BLOCKED: 'Unable to proceed',
    RUNNING: 'Active and running',
    RESULTS: 'Results available',
  };

  const getTaskProgressAndTask = (operation: ServerActionGetDataType[]): { progress: string; task: Task | null } => {
    const tasks = _.get(operation, '[0].info', []);
    if (_.isString(tasks) || _.isEmpty(tasks)) {
      return { progress: 'pending', task: null };
    } else {
      const sortedTasks = _.orderBy(tasks, ['updated_at'], ['desc']);
      const task = _.find(sortedTasks, (task: Task) => task.progress === TaskStatus.RESULTS);
      if (task) {
        if (!_.isString(task.data) && _.get(task, 'data.detail', '').includes('successful')) {
          return { progress: 'completed', task };
        } else {
          return { progress: 'failed', task };
        }
      } else {
        const runningTask = _.find(sortedTasks, (task: Task) => task.progress === TaskStatus.RUNNING);
        if (runningTask) {
          return { progress: 'running', task: runningTask };
        }
      }
      return { progress: 'pending', task: _.first(sortedTasks) || null };
    }
  };

  const getStatusIcon = (progress: string, task: any) => {
    if (progress === 'completed') {
      return statusIcon['completed'];
    } else if (progress === 'failed') {
      return (
        <Flex justifyContent="space-between">
          <Flex>{statusIcon['failed']}</Flex>
          <Flex>
            <Text whiteSpace="normal" ml="3" color="red" mt="1" fontSize="lg">
              {_.isString(task.data)
                ? task.data
                : _.get(JSON.parse(task.data?.detail), 'error_text', task.data?.detail)}
            </Text>
          </Flex>
        </Flex>
      );
    } else if (progress === 'running') {
      return statusIcon['running'];
    } else {
      return statusIcon['pending'];
    }
  };

  return (
    <>
      <Flex justifyContent="flex-end" alignItems="center">
        <Button
          colorScheme="red"
          onClick={() => handleActionButtonClick()}
          size="sm"
          mr="4"
          isDisabled={isButtonDisabled || isReadOnlyAccess}
        >
          <Text>Restart </Text>
          <MdRefresh size="25" />
        </Button>
        {postActionData && (
          <Box display="flex" flexDirection="row" justifyContent="center" alignItems="center">
            {getActionData &&
              (() => {
                const { progress, task } = getTaskProgressAndTask(getActionData);
                return getStatusIcon(progress, task);
              })()}
          </Box>
        )}
      </Flex>

      <AlertDialog isOpen={isOpen} onClose={onClose} isCentered leastDestructiveRef={cancelRef} size="lg">
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              <Flex flexDirection="row">Select a reason for the restart action</Flex>
            </AlertDialogHeader>
            <AlertDialogBody>
              <Menu>
                <MenuButton
                  as={Button}
                  rightIcon={<Icon as={ChevronDownIcon} />}
                  variant="outline"
                  colorScheme="red"
                  size="sm"
                >
                  {selectedOption || 'Click to select an option'}
                </MenuButton>
                <MenuList>
                  {_.map(ActionPhase, (value, key) => (
                    <MenuItem
                      key={key}
                      onClick={() => handleSelect(value)}
                      _hover={{
                        backgroundColor: 'red.400',
                        color: 'white',
                      }}
                    >
                      {value}
                    </MenuItem>
                  ))}
                </MenuList>
              </Menu>
            </AlertDialogBody>
            <AlertDialogFooter>
              <Button onClick={handleCancelClick} colorScheme="blue" ref={cancelRef}>
                Cancel
              </Button>
              <Button colorScheme="red" onClick={handleConfirmClick} isDisabled={_.isEmpty(selectedOption)} ml="3">
                Confirm
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
    </>
  );
};

export default RestartButton;
