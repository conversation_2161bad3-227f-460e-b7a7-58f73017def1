import { useColorModeValue, Progress, Box, Text } from '@chakra-ui/react';

interface UtilizationBarProps {
  percentage: number;
  inverted?: boolean;
  usePadding?: boolean;
  threshold?: number;
}

export const UtilizationBar: React.FC<UtilizationBarProps> = ({
  percentage,
  inverted,
  usePadding = true,
  threshold = 80,
}) => {
  const defaultColorScheme = useColorModeValue(
    percentage > threshold ? 'red' : 'green',
    percentage > threshold ? 'red' : 'green'
  );
  const invertedColorScheme = useColorModeValue(
    percentage > threshold ? 'green' : 'red',
    percentage > threshold ? 'green' : 'red'
  );
  const colorScheme = inverted ? invertedColorScheme : defaultColorScheme;

  return (
    <Box width="100%" pb={usePadding ? 4 : 0} pr={usePadding ? 2 : 0}>
      <Box position="relative">
        <Progress colorScheme={colorScheme} height={6} value={percentage} />
        <Text
          position="absolute"
          top="50%"
          left="50%"
          transform="translate(-50%, -50%)"
          fontWeight="bold"
          // color={percentage < 30 ? 'white' : 'black'}
        >
          {percentage}%
        </Text>
      </Box>
    </Box>
  );
};
