import { Box, Spinner, SpinnerProps } from '@chakra-ui/react';

export const Loader = (props: SpinnerProps) => {
  return (
    <Box textAlign="center" margin="auto" mt="4" mb="4" as={props.as || 'div'}>
      <Spinner
        thickness={props.thickness || '4px'}
        speed="0.65s"
        emptyColor="gray.200"
        color="blue.500"
        size={props.size || 'xl'}
        {...props}
      />
    </Box>
  );
};

export default Loader;
