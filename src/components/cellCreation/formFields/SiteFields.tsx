import { AddIcon } from '@chakra-ui/icons';
import {
  Button,
  ButtonGroup,
  FormControl,
  FormErrorMessage,
  FormHelperText,
  FormLabel,
  IconButton,
  Select,
} from '@chakra-ui/react';
import { UseFormReturn } from 'react-hook-form';
import { CellFormValues } from '../schema';

export type SiteFieldsProps = {
  form: UseFormReturn<CellFormValues>;
  regionSites: { [key: string]: { name: string; id: number }[] };
  isEdit?: boolean;
  updateCellMutation: (cell: { cell_ref: string; cell: { site_id: number } }) => void;
  checkRoleAccess: boolean;
  onCreateOpen: () => void;
};
const SiteFields = ({
  form: {
    control,
    getValues,
    register,
    setValue,
    watch,
    formState: { errors },
  },
  regionSites,
  isEdit,
  updateCellMutation,
  checkRoleAccess,
  onCreateOpen,
}: SiteFieldsProps) => {
  const selectedRegion = watch('region');
  const cellType = watch('oran_split');

  return (
    <FormControl isInvalid={!!errors.site} mb="4" isRequired>
      <FormLabel htmlFor="site">Site</FormLabel>
      <Select
        id="site"
        value={getValues('site')}
        placeholder="Select a site"
        {...register('site')}
        onChange={(e) => {
          setValue('site', e.target.value);
          if (isEdit) {
            updateCellMutation({
              cell_ref: getValues('cell_ref') ?? '',
              cell: { site_id: Number(e.target.value) },
            });
          }
        }}
      >
        {watch('region') &&
          regionSites[watch('region')]?.map(({ name, id }) => (
            <option key={`${id}-site`} value={id}>
              {name}
            </option>
          ))}
      </Select>
      <FormErrorMessage>{selectedRegion ? errors.site?.message : 'Select a region first'}</FormErrorMessage>
      {selectedRegion && cellType && checkRoleAccess ? (
        <FormHelperText>
          <ButtonGroup onClick={onCreateOpen} size="sm" isAttached variant="outline">
            <Button fontWeight="medium">Create site</Button>
            <IconButton aria-label="add new site" icon={<AddIcon />} />
          </ButtonGroup>
        </FormHelperText>
      ) : null}
    </FormControl>
  );
};

export default SiteFields;
