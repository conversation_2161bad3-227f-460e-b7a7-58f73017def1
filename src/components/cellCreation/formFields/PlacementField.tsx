import { FormControl, FormErrorMessage, FormLabel, Select } from '@chakra-ui/react';
import { UseFormReturn, Controller } from 'react-hook-form';
import { CellFormValues } from '../schema';

export type PlacementFieldProps = {
  form: UseFormReturn<CellFormValues>;
  placements?: { value: string; label: string }[];
  isEdit?: boolean;
};

const PlacementField = ({
  form: {
    control,
    register,
    formState: { errors },
  },
  placements = [
    { value: 'Indoor', label: 'Indoor' },
    { value: 'Outdoor', label: 'Outdoor' },
  ],
  isEdit,
}: PlacementFieldProps) => {
  return (
    <FormControl isInvalid={!!errors.placement} mt="2" mb="4" isDisabled={isEdit} isRequired flex="1">
      <FormLabel mb="4" htmlFor="placement">
        Placement
      </FormLabel>
      <Controller
        name="placement"
        control={control}
        render={({ field }) => (
          <Select
            {...field}
            placeholder="Select a Placement"
            id="placement"
            {...register('placement')}
            onChange={(e) => {
              field.onChange(e.target.value);
            }}
          >
            {placements.map((placement) => (
              <option key={placement.value} value={placement.value}>
                {placement.label}
              </option>
            ))}
          </Select>
        )}
      />
      <FormErrorMessage>{errors.placement?.message}</FormErrorMessage>
    </FormControl>
  );
};

export default PlacementField;
