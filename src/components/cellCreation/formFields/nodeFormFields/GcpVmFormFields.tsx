import { DeleteIcon } from '@chakra-ui/icons';
import {
  Badge,
  Button,
  FormControl,
  FormErrorMessage,
  FormLabel,
  HStack,
  Select,
  Text,
  useColorModeValue,
} from '@chakra-ui/react';
import { memo } from 'react';
import {
  Control,
  Controller,
  useFieldArray,
  UseFormGetValues,
  UseFormRegister,
  UseFormSetValue,
  UseFormWatch,
} from 'react-hook-form';
import { useLocation } from 'react-router';
import MultiSelect from 'react-select'; // react-select MultiSelect
import { LifeCycle, LIFE_CYCLE, ORIENTATION, ROLE_OF_NODE, ROLE_OF_NODE_ARRAY } from '../../../../data/constants';
import { useDeleteNodeFromCell } from '../../../../pages/CellOverview/hooks/services/use_Inv_DeleteNodeFromCell';
import { Node } from '../../../../types/InventoryManager.type';
import { useDeleteNode } from '../../hooks/useDeleteNode';
import useUpdateCell from '../../hooks/useUpdateCell';
import useUpdateNode from '../../hooks/useUpdateNode';
import { CellFormValues, NodeSchemaValues } from '../../schema';
import {
  rolesCustomDarkTheme,
  rolesCustomLightTheme,
  rolesMultiSelectDarkStyles,
  rolesMultiSelectLightStyles,
} from '../../styles/MultiSelectStyles';

export type GcpVmFormFieldsProps = {
  control: Control<CellFormValues>;
  getValues: UseFormGetValues<CellFormValues>;
  register: UseFormRegister<CellFormValues>;
  setValue: UseFormSetValue<CellFormValues>;
  watch: UseFormWatch<CellFormValues>;
  errors: any;
  isEdit?: boolean;
  isGNodeBTypeCell: boolean;
  isSplit6TypeCell: boolean;
  item: any;
  index: number;
  availableServerList: Node[] | undefined;
  regionSites: any;
};

const GcpVmFormFields = ({
  control,
  getValues,
  register,
  setValue,
  watch,
  errors,
  isEdit,
  item,
  index,
  availableServerList,
  regionSites,
}: GcpVmFormFieldsProps) => {
  const {
    fields: serverFields,
    remove: removeVMServer,
    prepend: prependServer,
  } = useFieldArray({
    control,
    name: 'serverVMNodes',
  });
  const borderColor = useColorModeValue('1px solid #E2E8F0', '1px solid #2D3748');
  const lightOrDarkTheme = useColorModeValue('light', 'dark');

  const { updateCellMutation } = useUpdateCell();
  const { updateNodeMutation } = useUpdateNode();
  const { deleteNodeMutation } = useDeleteNode();
  const { deleteNodeFromCellMutation } = useDeleteNodeFromCell();

  const { state } = useLocation();

  const defaultNode: NodeSchemaValues = {
    lifecycle: '',
    orientation: '',
    site: '',
    node_serial_no: '',
    node_id: '',
    node_type: 'StreetCell',
    version: '1.1',
    roles: [],
  };

  const getSelectedNode = (targetNode: string, poulatedRoles = false, useServerList: Node[] | undefined) => {
    const selectedNode: NodeSchemaValues | undefined = useServerList
      ?.map((node): NodeSchemaValues => {
        return {
          ...defaultNode,
          node_id: node.node_id,
          node_type: node.node_type,
          lifecycle: node.lifecycle,
          version: node.version || '1.1',
          roles: poulatedRoles ? [] : ['RU'],
          site: node.site_id?.toString() || '',
          orientation: node.orientation as ORIENTATION,
          node_serial_no: node.node_serial_no,
        };
      })
      ?.find((node) => node.node_serial_no === targetNode);

    return selectedNode;
  };

  return (
    <HStack alignItems="baseline">
      {/* Serial number */}
      <FormControl isInvalid={!!errors?.serverVMNodes?.[index]?.node_serial_no} mb="4" isRequired mr="4">
        <FormLabel>Serial Number</FormLabel>
        <Controller
          name={`serverVMNodes.${index}.node_serial_no`}
          defaultValue={item.node_serial_no}
          control={control}
          render={({ field }) => (
            <Select
              {...field}
              placeholder="Select a serial no"
              disabled={isEdit && watch(`serverVMNodes.${index}.node_id`) !== ''}
              onChange={(e) => {
                if (isEdit) {
                  updateNodeMutation({
                    node_id: getValues(`serverVMNodes.${index}.node_id`) ?? '',
                    node: { node_serial_no: e.target.value },
                  });
                  field.onChange(e);
                } else {
                  setValue(
                    `serverVMNodes.${index}`,
                    getSelectedNode(e.target.value, true, availableServerList) ?? defaultNode
                  );
                  field.onChange(e);
                }
              }}
            >
              {availableServerList
                ?.filter((node) => node.node_type !== 'StreetCell')
                .map((node) => (
                  <option key={node?.node_serial_no} value={node?.node_serial_no}>
                    {node?.node_serial_no}
                  </option>
                ))}
            </Select>
          )}
        />
        <FormErrorMessage>{errors?.serverVMNodes?.[index]?.node_serial_no?.message}</FormErrorMessage>
      </FormControl>
      {/* Node Type*/}
      <FormControl isInvalid={!!errors?.serverVMNodes?.[index]?.node_type} mb="4" isRequired mr="4">
        <FormLabel>Node type</FormLabel>
        <Controller
          name={`serverVMNodes.${index}.node_type`}
          control={control}
          render={({ field }) => (
            <Text {...register(`serverVMNodes.${index}.node_type`)} fontSize="medium" fontWeight="medium">
              <Badge paddingY="1" paddingX="4">
                {item.node_type}
              </Badge>
            </Text>
          )}
        />
        <FormErrorMessage>{errors?.node_serial_no?.message}</FormErrorMessage>
      </FormControl>
      {/* Role */}
      <FormControl isInvalid={!!errors?.serverNodes?.[index]?.roles} mb="4" isRequired mr="4">
        <FormLabel>Role</FormLabel>
        <Controller
          name={`serverNodes.${index}.roles`}
          control={control}
          defaultValue={item.roles}
          render={({ field }) => (
            <MultiSelect
              isMulti
              options={ROLE_OF_NODE_ARRAY}
              defaultValue={ROLE_OF_NODE_ARRAY.filter((option) => item.roles.includes(option.value))}
              theme={(theme) =>
                lightOrDarkTheme === 'light' ? rolesCustomLightTheme(theme) : rolesCustomDarkTheme(theme)
              }
              styles={lightOrDarkTheme === 'light' ? rolesMultiSelectLightStyles : rolesMultiSelectDarkStyles}
              name={`serverNodes.${index}.roles`}
              className="roles-multi-select"
              classNamePrefix="select"
              getOptionLabel={(option) => option.label}
              getOptionValue={(option) => option.value}
              onChange={(selectedOptions) => {
                const updatedRoles = selectedOptions.map((option) => option.value);
                const validRoles = updatedRoles.filter((role) =>
                  Object.values(ROLE_OF_NODE).includes(role as ROLE_OF_NODE)
                ) as ROLE_OF_NODE[];
                setValue(`serverNodes.${index}.roles`, validRoles);
                if (isEdit) {
                  updateNodeMutation({
                    node_id: item.node_id ?? '',
                    node: { roles: updatedRoles },
                  });
                }
              }}
            />
          )}
        />
        <FormErrorMessage>{errors?.serverNodes?.[index]?.roles?.message}</FormErrorMessage>
      </FormControl>
      {/* Lifecycle */}
      <FormControl isInvalid={!!errors?.serverVMNodes?.[index]?.lifecycle} mb="4" isRequired mr="4">
        <FormLabel>Lifecycle</FormLabel>
        <Controller
          name={`serverVMNodes.${index}.lifecycle`}
          control={control}
          defaultValue={item.lifecycle}
          render={({ field }) => (
            <Select
              {...field}
              placeholder="Select a lifecycle"
              onChange={(e) => {
                if (isEdit) {
                  field.onChange(e);
                  updateNodeMutation({
                    node_id: item.node_id,
                    node: {
                      lifecycle: e.target.value as LIFE_CYCLE,
                    },
                  });
                } else {
                  field.onChange(e);
                }
              }}
            >
              {Object.entries(LifeCycle).map(([key, value]) => (
                <option
                  key={value}
                  value={key}
                  disabled={!isEdit ? value > LifeCycle[getValues('lifecycle') as keyof typeof LifeCycle] : false}
                  style={{
                    color:
                      !isEdit && value > LifeCycle[getValues('lifecycle') as keyof typeof LifeCycle] ? '#85828280' : '',
                  }}
                >
                  {key}
                </option>
              ))}
            </Select>
          )}
        />
        <FormErrorMessage>{errors?.serverVMNodes?.[index]?.lifecycle?.message}</FormErrorMessage>
      </FormControl>
      {/* Site */}
      <FormControl isInvalid={!!errors?.serverVMNodes?.[index]?.site} mb="4" isDisabled={!watch('region')} isRequired>
        <FormLabel htmlFor={`${index}_site`}>Site</FormLabel>
        <Controller
          name={`serverVMNodes.${index}.site`}
          control={control}
          defaultValue={item.site}
          render={({ field }) => (
            <Select
              {...field}
              placeholder="Select a site"
              onChange={(e) => {
                if (isEdit) {
                  field.onChange(e);
                  updateNodeMutation({
                    node_id: item.node_id,
                    node: { site_id: Number(e.target.value) },
                  });
                } else {
                  field.onChange(e);
                }
              }}
            >
              {/* {regionSites[watch('region')]?.map(({ name, id }) => (
                  <option key={`${id}-site`} value={id}>
                    {name}
                  </option>
                ))} */}
              {regionSites[watch('region')]?.map(({ name, id }: { name: string; id: number }) => (
                <option key={`${id}-site`} value={id}>
                  {name}
                </option>
              ))}
            </Select>
          )}
        />
        <FormErrorMessage>
          {watch('region') ? errors?.serverVMNodes?.[index]?.site?.message : 'Select a region first'}
        </FormErrorMessage>
      </FormControl>
      {/* Delete button */}
      {isEdit ? (
        <FormControl width="25%">
          <FormLabel visibility="hidden">Del</FormLabel>
          <Button
            colorScheme="teal"
            variant="outline"
            onClick={() => {
              // Do a proper delete
              if (isEdit && getValues(`serverVMNodes.${index}.node_id`) !== '') {
                deleteNodeFromCellMutation({
                  node_id: item.node_id ?? '',
                  cell_ref: state?.cell_ref ?? '',
                });
              }
              removeVMServer(index);
            }}
          >
            <DeleteIcon />
          </Button>
        </FormControl>
      ) : (
        <FormControl width="25%">
          <FormLabel visibility="hidden">Del</FormLabel>
          <Button
            colorScheme="teal"
            variant="outline"
            onClick={() => {
              if (isEdit && getValues(`serverVMNodes.${index}.node_id`) !== '') {
                deleteNodeMutation({
                  node_id: getValues(`serverVMNodes.${index}.node_id`) ?? '',
                  cell_ref: state?.cell_ref ?? '',
                });
              }
              removeVMServer(index);
            }}
          >
            <DeleteIcon />
          </Button>
        </FormControl>
      )}
    </HStack>
  );
};

//export default ServerFormFields;
export default memo(GcpVmFormFields);
