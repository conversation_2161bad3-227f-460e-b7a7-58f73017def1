import { DeleteIcon } from '@chakra-ui/icons';
import {
  Badge,
  Button,
  FormControl,
  FormErrorMessage,
  FormLabel,
  HStack,
  Select,
  Text,
  useColorModeValue,
} from '@chakra-ui/react';
import { memo } from 'react';
import {
  Control,
  Controller,
  useFieldArray,
  UseFormGetValues,
  UseFormRegister,
  UseFormSetValue,
  UseFormWatch,
} from 'react-hook-form';
import { useLocation } from 'react-router';
import { LIFE_CYCLE, ORIENTATION, LifeCycle } from '../../../../data/constants';
import { useDeleteNodeFromCell } from '../../../../pages/CellOverview/hooks/services/use_Inv_DeleteNodeFromCell';
import { Node, UpdateCellRequest } from '../../../../types/InventoryManager.type';
import { useDeleteNode } from '../../hooks/useDeleteNode';
import useUpdateCell from '../../hooks/useUpdateCell';
import useUpdateNode from '../../hooks/useUpdateNode';
import { CellFormValues, NodeSchemaValues } from '../../schema';

export type EditStreetCellProps = {
  control: Control<CellFormValues>;
  getValues: UseFormGetValues<CellFormValues>;
  register: UseFormRegister<CellFormValues>;
  setValue: UseFormSetValue<CellFormValues>;
  watch: UseFormWatch<CellFormValues>;
  errors: any;
  availableNodeList: Node[] | undefined;
  isEdit?: boolean;
  isGNodeBTypeCell: boolean;
  isSplit6TypeCell: boolean;
  item: any;
  index: number;
  nodeTypeCount: any;
  regionSites: any;
};

const StreetCellFormFields = ({
  control,
  getValues,
  register,
  setValue,
  watch,
  errors,
  availableNodeList,
  isEdit,
  isGNodeBTypeCell,
  isSplit6TypeCell,
  item,
  index,
  nodeTypeCount,
  regionSites,
}: EditStreetCellProps) => {
  const {
    fields: streetCellFields,
    remove: removeStreetCell,
    prepend: prependStreetCell,
  } = useFieldArray({
    control,
    name: 'streetCellNodes',
  });

  const borderColor = useColorModeValue('1px solid #E2E8F0', '1px solid #2D3748');

  const { updateCellMutation } = useUpdateCell();
  const { updateNodeMutation } = useUpdateNode();
  const { deleteNodeMutation } = useDeleteNode();
  const { deleteNodeFromCellMutation } = useDeleteNodeFromCell();

  const { state } = useLocation();

  const defaultNode: NodeSchemaValues = {
    lifecycle: '',
    orientation: '',
    site: '',
    node_serial_no: '',
    node_id: '',
    node_type: 'StreetCell',
    version: '1.1',
    roles: [],
  };

  const getSelectedNode = (targetNode: string, poulatedRoles = false, useServerList: Node[] | undefined) => {
    const selectedNode: NodeSchemaValues | undefined = useServerList
      ?.map((node): NodeSchemaValues => {
        return {
          ...defaultNode,
          node_id: node.node_id,
          node_type: node.node_type,
          lifecycle: node.lifecycle,
          version: node.version || '1.1',
          roles: poulatedRoles ? [] : ['RU'],
          site: node.site_id?.toString() || '',
          orientation: node.orientation as ORIENTATION,
          node_serial_no: node.node_serial_no,
        };
      })
      ?.find((node) => node.node_serial_no === targetNode);

    return selectedNode;
  };

  return (
    <HStack alignItems="baseline">
      {/* Serial Number */}
      <FormControl isInvalid={!!errors?.streetCellNodes?.[index]?.node_serial_no} mb="4" isRequired mr="4">
        <FormLabel>Serial Number</FormLabel>
        <Controller
          name={`streetCellNodes.${index}.node_serial_no`}
          defaultValue={item.node_serial_no}
          control={control}
          render={({ field }) => (
            <Select
              {...register(`streetCellNodes.${index}.node_serial_no`)}
              placeholder="Select a serial no"
              disabled={isEdit && watch(`streetCellNodes.${index}.node_id`) !== ''}
              onChange={(e) => {
                if (isEdit) {
                  updateNodeMutation({
                    node_id: getValues(`streetCellNodes.${index}.node_id`) ?? '',
                    node: { node_serial_no: e.target.value },
                  });
                  field.onChange(e);
                } else {
                  setValue(
                    `streetCellNodes.${index}`,
                    getSelectedNode(e.target.value, true, availableNodeList) ?? defaultNode
                  );
                  field.onChange(e);
                }
              }}
            >
              {availableNodeList
                ?.filter((node) => node?.node_type === 'StreetCell')
                .map((node) => (
                  <option key={node?.node_serial_no} value={node?.node_serial_no}>
                    {node?.node_serial_no}
                  </option>
                ))}
            </Select>
          )}
        />
        <FormErrorMessage>{errors?.streetCellNodes?.[index]?.node_serial_no?.message}</FormErrorMessage>
      </FormControl>
      {/* Lifecycle */}
      <FormControl isInvalid={!!errors?.streetCellNodes?.[index]?.lifecycle} mb="4" isRequired mr="4">
        <FormLabel>Lifecycle</FormLabel>
        <Controller
          name={`streetCellNodes.${index}.lifecycle`}
          control={control}
          defaultValue={item.lifecycle}
          render={({ field }) => (
            <Select
              {...field}
              placeholder="Select a lifecycle"
              id="streetCellNodesLifecycle"
              onChange={(e) => {
                if (isEdit) {
                  field.onChange(e);
                  updateNodeMutation({
                    node_id: item.node_id,
                    node: {
                      lifecycle: e.target.value as LIFE_CYCLE,
                    },
                  });
                } else {
                  field.onChange(e);
                }
              }}
            >
              {Object.entries(LifeCycle).map(([key, value]) => (
                <option
                  key={value}
                  value={key}
                  disabled={!isEdit ? value > LifeCycle[getValues('lifecycle') as keyof typeof LifeCycle] : false}
                  style={{
                    color:
                      !isEdit && value > LifeCycle[getValues('lifecycle') as keyof typeof LifeCycle] ? '#85828280' : '',
                  }}
                >
                  {key}
                </option>
              ))}
            </Select>
          )}
        />
        <FormErrorMessage>{errors?.streetCellNodes?.[index]?.lifecycle?.message}</FormErrorMessage>
      </FormControl>
      {/* Site */}
      <FormControl isInvalid={!!errors?.streetCellNodes?.[index]?.site} mb="4" isDisabled={!watch('region')} isRequired>
        <FormLabel htmlFor={`${index}_site`}>Site</FormLabel>
        <Controller
          name={`streetCellNodes.${index}.site`}
          control={control}
          defaultValue={item.site}
          render={({ field }) => (
            <Select
              {...field}
              placeholder="Select a site"
              onChange={(e) => {
                if (isEdit) {
                  field.onChange(e);
                  updateNodeMutation({
                    node_id: item.node_id,
                    node: { site_id: Number(e.target.value) },
                  });
                } else {
                  field.onChange(e);
                }
              }}
            >
              {/* {regionSites[watch('region')]?.map(({ name, id }) => (
                  <option key={`${id}-site`} value={id}>
                    {name}
                  </option>
                ))} */}
              {regionSites[watch('region')]?.map(({ name, id }: { name: string; id: number }) => (
                <option key={`${id}-site`} value={id}>
                  {name}
                </option>
              ))}
            </Select>
          )}
        />
        <FormErrorMessage>
          {watch('region') ? errors?.streetCellNodes?.[index]?.site?.message : 'Select a region first'}
        </FormErrorMessage>
      </FormControl>
      {/* Orientation */}
      <FormControl isInvalid={!!errors?.streetCellNodes?.[index]?.orientation} mb="4" isRequired>
        <FormLabel>Orientation</FormLabel>
        <Controller
          name={`streetCellNodes.${index}.orientation`}
          defaultValue={item.orientation}
          control={control}
          render={({ field }) => (
            <Select
              placeholder="Select an orientation"
              id="streetCellNodesOrientation"
              defaultValue={getValues(`streetCellNodes.${index}.orientation`) as ORIENTATION}
              {...register(`streetCellNodes.${index}.orientation`)}
              onChange={(e) => {
                if (isEdit) {
                  if (isGNodeBTypeCell) {
                    updateCellMutation({
                      cell_ref: getValues('cell_ref') ?? '',
                      cell: {
                        orientation: e.target.value,
                      } as UpdateCellRequest,
                    });
                  }
                  updateNodeMutation({
                    node_id: item.node_id,
                    node: { orientation: e.target.value },
                  });
                }
              }}
            >
              {Object.entries(ORIENTATION).map(([key, value]) => (
                <option key={value} value={value}>
                  {key}
                </option>
              ))}
            </Select>
          )}
        />
        <FormErrorMessage>{errors?.orientation?.message}</FormErrorMessage>
      </FormControl>
      {/* Delete button */}
      {isSplit6TypeCell && isEdit && nodeTypeCount.StreetCell > 1 ? (
        <FormControl width="25%">
          <FormLabel visibility="hidden">Del</FormLabel>
          <Button
            colorScheme="teal"
            variant="outline"
            onClick={() => {
              // Do a proper delete
              if (isEdit && getValues(`streetCellNodes.${index}.node_id`) !== '') {
                deleteNodeFromCellMutation({
                  node_id: item.node_id ?? '',
                  cell_ref: state?.cell_ref ?? '',
                });
              }
              removeStreetCell(index);
            }}
          >
            <DeleteIcon />
          </Button>
        </FormControl>
      ) : null}
      {isEdit ? null : (
        <FormControl width="25%">
          <FormLabel visibility="hidden">Del</FormLabel>
          <Button
            colorScheme="teal"
            variant="outline"
            onClick={() => {
              if (isEdit && getValues(`streetCellNodes.${index}.node_id`) !== '') {
                deleteNodeMutation({
                  node_id: getValues(`streetCellNodes.${index}.node_id`) ?? '',
                  cell_ref: state?.cell_ref ?? '',
                });
              }
              removeStreetCell(index);
            }}
          >
            <DeleteIcon />
          </Button>
        </FormControl>
      )}
    </HStack>
  );
};

//export default StreetCellFormFields;
export default memo(StreetCellFormFields);
