import { Collapse, FormControl, FormErrorMessage, FormLabel, Select } from '@chakra-ui/react';
import { UseFormReturn } from 'react-hook-form';
import { ORIENTATION } from '../../../data/constants';
import { CellFormValues } from '../schema';

export type OrientationFieldsProps = {
  form: UseFormReturn<CellFormValues>;
  isEdit?: boolean;
  isSplit6TypeCell: boolean;
  updateCellMutation: (cell: { cell_ref: string; cell: { orientation: ORIENTATION } }) => void;
  //updateCellMutation: any;
};
const OrientationFields = ({
  form: {
    control,
    getValues,
    register,
    setValue,
    watch,
    formState: { errors },
  },
  isEdit,
  isSplit6TypeCell,
  updateCellMutation,
}: OrientationFieldsProps) => {
  return (
    <>
      <Collapse in={isSplit6TypeCell} unmountOnExit={true} className="chakra-form-control">
        <FormControl isInvalid={!!errors?.orientation} mb="4" isRequired>
          <FormLabel htmlFor="orientation">Orientation - HI</FormLabel>
          <Select
            id="orientation"
            placeholder="Select an orientation"
            {...register('orientation')}
            // onChange={(e) => {
            //   if (isEdit) {
            //     updateCellMutation({
            //       cell_ref: getValues('cell_ref') ?? '',
            //       cell: {
            //         orientation: e.target.value,
            //       } as UpdateCellRequest,
            //     });
            //   }
            // }}
          >
            {Object.entries(ORIENTATION).map(([key, value]) => (
              <option key={value} value={value}>
                {key}
              </option>
            ))}
          </Select>
          <FormErrorMessage>{errors?.orientation?.message}</FormErrorMessage>
        </FormControl>
      </Collapse>
    </>
  );
};

export default OrientationFields;
