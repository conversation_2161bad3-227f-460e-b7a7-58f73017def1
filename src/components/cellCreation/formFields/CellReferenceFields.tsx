import {
  Button,
  Center,
  Collapse,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Input,
  InputGroup,
  InputRightElement,
  Stack,
  Text,
} from '@chakra-ui/react';
import { UseFormReturn } from 'react-hook-form';
import CustomControl from '../CustomControl';
import { CellFormValues } from '../schema';
import ToolTipDisplay from '../../tooltip/ToolTipDisplay';

export type CellReferenceFieldsProps = {
  form: UseFormReturn<CellFormValues>;
  isEdit?: boolean;
  generatedCellRef: string;
  refetchCellRef: () => void;
};
const CellReferenceFields = ({
  form: {
    control,
    getValues,
    register,
    setValue,
    watch,
    formState: { errors },
  },
  isEdit,
  generatedCellRef,
  refetchCellRef,
}: CellReferenceFieldsProps) => {
  return (
    <Stack flex="1">
      <Collapse in={watch('site') ? true : false} animateOpacity>
        <Stack
          spacing="6"
          direction={{
            base: 'column',
            md: 'row',
          }}
          verticalAlign="bottom"
        >
          <FormControl isInvalid={!!errors.cell_ref} mb="4" isDisabled={watch('site') ? false : true}>
            <FormLabel htmlFor="cell_ref">
              <Flex>
                <Center>
                  <Text mr="4">Cell Reference</Text>
                </Center>
                <ToolTipDisplay />
              </Flex>
            </FormLabel>
            {isEdit ? (
              <CustomControl cell_ref={generatedCellRef} />
            ) : (
              <InputGroup>
                <Input
                  id="cell_ref"
                  pr="4.5rem"
                  readOnly={true}
                  type="text"
                  value={generatedCellRef}
                  placeholder="Generating cell reference..."
                  {...register('cell_ref')}
                />
                <InputRightElement width="6.5rem">
                  <Button
                    h="1.75rem"
                    size="xs"
                    isDisabled={watch('isConflictedCellRef') ? false : true}
                    onClick={() => refetchCellRef()}
                  >
                    Regenerate
                  </Button>
                </InputRightElement>
              </InputGroup>
            )}

            <FormErrorMessage>{errors.cell_ref?.message}</FormErrorMessage>
          </FormControl>
        </Stack>
      </Collapse>
    </Stack>
  );
};

export default CellReferenceFields;
