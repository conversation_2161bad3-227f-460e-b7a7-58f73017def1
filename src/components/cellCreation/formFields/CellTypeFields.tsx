import { FormControl, FormErrorMessage, FormLabel, Select } from '@chakra-ui/react';
import { Controller, UseFormReturn } from 'react-hook-form';
import { O_RAN_SPLIT } from '../../../data/constants';
import { CellFormValues } from '../schema';

export type CellTypeFieldsProps = {
  form: UseFormReturn<CellFormValues>;
  isEdit?: boolean;
  toast: any;
};
const CellTypeFields = ({
  form: {
    control,
    getValues,
    register,
    setValue,
    watch,
    formState: { errors },
  },
  isEdit,
  toast,
}: CellTypeFieldsProps) => {
  return (
    <FormControl isInvalid={!!errors.oran_split} mb="4" isRequired>
      <FormLabel htmlFor="oran_split">Cell Type</FormLabel>
      <Controller
        name="oran_split"
        control={control}
        render={({ field }) => (
          <Select
            {...field}
            placeholder="Select O-RAN split"
            id="oran_split"
            onChange={(e) => {
              if (isEdit) {
                toast({
                  title: `Can't change the cell type for an existing cell (copy
                    correction needed)`,
                  status: 'warning',
                  position: 'top',
                });
              } else {
                field.onChange(e);
              }
            }}
          >
            {Object.values(O_RAN_SPLIT).map((value) => (
              <option key={value} value={value}>
                {value}
              </option>
            ))}
          </Select>
        )}
      />
      <FormErrorMessage>{errors.oran_split?.message}</FormErrorMessage>
    </FormControl>
  );
};
export default CellTypeFields;
