import { FormControl, FormErrorMessage, FormLabel, Select } from '@chakra-ui/react';
import { Controller, UseFormReturn } from 'react-hook-form';
import { LifeCycle, LIFE_CYCLE } from '../../../data/constants';
import { CellFormValues } from '../schema';

export type LifeCycleFieldsProps = {
  form: UseFormReturn<CellFormValues>;
  isEdit?: boolean;
  updateCellMutation: (cell: { cell_ref: string; cell: { lifecycle: LIFE_CYCLE } }) => void;
};
const LifeCycleFields = ({
  form: {
    control,
    getValues,
    register,
    setValue,
    watch,
    formState: { errors },
  },
  isEdit,
  updateCellMutation,
}: LifeCycleFieldsProps) => {
  return (
    <FormControl isInvalid={!!errors.lifecycle} mb="4" isRequired>
      <FormLabel htmlFor="lifecycle">Lifecycle</FormLabel>
      <Controller
        name="lifecycle"
        control={control}
        defaultValue={!isEdit ? 'COMMISSIONING' : ''}
        render={({ field }) => (
          <Select
            {...field}
            placeholder="Select a lifecycle"
            id="lifecycle"
            onChange={(e) => {
              if (isEdit) {
                field.onChange(e);
                updateCellMutation({
                  cell_ref: getValues('cell_ref') ?? '',
                  cell: { lifecycle: e.target.value as LIFE_CYCLE },
                });
              } else {
                field.onChange(e);
              }
            }}
          >
            {Object.entries(LifeCycle).map(([key, value]) => (
              <option
                key={value}
                value={key}
                disabled={!isEdit ? value !== '2' : false}
                style={{
                  ...(!isEdit && {
                    color: value !== '2' ? '#85828280' : '',
                  }),
                }}
              >
                {key}
              </option>
            ))}
          </Select>
        )}
      />
      <FormErrorMessage>{errors.lifecycle?.message}</FormErrorMessage>
    </FormControl>
  );
};

export default LifeCycleFields;
