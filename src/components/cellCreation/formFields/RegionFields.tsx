import { FormControl, FormErrorMessage, FormLabel, Select } from '@chakra-ui/react';
import { UseFormReturn } from 'react-hook-form';
import { CellFormValues } from '../schema';

export type RegionFieldsProps = {
  form: UseFormReturn<CellFormValues>;
  regions: { value: string; label: string }[];
  isEdit?: boolean;
};

const RegionFields = ({
  form: {
    control,
    getValues,
    register,
    setValue,
    watch,
    formState: { errors },
  },
  regions,
  isEdit,
}: RegionFieldsProps) => {
  return (
    <FormControl isInvalid={!!errors.region} mb="4" isRequired>
      <FormLabel htmlFor="region">Region</FormLabel>
      <Select
        id="region"
        placeholder="Select a region"
        disabled={isEdit}
        value={getValues('region')}
        {...register('region')}
      >
        {regions.map((region) => (
          <option key={region.value} value={region.value}>
            {region.label}
          </option>
        ))}
      </Select>
      <FormErrorMessage>{errors.region?.message}</FormErrorMessage>
    </FormControl>
  );
};

export default RegionFields;
