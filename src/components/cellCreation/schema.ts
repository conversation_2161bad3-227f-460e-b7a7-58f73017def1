import { z } from 'zod';
import { LifeCycle, ORIENTATION, O_RAN_SPLIT } from '../../data/constants';

const baseSchema = z.object({
  lifecycle: z
    .nativeEnum(LifeCycle, {
      errorMap: () => {
        return { message: 'Lifecycle is required' };
      },
    })
    .or(z.string().nonempty({ message: 'Lifecycle is required' })),
  site: z.string().nonempty({ message: 'Site is required' }),
});

const cellSchema = z
  .object({
    orientation: z
      .nativeEnum(ORIENTATION, {
        errorMap: () => {
          return { message: 'Orientation is required' };
        },
      })
      .or(z.string().nonempty({ message: 'Orientation is required' })),
    cell_ref: z.string().optional(),
    region: z.string().nonempty({ message: 'Region is required' }),
    placement: z.string().nonempty({ message: 'Placement is required' }),
    node_serial_no: z.string().nonempty({ message: 'Serial number is required' }).optional(),
    oran_split: z
      .nativeEnum(O_RAN_SPLIT, {
        errorMap: () => {
          return { message: 'ORAN Split is required' };
        },
      })
      .or(z.string().nonempty({ message: 'ORAN Split is required' })),
  })
  .merge(baseSchema);

const nodeSchema = z
  .object({
    orientation: z
      .nativeEnum(ORIENTATION, {
        errorMap: () => {
          return { message: 'Orientation is required' };
        },
      })
      .or(z.string().nonempty({ message: 'Orientation is required' })),
    node_serial_no: z.string().nonempty({ message: 'Serial no is required' }),
    node_id: z.string().default(''),
    node_type: z.string().default('StreetCell'),
    version: z.string().default('1.1'),
    roles: z
      .array(z.enum(['RU']))
      .or(z.array(z.enum(['RU', 'DU', 'CU'])))
      .default(['RU']),
  })
  .merge(baseSchema);

const serverNodeSchema = z
  .object({
    node_serial_no: z.string().nonempty({ message: 'Serial no is required' }),
    node_id: z.string().default(''),
    node_type: z.string().default('Server'),
    version: z.string().default('1.1'),
    roles: z
      .array(z.enum(['RU', 'DU', 'CU', 'MMW', 'VSR', 'CORE', 'EMS', 'JUMP', 'SEGW', 'NHE', 'PKI', 'PDN', 'UPS']))
      .default(['RU']),
    orientation: z.nativeEnum(ORIENTATION).or(z.string()).optional(),
  })
  .merge(baseSchema);

const switchNodeSchema = z
  .object({
    node_serial_no: z.string().nonempty({ message: 'Serial no is required' }),
    node_id: z.string().default(''),
    node_type: z.string().default('Switch'),
    version: z.string().default('1.1'),
    roles: z
      .array(z.enum(['RU', 'DU', 'CU', 'MMW', 'VSR', 'CORE', 'EMS', 'JUMP', 'SEGW', 'NHE', 'PKI', 'PDN', 'UPS']))
      .default(['RU']),
    orientation: z.nativeEnum(ORIENTATION).or(z.string()).optional(),
  })
  .merge(baseSchema);

export const schema = z
  .object({
    streetCellNodes: z
      .array(nodeSchema)
      .default([])
      .refine(
        (data) => {
          const nodeSerialNumbers = new Set<string>();

          for (const node of data || []) {
            if (nodeSerialNumbers.has(node.node_serial_no)) {
              return false;
            }
            nodeSerialNumbers.add(node.node_serial_no);
          }

          return true;
        },
        {
          path: ['streetCellNodes', 0, 'node_serial_no'],
          message: 'Each Street Cell node should have a unique serial number',
        }
      ),
    serverNodes: z
      .array(serverNodeSchema)
      .default([])
      .refine(
        (data) => {
          const nodeSerialNumbers = new Set<string>();

          for (const node of data || []) {
            if (nodeSerialNumbers.has(node.node_serial_no)) {
              return false;
            }
            nodeSerialNumbers.add(node.node_serial_no);
          }

          return true;
        },
        {
          path: ['serverNodes', 0, 'node_serial_no'],
          message: 'Each Server node should have a unique serial number',
        }
      ),
    serverVMNodes: z
      .array(serverNodeSchema)
      .default([])
      .refine(
        (data) => {
          const nodeSerialNumbers = new Set<string>();

          for (const node of data || []) {
            if (nodeSerialNumbers.has(node.node_serial_no)) {
              return false;
            }
            nodeSerialNumbers.add(node.node_serial_no);
          }

          return true;
        },
        {
          path: ['serverNodes', 0, 'node_serial_no'],
          message: 'Each Server node should have a unique serial number',
        }
      ),
    meshRootNodes: z
      .array(serverNodeSchema)
      .default([])
      .refine(
        (data) => {
          const nodeSerialNumbers = new Set<string>();

          for (const node of data || []) {
            if (nodeSerialNumbers.has(node.node_serial_no)) {
              return false;
            }
            nodeSerialNumbers.add(node.node_serial_no);
          }

          return true;
        },
        {
          path: ['serverNodes', 0, 'node_serial_no'],
          message: 'Each Server node should have a unique serial number',
        }
      ),
    switchNodes: z
      .array(switchNodeSchema)
      .default([])
      .refine(
        (data) => {
          const nodeSerialNumbers = new Set<string>();

          for (const node of data || []) {
            if (nodeSerialNumbers.has(node.node_serial_no)) {
              return false;
            }
            nodeSerialNumbers.add(node.node_serial_no);
          }

          return true;
        },
        {
          path: ['switchNodes', 0, 'node_serial_no'],
          message: 'Each Switch node should have a unique serial number',
        }
      ),
    isConflictedCellRef: z.boolean().optional(),
    node_id: z.string().default('').optional(),
  })
  .merge(cellSchema)
  .refine(
    (data) => {
      if (data.oran_split === O_RAN_SPLIT.Split6) {
        //return data.streetCellNodes.length > 0 && data.serverNodes.length > 0;
        return data.streetCellNodes.length > 0;
      }
      return true;
    },
    {
      path: ['streetCellNodes', 'serverNodes', 'switchNodes'],
    }
  );

export type CellFormValues = z.infer<typeof schema>;
export type BasSchemaValues = z.infer<typeof baseSchema>;
export type CellSchemaValues = z.infer<typeof cellSchema>;
export type NodeSchemaValues = z.infer<typeof nodeSchema>;
