import { Container, Stack, Text } from '@chakra-ui/react';
import CreateNodeForm from './CreateNodeForm';

export default function CreateNodePage() {
  return (
    <Container
      py={{
        base: '4',
        md: '8',
      }}
    >
      <Stack spacing="5">
        <Text fontSize="2xl" fontWeight="medium" textAlign="center" data-testid="create-node-heading">
          Create node
        </Text>
        <CreateNodeForm />
      </Stack>
    </Container>
  );
}
