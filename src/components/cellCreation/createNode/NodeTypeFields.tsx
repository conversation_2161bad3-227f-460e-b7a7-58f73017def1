import { FormControl, FormErrorMessage, FormLabel, Select } from '@chakra-ui/react';
import { Controller, UseFormReturn } from 'react-hook-form';
import { SELECTED_NODE_TYPE } from '../../../data/constants';
import { NodeFormValues } from './schema';

export type NodeTypeFieldsProps = {
  form: UseFormReturn<NodeFormValues>;
};
const NodeTypeFields = ({
  form: {
    control,
    setValue,
    formState: { errors },
  },
}: NodeTypeFieldsProps) => {
  return (
    <FormControl isInvalid={!!errors.node_type} mb="4" isRequired>
      <FormLabel htmlFor="node_type" data-testid="create-node-type">
        Manifest Type
      </FormLabel>
      <Controller
        name="node_type"
        control={control}
        render={({ field }) => (
          <Select
            {...field}
            placeholder="Select manifest type"
            data-testid="create-node-type-select"
            id="node_type"
            onChange={(e) => {
              field.onChange(e);
              setValue('node_type', e.target.value);
            }}
          >
            {Object.values(SELECTED_NODE_TYPE).map((value) => (
              <option key={value} value={value}>
                {value}
              </option>
            ))}
          </Select>
        )}
      />
      <FormErrorMessage>{errors.node_type?.message}</FormErrorMessage>
    </FormControl>
  );
};
export default NodeTypeFields;
