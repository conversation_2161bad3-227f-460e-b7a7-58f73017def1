import Loader from '../../loader/Loader';
import { useData } from '../hooks/useData';
import NodeForm from './NodeForm';

const CreateNodeForm = () => {
  const { availableServerList, availableNetworkList, availableRadioList, isLoading, regionSites, regions } = useData(
    undefined,
    true
  );

  const formProps = {
    availableServerList,
    availableNetworkList,
    availableRadioList,
    isLoading,
    regionSites,
    regions,
  };
  if (isLoading) return <Loader />;

  return <NodeForm {...formProps} />;
};

export default CreateNodeForm;
