import { FormControl, FormErrorMessage, FormLabel, Select } from '@chakra-ui/react';
import { UseFormReturn } from 'react-hook-form';
import { NodeFormValues } from '../createNode/schema';

export type RegionFieldsProps = {
  form: UseFormReturn<NodeFormValues>;
  regions: { value: string; label: string }[];
};

const RegionFields = ({
  form: {
    getValues,
    register,
    formState: { errors },
  },
  regions,
}: RegionFieldsProps) => {
  return (
    <FormControl isInvalid={!!errors.region} mb="4" isRequired>
      <FormLabel htmlFor="region" data-testid="create-node-region">
        Region
      </FormLabel>
      <Select
        id="region"
        data-testid="create-node-region-select"
        placeholder="Select a region"
        value={getValues('region')}
        {...register('region')}
      >
        {regions.map((region) => (
          <option key={region.value} value={region.value}>
            {region.label}
          </option>
        ))}
      </Select>
      <FormErrorMessage>{errors?.region?.message}</FormErrorMessage>
    </FormControl>
  );
};

export default RegionFields;
