import { AddIcon, Icon } from '@chakra-ui/icons';
import {
  Box,
  Button,
  Checkbox,
  Divider,
  Flex,
  Modal,
  ModalCloseButton,
  ModalContent,
  ModalOverlay,
  Stack,
  useColorModeValue,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import { DevTool } from '@hookform/devtools';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { NodeFormValues, schema } from './schema';
import { createNode } from '../../../services/inventoryManager';
import useLogin from '../../../hooks/useLogin';
import { AUTH_TOKEN_KEY, LIFE_CYCLE, READ_WRITE_ACCESS_ROLES } from '../../../data/constants';
import RegionFields from './RegionFields';
import SiteFields from './SiteFields';
import NodeTypeFields from './NodeTypeFields';
import CreateSiteForm from '../../../pages/SiteManager/components/CreateSiteForm';
import ServerFields from './ServerFields'; // Import ServerFields component
import NetworkFields from './NetworkFields'; // Import NetworkFields component
import { Node } from '../../../types/InventoryManager.type';
import RadioFields from './RadioFields';

interface NodeFormProps {
  availableServerList: Node[];
  availableNetworkList: Node[];
  availableRadioList: Node[];
  isLoading?: boolean;
  regions: { value: string; label: string }[];
  regionSites: { [key: string]: { name: string; id: number }[] };
}

const NodeForm = ({
  availableServerList,
  availableNetworkList,
  availableRadioList,
  regionSites,
  regions,
}: NodeFormProps) => {
  const nodeForm = useForm<NodeFormValues>({
    shouldUnregister: true,
    resolver: zodResolver(schema),
  });

  const { checkApplicationAccess } = useLogin(AUTH_TOKEN_KEY);
  const checkRoleAccess = checkApplicationAccess(READ_WRITE_ACCESS_ROLES);
  const [createAnotherNode, setCreateAnotherNode] = React.useState(false);
  const {
    handleSubmit,
    control,
    getValues,
    reset,
    watch,
    formState: { isDirty },
  } = nodeForm;

  const toast = useToast();
  const navigate = useNavigate();
  const { isOpen: isCreateOpen, onOpen: onCreateOpen, onClose: onCreateClose } = useDisclosure();

  const { mutateAsync: nodeMutation, isLoading: isNodePosting } = useMutation({
    mutationFn: createNode,
  });

  const [nodeType, setNodeType] = useState<string>('');
  const isServerNode = nodeType === 'server';
  const isNetworkNode = nodeType === 'network';
  const isRadioNode = nodeType === 'radio';

  useEffect(() => {
    const subscription = watch((value, { name }) => {
      if (name === 'node_type') {
        setNodeType(value?.node_type ?? '');
      }
    });
    return () => subscription.unsubscribe();
  }, [watch]);

  useEffect(() => {
    if (isServerNode || isNetworkNode || isRadioNode) {
      reset({
        serverNodes: [],
        networkNodes: [],
        radioNodes: [],
        node_type: getValues('node_type'),
        region: getValues('region'),
        site: getValues('site'),
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [nodeType, reset]);

  const onSubmit = async (data: NodeFormValues) => {
    const handleNodeMutation = async (nodes: any[], roles: string[] = []) => {
      for (const node of nodes) {
        const nodeData = {
          node_type: node.node_type,
          lifecycle: node.lifecycle as LIFE_CYCLE,
          roles: node.roles || roles,
          site_id: Number(data.site),
          node_serial_no: node.node_serial_no ?? '',
          orientation: node.orientation,
          ...(node.node_id && { node_id: node.node_id }),
        };
        await nodeMutation(nodeData, {
          onSuccess: () => {
            toast({
              title: 'Node created.',
              description: 'The node has been successfully created.',
              status: 'success',
              duration: 5000,
              isClosable: true,
            });
            if (createAnotherNode) {
              resetButtonHandler();
            } else {
              navigate('/cell-overview/nodes');
            }
          },
          onError: () => {
            toast({
              title: 'Error.',
              description: 'There was an error creating the node.',
              status: 'error',
              duration: 5000,
              isClosable: true,
            });
          },
        });
      }
    };

    if (data.serverNodes && data.serverNodes.length > 0) {
      await handleNodeMutation(data.serverNodes);
    }

    if (data.networkNodes && data.networkNodes.length > 0) {
      await handleNodeMutation(data.networkNodes, []);
    }

    if (data.radioNodes && data.radioNodes.length > 0) {
      await handleNodeMutation(data.radioNodes, []);
    }
  };

  const getIsDisabled = () => {
    if (isServerNode) {
      const serverNodes = getValues('serverNodes');
      if (serverNodes?.length === 0) return true;
    }
    if (isNetworkNode) {
      const networkNodes = getValues('networkNodes');
      if (networkNodes?.length === 0) return true;
    }
    if (isRadioNode) {
      const radioNodes = getValues('radioNodes');
      if (radioNodes?.length === 0) return true;
    }
    return !isDirty || !nodeType;
  };
  const resetButtonHandler = () => {
    reset({
      node_type: '',
      region: '',
      site: '',
      serverNodes: [],
      networkNodes: [],
      radioNodes: [],
    });
    setNodeType('');
    setCreateAnotherNode(false);
  };

  const boxShadow = useColorModeValue('sm', 'sm-dark');

  return (
    <Box
      as="form"
      marginX="auto"
      width="full"
      maxW={{
        lg: '8xl',
      }}
      bg="bg-surface"
      onSubmit={handleSubmit(onSubmit)}
      boxShadow={boxShadow}
      borderRadius="lg"
      noValidate={true}
    >
      <Box display="flex" justifyContent="end" padding="4">
        <Button
          variant="primary"
          leftIcon={<Icon as={AddIcon} marginStart="-1" />}
          onClick={() => resetButtonHandler()}
          data-testid="create-node-reset-form"
        >
          Reset form
        </Button>
      </Box>

      <Stack
        spacing="5"
        px={{
          base: '4',
          md: '6',
        }}
        py={{
          base: '5',
          md: '6',
        }}
      >
        {/* Region & Site */}
        <Stack
          spacing="6"
          direction={{
            base: 'column',
            md: 'row',
          }}
        >
          <RegionFields form={nodeForm} regions={regions} />
          <SiteFields
            form={nodeForm}
            regionSites={regionSites}
            checkRoleAccess={checkRoleAccess}
            onCreateOpen={onCreateOpen}
          />
        </Stack>

        <Stack
          spacing="6"
          direction={{
            base: 'column',
            md: 'row',
          }}
        >
          <Box flex=".492">
            <NodeTypeFields form={nodeForm} />
          </Box>
        </Stack>

        {/* Conditional Fields based on Manifest Type */}
        {isServerNode && (
          <ServerFields form={nodeForm} regionSites={regionSites} availableServerList={availableServerList} />
        )}
        {isNetworkNode && <NetworkFields form={nodeForm} availableNetworkList={availableNetworkList} />}
        {isRadioNode && (
          <RadioFields form={nodeForm} availableRadioList={availableRadioList} regionSites={regionSites} />
        )}
      </Stack>
      <Divider />
      {/* Footer Controls */}
      <Flex
        direction="row-reverse"
        justifyContent="space-between"
        py="4"
        px={{
          base: '4',
          md: '6',
        }}
      >
        <Button
          data-testid="create-node-button"
          isDisabled={getIsDisabled()}
          isLoading={isNodePosting}
          loadingText={isNodePosting ? 'Creating...' : 'Loading...'}
          colorScheme="teal"
          type="submit"
        >
          Create node
        </Button>
        <Checkbox isChecked={createAnotherNode} onChange={(e) => setCreateAnotherNode(e.target.checked)}>
          Create another node?
        </Checkbox>
        <Button colorScheme="teal" variant="outline" onClick={() => navigate('/cell-overview/nodes')}>
          Back
        </Button>
      </Flex>

      <Modal isOpen={isCreateOpen} onClose={onCreateClose} size="lg" isCentered>
        <ModalOverlay bg="blackAlpha.900" />
        <ModalContent>
          <ModalCloseButton />
          <CreateSiteForm onClose={onCreateClose} />
        </ModalContent>
      </Modal>

      <DevTool control={control} />
    </Box>
  );
};

export default NodeForm;
