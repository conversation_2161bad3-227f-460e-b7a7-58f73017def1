import React, { useEffect } from 'react';
import {
  FormControl,
  FormErrorMessage,
  FormLabel,
  HStack,
  Select,
  Stack,
  Text,
  useColorModeValue,
} from '@chakra-ui/react';
import { Controller, useFieldArray, UseFormReturn } from 'react-hook-form';
import { NodeFormValues, NetworkNodeSchema } from './schema';
import { Node } from '../../../types/InventoryManager.type';
import { LifeCycle, ROLE_OF_NODE, ROLE_OF_NODE_ARRAY } from '../../../data/constants';
import MultiSelect from 'react-select';
import {
  rolesCustomDarkTheme,
  rolesCustomLightTheme,
  rolesMultiSelectDarkStyles,
  rolesMultiSelectLightStyles,
} from '../styles/MultiSelectStyles';

interface NetworkFieldsProps {
  form: UseFormReturn<NodeFormValues>;
  availableNetworkList: Node[];
}

const NetworkFields: React.FC<NetworkFieldsProps> = ({ form, availableNetworkList }) => {
  const {
    control,
    setValue,
    getValues,
    formState: { errors },
  } = form;
  const {
    fields: networkFields,
    append: appendNetwork,
    remove: removeNetwork,
  } = useFieldArray({
    control,
    name: 'networkNodes',
  });
  const defaultNode: NetworkNodeSchema = {
    lifecycle: '',
    node_serial_no: '',
    node_id: '',
    node_type: 'Network',
    version: '1.1',
    roles: [],
  };
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const lightOrDarkTheme = useColorModeValue('light', 'dark');

  useEffect(() => {
    if (networkFields.length === 0) appendNetwork(defaultNode);
  });

  const getSelectedNode = (targetNode: string, useNodeList: Node[] | undefined) => {
    const selectedNode: NetworkNodeSchema | undefined = useNodeList
      ?.map((node): NetworkNodeSchema => {
        return {
          ...defaultNode,
          node_type: node.node_type,
          node_serial_no: node.node_serial_no,
        };
      })
      ?.find((node) => node.node_serial_no === targetNode);

    return selectedNode;
  };
  return (
    <Stack
      spacing="6"
      direction={{
        base: 'row',
        md: 'column',
      }}
      verticalAlign="bottom"
      borderRadius="4"
      boxShadow="lg"
      border={borderColor}
      p="8"
      marginTop="4"
      marginBottom="12"
    >
      <HStack justifyContent="space-between">
        <Text fontSize="lg" fontWeight="bold">
          Network
        </Text>
      </HStack>
      {networkFields.map((item, index) => (
        <HStack key={item.id} alignItems="baseline">
          {/* Serial number */}
          <FormControl isInvalid={!!errors?.networkNodes?.[index]?.node_serial_no} mb="4" isRequired mr="4">
            <FormLabel>Serial Number</FormLabel>
            <Controller
              name={`networkNodes.${index}.node_serial_no`}
              defaultValue={item.node_serial_no}
              control={control}
              render={({ field }) => (
                <Select
                  {...field}
                  placeholder="Select a serial no"
                  onChange={(e) => {
                    setValue(
                      `networkNodes.${index}`,
                      getSelectedNode(e.target.value, availableNetworkList) ?? defaultNode
                    );
                    field.onChange(e);
                  }}
                >
                  {availableNetworkList?.map((node) => (
                    <option key={node?.node_serial_no} value={node?.node_serial_no}>
                      {node?.node_serial_no}
                    </option>
                  ))}
                </Select>
              )}
            />
            <FormErrorMessage>{errors?.networkNodes?.[index]?.node_serial_no?.message}</FormErrorMessage>
          </FormControl>
          {/* Lifecycle */}
          <FormControl isInvalid={!!errors?.networkNodes?.[index]?.lifecycle} mb="4" isRequired mr="4">
            <FormLabel>Lifecycle</FormLabel>
            <Controller
              name={`networkNodes.${index}.lifecycle`}
              control={control}
              defaultValue={item.lifecycle}
              render={({ field }) => (
                <Select
                  {...field}
                  placeholder="Select a lifecycle"
                  onChange={(e) => {
                    field.onChange(e);
                  }}
                >
                  {Object.entries(LifeCycle).map(([key, value]) => (
                    <option
                      key={value}
                      value={key}
                      disabled={value === '0' || value === '4'}
                      style={{
                        color:
                          value > LifeCycle[getValues(`networkNodes.${index}.lifecycle`) as keyof typeof LifeCycle]
                            ? '#85828280'
                            : '',
                      }}
                    >
                      {key}
                    </option>
                  ))}
                </Select>
              )}
            />
            <FormErrorMessage>{errors?.networkNodes?.[index]?.lifecycle?.message}</FormErrorMessage>
          </FormControl>
          {/* Role */}
          <FormControl isInvalid={!!errors?.networkNodes?.[index]?.roles} mb="4" mr="4">
            <FormLabel>Role</FormLabel>
            <Controller
              name={`networkNodes.${index}.roles`}
              control={control}
              defaultValue={item.roles}
              render={() => (
                <MultiSelect
                  isMulti
                  options={ROLE_OF_NODE_ARRAY}
                  defaultValue={ROLE_OF_NODE_ARRAY.filter((option) =>
                    item.roles.includes(option.value as ROLE_OF_NODE)
                  )}
                  theme={(theme) =>
                    lightOrDarkTheme === 'light' ? rolesCustomLightTheme(theme) : rolesCustomDarkTheme(theme)
                  }
                  styles={lightOrDarkTheme === 'light' ? rolesMultiSelectLightStyles : rolesMultiSelectDarkStyles}
                  name={`networkNodes.${index}.roles`}
                  className="roles-multi-select"
                  classNamePrefix="select"
                  getOptionLabel={(option) => option.label}
                  getOptionValue={(option) => option.value}
                  onChange={(selectedOptions) => {
                    const updatedRoles = selectedOptions.map((option) => option.value);
                    const validRoles = updatedRoles.filter((role) =>
                      Object.values(ROLE_OF_NODE).includes(role as ROLE_OF_NODE)
                    ) as ROLE_OF_NODE[];
                    setValue(`networkNodes.${index}.roles`, validRoles);
                  }}
                />
              )}
            />
            <FormErrorMessage>{errors?.networkNodes?.[index]?.roles?.message}</FormErrorMessage>
          </FormControl>
        </HStack>
      ))}
    </Stack>
  );
};

export default NetworkFields;
