import React, { useEffect } from 'react';
import {
  Badge,
  Button,
  FormControl,
  FormErrorMessage,
  FormLabel,
  HStack,
  Select,
  Stack,
  Text,
  useColorModeValue,
} from '@chakra-ui/react';
import { Controller, useFieldArray, UseFormReturn } from 'react-hook-form';
import { NodeFormValues, ServerNodeSchema } from './schema';
import { Node } from '../../../types/InventoryManager.type';
import { RegionSitesType } from '../utils';
import { LifeCycle, ROLE_OF_NODE, ROLE_OF_NODE_ARRAY } from '../../../data/constants';
import MultiSelect from 'react-select';
import {
  rolesCustomDarkTheme,
  rolesCustomLightTheme,
  rolesMultiSelectDarkStyles,
  rolesMultiSelectLightStyles,
} from '../styles/MultiSelectStyles';
import { DeleteIcon } from '@chakra-ui/icons';

interface ServerFieldsProps {
  form: UseFormReturn<NodeFormValues>;
  availableServerList: Node[];
  regionSites: RegionSitesType;
}

const ServerFields: React.FC<ServerFieldsProps> = ({ form, availableServerList, regionSites }) => {
  const {
    control,
    register,
    setValue,
    getValues,
    formState: { errors },
  } = form;
  const {
    fields: serverFields,
    append: appendServer,
    remove: removeServer,
  } = useFieldArray({
    control,
    name: 'serverNodes',
  });

  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const lightOrDarkTheme = useColorModeValue('light', 'dark');

  const defaultNode: ServerNodeSchema = {
    lifecycle: '',
    node_serial_no: '',
    node_id: '',
    node_type: 'Server',
    version: '1.1',
    roles: [],
  };
  useEffect(() => {
    if (serverFields.length === 0) appendServer(defaultNode);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  });
  const getSelectedNode = (targetNode: string, useNodeList: Node[] | undefined) => {
    const selectedNode: ServerNodeSchema | undefined = useNodeList
      ?.map((node): ServerNodeSchema => {
        return {
          ...defaultNode,
          node_type: node.node_type,
          node_serial_no: node.node_serial_no,
        };
      })
      ?.find((node) => node.node_serial_no === targetNode);

    return selectedNode;
  };

  return (
    <Stack
      spacing="6"
      direction={{
        base: 'row',
        md: 'column',
      }}
      verticalAlign="bottom"
      borderRadius="4"
      boxShadow="lg"
      border={borderColor}
      p="8"
      marginTop="4"
      marginBottom="12"
    >
      <HStack justifyContent="space-between">
        <Text fontSize="lg" fontWeight="bold">
          Server
        </Text>
      </HStack>
      {serverFields.map((item, index) => (
        <HStack key={item.id} alignItems="baseline">
          {/* Serial number */}
          <FormControl isInvalid={!!errors?.serverNodes?.[index]?.node_serial_no} mb="4" isRequired mr="4">
            <FormLabel>Serial Number</FormLabel>
            <Controller
              name={`serverNodes.${index}.node_serial_no`}
              defaultValue={item.node_serial_no}
              control={control}
              render={({ field }) => (
                <Select
                  {...field}
                  id={`serverNodes.${index}.node_serial_no`}
                  placeholder="Select a serial no"
                  onChange={(e) => {
                    setValue(
                      `serverNodes.${index}`,
                      getSelectedNode(e.target.value, availableServerList) ?? defaultNode
                    );
                    field.onChange(e);
                  }}
                >
                  {availableServerList?.map((node) => (
                    <option key={node?.node_serial_no} value={node?.node_serial_no}>
                      {node?.node_serial_no}
                    </option>
                  ))}
                </Select>
              )}
            />
            <FormErrorMessage>{errors?.serverNodes?.[index]?.node_serial_no?.message}</FormErrorMessage>
          </FormControl>
          {/* Node Type */}
          <FormControl isInvalid={!!errors?.serverNodes?.[index]?.node_type} mb="4" isRequired mr="4">
            <FormLabel>Node Type</FormLabel>
            <Controller
              name={`serverNodes.${index}.node_type`}
              control={control}
              render={() => (
                <Text {...register(`serverNodes.${index}.node_type`)} fontSize="medium" fontWeight="medium">
                  <Badge paddingY="1" paddingX="4">
                    {getValues(`serverNodes.${index}.node_type`)}
                  </Badge>
                </Text>
              )}
            />
            <FormErrorMessage>{errors?.serverNodes?.[index]?.node_serial_no?.message}</FormErrorMessage>
          </FormControl>
          {/* Role */}
          <FormControl isInvalid={!!errors?.serverNodes?.[index]?.roles} mb="4" mr="4">
            <FormLabel>Role</FormLabel>
            <Controller
              name={`serverNodes.${index}.roles`}
              control={control}
              defaultValue={item.roles}
              render={() => (
                <MultiSelect
                  isMulti
                  options={ROLE_OF_NODE_ARRAY}
                  defaultValue={ROLE_OF_NODE_ARRAY.filter((option) =>
                    item.roles.includes(option.value as ROLE_OF_NODE)
                  )}
                  theme={(theme) =>
                    lightOrDarkTheme === 'light' ? rolesCustomLightTheme(theme) : rolesCustomDarkTheme(theme)
                  }
                  styles={lightOrDarkTheme === 'light' ? rolesMultiSelectLightStyles : rolesMultiSelectDarkStyles}
                  name={`serverNodes.${index}.roles`}
                  className="roles-multi-select"
                  classNamePrefix="select"
                  getOptionLabel={(option) => option.label}
                  getOptionValue={(option) => option.value}
                  onChange={(selectedOptions) => {
                    const updatedRoles = selectedOptions.map((option) => option.value);
                    const validRoles = updatedRoles.filter((role) =>
                      Object.values(ROLE_OF_NODE).includes(role as ROLE_OF_NODE)
                    ) as ROLE_OF_NODE[];
                    setValue(`serverNodes.${index}.roles`, validRoles);
                  }}
                />
              )}
            />
            <FormErrorMessage>{errors?.serverNodes?.[index]?.roles?.message}</FormErrorMessage>
          </FormControl>
          {/* Lifecycle */}
          <FormControl isInvalid={!!errors?.serverNodes?.[index]?.lifecycle} mb="4" isRequired mr="4">
            <FormLabel>Lifecycle</FormLabel>
            <Controller
              name={`serverNodes.${index}.lifecycle`}
              control={control}
              defaultValue={item.lifecycle}
              render={({ field }) => (
                <Select
                  {...field}
                  placeholder="Select a lifecycle"
                  onChange={(e) => {
                    field.onChange(e);
                  }}
                >
                  {Object.entries(LifeCycle).map(([key, value]) => (
                    <option
                      key={value}
                      value={key}
                      disabled={value === '0' || value === '4'}
                      style={{
                        color:
                          value > LifeCycle[`serverNodes.${index}.lifecycle` as keyof typeof LifeCycle]
                            ? '#85828280'
                            : '',
                      }}
                    >
                      {key}
                    </option>
                  ))}
                </Select>
              )}
            />
            <FormErrorMessage>{errors?.serverNodes?.[index]?.lifecycle?.message}</FormErrorMessage>
          </FormControl>
        </HStack>
      ))}
    </Stack>
  );
};

export default ServerFields;
