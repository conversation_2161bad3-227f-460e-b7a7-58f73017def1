import React, { useEffect } from 'react';
import {
  Badge,
  FormControl,
  FormErrorMessage,
  FormLabel,
  HStack,
  Select,
  Stack,
  Text,
  useColorModeValue,
} from '@chakra-ui/react';
import { Controller, useFieldArray, UseFormReturn } from 'react-hook-form';
import { NodeFormValues, RadioNodeSchema } from './schema';
import { Node } from '../../../types/InventoryManager.type';
import { RegionSitesType } from '../utils';
import { LifeCycle, ORIENTATION, ROLE_OF_NODE, ROLE_OF_NODE_ARRAY } from '../../../data/constants';
import MultiSelect from 'react-select';
import {
  rolesCustomDarkTheme,
  rolesCustomLightTheme,
  rolesMultiSelectDarkStyles,
  rolesMultiSelectLightStyles,
} from '../styles/MultiSelectStyles';

type RadioFieldsProps = {
  form: UseFormReturn<NodeFormValues>;
  availableRadioList: Node[];
  regionSites: RegionSitesType;
};

const RadioFields: React.FC<RadioFieldsProps> = ({ form, availableRadioList, regionSites }) => {
  const {
    control,
    register,
    setValue,
    getValues,
    formState: { errors },
  } = form;
  const {
    fields: radioFields,
    append: appendRadio,
    remove: removeRadio,
  } = useFieldArray({
    control,
    name: 'radioNodes',
  });

  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const lightOrDarkTheme = useColorModeValue('light', 'dark');

  const defaultNode: RadioNodeSchema = {
    lifecycle: '',
    node_serial_no: '',
    node_id: '',
    node_type: 'Radio',
    version: '1.1',
    roles: ['RU'],
    orientation: '0',
  };
  useEffect(() => {
    if (radioFields.length === 0) appendRadio(defaultNode);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  });
  const getSelectedNode = (targetNode: string, useNodeList: Node[] | undefined) => {
    const selectedNode: RadioNodeSchema | undefined = useNodeList
      ?.map((node): RadioNodeSchema => {
        return {
          ...defaultNode,
          node_type: node.node_type,
          node_serial_no: node.node_serial_no,
        };
      })
      ?.find((node) => node.node_serial_no === targetNode);

    return selectedNode;
  };

  return (
    <Stack
      spacing="6"
      direction={{
        base: 'row',
        md: 'column',
      }}
      verticalAlign="bottom"
      borderRadius="4"
      boxShadow="lg"
      border={borderColor}
      p="8"
      marginTop="4"
      marginBottom="12"
    >
      <HStack justifyContent="space-between">
        <Text fontSize="lg" fontWeight="bold">
          Radio
        </Text>
      </HStack>
      {radioFields.map((item, index) => (
        <HStack key={item.id} alignItems="baseline">
          {/* Serial number */}
          <FormControl isInvalid={!!errors?.radioNodes?.[index]?.node_serial_no} mb="4" isRequired mr="4">
            <FormLabel>Serial Number</FormLabel>
            <Controller
              name={`radioNodes.${index}.node_serial_no`}
              defaultValue={item.node_serial_no}
              control={control}
              render={({ field }) => (
                <Select
                  {...field}
                  id={`radioNodes.${index}.node_serial_no`}
                  placeholder="Select a serial no"
                  onChange={(e) => {
                    setValue(`radioNodes.${index}`, getSelectedNode(e.target.value, availableRadioList) ?? defaultNode);
                    field.onChange(e);
                  }}
                >
                  {availableRadioList?.map((node) => (
                    <option key={node?.node_serial_no} value={node?.node_serial_no}>
                      {node?.node_serial_no}
                    </option>
                  ))}
                </Select>
              )}
            />
            <FormErrorMessage>{errors?.radioNodes?.[index]?.node_serial_no?.message}</FormErrorMessage>
          </FormControl>
          {/* Node Type */}
          <FormControl isInvalid={!!errors?.radioNodes?.[index]?.node_type} mb="4" isRequired mr="4">
            <FormLabel>Node Type</FormLabel>
            <Controller
              name={`radioNodes.${index}.node_type`}
              control={control}
              render={() => (
                <Text {...register(`radioNodes.${index}.node_type`)} fontSize="medium" fontWeight="medium">
                  <Badge paddingY="1" paddingX="4">
                    {getValues(`radioNodes.${index}.node_type`)}
                  </Badge>
                </Text>
              )}
            />
            <FormErrorMessage>{errors?.radioNodes?.[index]?.node_serial_no?.message}</FormErrorMessage>
          </FormControl>
          {/* Role */}
          <FormControl isInvalid={!!errors?.radioNodes?.[index]?.roles} mb="4" mr="4">
            <FormLabel>Role</FormLabel>
            <Controller
              name={`radioNodes.${index}.roles`}
              control={control}
              defaultValue={item.roles}
              render={() => (
                <MultiSelect
                  isMulti
                  options={ROLE_OF_NODE_ARRAY}
                  defaultValue={ROLE_OF_NODE_ARRAY.filter((option) =>
                    item.roles.includes(option.value as ROLE_OF_NODE)
                  )}
                  theme={(theme) =>
                    lightOrDarkTheme === 'light' ? rolesCustomLightTheme(theme) : rolesCustomDarkTheme(theme)
                  }
                  styles={lightOrDarkTheme === 'light' ? rolesMultiSelectLightStyles : rolesMultiSelectDarkStyles}
                  name={`radioNodes.${index}.roles`}
                  className="roles-multi-select"
                  classNamePrefix="select"
                  getOptionLabel={(option) => option.label}
                  getOptionValue={(option) => option.value}
                  onChange={(selectedOptions) => {
                    const updatedRoles = selectedOptions.map((option) => option.value);
                    const validRoles = updatedRoles.filter((role) =>
                      Object.values(ROLE_OF_NODE).includes(role as ROLE_OF_NODE)
                    ) as ROLE_OF_NODE[];
                    setValue(`radioNodes.${index}.roles`, validRoles);
                  }}
                />
              )}
            />
            <FormErrorMessage>{errors?.radioNodes?.[index]?.roles?.message}</FormErrorMessage>
          </FormControl>
          {/* Orientation */}
          <FormControl isInvalid={!!errors?.radioNodes?.[index]?.orientation} mb="4" isRequired mr="4">
            <FormLabel htmlFor="orientation">Orientation</FormLabel>

            <Controller
              name={`radioNodes.${index}.orientation`}
              defaultValue={item.orientation}
              control={control}
              render={({ field }) => (
                <Select {...field} id={`radioNodes.${index}.orientation`} placeholder="Select orientation">
                  {Object.entries(ORIENTATION).map(([key, value]) => (
                    <option key={value} value={value}>
                      {key}
                    </option>
                  ))}
                </Select>
              )}
            />
            <FormErrorMessage>{errors?.radioNodes?.[index]?.orientation?.message}</FormErrorMessage>
          </FormControl>
          {/* Lifecycle */}
          <FormControl isInvalid={!!errors?.radioNodes?.[index]?.lifecycle} mb="4" isRequired mr="4">
            <FormLabel>Lifecycle</FormLabel>
            <Controller
              name={`radioNodes.${index}.lifecycle`}
              control={control}
              defaultValue={item.lifecycle}
              render={({ field }) => (
                <Select
                  {...field}
                  placeholder="Select a lifecycle"
                  onChange={(e) => {
                    field.onChange(e);
                  }}
                >
                  {Object.entries(LifeCycle).map(([key, value]) => (
                    <option
                      key={value}
                      value={key}
                      //disabled={value === '0' || value === '4'}
                      style={{
                        color:
                          value > LifeCycle[`radioNodes.${index}.lifecycle` as keyof typeof LifeCycle]
                            ? '#85828280'
                            : '',
                      }}
                    >
                      {key}
                    </option>
                  ))}
                </Select>
              )}
            />
            <FormErrorMessage>{errors?.radioNodes?.[index]?.lifecycle?.message}</FormErrorMessage>
          </FormControl>
        </HStack>
      ))}
    </Stack>
  );
};

export default RadioFields;
