import { AddIcon } from '@chakra-ui/icons';
import {
  Button,
  ButtonGroup,
  FormControl,
  FormErrorMessage,
  FormHelperText,
  FormLabel,
  IconButton,
  Select,
} from '@chakra-ui/react';
import { UseFormReturn } from 'react-hook-form';
import { NodeFormValues } from './schema';

export type SiteFieldsProps = {
  form: UseFormReturn<NodeFormValues>;
  regionSites: { [key: string]: { name: string; id: number }[] };
  checkRoleAccess: boolean;
  onCreateOpen: () => void;
};

const SiteFields = ({
  form: {
    register,
    setValue,
    watch,
    formState: { errors },
  },
  regionSites,
  checkRoleAccess,
  onCreateOpen,
}: SiteFieldsProps) => {
  const region = watch('region');

  return (
    <FormControl isInvalid={!!errors.site} mb="4" isRequired isDisabled={!region}>
      <FormLabel htmlFor="site" data-testid="create-node-site">
        Site
      </FormLabel>
      <Select
        id="site"
        placeholder="Select a site"
        data-testid="create-node-site-select"
        {...register('site')}
        onChange={(e) => setValue('site', e.target.value)}
      >
        {region &&
          regionSites[region]?.map(({ name, id }) => (
            <option key={`${id}-site`} value={id}>
              {name}
            </option>
          ))}
      </Select>
      <FormErrorMessage>{region ? errors?.site?.message : 'Select a region first'}</FormErrorMessage>
      {region && checkRoleAccess && (
        <FormHelperText>
          <ButtonGroup onClick={onCreateOpen} size="sm" isAttached variant="outline">
            <Button fontWeight="medium">Create site</Button>
            <IconButton aria-label="add new site" icon={<AddIcon />} />
          </ButtonGroup>
        </FormHelperText>
      )}
    </FormControl>
  );
};

export default SiteFields;
