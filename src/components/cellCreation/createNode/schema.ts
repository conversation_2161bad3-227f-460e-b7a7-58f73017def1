import { z } from 'zod';
import { LifeCycle } from '../../../data/constants';

const baseSchema = z.object({
  site: z.string().nonempty({ message: 'Site is required' }),
});

const nodeSchema = z
  .object({
    region: z.string().nonempty({ message: 'Region is required' }),
    node_type: z.string().default(''),
    version: z.string().default('1.1'),
  })
  .merge(baseSchema);

const serverNodeSchema = z.object({
  node_serial_no: z.string().nonempty({ message: 'Serial no is required' }),
  node_id: z.string().default(''),
  node_type: z.string().default('Server'),
  version: z.string().default('1.1'),
  lifecycle: z
    .nativeEnum(LifeCycle, {
      errorMap: () => {
        return { message: 'Lifecycle is required' };
      },
    })
    .or(z.string().nonempty({ message: 'Lifecycle is required' })),
  roles: z
    .array(z.enum(['RU', 'DU', 'CU', 'MMW', 'VSR', 'CORE', 'EMS', 'JUMP', 'SEGW', 'NHE', 'PKI', 'PDN', 'UPS', 'GDCV']))
    .default(['RU']),
});

const networkNodeSchema = z.object({
  node_serial_no: z.string().nonempty({ message: 'Serial no is required' }),
  node_id: z.string().default(''),
  node_type: z.string().default('Network'),
  version: z.string().default('1.1'),
  lifecycle: z
    .nativeEnum(LifeCycle, {
      errorMap: () => {
        return { message: 'Lifecycle is required' };
      },
    })
    .or(z.string().nonempty({ message: 'Lifecycle is required' })),
  roles: z
    .array(z.enum(['RU', 'DU', 'CU', 'MMW', 'VSR', 'CORE', 'EMS', 'JUMP', 'SEGW', 'NHE', 'PKI', 'PDN', 'UPS', 'GDCV']))
    .default(['RU']),
});

const radioNodeSchema = z.object({
  node_serial_no: z.string().nonempty({ message: 'Serial no is required' }),
  node_id: z.string().default(''),
  node_type: z.string().default('Network'),
  version: z.string().default('1.1'),
  lifecycle: z
    .nativeEnum(LifeCycle, {
      errorMap: () => {
        return { message: 'Lifecycle is required' };
      },
    })
    .or(z.string().nonempty({ message: 'Lifecycle is required' })),
  roles: z
    .array(z.enum(['RU', 'DU', 'CU', 'MMW', 'VSR', 'CORE', 'EMS', 'JUMP', 'SEGW', 'NHE', 'PKI', 'PDN', 'UPS', 'GDCV']))
    .default(['RU']),
  orientation: z.string().default('0'),
});

export const schema = z
  .object({
    serverNodes: z
      .array(serverNodeSchema)
      .default([])
      .refine(
        (data) => {
          const nodeSerialNumbers = new Set<string>();
          for (const node of data || []) {
            if (nodeSerialNumbers.has(node.node_serial_no)) {
              return false;
            }
            nodeSerialNumbers.add(node.node_serial_no);
          }
          return true;
        },
        {
          path: ['serverNodes', 0, 'node_serial_no'],
          message: 'Each Server node should have a unique serial number',
        }
      ),
    networkNodes: z
      .array(networkNodeSchema)
      .default([])
      .refine(
        (data) => {
          const nodeSerialNumbers = new Set<string>();
          for (const node of data || []) {
            if (nodeSerialNumbers.has(node.node_serial_no)) {
              return false;
            }
            nodeSerialNumbers.add(node.node_serial_no);
          }
          return true;
        },
        {
          path: ['networkNodes', 0, 'node_serial_no'],
          message: 'Each Network node should have a unique serial number',
        }
      ),
    radioNodes: z
      .array(radioNodeSchema)
      .default([])
      .refine(
        (data) => {
          const nodeSerialNumbers = new Set<string>();
          for (const node of data || []) {
            if (nodeSerialNumbers.has(node.node_serial_no)) {
              return false;
            }
            nodeSerialNumbers.add(node.node_serial_no);
          }
          return true;
        },
        {
          path: ['radioNodes', 0, 'node_serial_no'],
          message: 'Each Radio node should have a unique serial number',
        }
      ),
    isConflictedCellRef: z.boolean().optional(),
    node_id: z.string().default('').optional(),
  })
  .merge(nodeSchema);

export type NodeFormValues = z.infer<typeof schema>;
export type BasSchemaValues = z.infer<typeof baseSchema>;
export type NodeSchemaValues = z.infer<typeof nodeSchema>;
export type ServerNodeSchema = z.infer<typeof serverNodeSchema>;
export type NetworkNodeSchema = z.infer<typeof networkNodeSchema>;
export type RadioNodeSchema = z.infer<typeof radioNodeSchema>;
