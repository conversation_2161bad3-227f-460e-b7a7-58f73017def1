import { AddIcon, Icon } from '@chakra-ui/icons';
import {
  Box,
  Button,
  Checkbox,
  Collapse,
  Divider,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Modal,
  ModalCloseButton,
  ModalContent,
  ModalOverlay,
  Select,
  Stack,
  useColorModeValue,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import { DevTool } from '@hookform/devtools';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import React from 'react';
import { Controller, useFieldArray, useForm } from 'react-hook-form';
import { useLocation, useNavigate } from 'react-router-dom';
import { AUTH_TOKEN_KEY, LIFE_CYCLE, ORIENTATION, READ_WRITE_ACCESS_ROLES } from '../../data/constants';
import useLogin from '../../hooks/useLogin';
import CreateSiteForm from '../../pages/SiteManager/components/CreateSiteForm';
import { postCell, postNode } from '../../services/inventoryManager';
import { CellRequest, Node, UpdateCellRequest } from '../../types/InventoryManager.type';
import CellReferenceFields from './formFields/CellReferenceFields';
import CellTypeFields from './formFields/CellTypeFields';
import LifeCycleFields from './formFields/LifeCycleFields';
import RegionFields from './formFields/RegionFields';
import SiteFields from './formFields/SiteFields';
import EditCellForm from './forms/EditCellForm';
import GnodeBForm from './forms/GnodeBForm';
import Split6Form from './forms/Split6Form';
import { useDeleteNode } from './hooks/useDeleteNode';
import useGetCellRef from './hooks/useGetCellRef';
import useUpdateCell from './hooks/useUpdateCell';
import useUpdateNode from './hooks/useUpdateNode';
import { CellFormValues, NodeSchemaValues, schema } from './schema';
import ENodeBForm from './forms/EnodeBForm';
import PlacementField from './formFields/PlacementField';

export interface CellFormProps {
  availableNodeList: Node[];
  availableENodeBList: Node[];
  availableServerList: Node[];
  availableSwitchList: Node[];
  assignedNodeList: Node[];
  assignedENodeBList: Node[];
  assignedServerList: Node[];
  assignedSwitchList: Node[];
  isLoading: boolean;
  regions: { value: string; label: string }[];
  regionSites: { [key: string]: { name: string; id: number }[] };
  isEdit: boolean;
  cellData: CellFormValues | undefined;
  refetchNodeList: () => void;
}

const CellForm = ({
  availableNodeList,
  availableENodeBList,
  availableServerList,
  availableSwitchList,
  refetchNodeList,
  assignedNodeList,
  assignedENodeBList,
  assignedServerList,
  assignedSwitchList,
  isLoading,
  regions,
  regionSites,
  isEdit,
  cellData,
}: CellFormProps) => {
  const cellForm = useForm<CellFormValues>({
    shouldUnregister: true,
    defaultValues: isEdit
      ? cellData
      : {
          node_serial_no: '',
        },
    resolver: zodResolver(schema),
  });

  const { state } = useLocation();

  const [createAnotherCell, setCreateAnotherCell] = React.useState(false);

  const { checkApplicationAccess } = useLogin(AUTH_TOKEN_KEY);
  const checkRoleAccess = checkApplicationAccess(READ_WRITE_ACCESS_ROLES);

  React.useEffect(() => {
    if (isEdit) reset({ ...cellData });
  }, [state?.cell_ref, isEdit, cellData]);

  const {
    handleSubmit,
    control,
    register,
    getValues,
    reset,
    resetField,
    setValue,
    watch,
    formState: { errors, isDirty },
  } = cellForm;

  const toast = useToast();
  const navigate = useNavigate();
  const { site } = getValues();
  const placement = watch('placement');
  const oran_split = watch('oran_split');
  const { isOpen: isCreateOpen, onOpen: onCreateOpen, onClose: onCreateClose } = useDisclosure();

  const {
    fields: streetCellFields,
    remove: removeStreetCell,
    prepend: prependStreetCell,
  } = useFieldArray({
    control,
    name: 'streetCellNodes',
  });

  const {
    fields: serverFields,
    remove: removeServer,
    prepend: prependServer,
  } = useFieldArray({
    control,
    name: 'serverNodes',
  });

  const {
    fields: switchFields,
    remove: removeSwitch,
    prepend: prependSwitch,
  } = useFieldArray({
    control,
    name: 'switchNodes',
  });

  const { data: generatedCellRef = '', refetchCellRef } = useGetCellRef(
    Number(site),
    isEdit,
    state?.cell_ref,
    oran_split,
    placement
  );

  const { mutateAsync: cellMutation, isLoading: isCellPosting } = useMutation({
    mutationFn: postCell,
  });
  const { mutateAsync: nodeMutation, isLoading: isNodePosting } = useMutation({
    mutationFn: postNode,
  });
  const [cellType, setCellType] = React.useState(cellData?.oran_split ?? '');
  const isGNodeBTypeCell = cellType === 'gNodeB';
  const isSplit6TypeCell = cellType === 'Split 6';
  const isENodeBTypeCell = cellType === 'eNodeB';
  const isOranTypeCell = cellType === 'ORAN';

  React.useEffect(() => {
    const subscription = watch((value, { name, type }) => {
      if (name === 'oran_split') {
        setCellType(value?.oran_split ?? '');
      }
    });

    return () => subscription.unsubscribe();
  }, [watch]);

  React.useEffect(() => {
    if (isGNodeBTypeCell) {
      resetField('node_serial_no');
      resetField('orientation');
      resetField('streetCellNodes');
      resetField('serverNodes');
    }
    if (isENodeBTypeCell) {
      resetField('node_serial_no');
      resetField('orientation');
      resetField('streetCellNodes');
      resetField('serverNodes');
    }
    if (isSplit6TypeCell) {
      resetField('node_serial_no');
      resetField('orientation');
      resetField('streetCellNodes');
      resetField('serverNodes');
    }
  }, [watch('oran_split')]);

  const isPosting = isCellPosting || isNodePosting;

  React.useEffect(() => {
    if (isEdit) reset({ ...cellData });
    setCellType(cellData?.oran_split ?? '');
  }, [state?.cell_ref, isEdit, cellData]);

  const resetButtonHandler = () => {
    resetField('region');
    resetField('placement');
    resetField('site');
    resetField('lifecycle');
    resetField('oran_split');
    resetField('node_serial_no');
    resetField('orientation');
    resetField('streetCellNodes');
    resetField('serverNodes');
    reset({ ...cellData });
    setCreateAnotherCell(false);
  };

  const onSubmit = async (data: CellFormValues) => {
    const postCellData: CellRequest = {
      cell_ref: generatedCellRef,
      site_id: data.site,
      oran_split: data.oran_split,
      orientation: data?.orientation as ORIENTATION,
      lifecycle: data.lifecycle as LIFE_CYCLE,
    };
    if (isENodeBTypeCell) {
      postCellData.ran_type = '4G';
    }

    await cellMutation(postCellData, {
      onSuccess: (variables) => {
        setValue('isConflictedCellRef', false);
        // StreetCell
        if (data.oran_split === 'Split 6') {
          data?.streetCellNodes?.forEach(async (streetCell) => {
            const postSplit6NodeData = {
              node_type: streetCell.node_type,
              lifecycle: streetCell.lifecycle as LIFE_CYCLE,
              version: streetCell.version || '1.1',
              roles: ['RU'],
              site_id: Number(streetCell.site),
              orientation: streetCell.orientation as ORIENTATION,
              node_serial_no: streetCell.node_serial_no ?? '',
              ...(streetCell.node_id && { node_id: streetCell.node_id }),
            };
            await nodeMutation({
              cell_ref: variables?.cell_ref ?? '',
              node: postSplit6NodeData,
            });
          });
          if (data.serverNodes && data.serverNodes.length > 0) {
            data?.serverNodes?.forEach(async (server) => {
              const postSplit6NodeData = {
                node_type: server.node_type,
                lifecycle: server.lifecycle as LIFE_CYCLE,
                version: server.version || '1.1',
                roles: server.roles, // My roles
                site_id: Number(server.site),
                orientation: server.orientation as ORIENTATION,
                node_serial_no: server.node_serial_no ?? '',
                ...(server.node_id && { node_id: server.node_id }),
              };
              await nodeMutation({
                cell_ref: variables?.cell_ref ?? '',
                node: postSplit6NodeData,
              });
            });
          }
          if (data.switchNodes && data.switchNodes.length > 0) {
            data?.switchNodes?.forEach(async (server) => {
              const postSplit6NodeData = {
                node_type: server.node_type,
                lifecycle: server.lifecycle as LIFE_CYCLE,
                version: server.version || '1.1',
                roles: server.roles, // My roles
                site_id: Number(server.site),
                orientation: server.orientation as ORIENTATION,
                node_serial_no: server.node_serial_no ?? '',
                ...(server.node_id && { node_id: server.node_id }),
              };
              await nodeMutation({
                cell_ref: variables?.cell_ref ?? '',
                node: postSplit6NodeData,
              });
            });
          }
        } // eNodeB
        else if (data.oran_split === 'eNodeB') {
          const postNodeData = {
            roles: ['ENB'],
            version: '1.1',
            node_type: 'Airspan4G',
            lifecycle: data.lifecycle as LIFE_CYCLE,
            //orientation same as cell
            orientation: data.orientation as ORIENTATION,
            site_id: Number(data.site),
            node_serial_no: data.node_serial_no ?? '',
            ...(data.node_id && { node_id: data.node_id }),
          };
          nodeMutation({
            cell_ref: variables?.cell_ref ?? '',
            node: postNodeData,
          });
          if (data.serverNodes && data.serverNodes.length > 0) {
            data?.serverNodes?.forEach(async (server) => {
              const postSplit6NodeData = {
                node_type: server.node_type,
                lifecycle: server.lifecycle as LIFE_CYCLE,
                version: server.version || '1.1',
                roles: server.roles, // My roles
                site_id: Number(server.site),
                orientation: server.orientation as ORIENTATION,
                node_serial_no: server.node_serial_no ?? '',
                ...(server.node_id && { node_id: server.node_id }),
              };
              await nodeMutation({
                cell_ref: variables?.cell_ref ?? '',
                node: postSplit6NodeData,
              });
            });
          }
          if (data.switchNodes && data.switchNodes.length > 0) {
            data?.switchNodes?.forEach(async (server) => {
              const postSplit6NodeData = {
                node_type: server.node_type,
                lifecycle: server.lifecycle as LIFE_CYCLE,
                version: server.version || '1.1',
                roles: server.roles, // My roles
                site_id: Number(server.site),
                orientation: server.orientation as ORIENTATION,
                node_serial_no: server.node_serial_no ?? '',
                ...(server.node_id && { node_id: server.node_id }),
              };
              await nodeMutation({
                cell_ref: variables?.cell_ref ?? '',
                node: postSplit6NodeData,
              });
            });
          }
        } else {
          // GnodeB
          const postNodeData = {
            roles: ['RU', 'DU', 'CU'],
            version: '1.1',
            node_type: 'StreetCell',
            lifecycle: data.lifecycle as LIFE_CYCLE,
            //orientation same as cell
            orientation: data.orientation as ORIENTATION,
            site_id: Number(data.site),
            node_serial_no: data.node_serial_no ?? '',
            ...(data.node_id && { node_id: data.node_id }),
          };
          nodeMutation({
            cell_ref: variables?.cell_ref ?? '',
            node: postNodeData,
          });
          if (data.serverNodes && data.serverNodes.length > 0) {
            data?.serverNodes?.forEach(async (server) => {
              const postSplit6NodeData = {
                node_type: server.node_type,
                lifecycle: server.lifecycle as LIFE_CYCLE,
                version: server.version || '1.1',
                roles: server.roles, // My roles
                site_id: Number(server.site),
                orientation: server.orientation as ORIENTATION,
                node_serial_no: server.node_serial_no ?? '',
                ...(server.node_id && { node_id: server.node_id }),
              };
              await nodeMutation({
                cell_ref: variables?.cell_ref ?? '',
                node: postSplit6NodeData,
              });
            });
          }
          if (data.switchNodes && data.switchNodes.length > 0) {
            data?.switchNodes?.forEach(async (server) => {
              const postSplit6NodeData = {
                node_type: server.node_type,
                lifecycle: server.lifecycle as LIFE_CYCLE,
                version: server.version || '1.1',
                roles: server.roles, // My roles
                site_id: Number(server.site),
                orientation: server.orientation as ORIENTATION,
                node_serial_no: server.node_serial_no ?? '',
                ...(server.node_id && { node_id: server.node_id }),
              };
              await nodeMutation({
                cell_ref: variables?.cell_ref ?? '',
                node: postSplit6NodeData,
              });
            });
          }
        }
        if (createAnotherCell) {
          resetButtonHandler();
        } else {
          navigate('/cell-overview');
        }
        toast({
          title: `Cell ref  ${variables?.cell_ref} created successfully.`,
          status: 'success',
          duration: 5000,
          isClosable: true,
          position: 'top',
        });
      },
      onError: (error, variables) => {
        if ((error as { response: { status: number } })?.response.status === 409) {
          setValue('isConflictedCellRef', true);
          toast({
            title: `Cell ref  ${variables?.cell_ref} already exist. Please regenerate.`,
            status: 'error',
            duration: 5000,
            isClosable: true,
            position: 'top',
          });
        }
      },
    });
  };

  const { updateCellMutation } = useUpdateCell();
  const { updateNodeMutation } = useUpdateNode();
  const { deleteNodeMutation } = useDeleteNode();

  const defaultNode: NodeSchemaValues = {
    lifecycle: '',
    orientation: '',
    site: '',
    node_serial_no: '',
    node_id: '',
    node_type: 'StreetCell',
    version: '1.1',
    roles: [],
  };

  const borderColor = useColorModeValue('1px solid #E2E8F0', '1px solid #2D3748');
  const boxShadow = useColorModeValue('sm', 'sm-dark');

  const getSelectedNode = (targetNode: string, useServerList = false) => {
    const listToUse = useServerList ? availableServerList : availableNodeList;

    const selectedNode: NodeSchemaValues | undefined = listToUse
      ?.map((node): NodeSchemaValues => {
        return {
          ...defaultNode,
          node_id: node.node_id,
          node_type: node.node_type,
          lifecycle: node.lifecycle,
          version: node.version || '1.1',
          roles: useServerList ? [] : ['RU'],
          site: node.site_id?.toString() || '',
          orientation: node.orientation as ORIENTATION,
          node_serial_no: node.node_serial_no,
        };
      })
      ?.find((node) => node.node_serial_no === targetNode);

    return selectedNode;
  };

  return (
    <Box
      as="form"
      marginX="auto"
      width="full"
      maxW={{
        lg: '8xl',
      }}
      bg="bg-surface"
      onSubmit={handleSubmit(onSubmit)}
      boxShadow={boxShadow}
      borderRadius="lg"
      noValidate={true}
    >
      <Box display="flex" justifyContent="end" padding="4">
        <Button
          variant="primary"
          leftIcon={<Icon as={AddIcon} marginStart="-1" />}
          onClick={() => resetButtonHandler()}
        >
          Reset form
        </Button>
      </Box>

      <Stack
        spacing="5"
        px={{
          base: '4',
          md: '6',
        }}
        py={{
          base: '5',
          md: '6',
        }}
      >
        {/* Region & Site */}
        <Stack
          spacing="6"
          direction={{
            base: 'column',
            md: 'row',
          }}
        >
          {/* Region */}
          <RegionFields form={cellForm} regions={regions} isEdit={isEdit} />
          {/* Site */}
          <SiteFields
            form={cellForm}
            regionSites={regionSites}
            isEdit={isEdit}
            updateCellMutation={updateCellMutation}
            checkRoleAccess={checkRoleAccess}
            onCreateOpen={onCreateOpen}
          />
        </Stack>
        {/* Cell Type & Lifecycle */}
        <Stack
          spacing="6"
          direction={{
            base: 'column',
            md: 'row',
          }}
        >
          {/* Cell Type */}
          <CellTypeFields form={cellForm} isEdit={isEdit} toast={toast} />
          {/* Lifecycle */}
          <LifeCycleFields form={cellForm} isEdit={isEdit} updateCellMutation={updateCellMutation} />
        </Stack>
        {/* Placement & Cell Reference */}
        <Stack
          spacing="6"
          direction={{
            base: 'column',
            md: 'row',
          }}
        >
          {/* Placement */}
          <PlacementField form={cellForm} isEdit={isEdit} />

          {/* Cell Reference */}
          <CellReferenceFields
            form={cellForm}
            isEdit={isEdit}
            generatedCellRef={generatedCellRef}
            refetchCellRef={refetchCellRef}
          />
        </Stack>
        {/* Orientation - OrientationFields breaking */}
        {/* Orientation */}
        <Collapse in={isSplit6TypeCell} unmountOnExit={true} className="chakra-form-control">
          <FormControl isInvalid={!!errors?.orientation} mb="4" isRequired>
            <FormLabel>Orientation</FormLabel>
            <Controller
              // name={`serverNodes.${index}.orientation`}
              // defaultValue={item.orientation}
              name={`orientation`}
              control={control}
              render={({ field }) => (
                <Select
                  id="orientation"
                  placeholder="Select an orientation"
                  {...register('orientation')}
                  onChange={(e) => {
                    if (isEdit) {
                      updateCellMutation({
                        cell_ref: getValues('cell_ref') ?? '',
                        cell: {
                          orientation: e.target.value,
                        } as UpdateCellRequest,
                      });
                    }
                  }}
                >
                  {Object.entries(ORIENTATION).map(([key, value]) => (
                    <option key={value} value={value}>
                      {key}
                    </option>
                  ))}
                </Select>
              )}
            />
            <FormErrorMessage>{errors?.orientation?.message}</FormErrorMessage>
          </FormControl>
        </Collapse>
        {/* Split 6 cell creation */}
        <Collapse in={isSplit6TypeCell} animateOpacity>
          {isSplit6TypeCell === true && !isEdit && (
            <Box>
              {/* This is needed for split-6 cell creation */}
              <Split6Form
                form={cellForm}
                availableNodeList={availableNodeList}
                availableServerList={availableServerList}
                availableSwitchList={availableSwitchList}
                regionSites={regionSites}
                isEdit={isEdit}
              />
            </Box>
          )}
        </Collapse>
        {/* GnodeB cell creation */}
        {isGNodeBTypeCell === true && !isEdit && (
          <GnodeBForm
            form={cellForm}
            availableNodeList={availableNodeList}
            availableServerList={availableServerList}
            availableSwitchList={availableSwitchList}
            regionSites={regionSites}
            isEdit={isEdit}
            isGNodeBTypeCell={isGNodeBTypeCell}
            state={state}
            assignedNodeList={assignedNodeList}
          />
        )}

        {/* ENodeB cell creation */}
        {isENodeBTypeCell === true && !isEdit && (
          <ENodeBForm
            form={cellForm}
            availableNodeList={availableENodeBList}
            availableServerList={availableServerList}
            availableSwitchList={availableSwitchList}
            regionSites={regionSites}
            isEdit={isEdit}
            isENodeBTypeCell={isENodeBTypeCell}
            state={state}
            assignedNodeList={assignedENodeBList}
          />
        )}

        <Divider />
        {/* Cell edit screen */}
        {isEdit && (
          <EditCellForm
            form={cellForm}
            availableNodeList={availableNodeList}
            availableServerList={availableServerList}
            availableSwitchList={availableSwitchList}
            regionSites={regionSites}
            isEdit={isEdit}
            isGNodeBTypeCell={isGNodeBTypeCell}
            isSplit6TypeCell={isSplit6TypeCell}
            assignedNodeList={assignedNodeList}
          />
        )}
      </Stack>

      <Divider />

      {/* Footer Controls */}
      <Flex
        direction="row-reverse"
        justifyContent="space-between"
        py="4"
        px={{
          base: '4',
          md: '6',
        }}
      >
        {isEdit ? (
          <Button colorScheme="teal" type="submit" visibility="hidden">
            Update
          </Button>
        ) : (
          <>
            <Button
              data-testid="create-cell-button"
              isDisabled={!isDirty}
              isLoading={isPosting || isLoading}
              loadingText={isPosting ? 'Creating...' : 'Loading...'}
              colorScheme="teal"
              type="submit"
            >
              Create cell
            </Button>
            <Checkbox isChecked={createAnotherCell} onChange={(e) => setCreateAnotherCell(e.target.checked)}>
              Create another cell?
            </Checkbox>
          </>
        )}
        <Button colorScheme="teal" variant="outline" onClick={() => navigate('/cell-overview')}>
          Back
        </Button>
      </Flex>
      <Modal isOpen={isCreateOpen} onClose={onCreateClose} size="lg" isCentered>
        <ModalOverlay bg="blackAlpha.900" />
        <ModalContent>
          <ModalCloseButton />
          <CreateSiteForm onClose={onCreateClose} />
        </ModalContent>
      </Modal>
      <DevTool control={control} />
    </Box>
  );
};

export default CellForm;
