import { Region, Site } from '../../types/InventoryManager.type';
export type RegionSitesType = Record<string, { name: string; id: number }[]>;
export type RegionSitesReturnType = (
  regionsData: Region[],
  regionSitesData: Site[]
) => {
  regions: {
    label: string;
    value: string;
  }[];
  regionSites: RegionSitesType;
};

export const constructRegionSites: RegionSitesReturnType = (regionsData: Region[], regionSitesData: Site[]) => {
  const regions: {
    label: string;
    value: string;
  }[] = regionsData.map((region) => ({
    label: `${region.region_name} ${region.country_name}`,
    value: region.region_code,
  }));
  const regionSites: RegionSitesType = regions.reduce(
    (acc, region) => ({
      ...acc,
      [region.value]: regionSitesData
        .filter((site) => site.region_code === region.value)
        .map((site) => ({ name: site.name, id: site.site_id })),
    }),
    {}
  );
  return { regions, regionSites };
};
