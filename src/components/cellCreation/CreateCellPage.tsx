import { Container, Stack, Text } from '@chakra-ui/react';
import { useLocation } from 'react-router';
import CreateCellForm from './CreateCellForm';

export default function CreateCellPage() {
  const { pathname } = useLocation();
  return (
    <Container
      py={{
        base: '4',
        md: '8',
      }}
    >
      <Stack spacing="5">
        <Text fontSize="2xl" fontWeight="medium" textAlign="center">
          {(pathname as string).includes('edit') ? 'Edit cell' : 'Create cell'}
        </Text>
        <CreateCellForm />
      </Stack>
    </Container>
  );
}
