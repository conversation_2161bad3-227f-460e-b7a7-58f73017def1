import { useMemo } from 'react';
import { useLocation } from 'react-router-dom';
import Loader from '../loader/Loader';
import CellForm from './CellForm';
import { useCellListByRef, useData } from './hooks/useData';

const CellCreationForm = (): JSX.Element => {
  const { pathname, state } = useLocation();
  const isEdit = (pathname as string).includes('edit') && state !== null ? true : false;

  const {
    availableNodeList,
    availableENodeBList,
    assignedENodeBList,
    refetchNodeList,
    assignedNodeList,
    availableServerList,
    assignedServerList,
    availableSwitchList,
    assignedSwitchList,
    isLoading,
    regionSites,
    regions,
  } = useData(state?.cell_ref);
  const { data: cellData, isLoading: isCellDataLoading } = useCellListByRef(state?.cell_ref);

  const availableNodes = useMemo(
    () => [
      ...availableNodeList,
      ...assignedNodeList,
      // ...(assignedNodeList.map(({ node_serial_no }) => node_serial_no) ?? []),
    ],
    [isEdit, availableNodeList, assignedNodeList]
  );

  const availableENodeBs = useMemo(
    () => [...availableENodeBList, ...assignedENodeBList],
    [isEdit, availableENodeBList, assignedENodeBList]
  );

  const availableServers = useMemo(
    () => [...availableServerList, ...assignedServerList],
    [isEdit, availableServerList, assignedServerList]
  );

  const availableSwitches = useMemo(
    () => [...availableSwitchList, ...assignedSwitchList],
    [isEdit, availableSwitchList, assignedSwitchList]
  );

  const formProps = {
    availableNodeList: isEdit ? availableNodes : availableNodeList,
    availableENodeBList: isEdit ? availableENodeBs : availableENodeBList,
    availableServerList: isEdit ? availableServers : availableServerList,
    availableSwitchList: isEdit ? availableSwitches : availableSwitchList,
    refetchNodeList,
    assignedNodeList,
    assignedENodeBList,
    assignedServerList,
    assignedSwitchList,
    isLoading,
    regionSites,
    regions,
    isEdit,
    cellData: {
      ...cellData,
      region: cellData?.region_code,
      site: cellData?.site_id.toString(),
      nodes: [...(assignedNodeList.length >= 1 ? assignedNodeList : [])],
      ...{
        ...(assignedNodeList.length === 1 && {
          node_serial_no: assignedNodeList[0].node_serial_no,
        }),
      },
    },
  };
  if (isLoading) return <Loader />;

  return <CellForm {...formProps} />;
};

export default CellCreationForm;
