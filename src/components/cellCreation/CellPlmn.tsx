import React, { useEffect, useState } from 'react';
import { Button, Box, Flex, Text, Icon, Heading, Table, Thead, Tr, Th, Tbody, Td } from '@chakra-ui/react';
import { useLocation, useNavigate } from 'react-router-dom';
import usePlmnList from '../../pages/SiteManager/hooks/usePlmnList';
import { Plmn } from '../../types/InventoryManager.type';
import usePostPlmns from './hooks/usePostPlmns';
import { useCellListByRef } from './hooks/useData';
import Loader from '../loader/Loader';
import { useDeletePlmns } from './hooks/useDeletePlmns';
import { ArrowBackIcon } from '@chakra-ui/icons';

const CellPlmn: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const state = location.state as { country_code: string; cell_ref: string } | null;
  const { data: plmnListData = [], isLoading: isPlmnLoading } = usePlmnList(state?.country_code);
  const { data: cellData, isLoading: isCellDataLoading } = useCellListByRef(state?.cell_ref || '');
  const addPlmns = usePostPlmns(state?.cell_ref || '', state?.country_code);
  const { deletePlmnsMutation } = useDeletePlmns(state?.cell_ref);
  const [assignedPlmns, setAssignedPlmns] = useState<Plmn[]>([]);
  const [plmnList, setPlmnList] = useState<Plmn[]>([]);

  useEffect(() => {
    setAssignedPlmns(cellData?.plmns);
  }, [cellData]);

  useEffect(() => {
    if (assignedPlmns) {
      if (assignedPlmns?.length) {
        const filteredPlmnList = plmnListData.filter(
          (plmn) =>
            !assignedPlmns?.some((selectedPlmn) => selectedPlmn.mcc === plmn.mcc && selectedPlmn.mnc === plmn.mnc)
        );
        setPlmnList(filteredPlmnList);
      } else {
        setPlmnList(plmnListData);
      }
    }
  }, [plmnListData, assignedPlmns]);

  const handleAddPLMN = (plmn: Plmn) => {
    addPlmns.mutate(plmn);
    if (!assignedPlmns.find((p) => p.mcc === plmn.mcc && p.mnc === plmn.mnc)) {
      setAssignedPlmns([...assignedPlmns, plmn]);
    }
  };

  const handleRemovePLMN = (plmn: Plmn) => {
    const variables = {
      cell_ref: state?.cell_ref || '',
      mcc: plmn.mcc,
      mnc: plmn.mnc,
    };
    deletePlmnsMutation(variables);
  };
  const header = ['Action', 'Operator', 'MCC', 'MNC'];

  if (isPlmnLoading || isCellDataLoading) return <Loader />;
  return (
    <Box p="5">
      <Flex alignItems="center" mb="5">
        <Icon
          as={ArrowBackIcon}
          w={5}
          h={5}
          mr={4}
          onClick={() => {
            localStorage.removeItem('nodeId');
            navigate(-1);
          }}
        />
        <Text data-testid="editNode-heading" fontSize="2xl" fontWeight="bold">
          Cell Reference: {state?.cell_ref}
        </Text>
      </Flex>
      <Flex p={5} justifyContent="space-between" gap="20">
        <Box flex="1" p="5" backgroundColor="white" data-testid="available-plmns">
          <Heading size="md" mb="4" textAlign="center">
            Available PLMNs
          </Heading>
          <Table p={10} colorScheme="gray" variant="simple" data-testid="available-plmns-table">
            <Thead>
              <Tr>
                {header.map((value) => (
                  <Th key={value} width="25%">
                    {value}
                  </Th>
                ))}
              </Tr>
            </Thead>
            <Tbody>
              {plmnList?.length ? (
                plmnList.map((plmn) => (
                  <Tr key={`${plmn.mcc}-${plmn.mnc}`}>
                    <Td>
                      <Button
                        size="sm"
                        colorScheme="blue"
                        onClick={() => handleAddPLMN(plmn)}
                        data-testid="available-plmns-add"
                      >
                        +
                      </Button>
                    </Td>
                    <Td>{plmn.operator}</Td>
                    <Td>{plmn.mcc}</Td>
                    <Td>{plmn.mnc}</Td>
                  </Tr>
                ))
              ) : (
                <Tr>
                  <Td colSpan={4} textAlign="center">
                    {plmnListData.length > 0 ? (
                      <Text fontSize="xl">{`ALL Plmns are assigned for ${state?.cell_ref} cell reference.`}</Text>
                    ) : (
                      <Text fontSize="xl">{`No Plmns are found for ${state?.cell_ref} cell reference.`}</Text>
                    )}
                  </Td>
                </Tr>
              )}
            </Tbody>
          </Table>
        </Box>

        <Box flex="1" p="5" backgroundColor="white" data-testid="assigned-plmns">
          <Heading size="md" mb="4" textAlign="center">
            Assigned PLMNs
          </Heading>
          <Table p="10" variant="simple" colorScheme="gray" data-testid="assigned-plmns-table">
            <Thead>
              <Tr>
                {header.map((value) => (
                  <Th key={value} width="25%">
                    {value}
                  </Th>
                ))}
              </Tr>
            </Thead>
            <Tbody>
              {assignedPlmns?.length ? (
                assignedPlmns.map((plmn) => (
                  <Tr key={`${plmn.mcc}-${plmn.mnc}`}>
                    <Td>
                      <Button
                        size="sm"
                        colorScheme="red"
                        onClick={() => handleRemovePLMN(plmn)}
                        data-testid="assigned-plmns-remove"
                      >
                        -
                      </Button>
                    </Td>
                    <Td>{plmn.operator}</Td>
                    <Td>{plmn.mcc}</Td>
                    <Td>{plmn.mnc}</Td>
                  </Tr>
                ))
              ) : (
                <Tr>
                  <Td colSpan={4} textAlign="center">
                    <Text fontSize="xl">{`No Plmns are found for ${state?.cell_ref} cell reference.`}</Text>
                    <Text mt="2" fontSize="lg">
                      Try Adding the Plmn for this cell ref
                    </Text>
                  </Td>
                </Tr>
              )}
            </Tbody>
          </Table>
        </Box>
      </Flex>
    </Box>
  );
};

export default CellPlmn;
