import { CheckIcon, CloseIcon, EditIcon } from '@chakra-ui/icons';
import {
  useToast,
  useEditableControls,
  ButtonGroup,
  IconButton,
  Flex,
  Editable,
  InputGroup,
  EditablePreview,
  Input,
  EditableInput,
  InputRightElement,
} from '@chakra-ui/react';
import { useQueryClient, useMutation } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { updateCell } from '../../services/inventoryManager';
import useUpdateCell from './hooks/useUpdateCell';

export default function CustomControl({ cell_ref }: { cell_ref: string }) {
  const navigate = useNavigate();
  const { updateCellMutation } = useUpdateCell();

  function EditableControls() {
    const { isEditing, getSubmitButtonProps, getCancelButtonProps, getEditButtonProps } = useEditableControls();

    return isEditing ? (
      <ButtonGroup justifyContent="right" size="sm">
        <IconButton aria-label="change cell ref" icon={<CheckIcon />} {...getSubmitButtonProps()} />
        <IconButton aria-label="cancel change cell ref" icon={<CloseIcon />} {...getCancelButtonProps()} />
      </ButtonGroup>
    ) : (
      <Flex justifyContent="right">
        <IconButton aria-label="change cell ref" size="sm" icon={<EditIcon />} {...getEditButtonProps()} />
      </Flex>
    );
  }
  const onSubmit = (val: string) => {
    if (cell_ref !== val) {
      updateCellMutation({ cell_ref: cell_ref, cell: { cell_ref: val } });
      navigate(`/cell-overview/edit`, { state: { cell_ref: val } });
    }
  };

  return (
    <Editable
      defaultValue={cell_ref}
      fontSize="xl"
      isPreviewFocusable={false}
      onSubmit={onSubmit}
      submitOnBlur={false}
      height="30px"
    >
      <InputGroup>
        <EditablePreview pl="1rem" />
        <Input as={EditableInput} />
        <InputRightElement justifyContent="right">
          <EditableControls />
        </InputRightElement>
      </InputGroup>
    </Editable>
  );
}
