import { useToast } from '@chakra-ui/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { deletePlmns } from '../../../services/inventoryManager';
import { Plmn } from '../../../types/InventoryManager.type';

interface Error {
  message: string[];
  statusCode: number;
}
export function useDeletePlmns(cell_ref?: string) {
  const queryClient = useQueryClient();
  const toast = useToast();

  const { mutate: deletePlmnsMutation } = useMutation({
    mutationFn: ({ cell_ref, mcc, mnc }: { cell_ref: string; mcc: Plmn['mcc']; mnc: Plmn['mnc'] }) =>
      deletePlmns(cell_ref, mcc, mnc),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['getPlmnList'] });
      queryClient.invalidateQueries({ queryKey: ['getCellListByRef', cell_ref] });
      toast({
        title: 'Success',
        description: `PLMN has been removed successfully.`,
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `${(error as AxiosError<Error>)?.response?.data?.message}`,
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
  });
  return { deletePlmnsMutation };
}
