import { useToast } from '@chakra-ui/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { postPlmns } from '../../../services/inventoryManager';
import { Plmn } from '../../../types/InventoryManager.type';

export default function usePostPlmns(cell_ref: string, countryCode?: string) {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: (plmn: Plmn) => {
      // Extract only mcc and mnc from Plmn
      const payload = { mcc: plmn.mcc, mnc: plmn.mnc };
      return postPlmns(payload, cell_ref);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['getPlmnList', countryCode] });
      queryClient.invalidateQueries({ queryKey: ['getCellListByRef', cell_ref] });
      toast({
        title: 'PLMN Added.',
        description: 'PLMN has been added successfully.',
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: (error) => {
      toast({
        title: `Error ${error}`,
        description: 'Something went wrong.',
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
  });
}
