import { useQuery } from '@tanstack/react-query';
import { useMemo } from 'react';
import { MANIFEST_TYPE, NODE_TYPE } from '../../../data/constants';
import { getCellList, getDeployedNodeList, getNodesForCell } from '../../../services/inventoryManager';
import { Region, Site } from '../../../types/InventoryManager.type';
import { constructRegionSites } from '../utils';
import { getRegionList, getSiteList } from './../../../services/inventoryManager';

export default function useSiteList() {
  return useQuery<Site[], Error>(['sites'], getSiteList);
}

function useRegionList() {
  return useQuery<Region[], Error>(['regions'], getRegionList);
}

function useNodeByCellRef(cell_ref: string) {
  return useQuery(['getNodesForCell', cell_ref], () => getNodesForCell(cell_ref), {
    enabled: !!cell_ref,
    select: (data) => data.map((data) => ({ ...data, site: data?.site_id.toString() })),
  });
}

function useNodeList(manifest_type: string, deployable: boolean) {
  return useQuery(
    ['availableSerialNos', manifest_type, deployable],
    () => getDeployedNodeList({ manifest_type, deployable })
    // {
    //   select: (nodes) => nodes.map(({ node_serial_no }) => node_serial_no),
    // }
  );
}

function useNetworkList(manifest_type: string, deployable: boolean, checkNode?: boolean) {
  return useQuery(['availableNetwork', manifest_type, deployable, checkNode], () =>
    getDeployedNodeList({ manifest_type, deployable, checkNode })
  );
}

function useServerList(manifest_type: string, deployable: boolean, checkNode?: boolean) {
  return useQuery(['availableServers', manifest_type, deployable, checkNode], () =>
    getDeployedNodeList({ manifest_type, deployable, checkNode })
  );
}

function useSwitchList(manifest_type: string, deployable: boolean, node_type: string) {
  return useQuery(['availableSwitch', manifest_type, deployable, node_type], () =>
    getDeployedNodeList({ manifest_type, deployable, node_type })
  );
}

function useRadioList(manifest_type: string, deployable: boolean, node_type: string) {
  return useQuery(['availableRadio', manifest_type, deployable, node_type], () =>
    getDeployedNodeList({ manifest_type, deployable, node_type })
  );
}

export function useCellListByRef(cell_ref: string) {
  const queryKey = cell_ref ? ['getCellListByRef', cell_ref] : ['getCellListByRef'];
  return useQuery(queryKey, () => getCellList(cell_ref), {
    enabled: !!cell_ref,
    select: (data) => {
      return data?.[0];
    },
  });
}

export const useData = (cell_ref?: string, checkNode?: boolean) => {
  const siteListQuery = useSiteList();
  const regionListQuery = useRegionList();
  const assignedNodeList = useNodeByCellRef(cell_ref!);
  const assignedENodeBList = useNodeByCellRef(cell_ref!);
  const nodeListQuery = useNodeList(MANIFEST_TYPE.STREETCELL, true);
  const eNodeBListQuery = useNodeList(MANIFEST_TYPE.AS4G, true);
  const assignedServerList = useNodeByCellRef(cell_ref!);
  const serverListQuery = useServerList(MANIFEST_TYPE.SERVER, true, checkNode);
  const assignedSwitchList = useNodeByCellRef(cell_ref!);
  const switchListQuery = useSwitchList('', true, NODE_TYPE.SWITCH);
  const networkListQuery = useNetworkList(MANIFEST_TYPE.NETWORK, true, checkNode);
  const isLoading = siteListQuery.isLoading || regionListQuery.isLoading;
  const radioListQuery = useRadioList('', true, NODE_TYPE.RADIO);

  const regionsAndSites = useMemo(
    () => constructRegionSites(regionListQuery.data || [], siteListQuery.data || []),
    [regionListQuery.data, siteListQuery.data]
  );

  return {
    availableNodeList: nodeListQuery.data || [],
    availableENodeBList: eNodeBListQuery.data || [],
    assignedENodeBList: assignedENodeBList.data || [],
    assignedNodeList: assignedNodeList.data || [],
    availableServerList: serverListQuery.data || [],
    availableNetworkList: networkListQuery.data || [],
    assignedServerList: assignedServerList.data || [],
    availableSwitchList: switchListQuery.data || [],
    availableRadioList: radioListQuery.data || [],
    assignedSwitchList: assignedSwitchList.data || [],
    refetchNodeList: nodeListQuery.refetch,
    refetchENodeBList: eNodeBListQuery.refetch,
    isLoading,
    ...regionsAndSites,
  };
};
