import { useToast } from '@chakra-ui/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { deleteNode, deleteSite, updateCell } from '../../../services/inventoryManager';
import { CellRequest, Site } from '../../../types/InventoryManager.type';
interface Error {
  message: string[];
  statusCode: number;
}
export function useDeleteNode() {
  const queryClient = useQueryClient();
  const toast = useToast();

  const { mutate: deleteNodeMutation } = useMutation({
    mutationFn: ({ cell_ref, node_id }: { cell_ref: string; node_id: string }) => deleteNode(cell_ref, node_id),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['nodes'] });
      toast({
        title: 'Success',
        description: `Node has been deleted successfully.`,
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `${(error as AxiosError<Error>)?.response?.data?.message}`,
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
  });
  return { deleteNodeMutation };
}
