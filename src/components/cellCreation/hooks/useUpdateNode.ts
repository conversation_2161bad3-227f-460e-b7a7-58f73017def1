import { useToast } from '@chakra-ui/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { updateNode } from '../../../services/inventoryManager';

interface Error {
  message: string[];
  status: number;
}
export default function useUpdateNode() {
  const queryClient = useQueryClient();
  const toast = useToast();

  const { mutateAsync: updateNodeMutation, isLoading: isNodeUpdating } = useMutation({
    mutationFn: updateNode,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['availableSerialNos'] });
      toast({
        title: 'Node updated.',
        description: `${data?.node_id} has been updated successfully.`,
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: (error) => {
      const errorMessage =
        (error as AxiosError<Error>)?.response?.data?.status === 409
          ? 'Node serial number is already assigned'
          : (error as AxiosError<Error>)?.response?.data?.message;
      toast({
        title: 'Error',
        description: errorMessage,
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
  });

  return { updateNodeMutation, isNodeUpdating };
}
