import { useToast } from '@chakra-ui/react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { updateCell } from '../../../services/inventoryManager';

export default function useUpdateCell() {
  const queryClient = useQueryClient();
  const toast = useToast();

  const { mutateAsync: updateCellMutation, isLoading: isCellUpdating } = useMutation({
    mutationFn: updateCell,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['getCellListByRef'] });
      toast({
        title: 'Cell updated.',
        description: `${data?.cell_ref} has been updated successfully.`,
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
  });
  return { updateCellMutation, isCellUpdating };
}
