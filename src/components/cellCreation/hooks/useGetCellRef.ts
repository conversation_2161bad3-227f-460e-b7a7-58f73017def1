import { useQuery } from '@tanstack/react-query';
import { getCalculateCellRef } from '../../../services/inventoryManager';

export default function useGetCellRef(
  site_id: number,
  isEditing: boolean,
  cell_ref: string,
  oran_split: string,
  placement?: string
) {
  const result = useQuery({
    queryKey: ['cellRef', site_id, placement, oran_split],
    queryFn: () => getCalculateCellRef(site_id, oran_split, placement),
    enabled: (!!placement || !!oran_split) && !isEditing && !!site_id,
  });

  const refetchCellRef = () => {
    result.refetch();
  };

  if (isEditing) return { data: cell_ref, refetchCellRef: () => void 0 };

  return { data: result.data?.cell_ref, refetchCellRef };
}
