import { HStack, Stack, Text, useColorModeValue } from '@chakra-ui/react';
import { Key } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { Node } from '../../../types/InventoryManager.type';
import GcpVmFormFields from '../formFields/nodeFormFields/GcpVmFormFields';
import MeshRootFormFields from '../formFields/nodeFormFields/MeshRootFormFields';
import ServerFormFields from '../formFields/nodeFormFields/ServerFormFields';
import ServerVMFormFields from '../formFields/nodeFormFields/ServerVMFormFields';
import StreetCellFormFields from '../formFields/nodeFormFields/StreetCellFormFields';
import SwitchFormFields from '../formFields/nodeFormFields/SwitchFormFields';
import { CellFormValues } from '../schema';
import { RegionSitesType } from '../utils';

export type EditCellFieldsProps = {
  form: UseFormReturn<CellFormValues>;
  availableNodeList: Node[];
  availableServerList?: Node[];
  availableSwitchList?: Node[];
  regionSites: RegionSitesType;
  isEdit?: boolean;
  isGNodeBTypeCell: boolean;
  isSplit6TypeCell: boolean;
  assignedNodeList: Node[];
};

export type nodeItemProps = {
  id: Key | null | undefined;
  node_serial_no: string | undefined;
  roles: ('RU' | 'DU' | 'CU' | 'MMW' | 'VSR' | 'CORE' | 'EMS')[] | undefined;
  lifecycle: string | undefined;
  site: string | undefined;
};

type groupedByNodeTypeProps = {
  StreetCell?: string;
  Server?: string;
  Switch?: string;
  ServerVM?: string;
  MeshRoot?: string;
  GCPVM?: string;
};

const EditCellFields = ({
  form: {
    control,
    getValues,
    register,
    setValue,
    watch,
    formState: { errors },
  },
  availableNodeList,
  availableServerList,
  availableSwitchList,
  regionSites,
  isEdit,
  isGNodeBTypeCell,
  isSplit6TypeCell,
  assignedNodeList,
}: EditCellFieldsProps) => {
  const countNodeTypes = (nodes: Node[]) => {
    const nodeTypeCounts: { [key: string]: number } = {};

    nodes.forEach((node) => {
      nodeTypeCounts[node.node_type] = (nodeTypeCounts[node.node_type] || 0) + 1;
    });

    return nodeTypeCounts;
  };
  const nodeTypeCount = countNodeTypes(assignedNodeList);

  const groupedByNodeType: groupedByNodeTypeProps = assignedNodeList.reduce((acc: Record<string, Node[]>, node) => {
    const nodeTypeKey = node.node_type.replace(/-/g, '');
    if (!acc[nodeTypeKey]) {
      acc[nodeTypeKey] = [];
    }
    acc[nodeTypeKey].push(node);
    return acc;
  }, {});

  const borderColor = useColorModeValue('1px solid #E2E8F0', '1px solid #2D3748');

  return (
    <>
      {/* Street Cell */}
      {groupedByNodeType.StreetCell && (
        <>
          <HStack justifyContent="space-between">
            <Text fontSize="lg" fontWeight="bold">
              Street cell
            </Text>
          </HStack>
          {/* {groupedByNodeType.StreetCell.map((item: Node, index: number) => ( */}
          <Stack borderRadius={4} boxShadow="lg" border={borderColor} p={4}>
            {Array.isArray(groupedByNodeType.StreetCell) &&
              groupedByNodeType.StreetCell.map((item, index) => (
                <StreetCellFormFields
                  key={`StreetCell-${item.node_id}-${index}`}
                  control={control}
                  getValues={getValues}
                  register={register}
                  setValue={setValue}
                  watch={watch}
                  errors={errors || {}}
                  availableNodeList={availableNodeList}
                  isEdit={isEdit}
                  isGNodeBTypeCell={isGNodeBTypeCell}
                  isSplit6TypeCell={isSplit6TypeCell}
                  item={item}
                  index={index}
                  nodeTypeCount={nodeTypeCount}
                  regionSites={regionSites}
                />
              ))}
          </Stack>
        </>
      )}

      {/* Server ----- This will need to be 2*/}
      {groupedByNodeType.MeshRoot && (
        <>
          <HStack justifyContent="space-between">
            <Text fontSize="lg" fontWeight="bold">
              Mesh root
            </Text>
          </HStack>
          {/* {groupedByNodeType.MeshRoot.map((item: Node, index: number) => ( */}
          <Stack borderRadius={4} boxShadow="lg" border={borderColor} p={4}>
            {Array.isArray(groupedByNodeType.MeshRoot) &&
              groupedByNodeType.MeshRoot.map((item, index) => (
                <MeshRootFormFields
                  key={`MeshRoot-${item.node_id}-${index}`}
                  control={control}
                  getValues={getValues}
                  register={register}
                  setValue={setValue}
                  watch={watch}
                  errors={errors}
                  isEdit={isEdit}
                  item={item}
                  index={index}
                  availableServerList={availableServerList}
                  regionSites={regionSites}
                  isGNodeBTypeCell={false}
                  isSplit6TypeCell={false}
                />
              ))}
          </Stack>
        </>
      )}

      {/* Server VM ----- This will need to be 2*/}
      {groupedByNodeType.ServerVM && (
        <>
          <HStack justifyContent="space-between">
            <Text fontSize="lg" fontWeight="bold">
              Server-VM
            </Text>
          </HStack>
          {/* {groupedByNodeType.MeshRoot.map((item: Node, index: number) => ( */}
          <Stack borderRadius={4} boxShadow="lg" border={borderColor} p={4}>
            {Array.isArray(groupedByNodeType.ServerVM) &&
              groupedByNodeType.ServerVM.map((item, index) => (
                <ServerVMFormFields
                  key={`ServerVM-${item.node_id}-${index}`}
                  control={control}
                  getValues={getValues}
                  register={register}
                  setValue={setValue}
                  watch={watch}
                  errors={errors}
                  isEdit={isEdit}
                  item={item}
                  index={index}
                  availableServerList={availableServerList}
                  regionSites={regionSites}
                  isGNodeBTypeCell={false}
                  isSplit6TypeCell={false}
                />
              ))}
          </Stack>
        </>
      )}

      {/* Server ----- This will need to be 2*/}
      {groupedByNodeType.Server && (
        <>
          <HStack justifyContent="space-between">
            <Text fontSize="lg" fontWeight="bold">
              Server
            </Text>
          </HStack>
          {/* {groupedByNodeType.MeshRoot.map((item: Node, index: number) => ( */}
          <Stack borderRadius={4} boxShadow="lg" border={borderColor} p={4}>
            {Array.isArray(groupedByNodeType.Server) &&
              groupedByNodeType.Server.map((item, index) => (
                <ServerFormFields
                  key={`Server-${item.node_id}-${index}`}
                  control={control}
                  getValues={getValues}
                  register={register}
                  setValue={setValue}
                  watch={watch}
                  errors={errors}
                  isEdit={isEdit}
                  item={item}
                  index={index}
                  availableServerList={availableServerList}
                  regionSites={regionSites}
                  isGNodeBTypeCell={false}
                  isSplit6TypeCell={false}
                />
              ))}
          </Stack>
        </>
      )}

      {/* GCP VM ----- This will need to be 2*/}
      {groupedByNodeType.GCPVM && (
        <>
          <HStack justifyContent="space-between">
            <Text fontSize="lg" fontWeight="bold">
              Gcp-VM
            </Text>
          </HStack>
          {/* {groupedByNodeType.MeshRoot.map((item: Node, index: number) => ( */}
          <Stack borderRadius={4} boxShadow="lg" border={borderColor} p={4}>
            {Array.isArray(groupedByNodeType.GCPVM) &&
              groupedByNodeType.GCPVM.map((item, index) => (
                <GcpVmFormFields
                  key={`GcpVM-${item.node_id}-${index}`}
                  control={control}
                  getValues={getValues}
                  register={register}
                  setValue={setValue}
                  watch={watch}
                  errors={errors}
                  isEdit={isEdit}
                  item={item}
                  index={index}
                  availableServerList={availableServerList}
                  regionSites={regionSites}
                  isGNodeBTypeCell={false}
                  isSplit6TypeCell={false}
                />
              ))}
          </Stack>
        </>
      )}

      {/* Switch*/}
      {groupedByNodeType.Switch && (
        <>
          <HStack justifyContent="space-between">
            <Text fontSize="lg" fontWeight="bold">
              Switch
            </Text>
          </HStack>
          {/* {groupedByNodeType.MeshRoot.map((item: Node, index: number) => ( */}
          <Stack borderRadius={4} boxShadow="lg" border={borderColor} p={4}>
            {Array.isArray(groupedByNodeType.Switch) &&
              groupedByNodeType.Switch.map((item, index) => (
                <SwitchFormFields
                  key={`Switch-${item.node_id}-${index}`}
                  control={control}
                  getValues={getValues}
                  register={register}
                  setValue={setValue}
                  watch={watch}
                  errors={errors}
                  isEdit={isEdit}
                  item={item}
                  index={index}
                  availableSwitchList={availableSwitchList}
                  regionSites={regionSites}
                  isGNodeBTypeCell={isGNodeBTypeCell}
                  isSplit6TypeCell={isSplit6TypeCell}
                />
              ))}
          </Stack>
        </>
      )}
    </>
  );
};

export default EditCellFields;
