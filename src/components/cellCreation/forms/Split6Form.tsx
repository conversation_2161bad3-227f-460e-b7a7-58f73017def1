import { DeleteIcon } from '@chakra-ui/icons';
import {
  <PERSON>ert,
  AlertIcon,
  Badge,
  Box,
  Button,
  FormControl,
  FormErrorMessage,
  FormLabel,
  HStack,
  Select,
  Stack,
  Text,
  useColorModeValue,
} from '@chakra-ui/react';
import { useState } from 'react';
import { Controller, useFieldArray, UseFormReturn } from 'react-hook-form';
import { useLocation } from 'react-router-dom';
import MultiSelect from 'react-select'; // react-select MultiSelect
import { LifeCycle, LIFE_CYCLE, ORIENTATION, ROLE_OF_NODE, ROLE_OF_NODE_ARRAY } from '../../../data/constants';
import { Node } from '../../../types/InventoryManager.type';
import { useDeleteNode } from '../hooks/useDeleteNode';
import useUpdateNode from '../hooks/useUpdateNode';
import { CellFormValues, NodeSchemaValues } from '../schema';
import {
  rolesCustomDarkTheme,
  rolesCustomLightTheme,
  rolesMultiSelectDarkStyles,
  rolesMultiSelectLightStyles,
} from '../styles/MultiSelectStyles';
import { RegionSitesType } from '../utils';

export type Split6FormProps = {
  form: UseFormReturn<CellFormValues>;
  availableNodeList: Node[];
  availableServerList?: Node[];
  availableSwitchList?: Node[];
  regionSites: RegionSitesType;
  isEdit?: boolean;
};

const Split6Form = ({
  form: {
    control,
    getValues,
    register,
    setValue,
    watch,
    formState: { errors },
  },
  availableNodeList,
  availableServerList,
  availableSwitchList,
  regionSites,
  isEdit,
}: Split6FormProps) => {
  const { state } = useLocation();
  const [selectedGNodeB, setSelectedGNodeB] = useState<Node | undefined>(undefined);
  const [selectedServer, setSelectedServer] = useState<Node | undefined>(undefined);
  const {
    fields: streetCellFields,
    remove: removeStreetCell,
    prepend: prependStreetCell,
  } = useFieldArray({
    control,
    name: 'streetCellNodes',
  });

  const {
    fields: serverFields,
    remove: removeServer,
    prepend: prependServer,
  } = useFieldArray({
    control,
    name: 'serverNodes',
  });

  const {
    fields: switchFields,
    remove: removeSwitch,
    prepend: prependSwitch,
  } = useFieldArray({
    control,
    name: 'switchNodes',
  });

  const minNodeLength = 1;
  const maxStreetCellLength = Math.min(availableNodeList.length, 6);
  const lightOrDarkTheme = useColorModeValue('light', 'dark');

  const borderColor = useColorModeValue('1px solid #E2E8F0', '1px solid #2D3748');
  const { updateNodeMutation } = useUpdateNode();
  const { deleteNodeMutation } = useDeleteNode();

  const defaultNode: NodeSchemaValues = {
    lifecycle: '',
    orientation: '',
    site: '',
    node_serial_no: '',
    node_id: '',
    node_type: 'StreetCell',
    version: '1.1',
    roles: [],
  };

  const getSelectedNode = (targetNode: string, useServerList: Node[] | undefined) => {
    //const listToUse = useServerList ? availableServerList : availableNodeList;

    const selectedNode: NodeSchemaValues | undefined = useServerList
      ?.map((node): NodeSchemaValues => {
        return {
          ...defaultNode,
          node_id: node.node_id,
          node_type: node.node_type,
          lifecycle: node.lifecycle,
          version: node.version || '1.1',
          roles: useServerList ? [] : ['RU'],
          site: node.site_id?.toString() || '',
          orientation: node.orientation as ORIENTATION,
          node_serial_no: node.node_serial_no,
        };
      })
      ?.find((node) => node.node_serial_no === targetNode);

    return selectedNode;
  };
  return (
    <Box>
      {/* Split 6 */}
      <Stack
        spacing="6"
        direction={{
          base: 'row',
          md: 'column',
        }}
        verticalAlign="bottom"
        borderRadius="4"
        boxShadow="lg"
        border={borderColor}
        p="8"
        marginBottom="12"
      >
        <HStack justifyContent="space-between">
          <Text fontSize="lg" fontWeight="bold">
            Split 6
          </Text>
          {isEdit ? null : (
            <Button
              colorScheme="teal"
              variant="outline"
              isDisabled={(getValues('streetCellNodes')?.length ?? []) >= maxStreetCellLength}
              onClick={() => prependStreetCell(defaultNode)}
            >
              Add Node
            </Button>
          )}
        </HStack>
        {(getValues('streetCellNodes')?.length ?? 0) < 1 && (
          <Alert status={errors?.streetCellNodes ? 'error' : 'info'}>
            <AlertIcon />
            <p>At least 1 Street cell is required</p>
          </Alert>
        )}
        {(getValues('streetCellNodes')?.length ?? []) >= maxStreetCellLength && (
          <Alert status="info">
            <AlertIcon />
            <p>Cannot proceed with more than the available nodes or a maximum of 6 nodes.</p>
          </Alert>
        )}
        {streetCellFields.map((item, index) => {
          return (
            <HStack key={item.id} alignItems="baseline" justifyContent="space-between">
              {/* Serial number */}
              <FormControl isInvalid={!!errors?.streetCellNodes?.[index]?.node_serial_no} mb="4" isRequired mr="4">
                <FormLabel htmlFor="serial_number">Serial Number</FormLabel>
                <Controller
                  name={`streetCellNodes.${index}.node_serial_no`}
                  defaultValue={item.node_serial_no}
                  control={control}
                  render={({ field }) => (
                    <Select
                      {...field}
                      id="serial_number"
                      placeholder="Select a serial no"
                      disabled={isEdit && watch(`streetCellNodes.${index}.node_id`) !== ''}
                      onChange={(e) => {
                        const foundGNodeB = availableNodeList.find((node) => node.node_id === e.target.value);
                        setSelectedGNodeB(foundGNodeB);
                        if (isEdit) {
                          updateNodeMutation({
                            node_id: getValues(`streetCellNodes.${index}.node_id`) ?? '',
                            node: { node_serial_no: e.target.value },
                          });
                          field.onChange(e);
                        } else {
                          setValue(
                            `streetCellNodes.${index}`,
                            getSelectedNode(e.target.value, availableNodeList) ?? defaultNode
                          );
                          field.onChange(e);
                        }
                      }}
                    >
                      {availableNodeList.map((node) => (
                        <option key={node?.node_serial_no} value={node?.node_serial_no}>
                          {node?.node_serial_no}
                        </option>
                      ))}
                    </Select>
                  )}
                />
                <FormErrorMessage>{errors?.streetCellNodes?.[index]?.node_serial_no?.message}</FormErrorMessage>
              </FormControl>
              {/* Orientation */}
              <FormControl isInvalid={!!errors?.streetCellNodes?.[index]?.orientation} mb="4" isRequired mr="4">
                <FormLabel htmlFor="orientation">Orientation</FormLabel>
                <Select
                  id="orientation"
                  placeholder="Select an orientation"
                  defaultValue={item.orientation}
                  {...register(`streetCellNodes.${index}.orientation`)}
                  onChange={(e) => {
                    setValue(`streetCellNodes.${index}.orientation`, e.target.value);
                    if (isEdit) {
                      updateNodeMutation({
                        node_id: getValues(`streetCellNodes.${index}.node_id`) ?? '',
                        node: { orientation: e.target.value },
                      });
                    }
                  }}
                >
                  {Object.entries(ORIENTATION).map(([key, value]) => (
                    <option key={value} value={value}>
                      {key}
                    </option>
                  ))}
                </Select>
                <FormErrorMessage>{errors?.streetCellNodes?.[index]?.orientation?.message}</FormErrorMessage>
              </FormControl>
              {/* Lifecycle */}
              <FormControl isInvalid={!!errors?.streetCellNodes?.[index]?.lifecycle} mb="4" isRequired mr="4">
                <FormLabel htmlFor="lifecycle">Lifecycle</FormLabel>
                <Controller
                  name={`streetCellNodes.${index}.lifecycle`}
                  control={control}
                  defaultValue={item.lifecycle}
                  render={({ field }) => (
                    <Select
                      {...field}
                      id="lifecycle"
                      placeholder="Select a lifecycle"
                      onChange={(e) => {
                        if (isEdit) {
                          field.onChange(e);
                          updateNodeMutation({
                            node_id: getValues(`streetCellNodes.${index}.node_id`) ?? '',
                            node: { lifecycle: e.target.value as LIFE_CYCLE },
                          });
                        } else {
                          field.onChange(e);
                        }
                      }}
                    >
                      {Object.entries(LifeCycle).map(([key, value]) => (
                        <option
                          key={value}
                          value={key}
                          disabled={
                            !isEdit ? value > LifeCycle[getValues('lifecycle') as keyof typeof LifeCycle] : false
                          }
                          style={{
                            color:
                              !isEdit && value > LifeCycle[getValues('lifecycle') as keyof typeof LifeCycle]
                                ? '#85828280'
                                : '',
                          }}
                        >
                          {key}
                        </option>
                      ))}
                    </Select>
                  )}
                />
                <FormErrorMessage>{errors?.streetCellNodes?.[index]?.lifecycle?.message}</FormErrorMessage>
              </FormControl>
              {/* Site */}
              <FormControl
                isInvalid={!!errors?.streetCellNodes?.[index]?.site}
                mb="4"
                //isDisabled={watch('region') ? false : true}
                isDisabled={!watch('region')}
                isRequired
              >
                {/* <FormLabel htmlFor={`${index}_site`}>Site</FormLabel> */}
                <FormLabel htmlFor={`site`}>Site</FormLabel>
                <Controller
                  name={`streetCellNodes.${index}.site`}
                  control={control}
                  defaultValue={item.site}
                  render={({ field }) => (
                    <Select
                      {...field}
                      id={`site`}
                      placeholder="Select a site"
                      onChange={(e) => {
                        if (isEdit) {
                          field.onChange(e);
                          updateNodeMutation({
                            node_id: getValues(`streetCellNodes.${index}.node_id`) ?? '',
                            node: { site_id: Number(e.target.value) },
                          });
                        } else {
                          field.onChange(e);
                        }
                      }}
                    >
                      {regionSites[watch('region')]?.map(({ name, id }) => (
                        <option key={`${id}-site`} value={id}>
                          {name}
                        </option>
                      ))}
                    </Select>
                  )}
                />
                <FormErrorMessage>
                  {watch('region') ? errors?.streetCellNodes?.[index]?.site?.message : 'Select a region first'}
                </FormErrorMessage>
              </FormControl>
              {/* Delete button */}
              {isEdit ? null : (
                <FormControl width="25%">
                  <FormLabel visibility="hidden">Del</FormLabel>
                  <Button
                    colorScheme="teal"
                    variant="outline"
                    onClick={() => {
                      if (isEdit && getValues(`streetCellNodes.${index}.node_id`) !== '') {
                        deleteNodeMutation({
                          node_id: getValues(`streetCellNodes.${index}.node_id`) ?? '',
                          cell_ref: state?.cell_ref ?? '',
                        });
                      }
                      removeStreetCell(index);
                    }}
                  >
                    <DeleteIcon />
                  </Button>
                </FormControl>
              )}
            </HStack>
          );
        })}
      </Stack>
      {/* Server */}
      <Stack
        spacing="6"
        direction={{
          base: 'row',
          md: 'column',
        }}
        verticalAlign="bottom"
        borderRadius="4"
        boxShadow="lg"
        border={borderColor}
        p="8"
        marginTop="4"
        marginBottom="12"
      >
        <HStack justifyContent="space-between">
          <Text fontSize="lg" fontWeight="bold">
            Server
          </Text>
          {isEdit ? null : (
            <Button colorScheme="teal" variant="outline" onClick={() => prependServer(defaultNode)}>
              Add Server
            </Button>
          )}
        </HStack>
        {serverFields.map((item, index) => {
          return (
            <HStack key={item.id} alignItems="baseline">
              {/* Serial number */}
              <FormControl isInvalid={!!errors?.serverNodes?.[index]?.node_serial_no} mb="4" isRequired mr="4">
                <FormLabel>Serial Number</FormLabel>
                <Controller
                  name={`serverNodes.${index}.node_serial_no`}
                  defaultValue={item.node_serial_no}
                  control={control}
                  render={({ field }) => (
                    <Select
                      {...field}
                      placeholder="Select a serial no"
                      disabled={isEdit && watch(`serverNodes.${index}.node_id`) !== ''}
                      onChange={(e) => {
                        if (isEdit) {
                          updateNodeMutation({
                            node_id: getValues(`serverNodes.${index}.node_id`) ?? '',
                            node: { node_serial_no: e.target.value },
                          });
                          field.onChange(e);
                        } else {
                          setValue(
                            `serverNodes.${index}`,
                            getSelectedNode(e.target.value, availableServerList) ?? defaultNode
                          );
                          field.onChange(e);
                        }
                      }}
                    >
                      {availableServerList?.map((node) => (
                        <option key={node?.node_serial_no} value={node?.node_serial_no}>
                          {node?.node_serial_no}
                        </option>
                      ))}
                    </Select>
                  )}
                />
                <FormErrorMessage>{errors?.serverNodes?.[index]?.node_serial_no?.message}</FormErrorMessage>
              </FormControl>
              {/* Node Type*/}
              <FormControl isInvalid={!!errors?.serverNodes?.[index]?.node_type} mb="4" isRequired mr="4">
                <FormLabel>Node Type</FormLabel>
                <Controller
                  name={`serverNodes.${index}.node_type`}
                  control={control}
                  render={({ field }) => (
                    <Text {...register(`serverNodes.${index}.node_type`)} fontSize="medium" fontWeight="medium">
                      <Badge paddingY="1" paddingX="4">
                        {getValues(`serverNodes.${index}.node_type`)}
                      </Badge>
                    </Text>
                  )}
                />
                <FormErrorMessage>{errors?.node_serial_no?.message}</FormErrorMessage>
              </FormControl>
              {/* Role */}
              <FormControl isInvalid={!!errors?.serverNodes?.[index]?.roles} mb="4" isRequired mr="4">
                <FormLabel>Role</FormLabel>
                <Controller
                  name={`serverNodes.${index}.roles`}
                  control={control}
                  defaultValue={item.roles}
                  render={({ field }) => (
                    <MultiSelect
                      isMulti
                      options={ROLE_OF_NODE_ARRAY}
                      defaultValue={ROLE_OF_NODE_ARRAY.filter((option) =>
                        item.roles.includes(option.value as ROLE_OF_NODE)
                      )}
                      theme={(theme) =>
                        lightOrDarkTheme === 'light' ? rolesCustomLightTheme(theme) : rolesCustomDarkTheme(theme)
                      }
                      styles={lightOrDarkTheme === 'light' ? rolesMultiSelectLightStyles : rolesMultiSelectDarkStyles}
                      name={`serverNodes.${index}.roles`}
                      className="roles-multi-select"
                      classNamePrefix="select"
                      getOptionLabel={(option) => option.label}
                      getOptionValue={(option) => option.value}
                      onChange={(selectedOptions) => {
                        const updatedRoles = selectedOptions.map((option) => option.value);
                        const validRoles = updatedRoles.filter((role) =>
                          Object.values(ROLE_OF_NODE).includes(role as ROLE_OF_NODE)
                        ) as ROLE_OF_NODE[];
                        setValue(`serverNodes.${index}.roles`, validRoles);
                        // const updatedRoles = e.target.value
                        //   ? [...field.value, key]
                        //   : field.value.filter((role) => role !== key);
                        // field.onChange(updatedRoles);
                        if (isEdit) {
                          updateNodeMutation({
                            node_id: getValues(`serverNodes.${index}.node_id`) ?? '',
                            node: { roles: updatedRoles },
                          });
                        }
                      }}
                    />
                  )}
                />
                <FormErrorMessage>{errors?.serverNodes?.[index]?.roles?.message}</FormErrorMessage>
              </FormControl>
              {/* Lifecycle */}
              <FormControl isInvalid={!!errors?.serverNodes?.[index]?.lifecycle} mb="4" isRequired mr="4">
                <FormLabel>Lifecycle</FormLabel>
                <Controller
                  name={`serverNodes.${index}.lifecycle`}
                  control={control}
                  defaultValue={item.lifecycle}
                  render={({ field }) => (
                    <Select
                      {...field}
                      placeholder="Select a lifecycle"
                      onChange={(e) => {
                        if (isEdit) {
                          field.onChange(e);
                          updateNodeMutation({
                            node_id: getValues(`serverNodes.${index}.node_id`) ?? '',
                            node: { lifecycle: e.target.value as LIFE_CYCLE },
                          });
                        } else {
                          field.onChange(e);
                        }
                      }}
                    >
                      {Object.entries(LifeCycle).map(([key, value]) => (
                        <option
                          key={value}
                          value={key}
                          disabled={
                            !isEdit ? value > LifeCycle[getValues('lifecycle') as keyof typeof LifeCycle] : false
                          }
                          style={{
                            color:
                              !isEdit && value > LifeCycle[getValues('lifecycle') as keyof typeof LifeCycle]
                                ? '#85828280'
                                : '',
                          }}
                        >
                          {key}
                        </option>
                      ))}
                    </Select>
                  )}
                />
                <FormErrorMessage>{errors?.serverNodes?.[index]?.lifecycle?.message}</FormErrorMessage>
              </FormControl>
              {/* Site */}
              <FormControl
                isInvalid={!!errors?.serverNodes?.[index]?.site}
                mb="4"
                isDisabled={!watch('region')}
                isRequired
              >
                <FormLabel htmlFor={`${index}_site`}>Site</FormLabel>
                <Controller
                  name={`serverNodes.${index}.site`}
                  control={control}
                  defaultValue={item.site}
                  render={({ field }) => (
                    <Select
                      {...field}
                      placeholder="Select a site"
                      onChange={(e) => {
                        if (isEdit) {
                          field.onChange(e);
                          updateNodeMutation({
                            node_id: getValues(`serverNodes.${index}.node_id`) ?? '',
                            node: { site_id: Number(e.target.value) },
                          });
                        } else {
                          field.onChange(e);
                        }
                      }}
                    >
                      {regionSites[watch('region')]?.map(({ name, id }) => (
                        <option key={`${id}-site`} value={id}>
                          {name}
                        </option>
                      ))}
                    </Select>
                  )}
                />
                <FormErrorMessage>
                  {watch('region') ? errors?.serverNodes?.[index]?.site?.message : 'Select a region first'}
                </FormErrorMessage>
              </FormControl>
              {/* Delete button */}
              {isEdit ? null : (
                <FormControl width="25%">
                  <FormLabel visibility="hidden">Del</FormLabel>
                  <Button
                    colorScheme="teal"
                    variant="outline"
                    onClick={() => {
                      if (isEdit && getValues(`serverNodes.${index}.node_id`) !== '') {
                        deleteNodeMutation({
                          node_id: getValues(`serverNodes.${index}.node_id`) ?? '',
                          cell_ref: state?.cell_ref ?? '',
                        });
                      }
                      removeServer(index);
                    }}
                  >
                    <DeleteIcon />
                  </Button>
                </FormControl>
              )}
            </HStack>
          );
        })}
      </Stack>
      {/* Switch */}
      <Stack
        spacing="6"
        direction={{
          base: 'row',
          md: 'column',
        }}
        verticalAlign="bottom"
        borderRadius="4"
        boxShadow="lg"
        border={borderColor}
        p="8"
        marginTop="4 "
        marginBottom="12"
      >
        <HStack justifyContent="space-between">
          <Text fontSize="lg" fontWeight="bold">
            Switch
          </Text>
          {isEdit ? null : (
            <Button colorScheme="teal" variant="outline" onClick={() => prependSwitch(defaultNode)}>
              Add Switch
            </Button>
          )}
        </HStack>
        {switchFields.map((item, index) => {
          return (
            <HStack key={item.id} alignItems="baseline">
              {/* Serial number */}
              <FormControl isInvalid={!!errors?.switchNodes?.[index]?.node_serial_no} mb="4" isRequired mr="4">
                <FormLabel>Serial Number</FormLabel>
                <Controller
                  name={`switchNodes.${index}.node_serial_no`}
                  defaultValue={item.node_serial_no}
                  control={control}
                  render={({ field }) => (
                    <Select
                      {...field}
                      placeholder="Select a serial no"
                      disabled={isEdit && watch(`switchNodes.${index}.node_id`) !== ''}
                      onChange={(e) => {
                        if (isEdit) {
                          updateNodeMutation({
                            node_id: getValues(`switchNodes.${index}.node_id`) ?? '',
                            node: { node_serial_no: e.target.value },
                          });
                          field.onChange(e);
                        } else {
                          setValue(
                            `switchNodes.${index}`,
                            getSelectedNode(e.target.value, availableSwitchList) ?? defaultNode
                          );
                          field.onChange(e);
                        }
                      }}
                    >
                      {availableSwitchList?.map((node) => (
                        <option key={node?.node_serial_no} value={node?.node_serial_no}>
                          {node?.node_serial_no}
                        </option>
                      ))}
                    </Select>
                  )}
                />
                <FormErrorMessage>{errors?.switchNodes?.[index]?.node_serial_no?.message}</FormErrorMessage>
              </FormControl>
              {/* Lifecycle */}
              <FormControl isInvalid={!!errors?.switchNodes?.[index]?.lifecycle} mb="4" isRequired mr="4">
                <FormLabel>Lifecycle</FormLabel>
                <Controller
                  name={`switchNodes.${index}.lifecycle`}
                  control={control}
                  defaultValue={item.lifecycle}
                  render={({ field }) => (
                    <Select
                      {...field}
                      placeholder="Select a lifecycle"
                      onChange={(e) => {
                        if (isEdit) {
                          field.onChange(e);
                          updateNodeMutation({
                            node_id: getValues(`switchNodes.${index}.node_id`) ?? '',
                            node: { lifecycle: e.target.value as LIFE_CYCLE },
                          });
                        } else {
                          field.onChange(e);
                        }
                      }}
                    >
                      {Object.entries(LifeCycle).map(([key, value]) => (
                        <option
                          key={value}
                          value={key}
                          disabled={
                            !isEdit ? value > LifeCycle[getValues('lifecycle') as keyof typeof LifeCycle] : false
                          }
                          style={{
                            color:
                              !isEdit && value > LifeCycle[getValues('lifecycle') as keyof typeof LifeCycle]
                                ? '#85828280'
                                : '',
                          }}
                        >
                          {key}
                        </option>
                      ))}
                    </Select>
                  )}
                />
                <FormErrorMessage>{errors?.switchNodes?.[index]?.lifecycle?.message}</FormErrorMessage>
              </FormControl>
              {/* Site */}
              <FormControl
                isInvalid={!!errors?.switchNodes?.[index]?.site}
                mb="4"
                //isDisabled={!watch('region')}
                isRequired
              >
                <FormLabel htmlFor={`${index}_site`}>Site</FormLabel>
                <Controller
                  name={`switchNodes.${index}.site`}
                  control={control}
                  defaultValue={item.site}
                  render={({ field }) => (
                    <Select
                      {...field}
                      placeholder="Select a site"
                      onChange={(e) => {
                        if (isEdit) {
                          field.onChange(e);
                          updateNodeMutation({
                            node_id: getValues(`switchNodes.${index}.node_id`) ?? '',
                            node: { site_id: Number(e.target.value) },
                          });
                        } else {
                          field.onChange(e);
                        }
                      }}
                    >
                      {regionSites[watch('region')]?.map(({ name, id }) => (
                        <option key={`${id}-site`} value={id}>
                          {name}
                        </option>
                      ))}
                    </Select>
                  )}
                />
                <FormErrorMessage>
                  {watch('region') ? errors?.switchNodes?.[index]?.site?.message : 'Select a region first'}
                </FormErrorMessage>
              </FormControl>
              {/* Delete button */}
              {isEdit ? null : (
                <FormControl width="25%">
                  <FormLabel visibility="hidden">Del</FormLabel>
                  <Button
                    colorScheme="teal"
                    variant="outline"
                    onClick={() => {
                      if (isEdit && getValues(`switchNodes.${index}.node_id`) !== '') {
                        deleteNodeMutation({
                          node_id: getValues(`switchNodes.${index}.node_id`) ?? '',
                          cell_ref: state?.cell_ref ?? '',
                        });
                      }
                      removeSwitch(index);
                    }}
                  >
                    <DeleteIcon />
                  </Button>
                </FormControl>
              )}
            </HStack>
          );
        })}
      </Stack>
    </Box>
  );
};

export default Split6Form;
