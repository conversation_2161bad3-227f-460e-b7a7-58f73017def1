import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { render, screen } from '@testing-library/react';
import { ReactNode } from 'react';
import { MemoryRouter } from 'react-router-dom';
import { describe, it } from 'vitest';
import CreateCellPage from '../CreateCellPage';

type ChildrenProps = {
  children: ReactNode;
};

// Wrapper for react query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});
const wrapper = ({ children }: ChildrenProps) => (
  <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
);

// Debugging
// screen.debug();
// //This one in the function and before the issue
// screen.logTestingPlaygroundURL();

// Add snapshots when the specs for the views are fully agreed
describe('Cell Creation', () => {
  it('Render the  loading state', async () => {
    render(
      <MemoryRouter>
        <CreateCellPage />
      </MemoryRouter>,
      { wrapper }
    );
    screen.getByText('Loading...');
  });

  it('Load the form', async () => {
    render(
      <MemoryRouter>
        <CreateCellPage />
      </MemoryRouter>,
      { wrapper }
    );

    // Wait for a key element to appear that signifies the page has loaded
    await screen.findByText(/create cell/i);

    // Grouped expectations for comboboxes
    const comboboxNames = [/region/i, /site/i, /cell type/i, /lifecycle/i];
    for (const name of comboboxNames) {
      expect(await screen.findByRole('combobox', { name })).toBeInTheDocument();
    }

    // Expectations for buttons
    expect(await screen.findByRole('button', { name: /reset form/i })).toBeInTheDocument();
    expect(await screen.findByRole('button', { name: /back/i })).toBeInTheDocument();
    expect(await screen.findByRole('button', { name: /create/i })).toBeInTheDocument();

    // Other expectations
    expect(screen.getByText(/create another cell\?/i)).toBeInTheDocument();
  });

  //   it('Create a GNodeB cell', async () => {
  //     render(
  //       <MemoryRouter>
  //         <CreateCellPage />
  //       </MemoryRouter>,
  //       { wrapper }
  //     );

  //     await screen.findByText(/create cell/i);
  //     const region = screen.getByRole('combobox', {
  //       name: /region/i,
  //     });
  //     screen.logTestingPlaygroundURL();
  //     userEvent.selectOptions(region, 'Marlow United Kingdom');
  //   });
});
