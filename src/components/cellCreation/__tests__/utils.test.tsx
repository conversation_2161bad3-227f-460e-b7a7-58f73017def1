import { expect } from 'vitest';
import { Region, Site } from '../../../types/InventoryManager.type';
import { constructRegionSites } from '../utils';
type RegionSites = (
  regionsData: Region[],
  regionSitesData: Site[]
) => {
  regions: {
    label: string;
    value: string;
  }[];
  regionSites: Record<string, { name: string; id: number }[]>;
};
describe('constructRegionSites', () => {
  it('should construct the region sites correctly', () => {
    const regionsData = [
      {
        region_id: 1,
        region_code: 'MARL',
        region_name: 'Marlow',
        country_code: 'GBR',
        country_name: 'United Kingdom',
        description: 'The Marlow office lab',
        creation_date: '2023-06-06',
      },
      {
        region_id: 3,
        region_code: 'MLBK',
        region_name: 'Millbrook',
        country_code: 'GBR',
        country_name: 'United Kingdom',
        description: 'Millbrook Proving Grounds',
        creation_date: '2023-06-06',
      },
    ];

    const regionSitesData = [
      {
        site_id: 2,
        name: 'Test',
        address: 'Test marlow',
        region_id: 3,
        region_code: 'MLBK',
        country_code: 'GBR',
        description: 'marlow',
        additional_info: 'marlow',
        latitude: 79.22,
        longitude: -2.5,
        site_cells: [],
        site_nodes: [],
      },
      {
        site_id: 3,
        name: 'Marlow Lab 1',
        address: 'Atlas House, Globe Business Park, Parkway, Third Ave, Marlow SL7 1EY',
        region_id: 1,
        region_code: 'MARL',
        country_code: 'GBR',
        description: 'Main Marlow Dev Lab',
        additional_info: '',
        latitude: 51.57365374524476,
        longitude: -0.760690388181796,
        site_cells: [],
        site_nodes: [],
      },
      {
        site_id: 4,
        name: 'new site',
        address: 'new site',
        region_id: 3,
        region_code: 'MLBK',
        country_code: 'GBR',
        description: 'new site',
        additional_info: 'new site',
        latitude: 22,
        longitude: 2,
        site_cells: [],
        site_nodes: [],
      },
    ];

    const expectedOutput = {
      regions: [
        {
          label: 'Marlow United Kingdom',
          value: 'MARL',
        },
        {
          label: 'Millbrook United Kingdom',
          value: 'MLBK',
        },
      ],
      regionSites: {
        MARL: [
          {
            name: 'Marlow Lab 1',
            id: 3,
          },
        ],
        MLBK: [
          {
            name: 'Test',
            id: 2,
          },
          {
            name: 'new site',
            id: 4,
          },
        ],
      },
    };

    const result = constructRegionSites(regionsData as Region[], regionSitesData as Site[]);
    expect(result).toEqual(expectedOutput);
  });
});
