import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import NodeForm from '../createNode/NodeForm';

const queryClient = new QueryClient();

const mockRegions = [
  { value: 'region1', label: 'Region 1' },
  { value: 'region2', label: 'Region 2' },
];

const mockRegionSites = {
  region1: [{ name: 'Site 1', id: 1 }],
  region2: [{ name: 'Site 2', id: 2 }],
};

const mockServerNodes: any = [];
const mockSwitchNodes: any = [];
const mockRadioNodes: any = [];

describe('NodeForm Component', () => {
  const setup = () =>
    render(
      <QueryClientProvider client={queryClient}>
        <BrowserRouter>
          <NodeForm
            availableServerList={mockServerNodes}
            availableNetworkList={mockSwitchNodes}
            availableRadioList={mockRadioNodes}
            regions={mockRegions}
            regionSites={mockRegionSites}
          />
        </BrowserRouter>
      </QueryClientProvider>
    );

  test('renders the NodeForm component', () => {
    setup();
    expect(screen.getByText('Reset form')).toBeInTheDocument();
    expect(screen.getByText('Create node')).toBeInTheDocument();
  });

  test('renders region and site fields', () => {
    setup();
    expect(screen.getByText('Region')).toBeInTheDocument();
    expect(screen.getByText('Site')).toBeInTheDocument();
  });

  test('renders mainfest field', () => {
    setup();
    expect(screen.getByText('Manifest Type')).toBeInTheDocument();
  });

  test('renders navigation button on the form', () => {
    setup();
    expect(screen.getByText('Back')).toBeInTheDocument();
    expect(screen.getByText('Create another node?')).toBeInTheDocument();
  });
});
