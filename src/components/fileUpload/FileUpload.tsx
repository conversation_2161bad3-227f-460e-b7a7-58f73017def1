import React, { useEffect, useRef, useState } from 'react';
import { Button, Input, Box, Flex } from '@chakra-ui/react';
import { Path, UseFormReturn } from 'react-hook-form';
import { FiUpload, FiDownload } from 'react-icons/fi';
import { FileUploadTypes } from '../../data/constants';
import { DeploymentParams, InterfaceFieldData } from '../../types/duCuManager.type';
import { fieldMappings } from '../../pages/OranDuCuManager/hooks/interface/useTransformInterfaceTableData';
import { ca } from 'date-fns/locale';

type GroupKeys = keyof typeof fieldMappings;

export type DeploymentFormData = {
  deployment_name: string;
  cucp_site_name: string;
  cuup_site_name: string;
  du_site_name: string;
  du_cluster: number | null;
  cucp_cluster: number | null;
  cuup_cluster: number | null;
  f1_ip: string;
  e1_ip: string;
  ru_vendor: string | null;
  deployment_params: DeploymentParams;
};

type FileUploadProps = {
  formMethods: UseFormReturn<InterfaceFieldData> | UseFormReturn<DeploymentFormData>;
  caller?: string;
  type: FileUploadTypes.import | FileUploadTypes.export | FileUploadTypes.upload | FileUploadTypes.download;
  text: string;
  setEditing?: React.Dispatch<React.SetStateAction<Record<string, boolean>>>;
  editing?: Record<string, boolean>;
  setEditedUserSettings?: any;
  onBandSelection?: (selectedBand: string, selectedBandwidth?: string) => void;
};

function isInterfaceFieldData(
  formMethods: UseFormReturn<InterfaceFieldData> | UseFormReturn<DeploymentFormData>
): formMethods is UseFormReturn<InterfaceFieldData> {
  return 'someSpecificInterfaceField' in formMethods.getValues();
}

const FileUpload: React.FC<FileUploadProps> = ({
  formMethods,
  caller,
  type,
  text,
  setEditing,
  editing,
  setEditedUserSettings,
  onBandSelection,
}) => {
  const [fileName, setFileName] = useState('FileName.json');
  const inputRef = useRef<HTMLInputElement | null>(null);

  const handleButtonClick = () => {
    if (type === FileUploadTypes.import || type === FileUploadTypes.upload) {
      inputRef.current?.click();
    }
  };

  const handleFileChange = (
    formMethods: UseFormReturn<InterfaceFieldData> | UseFormReturn<DeploymentFormData>,
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        try {
          const data = JSON.parse(event.target?.result as string);

          const cleanedSettings: Record<string, any> = {};

          Object.keys(data).forEach((key) => {
            if (key === 'deployment_name') return;

            const value = data[key];
            cleanedSettings[key] = value;

            if (isInterfaceFieldData(formMethods)) {
              formMethods.setValue(key as Path<InterfaceFieldData>, value, {
                shouldValidate: true,
                shouldDirty: true,
                shouldTouch: true,
              });
            } else {
              formMethods.setValue(key as Path<DeploymentFormData>, value, {
                shouldValidate: true,
                shouldDirty: true,
                shouldTouch: true,
              });
            }
          });

          const formattedSettings: Record<string, any> = {};

          Object.keys(cleanedSettings).forEach((key) => {
            if (key === 'deployment_name') return;
            formattedSettings[key] = {
              user_setting: cleanedSettings[key],
              default: cleanedSettings[key],
            };

            if (setEditing) {
              setEditing((prev) => ({ ...prev, [key]: true }));
            }
          });

          if (setEditedUserSettings) {
            setEditedUserSettings(formattedSettings);
          }

          formMethods.trigger();
        } catch (error) {
          console.error('Error importing form data:', error);
        }
      };
      reader.readAsText(file);
    }
  };

  const renderIcon = () => {
    switch (type) {
      case FileUploadTypes.upload:
      case FileUploadTypes.import:
        return <FiUpload />;
      case FileUploadTypes.export:
      case FileUploadTypes.download:
        return <FiDownload />;
      default:
        return <></>;
    }
  };

  const handleExport = () => {
    try {
      const data = formMethods.getValues() as Record<string, any>;
      const exportData: Record<string, any> = {};

      if (!exportData.deployment_name) {
        exportData.deployment_name = data.deployment_name || 'default_deployment_name';
      }

      let dataStr: string;

      if (fieldMappings && (caller === 'simpleViewIdentities' || caller === 'simpleViewInterface')) {
        Object.keys(fieldMappings).forEach((group) => {
          Object.keys(fieldMappings[group as GroupKeys]).forEach((index) => {
            const key = `${group}-user_setting-${index}`;
            if (data[key] !== undefined) {
              exportData[key] = data[key];
            }
          });
        });
        dataStr = JSON.stringify(exportData, null, 2);
      } else {
        dataStr = JSON.stringify(data, null, 2);
      }

      const blob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName?.endsWith('.json') ? fileName : `${fileName || 'formData'}.json`;
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (error) {
      console.error('Error exporting data:', error);
      alert('An error occurred while exporting the data. Please try again.');
    }
  };

  useEffect(() => {
    const subscription = formMethods.watch((value, { name, type }) => {
      if ((type as string) === 'submit' || (type as string) === 'import') {
        formMethods.trigger();
      }
    }) as unknown as { unsubscribe: () => void };

    return () => {
      if (subscription && typeof subscription.unsubscribe === 'function') {
        subscription.unsubscribe();
      }
    };
  }, [formMethods]);

  if (type === FileUploadTypes.import || type === FileUploadTypes.upload) {
    return (
      <Box>
        <Input
          type="file"
          ref={inputRef}
          display="none"
          accept="application/json"
          onChange={(e) => handleFileChange(formMethods, e)}
        />
        <Button leftIcon={renderIcon()} variant="outline" colorScheme="teal" onClick={handleButtonClick}>
          {text}
        </Button>
      </Box>
    );
  }

  return (
    <Box>
      <Flex mb={2}>
        <Input
          placeholder={`Enter ${type.toLowerCase()} name`}
          value={fileName}
          onChange={(e) => setFileName(e.target.value)}
          mr={2}
        />
        <Button leftIcon={renderIcon()} variant="outline" colorScheme="teal" onClick={handleExport} width="100%">
          {text}
        </Button>
      </Flex>
    </Box>
  );
};

export default FileUpload;
