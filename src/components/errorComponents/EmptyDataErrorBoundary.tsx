import { Box, Code, Text } from '@chakra-ui/react';
import React, { ReactNode } from 'react';

type EmptyDataErrorBoundaryProps = {
  data: unknown;
  borderRadius?: string | number;
  boxShadow?: string;
  p?: string | number;
  marginTop?: string | number;
  marginBottom?: string | number;
  message?: any;
  children?: ReactNode;
};

// Utility function to check if data is empty
const isDataEmpty = (data: unknown): boolean => {
  if (Array.isArray(data)) {
    return data.length === 0 || data.every(isDataEmpty);
  } else if (data && typeof data === 'object') {
    return Object.keys(data).length === 0 || Object.values(data).every(isDataEmpty);
  }
  return !data;
};

const EmptyDataErrorBoundary: React.FC<EmptyDataErrorBoundaryProps> = ({
  data,
  borderRadius = '4',
  boxShadow = 'lg',
  p = '8',
  marginTop = '4',
  marginBottom = '12',
  message = 'The provided data is empty or invalid.',
  children,
}) => {
  if (isDataEmpty(data)) {
    const messageParts = message?.message.split('\n') || [message?.message];
    return (
      <Box
        role="alert"
        borderRadius={borderRadius}
        boxShadow={boxShadow}
        p={p}
        marginBottom={marginBottom}
        marginTop={marginTop}
      >
        <Text>Something went wrong:</Text>
        {messageParts.map((part: string, index: number) => (
          <Box key={index as number} mb="2">
            {index === 0 ? (
              <Code color="red" display="block">
                {part.trim()}
              </Code>
            ) : (
              <Text as="pre" whiteSpace="pre-wrap" fontSize="sm" color="red.600">
                {part.trim()}
              </Text>
            )}
          </Box>
        ))}
      </Box>
    );
  }

  return <>{children}</>;
};

export default EmptyDataErrorBoundary;
