import { Box, Heading, Stack, Text, useColorModeValue } from '@chakra-ui/react';

type errorProps = {
  errorObj: any;
  message: string;
};
const AxiosErrorUi = ({ errorObj, message }: errorProps) => {
  const colorModeValue = useColorModeValue('sm', 'sm-dark');

  return (
    <Stack
      spacing={{
        base: '5',
        lg: '6',
      }}
    >
      <Box
        bg="bg-surface"
        boxShadow={{
          base: 'none',
          md: colorModeValue,
        }}
        p="21"
      >
        <Heading textAlign="center" as="h2" mt="4" mb="8">
          {`${errorObj?.response?.status} - ${errorObj?.response?.statusText}`}
        </Heading>
        <Text textAlign="center">{`${message}`}</Text>
      </Box>
    </Stack>
  );
};

export default AxiosErrorUi;
