import { ExternalLinkIcon } from '@chakra-ui/icons';
import {
  <PERSON>ge,
  Card,
  CardBody,
  CardHeader,
  Flex,
  Grid,
  GridItem,
  Heading,
  Icon,
  Link,
  Stack,
  StackDivider,
  Text,
} from '@chakra-ui/react';
import { <PERSON>als<PERSON> } from 'lodash';
import { useState } from 'react';
import { getStatusColor } from '../../pages/CellOverview/hooks/useStatus';
import StatusComponent, { StaticStatusCircleIcon } from '../icons/StatusIcon';
import { BsArrowReturnRight } from 'react-icons/bs';
import { getComponentBgColor } from '../nodeComponents/utils';
import { StatusToColor } from '../../data/constants';

type NodeComponentErrorCardProps = {
  id?: string | number | undefined;
  errorData: any;
  compName: string;
  testId: string;
  linkText?: string;
  linkUrl?: string | Falsey;
  marginSpace?: string;
  CardIcon?: any;
};

const NodeComponentErrorCard = ({
  id,
  errorData,
  compName,
  testId,
  linkText,
  linkUrl,
  marginSpace,
  CardIcon,
}: NodeComponentErrorCardProps) => {
  const [showContent, setShowContent] = useState(false);

  const boxShadowColor = 'rgba(255, 0, 0, 0.8)';
  const statusColor = getStatusColor(errorData?.status);
  const bgColor = getComponentBgColor(statusColor);

  return (
    <Card id={id?.toString()} width="100%" data-testid={testId} border={'0.1em solid red'} borderRadius="lg">
      <CardHeader
        _hover={{
          bg: bgColor,
          cursor: 'pointer',
          boxShadow: `0 0 12px ${boxShadowColor}`,
        }}
        transition="background-color 0.2s ease, box-shadow 0.2s ease"
        borderRadius="lg"
        onClick={() => setShowContent(!showContent)}
        display="flex"
        alignItems="center"
        justifyContent="space-between"
      >
        <Heading size="xs" fontWeight="500" display="flex" alignItems="center" justifyContent="space-between">
          <Flex alignItems="center">
            <Icon as={BsArrowReturnRight} color="red.600" />
            {CardIcon && <CardIcon width="30px" height="30px" color="red" />}
            <Text ml={2} color="red.500">
              {' '}
              {compName}
            </Text>
          </Flex>
        </Heading>
        <StaticStatusCircleIcon size={40} color={StatusToColor.UNKNOWN} />
      </CardHeader>

      {showContent && (
        <CardBody borderTop="1px solid #e2e2e2">
          <>
            {/* Show status code */}
            {errorData?.error?.status_code && (
              <Grid templateColumns="repeat(4, 6fr)" gap={4} mb={4}>
                <GridItem colSpan={1}>Error Code :</GridItem>
                <GridItem colSpan={3}>
                  <Badge whiteSpace="normal">{errorData?.error?.status_code}</Badge>
                </GridItem>
              </Grid>
            )}
            {/* Type 1  error message */}
            {errorData ? (
              <Grid templateColumns="repeat(4, 6fr)" gap={4} mb={4}>
                <GridItem colSpan={1}>Reason :</GridItem>
                <GridItem colSpan={3}>
                  <Badge whiteSpace="normal" color="hsl(1, 100%, 50%)">
                    {errorData?.error?.message?.detail ||
                      errorData?.message?.detail ||
                      errorData?.message?.message ||
                      errorData?.error?.message?.error ||
                      errorData?.error?.message?.message ||
                      errorData?.pod?.error?.message?.detail?.message}
                  </Badge>
                </GridItem>
              </Grid>
            ) : (
              <Grid templateColumns="repeat(4, 6fr)" gap={4} mb={4}>
                <GridItem colSpan={1}>Error :</GridItem>
                <GridItem colSpan={3}>
                  <Badge whiteSpace="normal" color="hsl(1, 100%, 50%)">
                    null
                  </Badge>
                </GridItem>
              </Grid>
            )}

            {/* NOTE: does this work? */}
            {errorData?.error?.url && (
              <Grid templateColumns="repeat(4, 6fr)" gap={4} mb={4}>
                <GridItem colSpan={1}>URL :</GridItem>
                <GridItem colSpan={3}>
                  <Badge whiteSpace="normal">{errorData?.error?.url}</Badge>
                </GridItem>
              </Grid>
            )}
            {errorData?.pod?.error?.url && (
              <Grid templateColumns="repeat(4, 6fr)" gap={4} mb={4}>
                <GridItem colSpan={1}>URL :</GridItem>
                <GridItem colSpan={3}>
                  <Badge whiteSpace="normal">{errorData?.pod?.error?.url}</Badge>
                </GridItem>
              </Grid>
            )}
            {/* Show error data url */}
            <Stack divider={<StackDivider />} spacing="4">
              <StackDivider />
              <Link
                textAlign="center"
                isExternal
                rel="noopener noreferrer"
                href={
                  !errorData?.error && errorData?.url
                    ? `${linkUrl}/Management/DBId=${errorData.url}`
                    : `${linkUrl}/NodeList`
                }
              >
                {linkText}
                <ExternalLinkIcon mx="2px" />
              </Link>
            </Stack>
          </>
        </CardBody>
      )}
    </Card>
  );
};

export default NodeComponentErrorCard;
