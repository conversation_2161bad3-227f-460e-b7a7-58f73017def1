// ErrorPage.tsx
import { Button, Flex, Heading, Text } from '@chakra-ui/react';
import React from 'react';
import { useNavigate } from 'react-router-dom';

interface ErrorPageProps {
  message?: string;
  showHomeButton?: boolean;
}

const ErrorPage: React.FC<ErrorPageProps> = ({ message = 'Sorry, something went wrong.', showHomeButton = true }) => {
  const navigate = useNavigate();

  const handleGoHome = () => {
    navigate('/');
  };

  return (
    <Flex direction="column" alignItems="center" justifyContent="center" textAlign="center" my="21">
      <Heading mt="8">Error</Heading>
      <Text mt="8">{message}</Text>
      {showHomeButton && (
        <Button mt="8" p="8" onClick={handleGoHome} cursor="pointer">
          Go to Home Page
        </Button>
      )}
    </Flex>
  );
};

export default ErrorPage;
