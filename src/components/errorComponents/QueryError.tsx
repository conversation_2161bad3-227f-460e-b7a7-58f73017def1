import AxiosErrorUi from './AxiosErrorUi';

type errorProps = {
  message?: string;
  name?: string;
};

type QueryErrorProps = {
  error?: errorProps;
};

const QueryError: React.FC<QueryErrorProps> = ({ error }) => {
  return (
    <>
      {error && error.message ? (
        <AxiosErrorUi errorObj={error} message={error.message} />
      ) : (
        <div>There has been an error</div>
      )}
    </>
  );
};

export default QueryError;
