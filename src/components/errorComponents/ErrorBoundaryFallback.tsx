import { Box, Text } from '@chakra-ui/react';

export const ErrorBoundaryFallback = ({ error, resetErrorBoundary }: any) => {
  console.error('🚀 ~ file: ErrorBoundaryFallback.tsx:2 ~ ErrorBoundaryFallback ~ error:', error && error);
  return (
    <Box role="alert">
      <Text>Something went wrong:</Text>
      <Text style={{ color: 'red' }}>{error?.message}</Text>
      {/* <button onClick={resetErrorBoundary}>Try Again</button> */}
    </Box>
  );
};

export const ErrorBoundaryLogError = (error: Error, info: { componentStack: string }) => {
  console.error('Error occurred:', error && error);
  console.error('Error info:', info && info);
};
