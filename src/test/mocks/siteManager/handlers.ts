import { rest } from 'msw';

export const siteManagerHandlers = [
  //get all regions
  rest.get('http://localhost/nms/inv/ref/regions', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json([
        {
          region_id: 1,
          region_code: 'MARL',
          region_name: 'Marlow',
          country_code: 'GBR',
          country_name: 'United Kingdom',
          description: 'The Marlow office lab',
          creation_date: '2023-03-30',
        },
        {
          region_id: 2,
          region_code: 'AUCK',
          region_name: 'Auckland',
          country_code: 'NZL',
          country_name: 'New Zealand',
          description: 'Auckland 5G RAN',
          creation_date: '2023-05-14',
        },
      ])
    );
  }),
  // get region by id
  rest.get('http://localhost/nms/inv/ref/regions/2', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        region_id: 2,
        region_code: 'DEV2',
        region_name: 'Marlow Development',
        country_code: 'GBR',
        country_name: 'United Kingdom',
        description: 'Devices for nms-dev-2 in Marlow lab',
        creation_date: '2023-08-02',
      })
    );
  }),
  //get all sites
  rest.get('http://localhost/nms/inv/ref/sites', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json([
        {
          site_id: 1,
          name: 'Marlow Lab 1',
          address: 'Atlas House, Globe Business Park, Parkway, Third Ave, Marlow SL7 1EY',
          region_id: 1,
          region_code: 'MARL',
          country_code: 'GBR',
          description: 'Marlow Office Lab deployment',
          additional_info: '',
          latitude: 51.57365374524476,
          longitude: -0.760690388181796,
        },
        {
          site_id: 2,
          name: 'Marlow Lab cluster 2',
          address: 'Marlow Water Ski Club, Fieldhouse Ln, Marlow SL7 1QU',
          region_id: 1,
          region_code: 'MARL',
          country_code: 'GBR',
          description: 'Made up!',
          additional_info: '',
          latitude: 51.57135987535133,
          longitude: -0.756731447805331,
        },
        {
          site_id: 3,
          name: 'Marlow Lab cluster 3',
          address: 'Marlow Train Station, Station Approach, Marlow SL7 1NT',
          region_id: 1,
          region_code: 'MARL',
          country_code: 'GBR',
          description: 'Made up!',
          additional_info: '',
          latitude: 51.57115947440174,
          longitude: -0.766661224325391,
        },
        {
          site_id: 4,
          name: 'Marlow Lab cluster 4',
          address: 'Corner of Fourth Ave and Parkway',
          region_id: 1,
          region_code: 'MARL',
          country_code: 'GBR',
          description: 'Made up!',
          additional_info: '',
          latitude: 51.57450491552419,
          longitude: -0.757271199331985,
        },
        {
          site_id: 5,
          name: 'Marlow Lab 1',
          address: 'Atlas House, Globe Business Park, Parkway, Third Ave, Marlow SL7 1EY',
          region_id: 1,
          region_code: 'MARL',
          country_code: 'GBR',
          description: 'string',
          additional_info: '',
          latitude: 51.57365374524476,
          longitude: -0.760690388181796,
        },
        {
          site_id: 6,
          name: 'Marlow Lab 1',
          address: 'Atlas House, Globe Business Park, Parkway, Third Ave, Marlow SL7 1EY',
          region_id: 1,
          region_code: 'MARL',
          country_code: 'GBR',
          description: 'string',
          additional_info: '',
          latitude: 51.57365374524476,
          longitude: -0.760690388181796,
        },
        {
          site_id: 7,
          name: 'Auck Lab 1',
          address: 'Atlas House, Globe Business Park, Parkway, Third Ave, Marlow Auck',
          region_id: 2,
          region_code: 'AUCK',
          country_code: 'NZL',
          description: 'string',
          additional_info: 'dummy text',
          latitude: 51.57365374524476,
          longitude: -0.760690388181796,
        },
      ])
    );
  }),
  rest.get('http://localhost/nms/inv/stats/countries', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json([
        {
          country_code: 'NZL',
          country_name: 'New Zealand',
          region_count: 1,
        },
        {
          country_code: 'GBR',
          country_name: 'United Kingdom',
          region_count: 1,
        },
        {
          country_code: 'USA',
          country_name: 'United States',
          region_count: 0,
        },
      ])
    );
  }),
  rest.get('http://localhost/nms/inv/ref/regions/1', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        region_id: 1,
        region_code: 'MARL',
        region_name: 'Marlow',
        country_code: 'GBR',
        country_name: 'United Kingdom',
        description: 'The Marlow office lab',
        creation_date: '2023-03-30',
      })
    );
  }),
  rest.get('http://localhost/nms/inv/ref/sites/:site_id/contacts', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json([
        {
          contact_id: 1,
          name: 'John Smith 2',
          role: 'Facilities Manager 2',
          address: 'Atlas House, Globe Business Park, Parkway, Third Ave, Marlow SL7 1EY',
          email: '<EMAIL>',
          landline: '+441628477663',
          mobile: '+447979123123',
          additional_info: 'Keyholder (Office Hours Only)',
          is_primary: true,
        },
      ])
    );
  }),

  rest.get('http://localhost/nms/inv/manifest/nodes', (req, res, ctx) => {
    const pageParam = req.url.searchParams.getAll('page');
    const limitParam = req.url.searchParams.getAll('limit');
    let response;

    if (pageParam[0] === '1' && limitParam[0] === '200') {
      response = [
        {
          node_serial_no: 'AAAA31300103',
          lifecycle: 'STAGING',
          site_id: 3,
          country_code: 'GBR',
          country_name: 'United Kingdom',
          region_id: 1,
          region_code: 'MARL',
          region_name: 'Marlow',
          site_name: 'Marlow Lab 1',
          latitude: 51.57365374524476,
          longitude: -0.760690388181796,
          has_manifest: false,
        },
        {
          node_serial_no: 'AAAA31300203',
          lifecycle: 'COMMISSIONING',
          site_id: 6,
          country_code: 'GBR',
          country_name: 'United Kingdom',
          region_id: 1,
          region_code: 'MARL',
          region_name: 'Marlow',
          site_name: 'Marlow Lab 3',
          latitude: 79.22,
          longitude: -2.5,
          has_manifest: true,
        },
        {
          node_serial_no: 'AAAA31900403',
          lifecycle: 'FACTORY',
          site_id: 0,
          has_manifest: true,
        },
        {
          node_serial_no: 'AABA31300103',
          lifecycle: 'COMMISSIONING',
          site_id: 6,
          country_code: 'GBR',
          country_name: 'United Kingdom',
          region_id: 1,
          region_code: 'MARL',
          region_name: 'Marlow',
          site_name: 'Marlow Lab 3',
          latitude: 79.22,
          longitude: -2.5,
          has_manifest: true,
        },
        {
          node_serial_no: 'AABA31300203',
          lifecycle: 'COMMISSIONING',
          site_id: 5,
          country_code: 'GBR',
          country_name: 'United Kingdom',
          region_id: 1,
          region_code: 'MARL',
          region_name: 'Marlow',
          site_name: 'Marlow Lab 2',
          latitude: 79.22,
          longitude: -2.5,
          has_manifest: true,
        },
        {
          node_serial_no: 'AABA31700303',
          lifecycle: 'COMMISSIONING',
          site_id: 4,
          country_code: 'GBR',
          country_name: 'United Kingdom',
          region_id: 3,
          region_code: 'MLBK',
          region_name: 'Millbrook',
          site_name: 'new site',
          latitude: 22,
          longitude: 2,
          has_manifest: true,
        },
        {
          node_serial_no: 'AABA31700403',
          lifecycle: 'COMMISSIONING',
          site_id: 4,
          country_code: 'GBR',
          country_name: 'United Kingdom',
          region_id: 3,
          region_code: 'MLBK',
          region_name: 'Millbrook',
          site_name: 'new site',
          latitude: 22,
          longitude: 2,
          has_manifest: true,
        },
        {
          node_serial_no: 'AABA31700503',
          lifecycle: 'COMMISSIONING',
          site_id: 6,
          country_code: 'GBR',
          country_name: 'United Kingdom',
          region_id: 1,
          region_code: 'MARL',
          region_name: 'Marlow',
          site_name: 'Marlow Lab 3',
          latitude: 79.22,
          longitude: -2.5,
          has_manifest: true,
        },
        {
          node_serial_no: 'AABA31700603',
          lifecycle: 'FACTORY',
          site_id: 0,
          has_manifest: true,
        },
      ];
    }

    return res(ctx.status(200), ctx.json(response));
  }),

  rest.get('http://localhost/nms/inv/manifest/streetcell', (req, res, ctx) => {
    const pageParam = req.url.searchParams.getAll('page');
    const limitParam = req.url.searchParams.getAll('limit');
    const CellSerialNo = req.url.searchParams.getAll('cell_serial_no');

    let response;

    if (pageParam[0] === '1' && limitParam[0] === '20' && CellSerialNo[0] === 'AAAA31300103') {
      response = [
        {
          cell_manifest: {
            cell_serial_no: 'AAAA31300103',
            front_id: 'SUB00577-7',
            rear_id: 'SUB00582-6',
            factory_notes: null,
            test_result: null,
          },
          airspan_manifest: {
            carrier_serial: 'CCE86C01396C',
            psu_serial: 'CCF86900035B',
            secondary_psu_serial: 'CC586C99A223',
            rf1_serial: null,
            rf2_serial: 'CF186A0001CB',
            model: 'AS1900',
            mac_address: null,
            xpu_serial: 'F0487101396C',
            test_result: null,
          },
          bw_manifest: {
            kit_serial: null,
            npu_serial: '070C00 B2243 0088',
            modem_serial: null,
            rf1_serial: null,
            rf2_serial: null,
            model: null,
            hostname: 'bwt-12-29-ca',
            mac_address: '68:1f:40:12:29:ca',
            initial_config_filepath: null,
            calibration_filepath: null,
            calibration_version: null,
            test_result: 'PASS',
          },
          dal_manifest: {
            board_serial: 'A1580040',
            board_test_result: 'PASS',
            particle_device: '0a10aced202194944a022570',
            rf1_serial: 'A1573269',
            rf2_serial: 'A1573257',
            rf1_test_result: null,
            rf2_test_result: null,
          },
          fibrolan_manifest: {
            switch_serial_no: '7086PCB23020269',
            mac_address: null,
            test_result: null,
          },
        },
      ];
    }

    return res(ctx.status(200), ctx.json(response));
  }),
];
