import { rest } from 'msw';

export const metricsCollectorHandlers = [
  //get all regions
  rest.get('http://localhost/nms/mc/particle_metrics/search', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        count: 1144,
        events: [
          {
            id: 330537,
            coreid: '0a10aced202194944a0216a4',
            date: '2023-10-15T20:38:46',
            temperature: 47.5,
            bluwireless_fan_speed: 60,
            airspan_fan_speed: 0,
          },
          {
            id: 330538,
            coreid: '0a10aced202194944a0216a4',
            date: '2023-10-15T20:39:46',
            temperature: 47.4,
            bluwireless_fan_speed: 60,
            airspan_fan_speed: 0,
          },
          {
            id: 330539,
            coreid: '0a10aced202194944a0216a4',
            date: '2023-10-15T20:40:46',
            temperature: 47.5,
            bluwireless_fan_speed: 60,
            airspan_fan_speed: 0,
          },
          {
            id: 330540,
            coreid: '0a10aced202194944a0216a4',
            date: '2023-10-15T20:41:46',
            temperature: 47.5,
            bluwireless_fan_speed: 60,
            airspan_fan_speed: 0,
          },
        ],
      })
    );
  }),
];
