import { rest } from 'msw';
import { metricsCollectorHandlers } from './metricsCollector/handler';
import { siteManagerHandlers } from './siteManager/handlers';
export const handlers = [
  // Get Cell List

  rest.get('http://localhost/nms/inv/cells', async (req, res, ctx) => {
    const pageParam = req.url.searchParams.getAll('page');
    const limitParam = req.url.searchParams.getAll('limit');
    let response;

    if (pageParam[0] === '1' && limitParam[0] === '20') {
      response = [
        {
          cell_ref: 'GBMLBKNS000006',
          oran_split: 'gNodeB',
          country_code: 'GBR',
          country_name: 'United Kingdom',
          region_id: 1,
          region_code: 'MLBK',
          region_name: 'Millbrook',
          site_id: 3,
          site_name: 'CC3',
          latitude: 52.042917,
          longitude: -0.532572,
          orientation: 'SW',
          lifecycle: 'COMMISSIONING',
          status: 'ERROR',
          trouble_score: 0,
        },
        {
          cell_ref: 'GBMLBKNS000004',
          oran_split: 'gNodeB',
          country_code: 'GBR',
          country_name: 'United Kingdom',
          region_id: 1,
          region_code: 'MLBK',
          region_name: 'Millbrook',
          site_id: 4,
          site_name: 'Island B',
          latitude: 52.045037,
          longitude: -0.537845,
          orientation: 'E',
          lifecycle: 'COMMISSIONING',
          status: 'OK',
          trouble_score: 0,
        },
        {
          cell_ref: 'GBMLBKNS000003',
          oran_split: 'gNodeB',
          country_code: 'GBR',
          country_name: 'United Kingdom',
          region_id: 1,
          region_code: 'MLBK',
          region_name: 'Millbrook',
          site_id: 1,
          site_name: 'CC1',
          latitude: 52.042334,
          longitude: -0.536065,
          orientation: 'NW',
          lifecycle: 'COMMISSIONING',
          status: 'OK',
          trouble_score: 0,
        },
        {
          cell_ref: 'GBMLBKNS000005',
          oran_split: 'gNodeB',
          country_code: 'GBR',
          country_name: 'United Kingdom',
          region_id: 1,
          region_code: 'MLBK',
          region_name: 'Millbrook',
          site_id: 5,
          site_name: 'Trailer Park',
          latitude: 52.045466,
          longitude: -0.533774,
          orientation: 'S',
          lifecycle: 'COMMISSIONING',
          status: 'OK',
          trouble_score: 0,
        },
        {
          cell_ref: 'GBMLBKNS000001',
          oran_split: 'gNodeB',
          country_code: 'GBR',
          country_name: 'United Kingdom',
          region_id: 1,
          region_code: 'MLBK',
          region_name: 'Millbrook',
          site_id: 2,
          site_name: 'CC4',
          latitude: 52.044288,
          longitude: -0.535724,
          orientation: 'S',
          lifecycle: 'COMMISSIONING',
          status: 'OK',
          trouble_score: 0,
        },
        {
          cell_ref: 'GBMLBKNS000002',
          oran_split: 'gNodeB',
          country_code: 'GBR',
          country_name: 'United Kingdom',
          region_id: 1,
          region_code: 'MLBK',
          region_name: 'Millbrook',
          site_id: 2,
          site_name: 'CC4',
          latitude: 52.044288,
          longitude: -0.535724,
          orientation: 'N',
          lifecycle: 'COMMISSIONING',
          status: 'OK',
          trouble_score: 0,
        },
      ];
    }

    return res(ctx.status(200), ctx.json(response));
  }),

  // Get Cluster --Node Details
  rest.get('http://localhost/nms/inv/cells/clusters/GBMARLNS000001', async (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json([
        {
          type: 'CELL',
          id: 'GBMARLNS000001',
          lifecycle: 'STAGING',
          version: '1.1',
          oran_split: 'gNodeB',
          site_id: 3,
          site_name: 'Marlow Lab 1',
          site_address: 'Atlas House, Globe Business Park, Parkway, Third Ave, Marlow SL7 1EY',
          latitude: 51.57365374524476,
          longitude: -0.760690388181796,
          status: 'UNKNOWN',
          trouble_score: 0,
        },
      ])
    );
  }),
  // Get Cell --Node Comp By Id
  rest.get(
    // 'http://localhost/nms/orc/cell/GBMARLNS000001',
    'http://localhost/nms/orc/nodes/GBMARLNS000001',
    async (req, res, ctx) => {
      return res(
        ctx.status(200),
        ctx.json({
          sc_v1: [
            {
              id: 'GBMARLNS000001',
              type: 'Cell A',
              position: 'Root',
              airspan_node: {
                productType: 'AirSpeed 1900',
                productCode: 'AS19-F380-DSC1',
                gnbCuCp: [
                  {
                    id: 11,
                    cuCpCellList: [
                      {
                        id: 9,
                        cellIdentity: '000000171',
                        cellNumber: 1,
                        isEnabled: true,
                        localCellId: 1,
                        plmnInfoList: ['string'],
                        defaultXncAllowed: true,
                        neighbourManagementGroupId: 1,
                        neighbourList: ['string'],
                        externalNeighbourList: ['string'],
                        securityConfig: {
                          integrity: 'Optional',
                          nullIntegrityPriority: 2,
                          snow3GIntegrityPriority: 0,
                          aesIntegrityPriority: 1,
                          ciphering: 'Optional',
                          nullCipheringPriority: 2,
                          snow3GCipheringPriority: 0,
                          aesCipheringPriority: 1,
                        },
                        mobilityConfig: {
                          idleMode: {
                            commonReselection: {},
                          },
                          interRatIdleMode: {
                            epsFallback: {},
                          },
                        },
                        endpointList: [
                          {
                            index: 1,
                            isSameCloud: true,
                            localAddress: '*************',
                            localPort: 37412,
                            remoteId: 'AMF_Druid_VM',
                            remoteAddress: '*************',
                            remotePort: 38412,
                            type: 'F1-C',
                          },
                        ],
                      },
                    ],
                    endpointList: [
                      {
                        type: 'NG-C (N2)',
                        index: 1,
                        remoteId: 'Cav-Druid',
                        remoteAddress: '**************',
                        remotePort: 38412,
                      },
                    ],
                  },
                ],
                gnbCuUp: [
                  {
                    id: 42,
                    endpointList: [
                      {
                        index: 1,
                        isSameCloud: true,
                        localAddress: '*************',
                        localPort: 37412,
                        remoteId: 'AMF_Druid_VM',
                        remoteAddress: '*************',
                        remotePort: 38412,
                        type: 'F1-U',
                      },
                    ],
                  },
                ],
                gnbDu: [
                  {
                    id: 39,
                    duCellList: [
                      {
                        id: 9,
                        cellIdentity: '000000171',
                        srsCombineOffset: 0,
                        srsSequenceId: 231,
                        duCellConfiguration: {
                          advancedConfiguration: {
                            downlink256Qam: 'Disabled',
                            reservedForOperatorUse: 'Disabled',
                            pMax: '33',
                            qRxMinLevel: -70,
                            qRxMinLevelSul: '-50',
                            qMinQuality: '-30',
                            sib2Periodicity: '32',
                            sib3Periodicity: '64',
                            sib4Periodicity: '64',
                            defaultPagingCycle: '64',
                            totalPagingFrames: '1/16T',
                            pagingFrameOffset: '0',
                            pagingOccasionsPerPagingFrame: 'One',
                            t300: '600',
                            t301: '600',
                            t310: '200',
                            n310: '1',
                            t311: '1000',
                            n311: '1',
                            t319: '200',
                            powerRampingStep: '2',
                            preambleInitialReceivedTargetPower: '-90',
                            preambleMaxTransmit: '10',
                            preambleFormat: 'B4',
                            rootSequenceType: 'L139',
                            prachConfigIndex: 148,
                            zeroCorrelationZoneConfig: 12,
                            msg1ScsKhz: '30',
                            ssbsPerRach: '1',
                            contentionBasedPreamblePerSsb: 12,
                            numberRachPreambles: 30,
                            msg1Fdm: '1',
                            msg1FrequencyStart: 12,
                            responseWindowSizeSubframes: 'sl20',
                            contentionResolutionTimer: 'sf40',
                            rsrpThresholdSsb: 20,
                            restrictedSet: 'Unrestricted',
                            msg3MaxTxThreshold: 1,
                            msg3TransformPrecoder: 'Enabled',
                            maxNumUeInheritFromGnb: 'Enabled',
                            maxNumUe: 32,
                            frameStructure: '70/20/10',
                            flexibleSlotStructure: '10D4G',
                          },
                        },
                        cellNumber: 1,
                        isEnabled: true,
                        adminState: 'Locked',
                        localCellId: 1,
                        nrPci: 231,
                        nrRsi: 0,
                        nrTac: 0,
                        band: 77,
                        pdcchSubcarrierSpacingKhz: '30',
                        ssbSubcarrierSpacingKhz: '30',
                        nrArfcn: 657000,
                        bandwidthMhz: '100',
                        bwpSizeMhz: 100,
                        bwpStartRb: 0,
                        gscn: 8065,
                        ssbPeriodicityMs: '20',
                        ssbOffsetMs: 0,
                        ssbDurationMs: 4,
                        plmnInfoList: ['string'],
                        inheritPlmnInfoListFromCuCell: true,
                        cbrsEnabled: false,
                        pciManagementGroupId: 1,
                        excludeFromAutoPci: false,
                        rachManagementGroupId: 1,
                        excludeFromAutoRach: false,
                        sectorCarrierProperties: {
                          duMacAddress: 'string',
                          ruSectorCarrierId: 1,
                          maxTxPower: 10,
                          sectorCarrierBandwidthMhz: '100',
                          sectorCarrierNrArfcn: 0,
                        },
                      },
                    ],
                    endpointList: [
                      {
                        index: 1,
                        isSameCloud: true,
                        localAddress: '*************',
                        localPort: 37412,
                        remoteId: 'AMF_Druid_VM',
                        remoteAddress: '*************',
                        remotePort: 38412,
                        type: 'F1-C',
                      },
                    ],
                  },
                ],
                gnbRu: [
                  {
                    id: 40,
                    sectorCarriers: [
                      {
                        centreFrequency: 3855000,
                        duplexMode: 'TDD',
                        bands: 'n77',
                        duCell: 'DU-as1900-f0486900d99c-1 Cell 1',
                        ruSectorConfiguration: {
                          gnbRuSectorServiceManagerProperties: {
                            oRanFrontHaulIndicated: 'Enabled',
                            unitOverHeating: 'Enabled',
                            outOfSync: 'Enabled',
                          },
                        },
                        sectorCarrierId: 1,
                        nrArfcn: 657000,
                        bandwidth: '100',
                        inheritDuConfiguration: true,
                      },
                    ],
                    ruConfiguration: {
                      advancedConfiguration: {},
                    },
                  },
                ],
                nodeProperties: {
                  id: 11,
                  locationSource: 2,
                  name: 'AS1900 - Ravi Lab',
                  description: '**************',
                  regionId: 1,
                  siteId: 1,
                  latitude: 51.573909,
                  longitude: -0.759858,
                  altitude: 85,
                  isManaged: true,
                  isNbifEventAlarmForwarding: true,
                },
                gnbProperties: {
                  primaryPlmnInfo: {
                    plmn: {
                      mcc: '234',
                      mnc: '30',
                    },
                    snssaiList: [
                      {
                        sst: 1,
                        sd: true,
                        sdString: '000000',
                      },
                    ],
                  },
                },
                gnbType: 'Id32Bits',
                gnbId: 23,
                status: 'OK',
              },
              particle_board: {
                details: {
                  version: 509,
                  modeName: 'NORMAL_MODE',
                  darkMode: true,
                  caseOpen: true,
                  fanMode: 'ON',
                  overtemp: 50.0,
                  hysteresis: 45.0,
                  tempSensor1: 41.125,
                  tempSensor2: 0.0,
                  bwFanSpeed: 60,
                  asFanSpeed: 1080,
                  antennaConfiguration: 1,
                  antenna: {
                    board1: {
                      switch1: 'RF1',
                      switch2: 'RF2',
                      switch3: 'RF2',
                      switch4: 'RF1',
                      switch5: 'RF0',
                      switch6: 'RF2',
                    },
                    board2: {
                      switch1: 'RF1',
                      switch2: 'RF2',
                      switch3: 'RF2',
                      switch4: 'RF1',
                      switch5: 'RF0',
                      switch6: 'RF2',
                    },
                  },
                  networkConfiguration: {
                    DHCP: false,
                    IP: '**************',
                    SUB: '*************',
                    GW: '***************',
                    DNS: '0.0.0.0',
                  },
                  alarmCodes: [9, 10],
                },
                updated: '2023-07-24T07:56:50.916436+00:00',
                created: '2023-07-24T07:56:50.916436+00:00',
                published_at: '2023-07-24T07:56:52.616313+00:00',
                manager: 'particle_interactions',
                status: 'ERROR',
                status_reasons: ['Antenna board 1 is not connected.', 'Antenna board 2 is not connected.'],
                coreid: '0a10aced202194944a0216bc',
              },
              bw_mmwave: {
                status: 'OK',
              },
              fibrolan_switch: {
                status: 'OK',
              },
            },
          ],
          mesh_root: {
            id: 'GBR-MRLW-0001-Mesh',
            status: 'OK',
          },
          status: 'OK',
        })
      );
    }
  ),

  // Get Node By Cell Ref --
  rest.get('http://localhost/nms/inv/cells/nodes', async (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json([
        {
          node_id: 'GBMARLNS000001',
          node_type: 'StreetCell',
          cell_refs: ['GBMARLNS000001'],
          lifecycle: 'STAGING',
          version: '1.1',
          roles: ['RU', 'DU', 'CU'],
          site_id: 3,
          site_name: 'Marlow Lab 1',
          site_address: 'Atlas House, Globe Business Park, Parkway, Third Ave, Marlow SL7 1EY',
          latitude: 51.57365374524476,
          longitude: -0.760690388181796,
          orientation: 'N',
          location_error: null,
          node_location: null,
          node_serial_no: 'AAAA31300103',
          status: 'UNKNOWN',
          trouble_score: 0,
        },
      ])
    );
  }),

  // Get Node component By node Id -- dosent like the url params
  rest.get('http://localhost/nms/inv/nodes/GBMARLNS000001/components', async (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json([
        {
          component_type: 'AIRSPAN',
          component_id: '4',
          component_address: '1',
          manager_instance: 'acp_marlow',
        },
        {
          component_type: 'BLUWIRELESS',
          component_id: '68:1f:40:12:13:7b',
          component_address: '***************',
          manager_instance: null,
        },
        {
          component_type: 'FIBROLAN',
          component_id: '7086PCB22100131',
          component_address: '***************',
          manager_instance: null,
        },
        {
          component_type: 'PARTICLE',
          component_id: '0a10aced202194944a0216a4',
          component_address: null,
          manager_instance: 'particle-test',
        },
      ])
    );
  }),

  //
  rest.get('http://localhost/nms/orc/system_health', async (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        'airspan-acp-agent': {
          app: 'airspan_acp_agent',
          version: '1.0.1+d20230904083226',
          sha: '3886df21',
          started_at: '2023-09-06 17:13:39.693805+00:00',
          up_time: '0:41:12.026226',
        },
        'airspan-acp-events': {
          app: 'airspan_acp_events',
          version: '1.0.1+d20230901153042',
          sha: '643d49bc',
          started_at: '2023-09-06 17:13:38.219295+00:00',
          up_time: '0:41:13.512225',
        },
        'inventory-manager': {
          app: 'inventory_manager',
          version: '1.1.0+d20230905161353',
          sha: '7528155e',
          started_at: '2023-09-06 17:13:30.682834+00:00',
          up_time: '0:41:21.060093',
        },
        'inventory-manager-status-events': {
          app: 'inventory_manager_status_events',
          version: '0.4.1+d20230816101925',
          sha: '089a5484',
          started_at: '2023-09-06 17:13:41.006912+00:00',
          up_time: '0:41:10.745839',
        },
        metrics_collector: {
          app: 'metrics_collector',
          version: '1.1.0+d20230904124857',
          sha: '55d39698',
          started_at: '2023-09-06 17:13:39.404748+00:00',
          up_time: '0:41:12.358623',
        },
        'particle-interactions': {
          app: 'partint',
          version: '0.17.0+d20230823115858',
          sha: 'dbedaecd',
          started_at: '2023-09-06 17:13:38.717132+00:00',
          up_time: '0:41:13.059990',
        },
        'particle-events': {
          app: 'particle_events',
          version: '0.14.1+d20230831063807',
          sha: '89e6657d',
          started_at: '2023-09-06 17:13:37.926270+00:00',
          up_time: '0:41:13.866062',
        },
        'rad-mgr-configurator': {
          app: 'rad_mgr_config',
          version: '0.3.0+d20230906141122',
          sha: 'f5977855',
          started_at: '2023-09-06 17:13:38.311980+00:00',
          up_time: '0:41:13.493226',
        },
        'rad-mgr-kubectl': {
          app: 'rad_mgr_kubectl',
          version: '0.2.0+d20230822153725',
          sha: '4128a47c',
          started_at: '2023-09-06 17:13:39.999446+00:00',
          up_time: '0:41:11.814930',
        },
        nms_sharepoint_agent: {
          app: 'nms_sharepoint_agent',
          version: '1.1.0+d20230906103756',
          sha: 'adf9f03e',
          started_at: '2023-09-06 17:13:35.710429+00:00',
          up_time: '0:41:16.114518',
        },
        'snmp-interactions': {
          app: 'snmp_interactions',
          version: '0.7.1+d20230831084212',
          sha: 'bbafe751',
          started_at: '2023-09-06 17:13:36.742357+00:00',
          up_time: '0:41:15.093289',
        },
        'nms-orchestrator': {
          app: 'nms_orchestrator',
          version: '1.1.0+d20230906160706',
          sha: 'aa963554',
          started_at: '2023-09-06 17:13:39.763199+00:00',
          up_time: '0:41:12.076906',
        },
      })
    );
  }),

  // PUT
  //params: { cellRef: string, antenna: string[] })
  rest.put('http://localhost/cell/GBR-MARL-5G-00001-001/antenna', async (req, res, ctx) => {
    return res(ctx.status(200), ctx.json([]));
  }),

  ...siteManagerHandlers,
  ...metricsCollectorHandlers,
];

export default handlers;
