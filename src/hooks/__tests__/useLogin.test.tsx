import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { act, renderHook } from '@testing-library/react';
import { ReactNode } from 'react';
import useLogin from '../useLogin';

export const decodedJwtObject = {
  nbf: **********,
  exp: **********,
  iss: 'https://ids-dev.denseware.net',
  client_id: 'denseware',
  sub: '66ab485d-8bc1-4df6-9f10-b6d3ad0e1058',
  auth_time: **********,
  idp: 'local',
  ProviderId: [
    '353003',
    '353002',
    '353001',
    '351001',
    '64003',
    '64002',
    '64001',
    '61003',
    '44001',
    '44003',
    '44004',
    '44002',
    '1101',
    '1102',
    '351002',
    '1001',
    '1002',
    '1003',
    '1004',
    '32003',
    '32002',
    '61002',
    '61001',
    '351003',
    '32004',
    '64999',
    '351999',
    '61999',
    '353999',
    '44888',
    '1888',
  ],
  ViewRegionTag: ['View:Client', 'View:Internal'],
  ViewDataValues: 'ViewDataValues',
  FirstName: 'Mohammed',
  LastName: 'Ansir',
  PhoneNumber: '',
  Organization: 'Dense Air UK',
  email: '<EMAIL>',
  role: ['DenseAirGB', 'SystemAdmin', 'NmsDev'],
  jti: 'F398C274E711B3E529C4C3F4FB8EE173',
  iat: **********,
  scope: ['denseware-api', 'email', 'openid', 'profile'],
  amr: ['pwd'],
};

export const encodedJwtString =
  '.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.INkX-N4ukol9YyV1qV84aAPw5RiY8QmFWQvQ0IfkmYxDS354UbJIuBTJbLgYMDsccMzRbdO8ykzaUgrUM5ys8ZwLH6sgnD11fgjlCLQqkU3tWhpk17MIFwGCv8zQKj-9co7uQ6UduBnvPOYDr9sLH3Xqu_er_rTIvcLxVwLWfbaWzs_-dbdoYu1F9xWCIvuZo-qYdiiRri8JByjhjoC4R6Zy-EHidFyidH1T998h-aTyHGCZpRh9o_xE5bgi8zO585EOvqk10yUIkPzY7i--in3RU1by9BT8EA9GJzKwG8VDmyp6nMy3KgMeyap2sIjDmjW1VLOciYNV6EjCGbn4Xg';

type ChildrenProps = {
  children: ReactNode;
};

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});
const wrapper = ({ children }: ChildrenProps) => (
  <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
);

describe('Login ', () => {
  it('decodeJwtToken', async () => {
    localStorage.setItem('dw_authToken', encodedJwtString);
    const { result } = renderHook(() => useLogin('dw_authToken'));

    await act(async () => {
      expect(result.current.decodeJwtToken()).toEqual(decodedJwtObject);
    });
  });

  it('jwtTokenFromLocalStorage', () => {
    localStorage.setItem('dw_authToken', encodedJwtString);
    const { result } = renderHook(() => useLogin('dw_authToken'));

    expect(result.current.jwtTokenFromLocalStorage).toEqual(encodedJwtString);
  });

  it('removeTokenFromLocalStorage', () => {
    localStorage.setItem('dw_authToken', encodedJwtString);
    const { result } = renderHook(() => useLogin('dw_authToken'));

    act(() => result.current.removeTokenFromLocalStorage());
    expect(result.current.jwtTokenFromLocalStorage).toBeNull();
  });

  it.skip('redirect if no jwt token', () => {
    return;
  });

  it.skip('getJwtTokenFromUrl', () => {
    return;
  });

  it('checkApplicationAccess', () => {
    localStorage.setItem('dw_authToken', encodedJwtString);
    const { result } = renderHook(() => useLogin('dw_authToken'));

    act(() => result.current.decodeJwtToken());
    expect(result.current.checkApplicationAccess(['NmsDev'])).toBeTruthy();
  });

  it('checkTokenStatus', async () => {
    localStorage.setItem('dw_authToken', encodedJwtString);
    const { result } = renderHook(() => useLogin('dw_authToken'));
    await act(async () => {
      expect(result.current.checkTokenStatus()).toBe('expired');
    });
  });
});
