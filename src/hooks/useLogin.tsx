import { isAfter } from 'date-fns';
import { decodeJwt } from 'jose';
import { useState } from 'react';
import {
  CURRENT_HOST_URL,
  DEV_IDS_AUTH_URL,
  HARDCODED_HOST_URL,
  LOCAL_IDS,
  LOCAL_NMS,
  PROD_IDS_AUTH_URL,
  READ_WRITE_ACCESS_ROLES,
} from '../data/constants';
import { TokenPayload } from '../types/login.types';

const useLogin = (key: string) => {
  const [jwt, setJwt] = useState<string | null>(localStorage.getItem(key));

  const hostUrl = process.env.NODE_ENV === 'production' ? CURRENT_HOST_URL : HARDCODED_HOST_URL;
  const currentUrl = new URL(window.location.href);
  const tokenInUrl = currentUrl.searchParams.get('token') || '';

  const jwtTokenFromLocalStorage = jwt;

  const decodeJwtToken = () => {
    if (jwt) {
      const jwtDecode = jwt ? decodeJwt(jwt) : null;
      return jwtDecode as TokenPayload;
    }
    return null;
  };

  const getAuthProperty = <K extends keyof TokenPayload>(propertyName: K) => {
    if (tokenResult) {
      const value = tokenResult[propertyName];
      if (value) {
        return value;
      }
      return null;
    }
  };

  const tokenResult = decodeJwtToken();
  const jwtTokenRoles = tokenResult ? getAuthProperty('role') : null;
  const jwtTokenScope = tokenResult ? getAuthProperty('scope') : null;

  const checkRoleAccess = (rolesToCheckAgainst: string[] | undefined) => {
    if (jwtTokenRoles && rolesToCheckAgainst) {
      const rolesAllowed = rolesToCheckAgainst.filter((role) => jwtTokenRoles.includes(role));
      return rolesAllowed;
    }
    return undefined;
  };

  const checkApplicationAccess = (rolesList: string[] | undefined): boolean => {
    if (Array.isArray(jwtTokenRoles) && rolesList) {
      const roleAccess = checkRoleAccess(rolesList);
      if (roleAccess && roleAccess.length !== 0) {
        return true;
      }
    }
    return false;
  };

  const checkNmsDevAccess = (rolesList: string[] | undefined): boolean => {
    if (Array.isArray(jwtTokenRoles) && rolesList) {
      const roleAccess = checkRoleAccess(rolesList);
      if (roleAccess?.length !== 0 && READ_WRITE_ACCESS_ROLES.some((role) => READ_WRITE_ACCESS_ROLES.includes(role))) {
        return true;
      }
    }
    return false;
  };
  const getNMSRoles = (): string[] | [] => {
    if (Array.isArray(jwtTokenRoles)) {
      if (jwtTokenRoles && jwtTokenRoles.length !== 0) {
        return jwtTokenRoles;
      }
    }
    return [];
  };

  const getJwtTokenFromUrl = () => {
    localStorage.setItem('dw_authToken', tokenInUrl);
    currentUrl.searchParams.delete('token');
    window.location.replace(currentUrl.toString());
  };

  const removeTokenFromLocalStorage = () => {
    localStorage.removeItem('dw_authToken');
    setJwt(null);
  };

  const checkTokenStatus = (): 'no-token' | 'expired' | 'valid' => {
    if (tokenResult) {
      const expiryResult = getAuthProperty('exp');

      if (expiryResult) {
        const expiryDate = new Date(expiryResult * 1000);
        const isValid = isAfter(expiryDate, new Date());

        if (isValid) {
          return 'valid';
        } else {
          removeTokenFromLocalStorage();
          return 'expired';
        }
      }
    }
    return 'no-token';
  };

  if (tokenInUrl) {
    getJwtTokenFromUrl();
  } else {
    if (!jwt && process.env.NODE_ENV === 'production') {
      if (currentUrl.host === '**********' || currentUrl.host === 'danms.denseair.twsnoc.com') {
        window.location.href = `${PROD_IDS_AUTH_URL}?redirectUrl=${encodeURIComponent(hostUrl)}`;
      } else {
        window.location.href = `${DEV_IDS_AUTH_URL}?redirectUrl=${encodeURIComponent(hostUrl)}`;
      }
    }
    if (!jwt && process.env.NODE_ENV === 'development') {
      window.location.href = `${LOCAL_IDS}?redirectUrl=${encodeURIComponent(LOCAL_NMS)}`;
    }
  }

  return {
    jwtTokenFromLocalStorage,
    removeTokenFromLocalStorage,
    getJwtTokenFromUrl,
    decodeJwtToken,
    checkApplicationAccess,
    checkNmsDevAccess,
    checkTokenStatus,
    getNMSRoles,
  };
};

export default useLogin;
