import axios from 'axios';
import { useEffect, useState } from 'react';

const useFetchSettings = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        let settings;
        if (import.meta.env.PROD) {
          const response = await axios.get('/settings.json');
          settings = response.data;
        }
        localStorage.setItem('settings', JSON.stringify(settings));
        setIsLoading(false);
      } catch (err) {
        setError(err as any);
        setIsLoading(false);
      }
    };
    fetchSettings();
  }, []);

  return { isLoading, error };
};

export default useFetchSettings;
