import { Alert, AlertDescription, AlertIcon, Alert<PERSON>itle, Container, Flex, Stack } from '@chakra-ui/react';
import { Helmet } from 'react-helmet-async';
import { Outlet } from 'react-router-dom';
import Footer from '../components/footer/Index';
import Header from '../components/header/Index';
import { ENV_COLORS } from '../data/constants';
import useEnv from '../hooks/useEnv';

export default function Layout() {
  const ENV_NAME = useEnv('ENV_NAME');
  const envName = ENV_NAME ?? '';
  const color = ENV_COLORS[envName];
  return (
    <>
      <Helmet defaultTitle={`${color ? `${envName} —` : ''} NMS | Dense Air`} titleTemplate="%s - NMS | Dense Air" />

      {import.meta.env.MODE === 'local.test_1' && (
        <>
          <Alert status="error" position={'absolute'} zIndex={'99999'} p={'0.5rem'} mb={'2rem'}>
            <AlertIcon />
            <AlertTitle>Connected to Test APIs.</AlertTitle>
            <AlertDescription>
              For testing & debugging purposes ONLY ---- <u>DO NOT</u> do a POST, UPDATE or DELETE
            </AlertDescription>
          </Alert>
          <Alert status="error" position={'absolute'} zIndex={'99999'} top={'2rem'} p={'0.5rem'} mb={'2rem'}>
            <AlertIcon />
            <AlertTitle>Do not run unit or E2E tests against the nms-test-1 APIs.</AlertTitle>
            <AlertDescription>test API responses from dev or dev:2.</AlertDescription>
          </Alert>
        </>
      )}
      <Flex
        as="section"
        height={'100vh'}
        position={import.meta.env.MODE === 'local.test_1' ? 'relative' : 'static'}
        top={import.meta.env.MODE === 'local.test_1' ? '4rem' : '0'}
        //overflowY="auto"
        flexDirection={'column'}
      >
        <Header defaultMenuIconColor={color} />
        <Container
          flex="1 1 auto"
          pt={{
            base: '0',
            lg: '5',
          }}
          pb={{
            base: '0',
            lg: '24',
          }}
        >
          <Stack
            spacing={{
              base: '8',
              lg: '6',
            }}
            height="100%"
          >
            <Outlet />
          </Stack>
        </Container>
        <Footer />
      </Flex>
    </>
  );
}
