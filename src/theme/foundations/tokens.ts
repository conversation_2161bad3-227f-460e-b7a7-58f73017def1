//TODO: Need to have a naming convention for the colors, shadows, etc. to make it easier to use them in the components
export default {
  colors: {
    'bg-canvas': {
      default: 'gray.50',
      _dark: 'brand.900',
    },
    stackDivider: {
      default: 'gray.300',
      _dark: 'whiteAlpha.300',
    },
    default: {
      default: 'brand.700',
      _dark: 'white',
    },
    'bg-surface': {
      default: 'white',
      _dark: 'gray.800',
    },
    'bg-subtle': {
      default: 'gray.50',
      _dark: 'gray.700',
    },
    'bg-muted': {
      default: 'gray.100',
      _dark: 'gray.600',
    },
    inverted: {
      default: 'white',
      _dark: 'gray.900',
    },
    emphasized: {
      default: 'gray.700',
      _dark: 'gray.100',
    },
    muted: {
      default: 'gray.600',
      _dark: 'gray.300',
    },
    subtle: {
      default: 'gray.500',
      _dark: 'gray.400',
    },
    border: {
      default: 'gray.200',
      _dark: 'gray.700',
    },
    accent: {
      default: 'brand.500',
      _dark: 'brand.200',
    },
    success: {
      default: 'green.500',
      _dark: 'green.600',
    },
    error: {
      default: 'red.500',
      _dark: 'red.600',
    },
    warn: {
      default: 'orange.500',
      _dark: 'orange.600',
    },

    'bg-accent': 'brand.600',
    'bg-accent-subtle': 'brand.500',
    'bg-accent-muted': 'brand.400',
    'on-accent': 'white',
    'on-accent-muted': 'brand.50',
    'on-accent-subtle': 'brand.100',
  },
  shadows: {
    xs: {
      default: '0px 0px 1px rgba(45, 55, 72, 0.05), 0px 1px 2px rgba(45, 55, 72,  0.1)',
      _dark: '0px 0px 1px rgba(13, 14, 20, 1), 0px 1px 2px rgba(13, 14, 20, 0.9)',
    },
    sm: {
      default: '0px 0px 1px rgba(45, 55, 72, 0.05), 0px 2px 4px rgba(45, 55, 72,  0.1)',
      _dark: '0px 0px 1px rgba(13, 14, 20, 1), 0px 2px 4px rgba(13, 14, 20, 0.9)',
    },
    md: {
      default: '0px 0px 1px rgba(45, 55, 72, 0.05), 0px 4px 8px rgba(45, 55, 72,  0.1)',
      _dark: '0px 0px 1px rgba(13, 14, 20, 1), 0px 4px 8px rgba(13, 14, 20, 0.9)',
    },
    lg: {
      default: '0px 0px 1px rgba(45, 55, 72, 0.05), 0px 8px 16px rgba(45, 55, 72,  0.1)',
      _dark: '0px 0px 1px rgba(13, 14, 20, 1), 0px 8px 16px rgba(13, 14, 20, 0.9)',
    },
    xl: {
      default: '0px 0px 1px rgba(45, 55, 72, 0.05), 0px 16px 24px rgba(45, 55, 72,  0.1)',
      _dark: '0px 0px 1px rgba(13, 14, 20, 1), 0px 16px 24px rgba(13, 14, 20, 0.9)',
    },
  },
};
