export type SystemHealth = {
  json?: CELL_ENTITY;
};

export type CELL_ENTITY = {
  sc_v1?: ScV1[];
  mesh_root?: null;
};

export type ScV1 = {
  id?: string;
  airspan_node?: AirspanNode;
  particle_board?: ParticleBoard;
  bw_mmwave?: BWMmwave;
  fibrolan_switch?: FibrolanSwitch;
};

export type AirspanNode = {
  productType?: string;
  productCode?: string;
  gnbCuCp?: unknown[];
  gnbCuUp?: unknown[];
  gnbDu?: unknown[];
  gnbRu?: GnbRu[];
  nodeProperties?: NodeProperties;
  gnbProperties?: GnbProperties;
  gnbType?: string;
  gnbId?: number;
  platformVersion?: string;
  applicationVersion?: string;
  status?: string;
  serialNumber?: string;
};

export type GnbProperties = {
  primaryPlmnInfo?: PrimaryPlmnInfo;
  gnbAdvancedConfig?: GnbAdvancedConfig;
};

export type GnbAdvancedConfig = {
  cuCpMaxNumUe?: number;
  duCellMaxNumUe?: number;
};

export type PrimaryPlmnInfo = {
  plmn?: Plmn;
  snssaiList?: SnssaiList[];
};

export type Plmn = {
  mcc?: string;
  mnc?: string;
};

export type SnssaiList = {
  sst?: number;
  sd?: boolean;
  sdString?: string;
};

export type GnbRu = {
  id?: number;
  sectorCarriers?: SectorCarrier[];
  status?: string;
};

export type SectorCarrier = {
  duCell?: string;
  ruSectorConfiguration?: RuSectorConfiguration;
  sectorCarrierId?: number;
  inheritDuConfiguration?: boolean;
  vnfP5Ipv4Address?: string;
  vnfP5Port?: number;
  pnfP7Ipv4Address?: string;
  pnfP7Port?: number;
};

export type RuSectorConfiguration = {
  gnbRuSectorServiceManagerProperties?: GnbRuSectorServiceManagerProperties;
};

export type GnbRuSectorServiceManagerProperties = {
  oRanFrontHaulIndicated?: string;
  unitOverHeating?: string;
  outOfSync?: string;
};

export type NodeProperties = {
  id?: number;
  locationSource?: number;
  name?: string;
  description?: string;
  regionId?: number;
  siteId?: number;
  latitude?: number;
  longitude?: number;
  altitude?: number;
  isManaged?: boolean;
  isNbifEventAlarmForwarding?: boolean;
};

export type BWMmwave = {
  interfaces?: Interface[] | null;
  system?: System;
  firmware?: string;
  ip_address?: string;
  component_id?: string | number | undefined;
  monitored?: string;
  status?: string;
};

export type Interface = {
  ifIndex?: string;
  ifDescr?: string;
  ifType?: IfType;
  ifMtu?: string;
  ifSpeed?: string;
  ifPhysAddress?: string;
  ifAdminStatus?: IfStatus | null;
  ifOperStatus?: IfStatus | null;
};

export type IfStatus = 'up' | 'down';

export type IfType = 'softwareLoopback' | 'ethernetCsmacd' | 'tunnel' | 'l2vlan' | 'ieee8023adLag';

export type System = {
  sysDescr?: string;
  sysObjectID?: string;
  sysUpTime?: string;
  sysContact?: string;
  sysName?: string;
  latitude?: null;
  longitude?: null;
  sysServices?: string;
  sysORLastChange?: null | string;
};

// export type FibrolanSwitch = {
//   psu?: PSU;
//   cpu?: CPU;
//   interfaces?: Interface[];
//   system?: System;
//   firmware?: string | undefined | null;
//   ip_address?: string;
//   component_id?: string | number | undefined;
//   monitored?: string;
//   status?: string;
//   error?: any;
// };
export type FibroInterface = {
  ifIndex?: string;
  ifDescr?: string;
  ifType?: string;
  ifMtu?: string;
  ifSpeed?: string;
  ifPhysAddress?: string;
  ifAdminStatus?: string;
  ifOperStatus?: string;
  ifLastChange?: string;
  ifInOctets?: string;
  ifInUcastPkts?: string;
  ifInNUcastPkts?: string;
  ifInDiscards?: string;
  ifInErrors?: string;
  ifInUnknownProtos?: string;
  ifOutOctets?: string;
  ifOutUcastPkts?: string;
  ifOutNUcastPkts?: string;
  ifOutDiscards?: string;
  ifOutErrors?: string;
  ifOutQLen?: string;
  ifSpecific?: string;
};
export type FibrolanSwitch = {
  system?: System;
  firmware?: string | undefined | null;
  interfaces?: FibroInterface[];
  psu?: PSU;
  cpu?: CPU;
  sync?: FibroSync;
  gps?: FibroGps;
  ip_address?: string;
  component_id?: string | number | undefined;
  monitored?: string;
  device_type?: string;
  status?: string;
  error?: any;
};
export type JuniperSwitchType = {
  system?: System;
  firmware?: string | undefined | null;
  interfaces?: FibroInterface[];
  ip_address?: string;
  component_id?: string | number | undefined;
  monitored?: string;
  device_type?: string;
  status?: string;
  error?: any;
  box?: any;
  vpninfo?: any;
};

export type FortinetSwitchType = {
  manager_name: string;
  device_name: string;
  version: string;
  serial: string;
  node_type: string;
  description: string;
  ip_address: string;
  latitude: number;
  longitude: number;
  model: string;
  roles: string[];
  created_at: string;
  updated_at: string;
  licences: any;
  ipsec: any;
  alarms: any[];
  status: string;
  error?: any;
  dhcp?: DhcpEntry[] | undefined;
  ntp?: NtpConfig;
  certificates?: CertificateEntry[];
  interfaces?: InterfaceEntry[];
  [key: string]: any;
};

export type DhcpEntry = {
  oid: string;
  ipAddress: string;
  macAddress: string;
  leaseTime: number;
  [key: string]: any;
};

export type NtpConfig = {
  server: string;
  status: string;
  lastSyncTime: string;
  [key: string]: any;
};

export type CertificateEntry = {
  id: string;
  issuer: string;
  validFrom: string;
  validTo: string;
  [key: string]: any;
};

export type InterfaceEntry = {
  ifDescr: string;
  name: string;
  macAddress: string;
  ipAddress: string;
  netmask: string;
  status: string;
  [key: string]: any;
};

export type FibroSync = {
  status: {
    flSyncCenterId: number | undefined | null;
    flSyncCenterState: string | undefined | null;
    flSyncCenterStateLastChange: number | undefined | null;
    flSyncCenterSelectedInputId: number | undefined | null;
    flSyncCenterSelectedInputType: string | undefined | null;
    flSyncCenterSelectedInputLastChange: number | undefined | null;
    flSyncCenterClockOutputQuality: string | undefined | null;
    flSyncCenterClockOutputQualityLastChange: number | undefined | null;
    flSyncCenterBitsOutputState: string | undefined | null;
  };
  clocks: FibroClock[];
};
export type FibroClock = {
  flSyncCenterClockInputId: number | undefined | null;
  flSyncCenterClockInputType: string | undefined | null;
  flSyncCenterClockInputState: string | undefined | null;
  flSyncCenterClockInputStateLastChange: number | undefined | null;
  flSyncCenterClockInputQuality: string | undefined | null;
  flSyncCenterClockInputQualityLastChange: number | undefined | null;
};
export type FibroGps = {
  flGpsId: number | null | undefined;
  flGpsModulePartNumber: string | null | undefined;
  flGpsModuleSerialNumber: string | null | undefined;
  flGpsHardwareId: string | null | undefined;
  flGpsFirmwareVersion: string | null | undefined;
  flGpsFirmwareDate: string | null | undefined;
  flGpsState: string | null | undefined;
  flGpsStateLastChange: number | null | undefined;
  flGpsDateAndTime: string | null | undefined;
  flGpsLatitude: number | null | undefined;
  flGpsLongitude: number | null | undefined;
  flGpsAltitude: number | null | undefined;
  flGpsCableDelay: number | null | undefined;
  flGpsAntennaState: string | null | undefined;
  flGps1PpsState: string | null | undefined;
  flGpsTrackedSatelliteCount: number | null | undefined;
};
export type SwitchNode = {
  fibrolan_switch: FibrolanSwitch;
  id: string;
  juniper_switch: JuniperSwitchType;
  mosolabs_switch: MosolabsResponse;
};
export type FirewallNode = {
  id: string;
  juniper_firewall: JuniperSwitchType;
  fortigate_firewall: FortinetSwitchType;
};
export type CPU = {
  flDeviceCpuIndex?: string;
  flDeviceCpuUtilization?: string;
  flDeviceMemoryUtilization?: string;
  flDeviceNvMemoryUtilization?: string;
  flDeviceCpuAlarmsEnable?: string;
  flDeviceCpuAlarmStatus?: string;
  flDeviceCpuStatusLastChange?: string;
};

export type PSU = {
  flDevicePsuIndex?: string;
  flDevicePsuInstalled?: string;
  flDevicePsuStatus?: string;
  flDevicePsuFanStatus?: string;
  flDevicePsuAlarmsEnable?: string;
  flDevicePsuAlarmStatus?: string;
  flDevicePsuStatusLastChange?: string;
  flDeviceUpdateTableIndex?: string;
  flDeviceUpdateType?: string;
  flDeviceUpdateFileServerType?: string;
  flDeviceUpdateFileServerAddress?: string;
  flDeviceUpdateFileXferDirection?: string;
  flDeviceUpdateFileName?: string;
  flDeviceUpdateStart?: string;
  flDeviceUpdateStatus?: string;
  flDeviceUpdateErrorStatus?: string;
  flDeviceUpdateErrorCode?: string;
  flDeviceAlarmThresholdTableIndex?: string;
  flDeviceAlarmThresholdType?: string;
  flDeviceAlarmThresholdValue?: string;
  flDeviceAlarmThresholdClearValue?: string;
};

export type ParticleBoard = {
  coreid?: string;
  //details?: Details;
  details?: AntennaPatterDetails;
  updated?: Date;
  created?: Date;
  published_at?: Date;
  manager?: string;
  status?: string;
  status_reasons?: unknown[];
};

//export type Details = {
export type AntennaPatterDetails = {
  version?: number;
  modeName?: string;
  darkMode?: boolean;
  caseOpen?: boolean;
  fanMode?: string;
  overtemp?: number;
  hysteresis?: number;
  tempSensor1?: number;
  tempSensor2?: number;
  bwFanSpeed?: number;
  asFanSpeed?: number;
  antenna?: Antenna;
  antennaConfiguration?: number;
  networkConfiguration?: NetworkConfiguration;
  alarmCodes?: unknown[];
};

export type Antenna = {
  board1?: Board;
  board2?: Board;
};

export type Board = {
  switch1?: string;
  switch2?: string;
  switch3?: string;
  switch4?: string;
  switch5?: string;
  switch6?: string;
};

export type NetworkConfiguration = {
  DHCP?: boolean;
  IP?: string;
  SUB?: string;
  GW?: string;
  DNS?: string;
};

export type SNMP = {
  ip_address?: string;
  component_id?: string;
  device_type?: string;
  status?: string;
  status_change_time?: Date;
  connected_cells?: ConnectedCells;
  monitored?: string;
};

export type ConnectedCells = any;

export interface CollectDataProcess {
  'Cells Gathered': string[];
  'Skipped Cells': string[];
  'Number of cells gathered': number;
}

export interface InvalidStreetCellError {
  [cellRef: string]: string;
}

export interface FormatDataProcess {
  'Valid Street Cells': string[];
  'Invalid Street Cells': InvalidStreetCellError[];
}

export interface PostManifestInventoryManagerProcess {
  'Accepted Street Cells': string[];
  'Rejected Street Cells': string[];
}

export interface Detail {
  collect_data_process: CollectDataProcess;
  format_data_process: FormatDataProcess;
  post_manifest_inventory_manager_process: PostManifestInventoryManagerProcess;
}

export interface RefreshCellInventoryResponse {
  detail: Detail;
}

export type NetConfResponse = {
  utc_stamp: Date;
  origin: string;
  message: string;
  status: string;
  object: Record<string, unknown>;
};

export type NetworkUpdateRequestObject = {
  address?: string;
  netmask?: string;
  gateway?: string;
  dns?: string;
  dhcp?: boolean;
};

export type DarkModeUpdateObject = {
  Cmd: string;
};

export type FanModeUpdateObject = {
  Cmd: string;
};

export type SystemModeUpdateObject = {
  Cmd: string;
};

export type TempSensorUpdateObject = {
  OT: number;
  HY: number;
};
export type FastPath = [
  {
    destination: string;
    'next-hop': Array<{
      'next-hop': string;
      interface: string;
      active: boolean;
      uptime: string;
    }>;
  }
];
export type FlattenedFastPath = {
  destination: string;
  'next-hop': string;
  interface: string;
  active: boolean;
  uptime: string;
};

export type VsrData = {
  cpu_usage: number;
  license_expiry: string;
  version: string;
  host_name: string;
  status: string;
  updated: string;
  fastpath: FastPath;
};

export type Vsr = {
  id: string;
  app: string;
  version: string;
  sha: string;
  started_at: string;
  up_time: string;
  ip_address: string;
  poll: boolean;
  created: string;
  updated: string;
  vsr_data: VsrData;
};

export interface Parameters {
  cell_id: string | null;
  node_id: string | null;
  serial_nr: string;
  manager_instance: string;
}

export type ServerActionPostDataType = {
  type: string;
  uid: string;
  prior_uid: string | null;
  node_id: string | null;
  name: string;
  context: string | null;
  resource: string;
  priority: number;
  phase: string;
  parameters: Parameters;
  description: string;
  created_at: string;
};

export type OranRuActionPostDataType = {
  node_id: string | null;
  priority: number;
  phase: string;
};

export interface taskResult {
  detail: string;
}

export interface Task {
  type: string;
  uid: string;
  operation_uid: string;
  progress: string;
  data: string | taskResult;
  message: string;
  updated_at: string;
}

export interface ServerActionGetDataType {
  type: string;
  uid: string;
  prior_uid: string;
  node_id: string;
  name: string;
  context: string;
  resource: string;
  priority: number;
  phase: string;
  parameters: Record<string, unknown>;
  description: string;
  created_at: string;
  info: Task[];
}

type SystemInfo = {
  sysDescr: string;
  sysObjectID: string;
  sysUpTime: number;
  upTime: string;
  startTime: string;
  sysContact: string;
  sysName: string;
  latitude: number | null;
  longitude: number | null;
  sysServices: number;
  sysORLastChange: string | null;
};

type BatteryInfo = {
  battery_status: string;
  time_on_battery: number;
  estimated_time_left: number;
  estimated_charge_left: number;
  battery_voltage: number;
  battery_current: number;
  temperature: number;
};

type InputInfo = {
  index: number;
  voltage: number;
  current: number;
  power: number;
  frequency: number;
};

type OutputInfo = {
  index: number;
  voltage: number;
  current: number;
  power: number;
  percent_load: number;
};

export type PowerUPSType = {
  system: SystemInfo;
  info: {
    manufacturer: string;
    model: string;
    name: string;
  };
  battery: BatteryInfo;
  input: {
    inputs: InputInfo[];
  };
  output: {
    source: string;
    frequency: number;
    outputs: OutputInfo[];
  };
  firmware: string;
  ip_address: string;
  component_id: string;
  node_type: string;
  monitored: string;
  status: string;
  status_change_time: string;
  last_success_time: string;
  last_query_time: string;
  last_query_duration: number;
  last_error: string | null;
  query_error_count: number;
  error?: any;
};

export type PowerNode = {
  id: string;
  ups: PowerUPSType;
};

export type IpsecChildConfig = {
  id: number;
  name: string;
  lifetime: number;
  rekeytime: number;
  jitter: number;
};

export type IpsecChildConfigProposal = {
  id: number;
  child_cfg: number;
  prio: number;
  prop: number;
};

export type IpsecCertificate = {
  filename: string;
  subject: string;
  issuer: string;
  expiry_date: string;
  start_date: string;
};

export type IpsecPeerConfig = {
  id: number;
  name: string;
  type: string;
  ike_version: number;
  rekeytime: number;
  jitter: number;
  pool: string;
};

export type IpsecPrivateKey = {
  type: number;
  filename: string;
};

export type IpsecSecureAssociation = {
  name: string;
  creation_time: string;
  oper_state: string;
  direction: string;
  local_addr: string;
  local_id: string;
  remote_addr: string;
  remote_id: string;
  integrity_algorithm: string;
  encryption_algorithm: string;
  sa_bytes_in: number;
  sa_packets_in: number;
  sa_bytes_out: number;
  sa_packets_out: number;
};

export type S1Client = {
  oper_state: string;
  admin_state: string;
  name: string;
  s1_client_type: string;
  enb_name: string;
  plmn_id: string;
  cell_identity: string;
  max_downlink_bandwidth: number;
  max_uplink_bandwidth: number;
};

export type IpsecProposal = {
  id: number;
  proposal: string;
};

export type IpsecIkeConfigProposal = {
  id: number;
  ike_cfg: number;
  prio: number;
  prop: number;
};

export type SegwDataType = {
  ipsec_child_config?: IpsecChildConfig[];
  ipsec_child_config_proposal?: IpsecChildConfigProposal[];
  ipsec_certificate?: IpsecCertificate[];
  ipsec_peer_config?: IpsecPeerConfig[];
  ipsec_private_key?: IpsecPrivateKey[];
  ipsec_secure_association?: IpsecSecureAssociation[];
  s1client?: S1Client[];
  ipsec_proposal?: IpsecProposal[];
  ipsec_ikeconfig_proposal?: IpsecIkeConfigProposal[];
};

export type Druid = {
  device_id: string;
  status: string;
  aspects: Aspects;
  details: Details;
  networks: TDruidNetworks;
  segw: Segw;
  enodebs: Enodeb[];
  created_at: string;
};

export type ActivityInterval = {
  interval: string;
  count: number;
};

export type ActivityData = {
  statistics: string;
  intervals: ActivityInterval[];
};

export type NexusActivity = {
  issued: ActivityData;
  expiring: ActivityData;
  revoked: ActivityData;
};

export type Nexus = {
  name: string;
  ip_address: string;
  gcp_project_id: string;
  description: string;
  created: string;
  last_contacted: string;
  status: string;
  last_status: string;
  activity: NexusActivity;
};

export type Aspects = {
  enode_b_count: number;
  iproute_count: number;
  ipsec_secure_association_count: number;
  net_device_count: number;
  plmn_count: number;
  radio_zone_count: number;
  sgw_count: number;
  sgw_session_count: number;
  s1server_enb_count: number;
  users_count: number;
  enable_5g: boolean;
  enable_5g_nsa: boolean;
};

export type Details = {
  features: Features;
  system: DetailsSystem;
};

export type Features = {
  oper_state: string;
  product_id: string;
  system_id: string;
  license_id: string;
  license_status: string;
  issue_date: string;
  expiry_date: string | null;
  binding_date: string | null;
  supported_until: string;
  max_nbr_of_subs: string;
  max_cells: string;
  max_enbs: string;
  max_pdns: string;
  max_s1_clients: string;
  enbgw_max_enbs: string;
  enbgw_max_active_subs: string;
};

export type DetailsSystem = {
  id: number;
  oper_state: string;
  admin_state: string;
  service_state: string;
  system_id: string;
  license_id: string;
  product_id: string;
  software_version: string;
  restart_required: string;
  current_time: string;
};

export type IpRoute = {
  id: number;
  ipv4_subnet: string;
  ipv4_subnetmask: string;
  gateway_ipv4: string;
  net_device: string;
  metric: string;
  owner: string;
  raemis_id: string;
  dnm_managed: boolean;
};

export type Pdn = {
  id: number;
  apn: string;
  primary_dns: string;
  secondary_dns: string;
  ue_mtu: string;
  ipv4_pool_id: number;
  ep_group_id: string;
  allow_multiple_connections: string;
  primary_ipv6_dns: string;
  secondary_ipv6_dns: string;
  ipv6_prefix_pool_id: string;
  pcscf_ipv4_addr: string;
  pcscf_ipv6_addr: string;
  ipv4_config_method: string;
  ipv4_config_timeout: string;
  use_mac_mappings: string;
  raemis_id: string;
  sticky_ip_assignment: string;
  ipv4pools: string;
};

export type Segw = {
  ipsec_child_config: IpsecChildConfig[];
  ipsec_child_config_proposal: IpsecChildConfigProposal[];
  ipsec_certificate: IpsecCertificate[];
  ipsec_ikeconfig_proposal: IpsecIkeconfigProposal[];
  ipsec_peer_config: IpsecPeerConfig[];
  ipsec_private_key: IpsecPrivateKey[];
  ipsec_secure_association: IpsecSecureAssociation[];
  ipsec_proposal: IpsecProposal[];
  s1client: S1Client[];
};

export type IpsecIkeconfigProposal = {
  id: number;
  ike_cfg: number;
  prio: number;
  prop: number;
};

export type Enodeb = {
  id: number;
  oper_state: string;
  admin_state: string;
  name: string;
  tac: number;
  cell_id: number;
  enb_id: number;
  enodeb: EnodebDetails;
};

export type EnodebDetails = {
  id: number;
  oper_state: string;
  admin_state: string;
  plmn_id: string;
  identity: number;
  name: string;
  sctp_address: string;
  last_inform_time: string | null;
};

// Group Interface
export interface Group {
  id: number;
  num: string;
  name: string;
  imsi_prefix: string;
  description: string;
  notify_presence: number;
  disallow_mno_services: number;
  disallow_local_services: number;
  radio_zones: string;
  subscription_profile_preference_id: number;
  eir_bypass: number;
  csg_ids: string;
  cag_data: string;
  raemis_id: number;
  subscribers: string;
}

// IPv4 Pool Interface
export interface Ipv4Pool {
  id: number;
  name: string;
  first_ip: string;
  last_ip: string;
  used_ips: number;
  raemis_id: number;
}

// MGW Endpoint Interface
export interface MgwEndpoint {
  id: number;
  mgw_id: number;
  ep_id: number;
  type: number;
  net_device: string;
  autocreate_net_device: number;
  snat_ipv4_pool_id: number;
  rcv_buf_size: number;
  snd_buf_size: number;
  external_ipv4: string;
  max_segment_size: number;
  adjust_tcp_mss: number;
  stats_measurement_period: number;
  last_period_sent_octets: number;
  last_period_recv_octets: number;
  measurement_period: number;
  allow_traffic_shaping: number;
  max_latency: number;
  max_frame_size: number;
  packets_per_block: number;
  nbr_of_blocks: number;
  af_blocks_per_read: number;
  packet_fanout_mode: number;
  redirection_ep: number;
  local_port: number;
  udp_type: number;
  local_ipv4: string;
  additional_local_ipv4s: string;
  omit_src_addr_in_arp: number;
  read_outgoing_packets: number;
  max_read_count: number;
  max_packets_per_read: number;
  ue_to_ue_data_enabled: number;
  subnet_routing_enabled: boolean;
  txqueuelen: number;
  address_change_action: number;
  raemis_id: number;
  insert_ip_tables_rules: number;
  calc_udp_checksum: number;
}

// Net Device Interface
export interface NetDevice {
  id: number;
  admin_state: number;
  oper_state: number;
  mac: string;
  device: string;
  parent_device: string;
  vlan_id: number;
  bootproto: string;
  ip: string;
  netmask: string;
  ipv6: string;
  nat_enabled: number;
  ipv4_forwarding_enabled: number;
  ipv6_forwarding_enabled: number;
  owner: string;
  device_type: number;
  raemis_id: number;
}

// Network Slice Interface
export interface NetworkSlice {
  id: number;
  name: string;
  plmn_id: string;
  sst: number;
  sd: number;
  raemis_id: number;
  overload_action: number;
  traffic_load_reduction_indication: number;
}

// Subscription Profile Interface
export interface SubscriptionProfile {
  id: number;
  pdn_type: number;
  apn: string;
  name: string;
  qci: number;
  priority: number;
  may_trigger_preemption: number;
  preemptable: number;
  dl_apn_ambr: number;
  ul_apn_ambr: number;
  pgw_address: string;
  apply_to_all_subs: number;
  plmn_id: string;
  scef_id: string;
  network_slice_id: number;
  monitoring_key: string;
  total_granted_octets_monitoring_interval: number;
  input_granted_octets_monitoring_interval: number;
  output_granted_octets_monitoring_interval: number;
  granted_time_monitoring_interval: number;
  up_integrity_protection: number;
  up_confidentiality_protection: number;
  online: number;
  rating_group: number;
  service_identifier: number;
  raemis_id: number;
}

export type TDruidNetworks = {
  ip_route: IpRoute[];
  pdn: Pdn[];
  group: Group[];
  ipv4_pool: Ipv4Pool[];
  mgw_endpoint: MgwEndpoint[];
  net_device: NetDevice[];
  network_slice: NetworkSlice[];
  subscription_profile: SubscriptionProfile[];
};

export interface MosolabsResponse {
  system?: MosolabsSystem;
  ports?: Record<string, MosolabsPort>;
  alarms?: any;
  ip_address?: string;
  component_id?: string;
  node_type?: string;
  monitored?: string;
  status?: string;
  status_change_time?: string;
  last_success_time?: string;
  last_query_time?: string;
  last_query_duration?: number;
  last_error?: string | null;
  query_error_count?: number;
  utc_stamp?: string;
  client?: string;
  error?: {
    status_code?: number;
    message?: {
      error?: string;
      url?: string;
      method?: string;
    };
    url?: string;
  };
}

export interface MosolabsSystem {
  info: MosolabsSystemInfo;
  ntp: MosolabsSystemNtp;
  ptp: MosolabsSystemPtp;
  poe: MosolabsSystemPoe;
}

export interface MosolabsSystemInfo {
  systemInfoModelName: string;
  systemInfoMACAddress: string;
  systemInfoSerialNumber: string;
  systemInfoHardwareVersion: string;
  systemInfoSoftwareVersion: string;
  systemInfoSysName: string;
  systemInfoSysLocation: string;
  systemInfoSysContact: string;
  systemInfoSysGroup: string;
  systemInfoLastBootTime: string;
  systemInfoUptime: string;
  deviceHardwareMonitorPowerRedundant1Current: number;
  deviceHardwareMonitorPowerRedundant1Consumption: number;
  deviceHardwareMonitorPowerRedundant1Temperature: number;
  deviceHardwareMonitorPowerRedundant1Voltage: number;
  deviceHardwareMonitorPowerRedundant2Current: number;
  deviceHardwareMonitorPowerRedundant2Consumption: number;
  deviceHardwareMonitorPowerRedundant2Temperature: number;
  deviceHardwareMonitorPowerRedundant2Voltage: number;
  deviceHardwareMonitorFan1Utilization: number;
  deviceHardwareMonitorFan2Utilization: number;
  deviceHardwareMonitorFan3Utilization: number;
  deviceHardwareMonitorTemperature: number;
  deviceHardwareMonitorPower: number;
  systemLEDsPowerLEDIsLit: boolean;
  systemLEDsSystemLEDIsLit: boolean;
  systemLEDsPoeMaxLEDIsLit: boolean;
  systemLEDsFanLEDIsLit: boolean;
  systemLEDsGPSLEDIsLit: boolean;
}

export interface MosolabsSystemNtp {
  ntpTimeMode: string;
  ntpMainNtpServer: string;
  ntpBackupNtpServer: string;
  ntpTrustedServer: boolean;
  ntpSyncInterval: number;
  ntpShowTimeDate: string;
}

export interface MosolabsSystemPtp {
  ptpEnabled: boolean;
  ptpProfile: string;
  ptpMode: string;
  ptpDomainNumber: number;
  ptpDelayMechanism: string;
  ptpMasterAnnouceRate: number;
  ptpMasterAnnouceTimeout: number;
  ptpMasterSyncRate: number;
  ptpMasterSyncLimit: number;
  ptpMasterEncaspsulation: string;
  ptpMasterTransmission: string;
  ptpMasterPeerDelayRequestRate: string;
  ptpSlaveEncaspsulation: string;
  ptpSlaveTransmission: string;
  ptpClockStep: string;
  ptpGrandMasterInfoDomainNumber: number;
  ptpGrandMasterInfoPortIdentity: string;
  ptpGrandMasterInfoClockIdentity: string;
  ptpGrandMasterInfoNumberOfPorts: number;
  ptpGrandMasterQualityClass: number;
  ptpGrandMasterQualityAccuracy: string;
  ptpGrandMasterQualityTimesource: string;
  ptpGrandMasterQualityOffsetScaledLogVariance: number;
  ptpGrandMasterQualityOffset: number;
  ptpGrandMasterClockErrorOffset1: number;
  ptpGrandMasterClockErrorOffset2: number;
  ptpGrandMasterClockErrorFractionalFrequencyOffset: string;
  ptpGrandMasterAPTSStatusGNSSStatus: string;
  ptpGrandMasterAPTSStatusGNSSDateTime: string;
  ptpGrandMasterAPTSStatusPTPStatus: string;
  ptpGrandMasterAPTSStatusPTPDateTime: string;
}

export interface MosolabsSystemPoe {
  poeMaxAvailablePower: number;
  poeMainCurrent: number;
  poeMainVoltage: number;
  poeTotalPowerConsumption: number;
}

export interface MosolabsPort {
  status: string;
  config: MosolabsPortConfig;
  poe: MosolabsPortPoe | null;
  led: MosolabsPortLED;
  usage: MosolabsPortUsage;
}

export interface MosolabsPortConfig {
  portConfigAlias: string;
  portConfigPortOperation: boolean;
  portConfigSpeed: string;
  portConfigFlowControl: boolean;
  portStatusLinkUp: boolean;
  portStatusLastLinkChange: string;
  portStatusLinkState: string;
  portStatusSpeedUsed: string;
  portStatusFlowcontrolUsed: boolean;
  ptpPortPtpEnabled: boolean;
  ptpPortRole: string;
}

export interface MosolabsPortPoe {
  poeConfigEnabled: boolean;
  poeConfigMode: string;
  poeConfigPriority: string;
  poePortStatusDeterminedClass: string;
  poePortStatusCondition: string;
  poePortStatusPriority: string;
  poePortStatusOutputCurrent: number;
  poePortStatusOutputVoltage: number;
  poePortStatusOutputPower: number;
  poePortStatusPowerConsumption: number;
}

export interface MosolabsPortLED {
  portsLEDsStatusLinkIsLit: boolean;
  portsLEDsStatusSpeedIsLit: boolean | null;
  portsLEDsStatusPoEIsLit: boolean | null;
}

export interface MosolabsPortUsage {
  ingressInGoodOctets: number;
  ingressInFcsErrors: number;
  ingressInDiscarded: number;
  egressOutGoodOctets: number;
  egressOutFcsErrors: number;
  egressOutDroppedPackets: number;
  ifIn1SecRate: number | null;
  ifOut1SecRate: number | null;
  inDiscardDelta: number | null;
  outDiscardDelta: number | null;
  inErrorDelta: number | null;
  outErrorDelta: number | null;
}
