export type Rollout = {
  id: string;
  name: string;
  description: string;
  created_at: string;
  deployments: string[];
  events: string[];
  contacts: string[];
  rollout_id: string;
  status: string;
};

export type RollOutData = {
  rollouts: Rollout[];
};

export type Device = {
  id: string | null;
  device_id: string | null;
  name: string;
  device_type: string | null;
};

export type Event = {
  id: string;
  action: string;
  details: {
    rollout_id: string;
    name: string;
    description: string;
    artifact_name: string;
    deployment_type: string;
    id: string;
    deployment_id: string | null;
    status: string | null;
  };
  created_at: string;
  user: {
    email: string;
    first_name: string;
    last_name: string;
    organization: string;
    role: string[];
    iss: string;
    sub: string;
  };
};

export type Deployment = {
  id: string;
  name: string;
  description: string;
  artifact_name: string;
  deployment_type: string;
  status: string;
  devices: Device[];
  rollout_id: string;
  deployment_id: string;
  events: Event[];
  rollout_name: string;
};
