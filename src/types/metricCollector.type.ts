import { LIFE_CYCLE } from '../data/constants';

export type Events = {
  count: number;
  events: Event[] | [];
};

export type Event = {
  data: EventData;
  header: EventHeader;
};

export type EventDataInventoryNode = {
  node_id: string;
  node_type: string;
  cell_refs: string[];
  site_id: string;
  site_name: string;
  latitude: number;
  longitude: number;
  orientation: string;
  status: string;
};

export type EventDataInventoryCell = {
  cell_ref: string;
  oran_split: string;
  site_name: string;
  latitude: number;
  longitude: number;
  orientation: string;
  status: string;
};

export type EventDataInventory = {
  node: EventDataInventoryNode;
  cells: EventDataInventoryCell[];
};

export type EventData = {
  uri?: string;
  type?: string;
  cause?: string;
  object_id?: number;
  object_type?: string;
  additional_text?: string;
  trend_indication?: string;
  specific_problem?: string;
  severity?: string;
  monitored_attributes?: MonitoredAttribute[];
  additional_information?: string;
  proposed_repair_actions?: string[];
  inventory?: EventDataInventory;
};

export type MonitoredAttribute = {
  tempSensor1?: number;
  tempSensor2?: number;
  bwFanSpeed?: number;
  asFanSpeed?: number;
};

export type EventHeader = {
  domain: string;
  eventId: string;
  priority: string;
  sourceId: string;
  eventName: string;
  eventTime: string;
  eventType: string;
  sourceName: string;
  eventDuration: number;
  reportingEntityName: string;
  subRows: EventData;
  inventory?: Record<string, any>;
  expander?: string;
};

export type Alarm = {
  count: number;
  alarms: AlarmElement[];
};

export type AlarmElement = {
  id: number;
  internal_id: string;
  object_id?: string;
  object_type?: string;
  event_name?: string;
  system_dn?: string;
  proposed_repair_actions?: string[];
  severity: 'major' | 'minor' | 'none';
  type: string;
  status: 'new' | 'acknowledged' | 'updated' | 'resolved';
  created?: Date;
  updated?: Date;
  expires?: Date | null;
  acknowledged?: null;
  acknowledger?: null;
  resolved?: null;
  resolver?: null;
  resolution?: null;
  details?: string | null;
  source_name?: string;
  cause?: string;
  specific_problem: string;
  additional_text?: string;
  additional_information?: string;
  subRows?: (AlarmElement & { userFullName: string })[];
  inventory?: Record<string, any>;
  count?: number;
  expander?: string;
};

export type EventSearchQueryParams = {
  limit?: number;
  offset?: number;
  start_date?: string;
  end_date?: string;
  source_id?: string;
  asc?: string;
  desc?: string;
  alarm_id?: string;
  event_name?: string;
  event_type?: string;
  priority?: string;
  reporting_entity_name?: string;
  source_name?: string;
  type?: string;
  lifecycle?: LIFE_CYCLE | null;
};

export type ParticleMetricsSearchQueryParams = {
  limit?: number;
  offset?: number;
  start_date?: string;
  end_date?: string;
  coreid?: string;
  asc?: string[];
  desc?: string[];
};

export type ParticleMetric = {
  id: number;
  coreid: string;
  date: string;
  temperature: number;
  bluwireless_fan_speed: number;
  airspan_fan_speed: number;
};

export type ParticleMetrics = {
  count: number;
  events: ParticleMetric[] | [];
};

export type UpdateAlarmRequest = {
  status?: string;
  expires?: Date;
  acknowledged?: Date;
  acknowledger?: string;
  resolved?: Date;
  resolver?: string;
  resolution?: string;
  details?: string | null;
};

export type ResolveAlarmRequest = {
  resolution?: string;
  details?: string;
};

export type TransporteMetricsSearchParams = {
  limit?: number;
  offset?: number;
  component_id: string;
  component_type: string;
  interfaces: string[];
  time_filter?: string;
};

export type TransportMetric = {
  date: string;
  ifOut1SecRate: number;
  ifIn1SecRate: number;
  outDiscardDelta: number;
  outErrorDelta: number;
  inDiscardDelta: number;
  inErrorDelta: number;
  interface_name: string;
  ifDescr: string;
  component_id: string;
};

export type TransportMetrics = {
  count: number;
  events: TransportMetric[] | [];
};
