import { List } from 'lodash';
import {
  LIFE_CYCLE,
  MONITOR_STATE,
  NODE_TYPE,
  ORIENTATION,
  O_RAN_SPLIT,
  ROLE_OF_NODE,
  STATUS,
} from '../data/constants';

//below should be in utility
type RequireAtLeastOne<T, <PERSON> extends keyof T = keyof T> = Pick<T, Exclude<keyof T, Keys>> &
  {
    [K in Keys]-?: Required<Pick<T, K>> & Partial<Pick<T, Exclude<Keys, K>>>;
  }[Keys];

export type Site = {
  site_id: number;
  name: string;
  address: string;
  region_id: number;
  region_code: string;
  country_code: string;
  description?: string;
  additional_info?: string;
  latitude?: number;
  longitude?: number;
  site_cells: string[];
  site_nodes: string[];
};

export type RegionBase = {
  region_id: number;
  region_code: string;
  region_name: string;
  country_code: string;
  country_name?: string;
  description?: string;
};

export type Region = RegionBase & {
  creation_date: string;
  default_site_id: number;
  discovery: boolean;
  radius: number;
  latitude: number;
  longitude: number;
  description: string;
  region_id: number;
  region_code: string;
  region_name: string;
  region_type: string;
  country_code: string;
  country_name: string;
  lifecycle?: LIFE_CYCLE;
};

export type RegionUpdate = {
  region_name: string;
  region_type: string;
  description: string;
  default_site_id: number;
  latitude: number;
  longitude: number;
  radius: number;
  discovery: boolean;
  lifecycle: LIFE_CYCLE;
};

export type RegionStats = RegionBase & {
  site_count?: number;
  cell_count?: number;
  ok_count?: number;
  warning_count?: number;
  error_count?: number;
  critical_count?: number;
  shutdown_count?: number;
  unknown_count?: number;
};

export type Country = {
  country_code: string;
  country_name: string;
  region_count: number;
};

export type IContact = {
  contact_id?: number;
  name: string;
  role?: string;
  address?: string;
  email?: string;
  landline?: string;
  mobile?: string;
  additional_info?: string;
  is_primary?: boolean;
};

export type Contact = RequireAtLeastOne<IContact, 'mobile' | 'landline'>;

export type CellRequest = {
  region?: string;
  site_id?: string;
  oran_split: O_RAN_SPLIT | string;
  lifecycle: LIFE_CYCLE;
  cell_ref?: string | undefined;
  orientation: string;
  ran_type?: string;
  placement?: string;
};

export type UpdateCellRequest = {
  cell_ref?: string;
  site_id?: number;
  orientation?: ORIENTATION;
  oran_split?: O_RAN_SPLIT;
  lifecycle?: LIFE_CYCLE;
};
export type UpdateNodeRequest = {
  has_manifest?: boolean;
  lifecycle?: LIFE_CYCLE;
  node_serial_no?: string;
  site_id?: number;
  version?: string;
  node_type?: string;
  node_id?: string;
  roles?: string[];
  orientation?: string;
};
export type Plmn = {
  mcc?: string;
  mnc?: string;
  country_code?: string | null;
  operator_code?: string;
  operator?: string;
};
export type Cell = {
  cell_ref?: string;
  oran_split?: string;
  country_code?: string;
  country_name?: string;
  region_id?: number;
  region_code?: string;
  region_name?: string;
  site_id?: number;
  site_name?: string;
  latitude?: number;
  longitude?: number;
  orientation?: string;
  lifecycle?: string;
  ran_type?: string;
  bands: string[];
  plmns: Plmn[];
  cell_lifecycle_num?: number;
  status: STATUS;
  cell_status_num?: number;
  trouble_score?: number;
  checkForDevMode: boolean;
  cell_alarms?: AlarmDetails;
};
export type CellType = Cell;

export type Node = {
  node_id: string;
  node_type: NODE_TYPE;
  nodes_in_cell?: string;
  cell_refs?: [string];
  lifecycle: LIFE_CYCLE;
  version?: string;
  roles: List<ROLE_OF_NODE>;
  site_id: number;
  site_name: string;
  site_address: string;
  latitude: number;
  longitude: number;
  orientation?: ORIENTATION;
  location_error?: number;
  node_location?: NodeLocation;
  node_serial_no: string;
  status: STATUS;
  trouble_score: number;
  node_alarms: AlarmDetails;
};
export type NodeType = Node;

export type NodeLocation = {
  latitude: number;
  longitude: number;
  altitude?: number;
};

export type NodeRequest = {
  has_manifest?: boolean;
  lifecycle: LIFE_CYCLE;
  node_serial_no: string;
  site_id: number;
  version?: string;
  node_type: string;
  node_id?: string;
  roles: string[];
  orientation?: string;
};

export type NodeComponents = {
  component_type: string;
  component_id: string;
  component_address: string;
  manager_instance: string;
};

export type ManagerProps = {
  manager_instance: string;
  component_type: string;
  manager_url: string;
  manager: any;
};

export type NodesManifestProps = {
  cell_lifecycle_num?: number;
  node_serial_no: string;
  node_id: string;
  lifecycle: string;
  site_id: number;
  has_manifest: boolean;
  country_code?: string;
  country_name?: string;
  region_id?: number;
  region_code?: string;
  region_name?: string;
  site_name?: string;
  latitude?: number;
  longitude?: number;
};

export type CellManifest = {
  cell_serial_no: string;
  front_id: string;
  rear_id: string;
  factory_notes: string | null;
  test_result: string | null;
};

export type AirspanManifest = {
  carrier_serial: string;
  psu_serial: string;
  secondary_psu_serial: string;
  rf1_serial: string | null;
  rf2_serial: string | null;
  model: string;
  mac_address: string | null;
  xpu_serial: string;
  test_result: string | null;
};

export type BwManifest = {
  kit_serial: string | null;
  npu_serial: string;
  modem_serial: string | null;
  rf1_serial: string | null;
  rf2_serial: string | null;
  model: string | null;
  hostname: string;
  mac_address: string;
  initial_config_filepath: string | null;
  calibration_filepath: string | null;
  calibration_version: string | null;
  test_result: string;
};

export type DalManifest = {
  board_serial: string;
  board_test_result: string;
  particle_device: string;
  rf1_serial: string;
  rf2_serial: string;
  rf1_test_result: string | null;
  rf2_test_result: string | null;
};

export type FibrolanManifest = {
  switch_serial_no: string;
  mac_address: string | null;
  test_result: string | null;
};

// Component types
export type Component = {
  id: string;
  mods: any[];
  owner: string;
  status: string;
  src_env: string | null;
  version: string;
  copy_env: string | null;
  location: string;
  testruns: any | null;
  changelogs: any | null;
  created_at: string;
  product_id: string;
  updated_at: string;
  serial_number: string;
};

export type Mainboard = Component & {
  label: string | null;
  kak_csk: string;
  hw_revision: string;
  mac_address: string;
  pmic_version: string;
  bootloader_version: string;
  edgeq_serial_number: string;
};

export type Nic = Component & {
  test1: string;
  test2: string;
  test3: string;
  test4: string;
};

export type Rffe = Component & {
  pcb: string;
  label: string;
  test1: string;
  test2: string;
  test3: string;
  test4: string;
  lna_type: string;
};

export type Psu = Component & {
  test1: string;
  test2: string;
  test3: string;
  test4: string;
};

// Manifest structure
export type RadioManifestContents = {
  mainboard: Mainboard;
  nic: Nic;
  rffe: (Rffe | Record<string, never>)[];
  psu: Psu;
};

// Main RadioManifest type
export type RadioManifest = {
  serial_no: string;
  mac_address: string;
  change_no: number;
  model: string;
  version: string;
  product_status: string;
  manifest: RadioManifestContents;
  supported_bands: any[];
  created_at: string;
};

export type StreetCellManifestProps = {
  cell_manifest: CellManifest;
  airspan_manifest: AirspanManifest;
  bw_manifest: BwManifest;
  dal_manifest: DalManifest;
  fibrolan_manifest: FibrolanManifest;
};

export type StreetCellConfiguration = {
  monitorState?: MONITOR_STATE;
  fibrolan_switch_monitored?: string;
  bw_mmwave_monitored?: string;
  airspan_node_id?: string;
  particle_device?: string;
  fibrolan_ip_address?: string;
  bluwireless_ip_address?: string;
};

export type AlarmSeverityCounts = {
  major?: { count: number };
  minor?: { count: number };
  critical?: { count: number };
  warning?: { count: number };
  none?: { count: number };
};

export type AlarmDetails = {
  total?: number;
  severity?: AlarmSeverityCounts;
  error?: Record<string, string>;
};
export type CellAlarmsProps = {
  alarmDetails: AlarmDetails;
  cell_ref?: string;
  node_id?: string;
};
export type StatusKeys =
  | 'ok_count'
  | 'warning_count'
  | 'error_count'
  | 'critical_count'
  | 'shutdown_count'
  | 'unknown_count';

export type Status = 'OK' | 'WARNING' | 'ERROR' | 'CRITICAL' | 'SHUTDOWN' | 'UNKNOWN';

export type NetworkManifestForm = {
  serial_no: string;
  name: string;
  description: string;
  node_type: string;
  component_type: string;
  model: string;
  version: string;
  ip_address: string;
  location: string;
  region_code: string;
  access_method: string;
  credentials: {
    username: string;
    auth: string;
    priv: string;
    auth_protocol: string;
    priv_protocol: string;
  };
};
