import { expect } from 'vitest';
import { bitsToOptimal, compactObject, timeticksToTime } from './helpers';
describe('compactObject', () => {
  test('should remove falsy values from an object', () => {
    const obj = {
      name: '<PERSON>',
      age: 30,
      address: null,
      email: '',
      isActive: false,
      hobbies: ['reading', null, 'swimming'],
      nested: {
        prop1: 'value1',
        prop2: null,
      },
    };
    const result = compactObject(obj);
    expect(result).toEqual({
      name: '<PERSON>',
      age: 30,
      hobbies: ['reading', 'swimming'],
      nested: {
        prop1: 'value1',
      },
    });
  });
  test('should remove falsy values from an array', () => {
    const arr = ['apple', '', 'banana', null, 'cherry'];
    const result = compactObject(arr);
    expect(result).toEqual(['apple', 'banana', 'cherry']);
  });
  test('should return an empty object if the input is an object with only falsy values', () => {
    const obj = {
      prop1: null,
      prop2: '',
      prop3: false,
    };
    const result = compactObject(obj);
    expect(result).toEqual({});
  });
  test('should return an empty array if the input is an array with only falsy values', () => {
    const arr = [null, '', false];
    const result = compactObject(arr);
    expect(result).toEqual([]);
  });
});

describe('timeticksToTime', () => {
  it('should convert timeticks in hundredths of a second to the correct time format', () => {
    expect(timeticksToTime(0)).toBe('00 days, 00:00:00');
    expect(timeticksToTime(50)).toBe('00 days, 00:00:00'); // 50 timeticks is rounded down to 0 seconds
    expect(timeticksToTime(100)).toBe('00 days, 00:00:01'); // 1 second
    expect(timeticksToTime(6000)).toBe('00 days, 00:01:00'); // 1 minute
    expect(timeticksToTime(360000)).toBe('00 days, 01:00:00'); // 1 hour
    expect(timeticksToTime(8640000)).toBe('01 days, 00:00:00'); // 1 day
    expect(timeticksToTime(9006100)).toBe('01 days, 01:01:01'); // 1 day, 1 hour, 1 minute, and 1 second
  });

  it('should handle large input values', () => {
    expect(timeticksToTime(3155695200)).toBe('365 days, 05:49:12');
    expect(timeticksToTime(123456789)).toBe('14 days, 06:56:07');
  });

  it('should pad single digit values with a zero', () => {
    expect(timeticksToTime(432000)).toBe('00 days, 01:12:00');
    expect(timeticksToTime(6100)).toBe('00 days, 00:01:01');
    expect(timeticksToTime(8646100)).toBe('01 days, 00:01:01');
  });
});

describe('bitsToOptimal', () => {
  it('should return correct values for given inputs', () => {
    expect(bitsToOptimal(0)).toBe('0 Bytes');
    expect(bitsToOptimal(500)).toBe('500 Bytes');
    expect(bitsToOptimal(1000)).toBe('1 Kb');
    expect(bitsToOptimal(1500)).toBe('1.5 Kb');
    expect(bitsToOptimal(1000000)).toBe('1 Mb');
    expect(bitsToOptimal(1000000000)).toBe('1 Gb');
    expect(bitsToOptimal(1234567, 3)).toBe('1.235 Mb'); // 3 decimal places
    expect(bitsToOptimal(1234567, 0)).toBe('1 Mb'); // No decimal places
  });
});
