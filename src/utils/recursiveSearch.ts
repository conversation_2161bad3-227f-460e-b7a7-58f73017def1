type RecursiveSearchObject = { [key: string]: any };
export const recursiveSearch = (
  obj: RecursiveSearchObject | null | undefined,
  keys_to_ignore: Array<string> = [],
  resultsVal: any[] = [],
  resultsKey: string[] = [],
  prefix = ''
): RecursiveSearchObject => {
  if (obj === null || typeof obj === 'undefined') {
    return {}; // Return an empty object if obj is null or undefined
  }

  const result: RecursiveSearchObject = {};
  const rKeys: string[] = resultsKey;
  const rvals: any[] = resultsVal;

  Object.keys(obj).forEach((key) => {
    const value = obj[key];
    const key_name = get_key(key.toString(), keys_to_ignore, prefix);
    if (typeof value !== 'object') {
      rvals.push(value);
      rKeys.push(key_name);
    } else if (typeof value === 'object') {
      recursiveSearch(value, keys_to_ignore, rvals, rKeys, key_name);
    }
  });
  const separator = ' | ';

  rKeys.forEach((k: string, i: number) => {
    if (Object.prototype.hasOwnProperty.call(result, k)) {
      result[k] += separator + rvals[i];
    } else {
      result[k] = rvals[i];
    }
  });
  return result;
};

function isNumeric(value: string) {
  return /^\d+$/.test(value);
}

function get_key(value: string, keys_to_ignore: Array<string>, prefix: string) {
  if (isNumeric(value)) {
    return prefix;
  }
  for (const entry of keys_to_ignore) {
    if (value === entry) {
      return prefix;
    }
  }
  return prefix ? `${prefix}.${value}` : value;
}
