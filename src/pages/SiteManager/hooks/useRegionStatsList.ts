import { useQuery } from '@tanstack/react-query';
import { RegionStats } from '../../../types/InventoryManager.type';
import { getRegionStatsList } from './../../../services/inventoryManager';

export default function useRegionStatsList(cc?: string) {
  return useQuery<RegionStats[], Error>({
    queryKey: ['regionsStats', cc],
    queryFn: () => getRegionStatsList(cc),
    refetchInterval: 30000,
    enabled: !!cc,
  });
}
