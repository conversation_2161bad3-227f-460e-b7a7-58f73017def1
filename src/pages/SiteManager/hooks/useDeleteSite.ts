import { useToast } from '@chakra-ui/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { deleteSite } from '../../../services/inventoryManager';
import { Site } from '../../../types/InventoryManager.type';
interface Error {
  message: string[];
  statusCode: number;
}
export default function useDeleteSite() {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: (site_id: Site['site_id']) => deleteSite(site_id),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['sites'] });
      toast({
        title: 'Site deleted.',
        description: `${data.name} has been deleted successfully.`,
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `${(error as AxiosError<Error>)?.response?.data?.message}`,
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
  });
}
