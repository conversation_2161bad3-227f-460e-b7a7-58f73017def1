import { useToast } from '@chakra-ui/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { updateSiteById } from '../../../services/inventoryManager';
import { Site } from '../../../types/InventoryManager.type';

interface Error {
  message: string[];
  statusCode: number;
}
export default function useUpdateSiteById() {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: ({ site_id, site }: { site_id: Site['site_id']; site: Omit<Site, 'region_id'> }) =>
      updateSiteById({ site_id, site }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['sites'] });
      toast({
        title: 'Site updated.',
        description: 'Site has been updated successfully.',
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `${(error as AxiosError<Error>)?.response?.data?.message}`,
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
  });
}
