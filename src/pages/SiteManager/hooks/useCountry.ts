import { useQuery } from '@tanstack/react-query';
import { getCountryList } from '../../../services/inventoryManager';
import { Country } from '../../../types/InventoryManager.type';

export default function useCountryList(country_code?: string) {
  const countryList = useQuery<Country[], Error>({
    queryKey: ['countries'],
    queryFn: () => getCountryList(),
    retry: 2,
  });
  if (country_code) {
    return countryList.data?.find((country) => country.country_code === country_code);
  }
  return countryList;
}
