import { useToast } from '@chakra-ui/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { deleteRegion } from '../../../services/inventoryManager';
import { Region } from '../../../types/InventoryManager.type';

interface Error {
  message: string[];
  statusCode: number;
}

export default function useDeleteRegion() {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: (region_code: Region['region_code']) => deleteRegion(region_code),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['regions'] });
      queryClient.invalidateQueries({ queryKey: ['sites'] });
      toast({
        title: 'Region deleted.',
        description: `${data.region_name} has been deleted successfully.`,
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `${(error as AxiosError<Error>)?.response?.data?.message}`,
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
  });
}
