import { useToast } from '@chakra-ui/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { updateRegionByRegionCode } from '../../../services/inventoryManager';
import { Region } from '../../../types/InventoryManager.type';

interface Error {
  message: string[];
  statusCode: number;
}
export default function useUpdateRegionByRegionCode() {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: ({
      region_code,
      region,
    }: {
      region_code: Region['region_code'];
      region: Omit<Region, 'region_code'>;
    }) => updateRegionByRegionCode({ region_code, region }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['regions'] });
      toast({
        title: 'Region updated.',
        description: 'Region has been updated successfully.',
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `${(error as AxiosError<Error>)?.response?.data?.message}`,
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
  });
}
