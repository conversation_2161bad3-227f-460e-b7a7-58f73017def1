import { useToast } from '@chakra-ui/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { postRegion } from '../../../services/inventoryManager';
import { CreateRegionSchema } from '../schema';

export default function usePostRegion() {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: (data: CreateRegionSchema) => postRegion(data),
    onSuccess: () => {
      // Invalidate both queries separately to ensure proper cache invalidation
      queryClient.invalidateQueries({ queryKey: ['regions'] });
      queryClient.invalidateQueries({ queryKey: ['sites'] });
      toast({
        title: 'Region created.',
        description: 'Region has been created successfully.',
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: (error) => {
      toast({
        title: `Error ${error}`,
        description: 'Something went wrong.',
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
  });
}
