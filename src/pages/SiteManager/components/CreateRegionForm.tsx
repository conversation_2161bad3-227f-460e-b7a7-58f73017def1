import {
  Box,
  Button,
  ButtonGroup,
  Checkbox,
  Divider,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Heading,
  Input,
  Select,
  Stack,
  Textarea,
  Tooltip,
  useColorModeValue,
} from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { CreateRegionSchema, postRegionSchema } from '../schema';
import Loader from '../../../components/loader/Loader';
import { useCallback, useEffect, useState, useMemo } from 'react';
import usePostRegion from '../hooks/usePostRegion';
import useUpdateRegionByRegionCode from '../hooks/useUpdateRegionByRegionCode';
import { LIFE_CYCLE, lifecycleToApiValue } from '../../../data/constants';
import { Region } from '../../../types/InventoryManager.type';
import { InfoIcon } from '@chakra-ui/icons';

interface CreateRegionFormProps {
  onClose: () => void;
  isEdit?: boolean;
  defaultValues?: Partial<CreateRegionSchema & { region_id: number }>;
}

const COUNTRY_TO_REGION_PREFIX: Record<string, string> = {
  GBR: 'GB',
  USA: 'US',
};

const REGION_TYPES: Record<string, string> = {
  venue: 'Venue',
  dc: 'Data Center',
  cloud: 'Cloud',
};

const CreateRegionForm: React.FC<CreateRegionFormProps> = ({ onClose, isEdit = false, defaultValues }) => {
  const [isLoading, setIsLoading] = useState(false);
  const createRegion = usePostRegion();
  const updateRegion = useUpdateRegionByRegionCode();

  const formDefaultValues = useMemo(() => {
    if (isEdit && defaultValues) {
      return {
        ...defaultValues,
        discovery: (defaultValues as any)?.discovery ?? true,
        region_type: (defaultValues as any)?.region_type ?? 'venue',
        lifecycle:
          defaultValues.lifecycle === null
            ? LIFE_CYCLE.UNSET
            : (defaultValues.lifecycle as LIFE_CYCLE) || LIFE_CYCLE.FACTORY,
      };
    }
    return {
      discovery: true,
      lifecycle: LIFE_CYCLE.FACTORY,
      region_type: 'venue',
    };
  }, [isEdit, defaultValues]);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting, isValid },
    reset,
    setValue,
    watch,
  } = useForm<CreateRegionSchema>({
    resolver: zodResolver(postRegionSchema),
    mode: 'onChange',
    defaultValues: formDefaultValues,
  });

  const countryCode = watch('country_code');
  const regionCode = watch('region_code');
  const discovery = watch('discovery');
  const regionType = watch('region_type');

  const onSubmit = async (data: CreateRegionSchema) => {
    const formData = {
      ...data,
      lifecycle: lifecycleToApiValue(data.lifecycle as LIFE_CYCLE),
    };

    if (isEdit) {
      updateRegion.mutate({
        region_code: defaultValues?.region_code as string,
        region: formData as unknown as Omit<Region, 'region_code'>,
      });
      return;
    }
    createRegion.mutate(formData as any);
  };

  useEffect(() => {
    (createRegion?.isSuccess || updateRegion?.isSuccess) && onClose();
  }, [createRegion, updateRegion, onClose]);

  const handleCancel = () => {
    reset();
    onClose();
  };

  const determineNewRegionCode = (
    newCountryCode: string,
    currentRegionCode: string | undefined
  ): string | undefined => {
    const expectedPrefix = COUNTRY_TO_REGION_PREFIX[newCountryCode];

    if (expectedPrefix) {
      if (currentRegionCode) {
        return currentRegionCode.startsWith(expectedPrefix) ? currentRegionCode : '';
      }
      return expectedPrefix;
    }

    return currentRegionCode ? '' : undefined;
  };

  const handleCountryCodeChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const rawValue = e.target.value;
      const newCountryCode = rawValue
        .toUpperCase()
        .replace(/[^A-Z]/g, '')
        .slice(0, 3);

      // Update the country code
      setValue('country_code', newCountryCode, {
        shouldValidate: true,
        shouldDirty: true,
      });

      // Determine and update the region code if necessary
      const newRegionCode = determineNewRegionCode(newCountryCode, regionCode);
      if (newRegionCode !== regionCode) {
        setValue('region_code', newRegionCode || '', {
          shouldValidate: true,
          shouldDirty: true,
        });
      }
    },
    [regionCode, setValue]
  );

  // Handler for region_code changes
  const handleRegionCodeChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const regionCode = e.target.value
        .toUpperCase()
        .replace(/[^A-Z0-9]/g, '')
        .slice(0, 6);
      setValue('region_code', regionCode, { shouldValidate: true, shouldDirty: true });

      if (regionCode) {
        const prefix = regionCode.slice(0, 2);
        const correspondingCountry = Object.keys(COUNTRY_TO_REGION_PREFIX).find(
          (key) => COUNTRY_TO_REGION_PREFIX[key] === prefix
        );

        if (correspondingCountry) {
          if (countryCode !== correspondingCountry) {
            setValue('country_code', correspondingCountry, { shouldValidate: true, shouldDirty: true });
          }
        } else {
          setValue('country_code', '', { shouldValidate: true, shouldDirty: true });
        }
      } else {
        if (countryCode) {
          setValue('country_code', '', { shouldValidate: true, shouldDirty: true });
        }
      }
    },
    [countryCode, setValue]
  );

  return (
    <Box
      as="form"
      bg="bg-surface"
      boxShadow={useColorModeValue('sm', 'sm-dark')}
      borderRadius="lg"
      onSubmit={handleSubmit(onSubmit)}
    >
      <Stack
        spacing="5"
        px={{
          base: '4',
          md: '6',
        }}
        py={{
          base: '5',
          md: '6',
        }}
      >
        <Heading size={'md'}>{isEdit ? 'Update Region' : 'Create New Region'}</Heading>
        <Divider />
        {isSubmitting || isLoading ? (
          <Loader>Submitting...</Loader>
        ) : (
          <>
            <Stack spacing="6">
              <FormControl isInvalid={!!errors.region_type} isRequired>
                <Flex alignItems="center" mb={1}>
                  <FormLabel htmlFor="region_type" mb={0}>
                    Region Type
                  </FormLabel>

                  <Tooltip label="Select a region type from the list" placement="top-start">
                    <InfoIcon color="gray.500" cursor="pointer" boxSize="1em" />
                  </Tooltip>
                </Flex>

                <Select
                  id="region_type"
                  data-testid="region-type-select"
                  size="md"
                  value={regionType}
                  {...register('region_type', {
                    required: 'Region type is required',
                  })}
                >
                  {Object.entries(REGION_TYPES).map(([value, label]) => (
                    <option key={value} value={value}>
                      {label}
                    </option>
                  ))}
                </Select>

                <FormErrorMessage data-testid="region-type-error">{errors.region_type?.message}</FormErrorMessage>
              </FormControl>

              <Stack
                direction={{
                  base: 'column',
                  md: 'row',
                }}
                spacing="6"
              >
                <FormControl isInvalid={!!errors.region_code} isRequired>
                  <Flex alignItems="center" mb={1}>
                    <FormLabel htmlFor="region_code" mb={0}>
                      Region Code
                    </FormLabel>
                    <Tooltip
                      label="Region code must start with 2-letter ISO country code followed by exactly 4-letter code"
                      placement="top-start"
                    >
                      <InfoIcon color="gray.500" cursor="pointer" boxSize="1em" />
                    </Tooltip>
                  </Flex>
                  <Input
                    id="region_code"
                    data-testid="region-code-input"
                    placeholder="e.g., GBMARL"
                    maxLength={6}
                    {...register('region_code', {
                      onChange: handleRegionCodeChange,
                    })}
                    isDisabled={isEdit}
                  />
                  <FormErrorMessage data-testid="region-code-error">{errors.region_code?.message}</FormErrorMessage>
                </FormControl>

                <FormControl isInvalid={!!errors.region_name} isRequired>
                  <Flex alignItems="center" mb={1}>
                    <FormLabel htmlFor="region_name" mb={0}>
                      Region Name
                    </FormLabel>
                    <Tooltip label="Enter the full name of the region" placement="top-start">
                      <InfoIcon color="gray.500" cursor="pointer" boxSize="1em" />
                    </Tooltip>
                  </Flex>
                  <Input
                    id="region_name"
                    data-testid="region-name-input"
                    placeholder="e.g., Marlow"
                    {...register('region_name')}
                    isDisabled={isEdit}
                  />
                  <FormErrorMessage data-testid="region-name-error">{errors.region_name?.message}</FormErrorMessage>
                </FormControl>
              </Stack>

              <FormControl isInvalid={!!errors.country_code} isRequired>
                <Flex alignItems="center" mb={1}>
                  <FormLabel htmlFor="country_code" mb={0}>
                    Country Code
                  </FormLabel>
                  <Tooltip label="Must be 3-letter ISO country code" placement="top-start">
                    <InfoIcon color="gray.500" cursor="pointer" boxSize="1em" />
                  </Tooltip>
                </Flex>
                <Input
                  id="country_code"
                  data-testid="country-code-input"
                  placeholder="e.g., GBR"
                  maxLength={3}
                  {...register('country_code', {
                    onChange: handleCountryCodeChange,
                  })}
                  isDisabled={isEdit}
                />
                <FormErrorMessage data-testid="country-code-error">{errors.country_code?.message}</FormErrorMessage>
              </FormControl>

              <Stack
                direction={{
                  base: 'column',
                  md: 'row',
                }}
                spacing="6"
              >
                <FormControl isInvalid={!!errors.latitude} isRequired>
                  <FormLabel htmlFor="latitude">Latitude</FormLabel>
                  <Input
                    id="latitude"
                    data-testid="latitude-input"
                    type="number"
                    step="0.000001"
                    placeholder="-90.000000 to 90.000000"
                    {...register('latitude', {
                      onChange: (e) => {
                        const value = e.target.value;
                        if (value === '') return;
                        const num = parseFloat(value);
                        if (num < -90) setValue('latitude', -90, { shouldValidate: true, shouldDirty: true });
                        if (num > 90) setValue('latitude', 90, { shouldValidate: true, shouldDirty: true });
                      },
                    })}
                  />
                  <FormErrorMessage data-testid="latitude-error">{errors.latitude?.message}</FormErrorMessage>
                </FormControl>

                <FormControl isInvalid={!!errors.longitude} isRequired>
                  <FormLabel htmlFor="longitude">Longitude</FormLabel>
                  <Input
                    id="longitude"
                    data-testid="longitude-input"
                    type="number"
                    step="0.000001"
                    placeholder="-180.000000 to 180.000000"
                    {...register('longitude', {
                      onChange: (e) => {
                        const value = e.target.value;
                        if (value === '') return;
                        const num = parseFloat(value);
                        if (num < -180) setValue('longitude', -180, { shouldValidate: true, shouldDirty: true });
                        if (num > 180) setValue('longitude', 180, { shouldValidate: true, shouldDirty: true });
                      },
                    })}
                  />
                  <FormErrorMessage data-testid="longitude-error">{errors.longitude?.message}</FormErrorMessage>
                </FormControl>
              </Stack>

              <FormControl isInvalid={!!errors.radius} isRequired>
                <FormLabel htmlFor="radius">Radius (m)</FormLabel>
                <Input
                  id="radius"
                  data-testid="radius-input"
                  type="number"
                  min="0"
                  step="1"
                  placeholder="Enter radius in meters"
                  {...register('radius', {
                    onChange: (e) => {
                      const value = e.target.value;
                      if (value === '') return;
                      const num = parseFloat(value);
                      if (num < 0) e.target.value = '0';
                    },
                  })}
                />
                <FormErrorMessage data-testid="radius-error">{errors.radius?.message}</FormErrorMessage>
              </FormControl>

              <FormControl isInvalid={!!errors.description}>
                <FormLabel htmlFor="description">Description</FormLabel>
                <Textarea
                  id="description"
                  data-testid="description-input"
                  placeholder="Enter region description"
                  rows={3}
                  {...register('description')}
                />
                <FormErrorMessage data-testid="description-error">{errors.description?.message}</FormErrorMessage>
              </FormControl>

              <FormControl isInvalid={!!errors.lifecycle} isRequired>
                <Flex alignItems="center" mb={1}>
                  <FormLabel htmlFor="lifecycle" mb={0}>
                    Lifecycle
                  </FormLabel>

                  <Tooltip
                    label="When you assign a lifecycle status to a region, all devices within that region inherit the same status."
                    placement="top-start"
                  >
                    <InfoIcon color="gray.500" cursor="pointer" boxSize="1em" />
                  </Tooltip>
                </Flex>

                <Select
                  id="lifecycle"
                  data-testid="lifecycle-select"
                  size="md"
                  {...register('lifecycle', {
                    required: 'Lifecycle is required',
                  })}
                >
                  {Object.entries(LIFE_CYCLE).map(([key, value]) => (
                    <option key={value} value={value}>
                      {key}
                    </option>
                  ))}
                </Select>
                <FormErrorMessage data-testid="lifecycle-error">{errors.lifecycle?.message}</FormErrorMessage>
              </FormControl>

              <FormControl isInvalid={!!errors.discovery} mb={4}>
                <FormLabel htmlFor="discovery">Discovery</FormLabel>

                <Checkbox
                  id="discovery"
                  data-testid="discovery-checkbox"
                  {...register('discovery')}
                  isChecked={discovery}
                >
                  Enable Region Discovery
                </Checkbox>
                <FormErrorMessage data-testid="discovery-error">{errors.discovery?.message}</FormErrorMessage>
              </FormControl>
            </Stack>
          </>
        )}
      </Stack>
      <Flex justify="flex-end" px="6" py="4" borderTopWidth="1px" gap="3">
        <ButtonGroup spacing="3">
          <Button
            onClick={handleCancel}
            variant="ghost"
            color="red"
            bg="red.200"
            id="cancel"
            data-testid="cancel-button"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="primary"
            isLoading={isSubmitting || isLoading}
            loadingText="Submitting"
            data-testid="submit-button"
          >
            {isEdit ? 'Update Region' : 'Create Region'}
          </Button>
        </ButtonGroup>
      </Flex>
    </Box>
  );
};

export default CreateRegionForm;
