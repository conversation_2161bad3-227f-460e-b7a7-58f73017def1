import { EditIcon, EmailIcon, Icon, PhoneIcon } from '@chakra-ui/icons';
import { Avatar, Box, Card, Heading, HStack, Stack, Text } from '@chakra-ui/react';

import { Contact } from '../../../types/InventoryManager.type';
const Contacts = ({ contact }: { contact: Contact }) => {
  return (
    <Box>
      <Card
        boxShadow={'md'}
        p={{
          base: '6',
          md: '8',
        }}
      >
        <Stack
          direction={{
            base: 'column',
            md: 'row',
          }}
        >
          <Avatar borderWidth="4px" insetEnd="3" />
          <Box width={'full'}>
            <Heading pt="2" fontSize="sm">
              {contact.name}
            </Heading>
            <Text mt="1" fontWeight="medium">
              {contact.role}
            </Text>
            <Stack spacing="1" mt="2">
              <HStack fontSize="sm">
                <Icon alignSelf="start" as={EditIcon} color="gray.500" />
                <Text> {contact.address}</Text>
              </HStack>
              <HStack fontSize="sm">
                <Icon as={PhoneIcon} color="gray.500" />
                <Text>{contact.landline || contact.mobile}</Text>
              </HStack>
              <HStack fontSize="sm">
                <Icon as={EmailIcon} color="gray.500" />
                <Text>{contact.email}</Text>
              </HStack>
            </Stack>
          </Box>
        </Stack>
      </Card>
    </Box>
  );
};

export default Contacts;
