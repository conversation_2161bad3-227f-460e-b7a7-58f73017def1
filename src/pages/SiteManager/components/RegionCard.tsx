import {
  Box,
  Button,
  Card,
  CardBody,
  CardHeader,
  Checkbox,
  Flex,
  Heading,
  Text,
  useColorModeValue,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalCloseButton,
  Icon,
  Stack,
  StackDivider,
  Link,
  Select,
} from '@chakra-ui/react';
import { DeleteIcon, ExternalLinkIcon } from '@chakra-ui/icons';
import { Region } from '../../../types/InventoryManager.type';
import { AUTH_TOKEN_KEY, LIFE_CYCLE, READ_WRITE_ACCESS_ROLES } from '../../../data/constants';
import { EditIcon } from '@chakra-ui/icons';
import useLogin from '../../../hooks/useLogin';
import { UnstyledTable } from '../../../components/nodeComponents/airspan/utils';
import CreateRegionForm from './CreateRegionForm';
import useDeleteRegion from '../hooks/useDeleteRegion';
import { useState, useEffect } from 'react';

export const RegionCard = ({ region }: { region: Region }) => {
  const { region_name, region_code, region_type, radius, latitude, longitude, description, country_name, lifecycle } =
    region;

  const [selectedLifecycle, setSelectedLifecycle] = useState<LIFE_CYCLE>(
    lifecycle === null ? LIFE_CYCLE.UNSET : (lifecycle as LIFE_CYCLE) || LIFE_CYCLE.FACTORY
  );

  const [isDiscovery, setIsDiscovery] = useState<boolean>(
    (region as Region).discovery !== undefined ? (region as Region).discovery : true
  );

  useEffect(() => {
    const newLifecycle =
      region.lifecycle === null ? LIFE_CYCLE.UNSET : (region.lifecycle as LIFE_CYCLE) || LIFE_CYCLE.FACTORY;

    setSelectedLifecycle(newLifecycle);
  }, [region, region.lifecycle]);

  useEffect(() => {
    setIsDiscovery((region as Region).discovery !== undefined ? (region as Region).discovery : true);
  }, [region]);

  const { isOpen, onOpen, onClose } = useDisclosure();
  const { checkApplicationAccess } = useLogin(AUTH_TOKEN_KEY);
  const checkRoleAccess = checkApplicationAccess(READ_WRITE_ACCESS_ROLES);

  const deleteRegion = useDeleteRegion();

  return (
    <>
      <Card>
        <CardHeader>
          <Flex justifyContent="space-between" alignItems="center">
            <Heading
              as="h1"
              size="lg"
              width="100%"
              textAlign="center"
              color={useColorModeValue('gray.700', 'white')}
              fontWeight="bold"
              data-testid="region-heading"
            >
              Region
            </Heading>
            {checkRoleAccess && (
              <Flex justifyContent="flex-end" alignItems="center" gridGap="2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => onOpen()}
                  leftIcon={<Icon as={EditIcon} color="gray.400" marginStart="-1" />}
                >
                  Edit
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => deleteRegion.mutate(region_code)}
                  leftIcon={<Icon as={DeleteIcon} color="gray.400" marginStart="-1" />}
                >
                  Delete
                </Button>
              </Flex>
            )}
          </Flex>
        </CardHeader>
        <CardBody>
          <Stack divider={<StackDivider borderColor={useColorModeValue('gray.300', 'white')} />} spacing="4">
            <Box data-testid="region-code">
              <Text fontSize="2xl" fontWeight="bold">
                {region_code} - {region_name}
              </Text>
            </Box>
            <Box data-testid="region-description">
              <Heading size="xs" textTransform="uppercase">
                Description
              </Heading>
              <Text pt="2" fontSize="sm">
                {description}
              </Text>
            </Box>
            <Box data-testid="region-description">
              <Heading size="xs" textTransform="uppercase">
                Region type
              </Heading>
              <Text pt="2" fontSize="sm">
                {region_type}
              </Text>
            </Box>

            <Box data-testid="region-address">
              <Heading size="xs" textTransform="uppercase">
                Address
              </Heading>
              <Flex direction="column">
                <Flex gap="10">
                  <Box alignItems="center">
                    <UnstyledTable
                      tableData={[
                        { key: 'latitude', value: latitude },
                        { key: 'longitude', value: longitude },
                      ]}
                    />
                  </Box>
                  <Box>
                    <Link
                      color="teal.500"
                      ml="3"
                      textTransform="lowercase"
                      href={`https://maps.google.com/?q=${latitude},${longitude}`}
                      isExternal
                    >
                      map <ExternalLinkIcon mx="2px" />
                    </Link>
                  </Box>
                </Flex>
                <Flex>
                  <Text fontSize="sm" mr="6" ml="1">
                    Radius:
                  </Text>
                  <Text fontSize="sm">{radius}m</Text>
                </Flex>
              </Flex>
            </Box>

            <Box data-testid="region-country">
              <Text fontSize="sm" mb="2">
                <strong>Country:</strong> {country_name}
              </Text>
            </Box>

            {/* Lifecycle Dropdown */}
            {checkRoleAccess && (
              <Box data-testid="region-lifecycle">
                <Heading size="xs" textTransform="uppercase" mb="2">
                  Lifecycle Status
                </Heading>
                <Flex alignItems="center">
                  <Select value={selectedLifecycle} size="sm" maxWidth="300px" isDisabled={true}>
                    {Object.entries(LIFE_CYCLE).map(([key, value]) => (
                      <option key={key} value={value}>
                        {key}
                      </option>
                    ))}
                  </Select>
                </Flex>
              </Box>
            )}

            {/* Discovery Checkbox */}
            {checkRoleAccess && (
              <Box data-testid="region-discovery">
                <Heading size="xs" textTransform="uppercase" mb="2">
                  Discovery
                </Heading>
                <Flex alignItems="center">
                  <Checkbox isChecked={isDiscovery} isDisabled={true}>
                    Enable Region Discovery
                  </Checkbox>
                </Flex>
              </Box>
            )}
          </Stack>
        </CardBody>
      </Card>

      <Modal isOpen={isOpen} onClose={onClose} size="lg" isCentered>
        <ModalOverlay />
        <ModalContent>
          <ModalCloseButton />
          <CreateRegionForm
            onClose={onClose}
            defaultValues={{
              ...region,
              region_type:
                (region.region_type as any) in ['venue', 'dc', 'cloud']
                  ? (region.region_type as 'venue' | 'dc' | 'cloud')
                  : 'venue',
            }}
            isEdit={true}
          />
        </ModalContent>
      </Modal>
    </>
  );
};

export default RegionCard;
