import { DeleteIcon, EditIcon, ExternalLinkIcon, Icon } from '@chakra-ui/icons';
import {
  Accordion,
  AccordionButton,
  AccordionIcon,
  AccordionItem,
  AccordionPanel,
  Box,
  Button,
  Card,
  CardBody,
  CardHeader,
  Flex,
  Heading,
  HStack,
  Link,
  Modal,
  ModalCloseButton,
  ModalContent,
  ModalOverlay,
  Stack,
  StackDivider,
  Text,
  useColorModeValue,
  useDisclosure,
} from '@chakra-ui/react';

import { AUTH_TOKEN_KEY, READ_WRITE_ACCESS_ROLES } from '../../../data/constants';
import useLogin from '../../../hooks/useLogin';
import { Contact, Site, Country } from '../../../types/InventoryManager.type';
import useContactList from '../hooks/useContactList';
import useCountry from '../hooks/useCountry';
import useDeleteSite from '../hooks/useDeleteSite';
import useRegion from '../hooks/useRegion';
import Contacts from './Contacts';
import CreateSiteForm from './CreateSiteForm';
import { UnstyledTable } from '../../../components/nodeComponents/airspan/utils';

export const SiteCard = ({ site }: { site: Site }) => {
  const {
    site_id,
    name,
    description,
    region_id,
    address,
    additional_info,
    latitude,
    longitude,
    country_code,
    site_cells,
    site_nodes,
  } = site;

  const { data: region } = useRegion(region_id);
  const country = useCountry(country_code) as Country;
  const { data: contacts } = useContactList(site_id);
  const deleteSite = useDeleteSite();
  const { checkApplicationAccess } = useLogin(AUTH_TOKEN_KEY);
  const checkRoleAccess = checkApplicationAccess(READ_WRITE_ACCESS_ROLES);
  const { isOpen, onOpen, onClose } = useDisclosure();

  return (
    <>
      <Card>
        <CardHeader>
          <Flex justifyContent="space-between" alignItems="center">
            <Heading
              as="h1"
              size="lg"
              width="100%"
              textAlign="center"
              color={useColorModeValue('gray.700', 'white')}
              fontWeight="bold" // Ensure the font weight remains bold if needed
            >
              <Text>Site</Text>
            </Heading>
            {checkRoleAccess && (
              <Flex justifyContent="flex-end" alignItems="center" gridGap="2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => onOpen()}
                  leftIcon={<Icon as={EditIcon} color="gray.400" marginStart="-1" />}
                >
                  Edit
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => deleteSite.mutate(site_id)}
                  leftIcon={<Icon as={DeleteIcon} color="gray.400" marginStart="-1" />}
                >
                  Delete
                </Button>
              </Flex>
            )}
          </Flex>
        </CardHeader>
        <CardBody>
          <Stack divider={<StackDivider borderColor={useColorModeValue('gray.300', 'white')} />} spacing="4">
            <Box data-testid="site-name">
              <Text fontSize="2xl" fontWeight="bold">
                {name}
              </Text>
            </Box>
            <Box data-testid="site-description">
              <Heading size="xs" textTransform="uppercase">
                Description
              </Heading>
              <Text pt="2" fontSize="sm">
                {description}
              </Text>
            </Box>

            <Box data-testid="site-address">
              <Heading size="xs" textTransform="uppercase">
                Address
              </Heading>
              <Flex direction="column">
                <Flex gap="10">
                  <Box alignItems="center">
                    <UnstyledTable
                      tableData={[
                        { key: 'latitude', value: latitude },
                        { key: 'longitude', value: longitude },
                      ]}
                    />
                  </Box>
                  <Box>
                    <Link
                      color="teal.500"
                      ml="3"
                      textTransform="lowercase"
                      href={`https://maps.google.com/?q=${latitude},${longitude}`}
                      isExternal
                    >
                      map <ExternalLinkIcon mx="2px" />
                    </Link>
                  </Box>
                </Flex>
                <Flex>
                  <Text fontSize="sm" mr="6" ml="1">
                    Address:
                  </Text>
                  <Text fontSize="sm">{address}</Text>
                </Flex>
              </Flex>
            </Box>
            <HStack>
              <Box w="50%" data-testid="site-country">
                <Heading size="xs" textTransform="uppercase">
                  Country
                </Heading>
                <Text pt="2" fontSize="sm">
                  {country?.country_name}
                </Text>
              </Box>
            </HStack>
            {additional_info ?? ''?.length > 0 ? (
              <Box data-testid="site-additional-info">
                <Heading size="xs" textTransform="uppercase">
                  Additional Information
                </Heading>
                <Text pt="2" fontSize="sm">
                  {additional_info}
                </Text>
              </Box>
            ) : null}
            <HStack>
              <Box w="50%" data-testid="site-cells-deployed">
                <Heading size="xs" textTransform="uppercase">
                  Cells Deployed
                </Heading>
                <Text pt="2" fontSize="sm">
                  {site_cells?.length}
                </Text>
              </Box>
              <Box w="50%" data-testid="site-nodes-deployed">
                <Heading size="xs" textTransform="uppercase">
                  Nodes Deployed
                </Heading>
                <Text pt="2" fontSize="sm">
                  {site_nodes?.length}
                </Text>
              </Box>
            </HStack>

            {(contacts ?? [])?.length > 0 ? (
              <Accordion allowMultiple>
                <AccordionItem sx={{ border: 'none' }}>
                  <AccordionButton sx={{ paddingLeft: '0', paddingRight: '0' }}>
                    <Box as="span" flex="1" textAlign="left">
                      <Heading size="xs" textTransform="uppercase">
                        Contacts ( {contacts?.length} )
                      </Heading>
                    </Box>
                    <AccordionIcon />
                  </AccordionButton>
                  <AccordionPanel>
                    {contacts?.map((item: Contact, index: number) => (
                      <Contacts key={index} contact={item} />
                    ))}
                  </AccordionPanel>
                </AccordionItem>
              </Accordion>
            ) : null}
          </Stack>
        </CardBody>
      </Card>
      <Modal isOpen={isOpen} onClose={onClose} size="lg" isCentered>
        <ModalOverlay bg="blackAlpha.900" />
        <ModalContent>
          <ModalCloseButton />
          <CreateSiteForm
            onClose={onClose}
            isEdit={true}
            defaultSiteValues={{
              ...site,
              region: `${site.region_code}-${site.country_code}`,
            }}
          />
        </ModalContent>
      </Modal>
    </>
  );
};

export default SiteCard;
