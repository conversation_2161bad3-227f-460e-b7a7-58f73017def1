import { AddIcon } from '@chakra-ui/icons';
import {
  Button,
  <PERSON>ing,
  Icon,
  Modal,
  ModalCloseButton,
  ModalContent,
  ModalOverlay,
  Stack,
  Text,
  useDisclosure,
} from '@chakra-ui/react';
import * as React from 'react';
import { AUTH_TOKEN_KEY, READ_WRITE_ACCESS_ROLES } from '../../data/constants';
import useLogin from '../../hooks/useLogin';
import CreateSiteForm from './components/CreateSiteForm';
import CreateRegionForm from './components/CreateRegionForm';
import ViewRegions from './viewRegions/ViewRegions';

export const SiteManager: React.FC = () => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [isCreatingRegion, setIsCreatingRegion] = React.useState(false);

  const { checkApplicationAccess } = useLogin(AUTH_TOKEN_KEY);
  const checkRoleAccess = checkApplicationAccess(READ_WRITE_ACCESS_ROLES);

  const handleOpenCreateSite = () => {
    setIsCreatingRegion(false);
    onOpen();
  };

  const handleOpenCreateRegion = () => {
    setIsCreatingRegion(true);
    onOpen();
  };

  return (
    <>
      <Stack
        spacing="4"
        direction={{
          base: 'column',
          lg: 'row',
        }}
        justify="space-between"
      >
        <Stack spacing="1">
          <Heading fontWeight="medium">Site Manager</Heading>
          <Text color="muted">All important metrics at a glance</Text>
        </Stack>
        {checkRoleAccess && (
          <Button
            onClick={handleOpenCreateRegion}
            variant="primary"
            leftIcon={<Icon as={AddIcon} marginStart="-1" />}
            data-testid="Create Region"
          >
            Create Region
          </Button>
        )}
        {checkRoleAccess && (
          <Button
            onClick={handleOpenCreateSite}
            variant="primary"
            leftIcon={<Icon as={AddIcon} marginStart="-1" />}
            data-testid="Create Site"
          >
            Create Site
          </Button>
        )}
      </Stack>

      <Stack
        spacing={{
          base: '5',
          lg: '6',
        }}
      >
        <ViewRegions />
      </Stack>

      <Modal isOpen={isOpen} onClose={onClose} size="lg" isCentered>
        <ModalOverlay bg="blackAlpha.900" />
        <ModalContent>
          <ModalCloseButton />
          {isCreatingRegion ? <CreateRegionForm onClose={onClose} /> : <CreateSiteForm onClose={onClose} />}
        </ModalContent>
      </Modal>
    </>
  );
};

export default SiteManager;
