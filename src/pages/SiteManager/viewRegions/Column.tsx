import { Flex, FlexProps, Heading, HeadingProps, useColorModeValue } from '@chakra-ui/react';

export const ColumnHeader = (props: FlexProps) => (
  <Flex
    minH="12"
    position="sticky"
    zIndex={1}
    top="0"
    px="3"
    align="center"
    bg={useColorModeValue('white', 'gray.800')}
    color={useColorModeValue('gray.700', 'white')}
    boxShadow="sm"
    {...props}
  />
);

export const ColumnHeading = (props: HeadingProps) => (
  <Heading fontWeight="bold" fontSize="sm" lineHeight="1.25rem" {...props} />
);
