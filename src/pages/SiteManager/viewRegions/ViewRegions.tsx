import { Box, Flex, useColorModeValue } from '@chakra-ui/react';
import * as React from 'react';
import EmptyState from '../../../components/emptyState/EmptyState';
import Loader from '../../../components/loader/Loader';
import { Region, Site } from '../../../types/InventoryManager.type';
import useRegionList from '../hooks/useRegionList';
import useSiteList from '../hooks/useSiteList';
import { Regions } from './Regions';
import SiteCard from '../components/SiteCard';
import { RegionCard } from '../components/RegionCard';
import { useEffect } from 'react';

const ViewRegions = () => {
  const { data: regionList = [], isLoading: isRegionsLoading } = useRegionList();
  const { data: siteList = [], isLoading: isSitesLoading } = useSiteList();

  const [selectedRegion, setSelectedRegion] = React.useState<Region | null>(null);
  const [selectedSite, setSelectedSite] = React.useState<Site | null>(null);

  const bgColor = useColorModeValue('white', 'gray.900');

  useEffect(() => {
    // This ensures that the selected site is updated with the latest site info received from siteList
    if (selectedSite) {
      const updatedSite = siteList.find((site) => site.site_id === selectedSite.site_id);
      if (updatedSite) {
        setSelectedSite(updatedSite);
      } else {
        // The selected site no longer exists in the siteList
        setSelectedSite(null);
      }
    }
  }, [siteList, selectedSite]);

  useEffect(() => {
    if (selectedRegion) {
      const updatedRegion = regionList.find((region) => region.region_code === selectedRegion.region_code);
      if (updatedRegion) {
        setSelectedRegion(updatedRegion);
      } else {
        setSelectedRegion(null);
      }
    }
  }, [regionList, selectedRegion]);

  if (isRegionsLoading || isSitesLoading) return <Loader data-testid="loader" />;
  if (!regionList.length) return <EmptyState>No regions found</EmptyState>;

  const handleSetSelectedRegion = (region: Region | null) => {
    setSelectedRegion(region);
    if (region === null) {
      setSelectedSite(null);
    }
  };

  return (
    <Flex height="80vh">
      {/* Regions List */}
      <Box bg={bgColor} width="33%" borderRightWidth="1px" overflowY="auto" p="4">
        {/* <ColumnHeading>Regions</ColumnHeading> */}
        <Regions
          regionList={regionList}
          sites={siteList}
          setSelectedRegion={handleSetSelectedRegion}
          setSelectedSite={setSelectedSite}
          selectedRegion={selectedRegion}
          selectedSite={selectedSite}
        />
      </Box>

      {/* Main Detail View */}
      <Box bg={bgColor} flex="1" overflowY="auto" p="4">
        {selectedRegion && !selectedSite ? (
          <RegionCard region={selectedRegion} />
        ) : selectedSite ? (
          <SiteCard site={selectedSite} />
        ) : (
          <EmptyState>Select a region or site to view details</EmptyState>
        )}
      </Box>
    </Flex>
  );
};

export default ViewRegions;
