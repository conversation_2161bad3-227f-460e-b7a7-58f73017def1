import { Image, Link, Stack, StackProps, Text, useColorModeValue as mode } from '@chakra-ui/react';
import Data from '../../../assets/images/illustration-data.svg';
import { Site } from '../../../types/InventoryManager.type';

interface SiteProps extends StackProps {
  sites: Site[];
  setSelectedSite: (id: number) => void;
  selectedSite: number | null;
}
export const Sidebar = (props: SiteProps) => {
  const { sites, setSelectedSite, selectedSite, ...rest } = props;
  if (sites.length <= 0) return <Image m={'200px auto'} w={'80%'} opacity={0.3} src={Data} alt="No site selected" />;
  return (
    <Stack spacing={{ base: '1px', lg: '1' }} px={{ lg: '3' }} py="3" {...rest}>
      {sites.map((site) => (
        <Link
          href="#"
          key={site.site_id}
          aria-current={site.site_id === selectedSite ? 'page' : undefined}
          _hover={{ textDecoration: 'none', bg: mode('gray.200', 'gray.700') }}
          _activeLink={{ bg: 'gray.700', color: 'white' }}
          borderRadius={{ lg: 'lg' }}
          onClick={() => setSelectedSite(site.site_id)}
        >
          <Stack
            spacing="1"
            py={{ base: '3', lg: '2' }}
            px={{ base: '3.5', lg: '3' }}
            fontSize="sm"
            lineHeight="1.25rem"
          >
            <Text fontWeight="medium">{site.name}</Text>
            <Text opacity={0.6}>{site.description}</Text>
          </Stack>
        </Link>
      ))}
    </Stack>
  );
};
