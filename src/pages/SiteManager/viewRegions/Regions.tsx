import {
  Accordion,
  AccordionButton,
  AccordionItem,
  AccordionPanel,
  Box,
  Button,
  ButtonGroup,
  Flex,
  FlexProps,
  HStack,
  Icon,
  Link,
  Stack,
  Text,
} from '@chakra-ui/react';
import { Region, Site } from '../../../types/InventoryManager.type';
import { ColumnHeader } from './Column';
import { ChevronRightIcon } from '@chakra-ui/icons';
import { useState, useEffect, useMemo } from 'react';
import { BsArrowReturnRight } from 'react-icons/bs';
import _ from 'lodash';

interface Props extends FlexProps {
  regionList: Region[];
  sites: Site[];
  setSelectedRegion: (region: Region | null) => void;
  setSelectedSite: (site: Site | null) => void;
  selectedRegion: Region | null;
  selectedSite: Site | null;
}

export const Regions = (props: Props) => {
  const { regionList, sites, setSelectedRegion, setSelectedSite, selectedRegion, selectedSite, ...rest } = props;

  // State to manage expanded regions by their indices
  const [expandedIndices, setExpandedIndices] = useState<number[]>([]);

  // State to manage selected country filter
  const [selectedCountry, setSelectedCountry] = useState<string | null>(null);

  // Extract unique country names from regionList
  const uniqueCountries = useMemo(() => {
    const countries = regionList.map((region) => region.country_name);
    return Array.from(new Set(countries)).sort((a, b) => a.localeCompare(b));
  }, [regionList]);

  // Handle "Collapse All" button click
  const handleCollapseAll = () => {
    setExpandedIndices([]);
    setSelectedSite(null);
  };

  // Handle country filter button click
  const handleFilterByCountry = (country: string | null) => {
    setSelectedCountry(country);
    if (country !== null) {
      if (!(selectedRegion && selectedRegion.country_name === country)) {
        setSelectedRegion(null);
        setSelectedSite(null);
      }
    }
  };

  // When a site is selected, ensure its parent region is expanded and highlighted
  useEffect(() => {
    if (selectedSite) {
      const parentRegion = regionList.find((region) => region.region_id === selectedSite.region_id);
      if (parentRegion) {
        const regionIndex = regionList.findIndex((region) => region.region_id === parentRegion.region_id);
        if (regionIndex !== -1 && !expandedIndices.includes(regionIndex)) {
          setExpandedIndices((prev) => [...prev, regionIndex]);
        }
        setSelectedRegion(parentRegion);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedSite]);

  // Handle region click
  const handleRegionClick = (region: Region) => {
    if (selectedRegion?.region_id === region.region_id && selectedSite?.region_id !== region.region_id) {
      // If the clicked region is already selected and no site is selected, deselect it
      setSelectedSite(null);
    } else {
      // Select the new region and deselect any selected site
      setSelectedRegion(region);
      setSelectedSite(null);

      // Ensure the new region is expanded
      const regionIndex = regionList.findIndex((r) => r.region_id === region.region_id);
      if (regionIndex !== -1 && !expandedIndices.includes(regionIndex)) {
        setExpandedIndices((prev) => [...prev, regionIndex]);
      }
    }
  };

  // Handle site click
  const handleSiteClick = (site: Site) => {
    if (selectedSite?.site_id === site.site_id) {
      // If the same site is clicked again, deselect it
      setSelectedSite(null);
    } else {
      // Select the new site
      setSelectedSite(site);
      // Highlight the parent region (handled by useEffect)
    }
  };

  // Filter regions based on selectedCountry
  const filteredRegions = useMemo(() => {
    if (!selectedCountry) return regionList;
    return regionList.filter((region) => region.country_name === selectedCountry);
  }, [regionList, selectedCountry]);

  return (
    <Flex as="nav" direction="column" {...rest}>
      <ColumnHeader>
        <HStack justifyContent="space-between" width="100%">
          <Text fontWeight="bold" fontSize="lg">
            Regions
          </Text>
          <Button size="sm" onClick={handleCollapseAll}>
            Collapse All
          </Button>
        </HStack>
      </ColumnHeader>

      {/* Filter Buttons */}
      <Box px="4" py="2">
        <ButtonGroup variant="outline" spacing="2">
          <Button
            size="md"
            onClick={() => handleFilterByCountry(null)}
            isActive={!selectedCountry}
            colorScheme={selectedCountry === null ? 'teal' : 'gray'}
            bg={selectedCountry === null ? 'gray.300' : 'transparent'}
          >
            All
          </Button>
          {uniqueCountries.map((country) => (
            <Button
              key={country}
              size="md"
              onClick={() => handleFilterByCountry(country ?? null)}
              isActive={selectedCountry === country}
              colorScheme={selectedCountry === country ? 'teal' : 'gray'}
              bg={selectedCountry === country ? 'gray.300' : 'transparent'}
            >
              {country}
            </Button>
          ))}
        </ButtonGroup>
      </Box>

      <Accordion
        allowMultiple
        index={expandedIndices}
        onChange={(indices) => setExpandedIndices(Array.isArray(indices) ? indices : [indices])}
        data-testid="all-regions"
      >
        {filteredRegions.map((region, index) => {
          const regionSites = sites
            .filter((site) => site.region_id === region.region_id)
            .sort((a, b) => a.name.localeCompare(b.name));

          const isRegionHighlighted =
            selectedRegion?.region_id === region.region_id ||
            (selectedSite && selectedSite.region_id === region.region_id);

          return (
            <AccordionItem key={region.region_id} border="none" p="1">
              {({ isExpanded }) => (
                <>
                  <AccordionButton
                    onClick={() => handleRegionClick(region)}
                    bg={isRegionHighlighted ? 'gray.300' : 'transparent'}
                    _hover={{
                      bg: 'gray.300',
                    }}
                    color={isRegionHighlighted ? 'teal.500' : 'gray.700'}
                    borderRadius="md"
                    fontWeight={isRegionHighlighted && !selectedSite ? 'bold' : 'normal'}
                    data-testid={`region-${region.region_id}`}
                  >
                    <Icon as={BsArrowReturnRight} />
                    <Box flex="1" textAlign="left" ml="2">
                      <Text fontWeight="medium" fontSize="md">
                        {region.region_code} - {region.region_name}
                      </Text>
                    </Box>
                  </AccordionButton>

                  <AccordionPanel px="4" pb="4">
                    <Stack spacing="2">
                      {regionSites.map((site) => (
                        <Link
                          key={site.site_id}
                          onClick={() => handleSiteClick(site)}
                          color={selectedSite?.site_id === site.site_id ? 'teal.500' : 'gray.700'}
                          fontWeight={selectedSite?.site_id === site.site_id ? 'bold' : 'normal'}
                          _hover={{
                            textDecoration: 'none',
                            color: 'teal.500',
                            bg: 'gray.200',
                          }}
                          display="flex"
                          alignItems="center"
                          p="2"
                          borderRadius="md"
                          bg={selectedSite?.site_id === site.site_id ? 'gray.200' : 'transparent'}
                          aria-current={selectedSite?.site_id === site.site_id ? 'page' : undefined}
                          data-testid={`site-${site.site_id}`}
                        >
                          <ChevronRightIcon boxSize={6} mr="2" />
                          <Box flex="1" textAlign="left" ml="2">
                            <Text fontWeight="medium" fontSize="md">
                              {site.name}
                            </Text>
                          </Box>
                        </Link>
                      ))}
                    </Stack>
                  </AccordionPanel>
                </>
              )}
            </AccordionItem>
          );
        })}
      </Accordion>
    </Flex>
  );
};

export default Regions;
