import { Box, BoxProps, Image, Stack } from '@chakra-ui/react';
import Report from '../../../assets/images/illustration-report.svg';
import { Site } from '../../../types/InventoryManager.type';
import SiteCard from '../components/SiteCard';

export interface Props extends BoxProps {
  site?: Site;
}
export const Main = (props: Props) => {
  const { site } = props;

  if (site === undefined) return <Image src={Report} m={'200px auto'} w={'50%'} opacity={0.3} alt="No site selected" />;
  return (
    <Box as="main" {...props}>
      <Stack spacing="8">{site && <SiteCard site={site} />}</Stack>
    </Box>
  );
};
