import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { render, screen } from '@testing-library/react';
import { Main, Props } from '../viewRegions/Main';

type ChildrenProps = {
  children: React.ReactNode;
};

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});
const wrapper = ({ children }: ChildrenProps) => (
  <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
);

const props: Props = {
  maxW: '3xl',
  mx: 'auto',
};

describe('Main', () => {
  it('render empty site panel', () => {
    render(<Main {...props} />);
    screen.getByRole('img', {
      name: /no site selected/i,
    });
  });

  it('renders site card when site prop is provided', () => {
    const mockSite: Props = {
      site: {
        site_id: 1,
        name: 'Marlow Lab 1',
        address: 'Atlas House, Globe Business Park, Parkway, Third Ave, Marlow SL7 1EY',
        region_id: 1,
        region_code: 'MARL',
        country_code: 'GBR',
        description: 'Marlow Office Lab deployment',
        additional_info: '',
        latitude: 51.57365374524476,
        longitude: -0.760690388181796,
        site_cells: [],
        site_nodes: [],
      },
    };

    render(<Main site={mockSite.site} />, { wrapper });
    screen.getByText(/atlas house, globe business park, parkway, third ave, marlow sl7 1ey/i);
    screen.getByRole('heading', {
      name: /marlow lab 1/i,
    });
  });
});
