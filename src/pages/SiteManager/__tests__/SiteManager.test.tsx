import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { render, screen } from '@testing-library/react';
import SiteManager from '../SiteManager';

type ChildrenProps = {
  children: React.ReactNode;
};

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});
const wrapper = ({ children }: ChildrenProps) => (
  <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
);

test.skip('counter increments and decrements when the buttons are clicked', () => {
  render(<SiteManager />, { wrapper });
  screen.getByRole('button', {
    name: /create site/i,
  });
});
