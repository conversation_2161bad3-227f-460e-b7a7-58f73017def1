import { fireEvent, render, screen } from '@testing-library/react';
import { expect, vi } from 'vitest';
import { Sidebar } from '../viewRegions/Sidebar';

describe('Sidebar', () => {
  const mockSites = [
    {
      site_id: 1,
      name: 'Site 1',
      description: 'Site 1 description',
      address: 'test 1',
      region_id: 1,
      region_code: 'MRL',
      country_code: 'GBR',
      site_cells: [],
      site_nodes: [],
    },
    {
      site_id: 2,
      name: 'Site 2',
      description: 'Site 2 description',
      address: 'test 1',
      region_id: 2,
      region_code: 'MRL',
      country_code: 'GBR',
      site_cells: [],
      site_nodes: [],
    },
  ];

  const mockSetSelectedSite = vi.fn();

  it('renders "No site selected" image when sites array is empty', () => {
    render(<Sidebar sites={[]} setSelectedSite={mockSetSelectedSite} selectedSite={null} />);

    const noSiteImage = screen.getByAltText('No site selected');
    expect(noSiteImage).toBeInTheDocument();
  });

  it('renders site links when sites array is provided', () => {
    render(<Sidebar sites={mockSites} setSelectedSite={mockSetSelectedSite} selectedSite={1} />);

    const siteLinks = screen.getAllByRole('link');
    expect(siteLinks).toHaveLength(mockSites.length);

    siteLinks.forEach((siteLink, index) => {
      expect(siteLink).toHaveTextContent(mockSites[index].name);
      expect(siteLink).toHaveTextContent(mockSites[index].description);
    });
  });

  it('calls setSelectedSite with correct site id when a site link is clicked', () => {
    render(<Sidebar sites={mockSites} setSelectedSite={mockSetSelectedSite} selectedSite={null} />);

    const siteLink = screen.getByText('Site 1');
    fireEvent.click(siteLink);

    expect(mockSetSelectedSite).toHaveBeenCalledTimes(1);
    expect(mockSetSelectedSite).toHaveBeenCalledWith(1);
  });
});
