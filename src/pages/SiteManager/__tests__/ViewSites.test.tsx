import { fireEvent, render, renderHook, screen, waitFor } from '@testing-library/react';
import React from 'react';

import '@testing-library/jest-dom';
import { MemoryRouter } from 'react-router-dom';
import { describe, expect } from 'vitest';
import useRegionList from '../hooks/useRegionList';
import ViewRegions from '../viewRegions/ViewRegions';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});
const wrapper = ({ children }: ChildrenProps) => (
  <QueryClientProvider client={queryClient}>
    <MemoryRouter>{children}</MemoryRouter>
  </QueryClientProvider>
);
type ChildrenProps = {
  children: React.ReactNode;
};
describe('ViewSites', () => {
  it('renders empty state when no regions are available', async () => {
    const { result } = renderHook(() => useRegionList(), { wrapper });
    await waitFor(() => result.current.isSuccess);

    render(<ViewRegions />, { wrapper });
    // const link = screen.getByTestId('region-link-1');
    // fireEvent.click(link);

    await waitFor(() => expect(result.current.isSuccess).toBe(false));
    // expect(screen.getByText('No sites found')).toBeInTheDocument();
  });

  it('renders regions, sidebar, and main content when data is available', async () => {
    render(<ViewRegions />, { wrapper });
    // const link = await screen.findByTestId('region-link-1');
    // fireEvent.click(link);
    // Assert regions are rendered
    // screen.getByText(/auck lab 1/i);
    // const siteLink = screen.getByRole('link');

    // Select a region
    // fireEvent.click(siteLink);

    // Assert sidebar and main content are rendered
    // expect(
    //   screen.getByRole('heading', {
    //     name: /auck lab 1/i,
    //   })
    // ).toBeInTheDocument();
  });
});
