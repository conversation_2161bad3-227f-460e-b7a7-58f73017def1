import {
  Box,
  Button,
  Circle,
  Container,
  Flex,
  Heading,
  HStack,
  SimpleGrid,
  Stack,
  Text,
  useColorModeValue,
  VStack,
} from '@chakra-ui/react';
import { useNavigate } from 'react-router-dom';
import { ReactComponent as Abstract } from '../assets/icons/abstract.svg';
import { ReactComponent as Alarm } from '../assets/icons/alarm.svg';
import { ReactComponent as Cell } from '../assets/icons/cell.svg';
import { ReactComponent as Research } from '../assets/icons/research.svg';
import { ReactComponent as OranDuCuManager } from '../assets/icons/oranManager.svg';

export const Landing: React.FC = () => {
  const navigate = useNavigate();
  return (
    <>
      <Stack spacing="1">
        <Heading fontWeight="medium">Network Management System</Heading>
        <Text color="muted">All important metrics at a glance</Text>
      </Stack>
      <Stack
        spacing={{
          base: '5',
          lg: '6',
        }}
      >
        <Container
          centerContent
          bg={useColorModeValue('gray.100', 'gray.800')}
          px={{
            base: '6',
            md: '8',
          }}
          py="12"
        >
          <SimpleGrid
            columns={{
              base: 2,
              md: 3,
            }}
            spacing="6"
          >
            {/* Cell Overview */}
            <Flex
              direction="column"
              alignItems="center"
              rounded="md"
              padding="8"
              position="relative"
              bg={useColorModeValue('white', 'gray.700')}
              _hover={{
                shadow: 'md',
                zIndex: 1,
              }}
              shadow={{
                md: 'base',
              }}
            >
              <Circle size="6rem" bg="accent">
                <Cell />
              </Circle>
              <VStack spacing="1" flex="1" mt="3" mb="3">
                <HStack>
                  <Text marginBottom={'1.5rem'} fontWeight="bold">
                    Cell Overview
                  </Text>
                </HStack>
              </VStack>
              <Button
                variant="outline"
                colorScheme="brand.600"
                rounded="full"
                size="sm"
                width="full"
                _hover={{
                  bg: useColorModeValue('gray.200', 'gray.600'),
                }}
                onClick={() => navigate('/cell-overview')}
              >
                Launch
              </Button>
            </Flex>
            {/* Events and Alarms */}
            <Flex
              direction="column"
              alignItems="center"
              rounded="md"
              padding="8"
              position="relative"
              bg={useColorModeValue('white', 'gray.700')}
              _hover={{
                shadow: 'md',
                zIndex: 1,
              }}
              shadow={{
                md: 'base',
              }}
            >
              <Circle size="6rem" bg="accent">
                <Research />
              </Circle>
              <VStack spacing="1" flex="1" mt="3" mb="3">
                <HStack>
                  <Text fontWeight="bold">Alarms and Events</Text>
                </HStack>
              </VStack>
              <Button
                variant="outline"
                colorScheme="brand.600"
                rounded="full"
                size="sm"
                width="full"
                _hover={{
                  bg: useColorModeValue('gray.200', 'gray.600'),
                }}
                onClick={() => navigate('/alarms-and-events/alarms')}
              >
                Launch
              </Button>
            </Flex>
            {/* Site Manager */}
            <Flex
              direction="column"
              alignItems="center"
              rounded="md"
              padding="8"
              position="relative"
              bg={useColorModeValue('white', 'gray.700')}
              _hover={{
                shadow: 'md',
                zIndex: 1,
              }}
              shadow={{
                md: 'base',
              }}
            >
              <Circle size="6rem" bg="accent">
                <Abstract />
              </Circle>
              <VStack spacing="1" flex="1" mt="3" mb="3">
                <HStack>
                  <Text fontWeight="bold">Site Manager</Text>
                </HStack>
              </VStack>
              <Button
                variant="outline"
                colorScheme="brand.600"
                rounded="full"
                size="sm"
                width="full"
                _hover={{
                  bg: useColorModeValue('gray.200', 'gray.600'),
                }}
                onClick={() => navigate('/site-manager')}
              >
                Launch
              </Button>
            </Flex>
            {/* Manifest */}
            <Flex
              direction="column"
              alignItems="center"
              rounded="md"
              padding="8"
              position="relative"
              bg={useColorModeValue('white', 'gray.700')}
              shadow={{
                md: 'base',
              }}
            >
              <Circle size="6rem" bg="accent">
                <Alarm />
              </Circle>
              <VStack spacing="1" flex="1" mt="3" mb="3">
                <HStack>
                  <Text fontWeight="bold">Manifests</Text>
                </HStack>
              </VStack>
              <Button
                variant="outline"
                colorScheme="brand.600"
                rounded="full"
                size="sm"
                width="full"
                _hover={{
                  bg: useColorModeValue('gray.200', 'gray.600'),
                }}
                onClick={() => navigate('/manifest-overview/device-manifests')}
              >
                Launch
              </Button>
            </Flex>
            {/* O-RAN DU/CU Manager */}
            <Flex
              direction="column"
              alignItems="center"
              rounded="md"
              padding="8"
              position="relative"
              bg={useColorModeValue('white', 'gray.700')}
              shadow={{
                md: 'base',
              }}
            >
              <Circle size="6rem" bg="accent">
                <OranDuCuManager />
              </Circle>
              <VStack spacing="1" flex="1" mt="3" mb="3">
                <Box>
                  <Text fontWeight="bold">O-RAN DU/CU</Text>
                  <Text fontWeight="bold">Manager</Text>
                </Box>
              </VStack>
              <Button
                variant="outline"
                colorScheme="brand.600"
                rounded="full"
                size="sm"
                width="full"
                _hover={{
                  bg: useColorModeValue('gray.200', 'gray.600'),
                }}
                onClick={() => navigate('/oran-du-cu-manager')}
              >
                Launch
              </Button>
            </Flex>
            {/*Software Upgrade */}
            <Flex
              direction="column"
              alignItems="center"
              rounded="md"
              padding="8"
              position="relative"
              bg={useColorModeValue('white', 'gray.700')}
              shadow={{
                md: 'base',
              }}
            >
              <Circle size="6rem" bg="accent">
                <Research />
              </Circle>
              <VStack spacing="1" flex="1" mt="3" mb="3">
                <Box>
                  <Text fontWeight="bold">Software Upgrade</Text>
                </Box>
              </VStack>
              <Button
                variant="outline"
                colorScheme="brand.600"
                rounded="full"
                size="sm"
                width="full"
                _hover={{
                  bg: useColorModeValue('gray.200', 'gray.600'),
                }}
                onClick={() => navigate('/software-upgrade/rollouts')}
              >
                Launch
              </Button>
            </Flex>
          </SimpleGrid>
        </Container>
      </Stack>
    </>
  );
};

export default Landing;
