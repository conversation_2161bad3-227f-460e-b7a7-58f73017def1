import {
  Box,
  FormControl,
  FormLabel,
  Grid,
  GridItem,
  Input,
  Textarea,
  Select,
  FormErrorMessage,
} from '@chakra-ui/react';
import { useFormContext, useWatch } from 'react-hook-form';
import { Dispatch, SetStateAction, useEffect } from 'react';
import Loader from '../../../components/loader/Loader';
import { Deployment, Rollout } from '../../../types/softwareController.types';
import useArtifactNameList from '../hooks/useArtifactName';

const CreateDeployment = ({
  rolloutData,
  onSubmit,
  setDeployments,
  addMoreDeployment,
  setAddMoreDeployment,
}: {
  rolloutData: Rollout | null;
  onSubmit: (a: Deployment, b: boolean) => void;
  addMoreDeployment: boolean;
  setDeployments: Dispatch<SetStateAction<Record<string, string>[]>>;
  setAddMoreDeployment: Dispatch<SetStateAction<boolean>>;
}) => {
  const {
    register,
    formState: { errors },
    reset,
    handleSubmit,
  } = useFormContext();
  const deploymentType = useWatch({ name: 'deploymentType' });

  const deployment: Record<string, string> = {
    Radisys: 'RAD_K8',
    Mender: 'E500',
  };

  const { data: artifactNames = [], isLoading: loadingArtifacts } = useArtifactNameList(deployment[deploymentType]);

  const handleAddAnother = async (data: any) => {
    try {
      await onSubmit(data, false);
      setDeployments((prev) => [...prev, data]);
      reset();
    } catch (error) {
      console.error('Error creating deployment:', error);
    }
  };

  useEffect(() => {
    if (addMoreDeployment) {
      handleSubmit(handleAddAnother)();
    }
    setAddMoreDeployment(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [addMoreDeployment]);

  return (
    <Grid templateColumns="1fr" gap="6" alignItems="start">
      <Box>
        <Box p="6" borderRadius="md" mx="auto" data-testid="deployment-container">
          <Grid templateColumns={{ base: '1fr', md: '1fr 2fr' }} gap="6">
            <GridItem>
              <FormControl isDisabled>
                <FormLabel>Rollout ID</FormLabel>
                <Input
                  {...register('rollout_id')}
                  data-testid="rollout_id"
                  value={rolloutData?.id || ''}
                  placeholder="Rollout ID"
                />
              </FormControl>
            </GridItem>

            <GridItem>
              <FormControl isRequired isInvalid={!!errors.deploymentName}>
                <FormLabel>Deployment Name</FormLabel>
                <Input
                  {...register('deploymentName', { required: 'Deployment Name is required' })}
                  placeholder="Enter deployment name"
                  data-testid="deploymentName"
                />
                <FormErrorMessage>
                  {errors.deploymentName && (errors.deploymentName.message as string)}
                </FormErrorMessage>
              </FormControl>
            </GridItem>

            <GridItem colSpan={{ base: 1, md: 2 }}>
              <FormControl isRequired isInvalid={!!errors.deploymentDescription}>
                <FormLabel>Description</FormLabel>
                <Textarea
                  {...register('deploymentDescription', { required: 'Description is required' })}
                  placeholder="Enter deployment description"
                  resize="vertical"
                  data-testid="deploymentDescription"
                />
                <FormErrorMessage>
                  {errors.deploymentDescription && (errors.deploymentDescription.message as string)}
                </FormErrorMessage>
              </FormControl>
            </GridItem>

            <GridItem>
              <FormControl isRequired isInvalid={!!errors.deploymentType}>
                <FormLabel>Deployment Type</FormLabel>
                <Select
                  {...register('deploymentType', { required: 'Deployment Type is required' })}
                  placeholder="Select deployment type"
                  data-testid="deploymentType"
                >
                  <option value="Radisys">RAD_K8</option>
                  <option value="Mender">E500</option>
                </Select>
                <FormErrorMessage>
                  {errors.deploymentType && (errors.deploymentType.message as string)}
                </FormErrorMessage>
              </FormControl>
            </GridItem>

            <GridItem>
              <FormControl isRequired isInvalid={!!errors.artifactName} isDisabled={!deploymentType}>
                <FormLabel>Artifact Name</FormLabel>
                {loadingArtifacts && deploymentType ? (
                  <Loader />
                ) : (
                  <Select
                    {...register('artifactName', { required: 'Artifact Name is required' })}
                    placeholder={!deploymentType ? 'Select deployment type first' : 'Select artifact name'}
                    data-testid="artifactName"
                  >
                    {artifactNames.map((artifact) => (
                      <option key={artifact} value={artifact}>
                        {artifact}
                      </option>
                    ))}
                  </Select>
                )}
                <FormErrorMessage>{errors.artifactName && (errors.artifactName.message as string)}</FormErrorMessage>
              </FormControl>
            </GridItem>
          </Grid>
        </Box>
      </Box>
    </Grid>
  );
};

export default CreateDeployment;
