import { useState } from 'react';
import {
  Accordion,
  AccordionButton,
  AccordionIcon,
  AccordionItem,
  AccordionPanel,
  Box,
  Button,
  Divider,
  Flex,
  Stack,
  Text,
  useColorModeValue,
} from '@chakra-ui/react';
import { useNavigate } from 'react-router';
import { useForm, FormProvider } from 'react-hook-form';
import CreateRollout from './CreateRollout';
import CreateDeploymnet from './CreateDeploymnet';
import StepperComponent from '../../../components/Stepper/Stepper';
import usePostRollout from '../hooks/usePostRollout';
import usePostDeployment from '../hooks/usePostDeployment';
import { Rollout } from '../../../types/softwareController.types';
import { AddIcon } from '@chakra-ui/icons';

const CreateRolloutForm = () => {
  const [activeStep, setActiveStep] = useState(0);
  const [rolloutData, setRolloutData] = useState<Rollout | null>(null);
  const navigate = useNavigate();
  const boxShadow = useColorModeValue('sm', 'sm-dark');
  const [deployments, setDeployments] = useState<Record<string, string>[]>([]);
  const [addMoreDeployment, setAddMoreDeployment] = useState<boolean>(false);

  const { mutateAsync: postRollout, isLoading: isRolloutPosting } = usePostRollout();
  const { mutateAsync: postDeploymnet, isLoading: isDeploymentPosting } = usePostDeployment();

  const methods = useForm({
    defaultValues: {
      name: '',
      description: '',
      deployment: [],
      deploymentName: '',
      deploymentDescription: '',
      deploymentType: '',
      artifactName: '',
    },
    mode: 'onTouched',
  });

  const { handleSubmit } = methods;

  const steps = [
    { title: 'Create a Rollout', description: '' },
    { title: 'Create a Deployment', description: '' },
  ];

  const onSubmit = async (data: any, isNavigate = true) => {
    if (activeStep === 0) {
      try {
        const result = await postRollout({
          name: data.name,
          description: data.description,
        });
        setRolloutData(result);
        setActiveStep((prev) => prev + 1);
      } catch (error) {
        console.error('Error creating rollout:', error);
      }
    } else if (activeStep === 1) {
      try {
        await postDeploymnet({
          name: data.deploymentName,
          description: data.deploymentDescription,
          deployment_type: data.deploymentType,
          artifact_name: data.artifactName,
          rollout_id: rolloutData?.id || '',
        });
        isNavigate && navigate('/software-upgrade/rollout');
      } catch (error) {
        console.error('Error creating deployment:', error);
        throw error;
      }
    } else {
      navigate('/software-upgrade/rollout');
    }
  };

  const handleOnSubmit = (data: any) => {
    onSubmit(data, true);
  };

  const getChildComponent = () => {
    switch (activeStep) {
      case 0:
        return <CreateRollout />;
      case 1:
        return (
          <CreateDeploymnet
            rolloutData={rolloutData}
            onSubmit={onSubmit}
            setDeployments={setDeployments}
            addMoreDeployment={addMoreDeployment}
            setAddMoreDeployment={setAddMoreDeployment}
          />
        );
      default:
        return null;
    }
  };

  return (
    <FormProvider {...methods}>
      <Box display="flex" justifyContent={activeStep === 0 ? 'center' : 'unset'}>
        {activeStep === 1 && (
          <Box
            width="full"
            maxW={{ lg: 'lg' }}
            height="fit-content"
            boxShadow={{ base: 'none', md: boxShadow }}
            borderRadius="lg"
            borderWidth="1px"
            marginTop="120px"
            padding="6"
            flexDirection="column"
            display="flex"
          >
            <Text pb={4}>Deployments</Text>
            <Accordion allowMultiple overflowY="auto" maxHeight="300px">
              {deployments.map((deployment, index) => (
                <AccordionItem key={index}>
                  <AccordionButton>
                    <Box flex="1" textAlign="left">
                      {deployment.deploymentName}
                    </Box>
                    <AccordionIcon />
                  </AccordionButton>
                  <AccordionPanel p="4">
                    <Text>
                      <strong>Description:</strong> {deployment.deploymentDescription}
                    </Text>
                    <Text>
                      <strong>Deployment Type:</strong> {deployment.deploymentType}
                    </Text>
                    <Text>
                      <strong>Artifact Name:</strong> {deployment.artifactName}
                    </Text>
                  </AccordionPanel>
                </AccordionItem>
              ))}
            </Accordion>
            <Box my="4" textAlign="right">
              <Button
                leftIcon={<AddIcon />}
                isLoading={isDeploymentPosting}
                colorScheme="teal"
                mr="3"
                onClick={() => setAddMoreDeployment(true)}
                data-testid="addmore-button"
              >
                Add more
              </Button>
            </Box>
          </Box>
        )}

        <Box width="full" maxW={{ lg: '5xl' }} p="12">
          <StepperComponent
            colorScheme="teal"
            size="lg"
            steps={steps}
            activeStep={activeStep}
            setActiveStep={setActiveStep}
          />
          <Stack spacing={{ base: '5', lg: '6' }} padding="8">
            <Box boxShadow={{ base: 'none', md: boxShadow }} borderRadius="lg" borderWidth="1px" padding="6">
              {getChildComponent()}
              <Divider />
              <Flex justifyContent="space-between" mt="4">
                <Button colorScheme="teal" variant="outline" onClick={() => navigate('/software-upgrade/rollout')}>
                  Cancel
                </Button>
                <Button
                  onClick={handleSubmit(handleOnSubmit)}
                  colorScheme="teal"
                  data-testid="submit-button"
                  isLoading={isRolloutPosting || isDeploymentPosting}
                  isDisabled={activeStep === steps.length}
                >
                  {activeStep === steps.length - 1 ? 'Done' : 'Next'}
                </Button>
              </Flex>
            </Box>
          </Stack>
        </Box>
      </Box>
    </FormProvider>
  );
};

export default CreateRolloutForm;
