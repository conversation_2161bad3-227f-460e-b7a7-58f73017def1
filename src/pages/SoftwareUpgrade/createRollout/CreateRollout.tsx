import { Box, FormControl, FormLabel, Grid, GridItem, Input, Textarea, FormErrorMessage } from '@chakra-ui/react';
import { useFormContext } from 'react-hook-form';

const CreateRollout = () => {
  const {
    register,
    formState: { errors },
  } = useFormContext();

  return (
    <Box p="6" borderRadius="md" mx="auto">
      <Grid templateColumns={{ base: '1fr', md: '1fr 2fr' }} gap="6">
        <GridItem>
          <FormControl isRequired isInvalid={!!errors.name}>
            <FormLabel>Name</FormLabel>
            <Input
              {...register('name', { required: 'Name is required' })}
              placeholder="Enter rollout name"
              data-testid="display-name"
            />
            <FormErrorMessage>{errors.name && (errors.name.message as string)}</FormErrorMessage>
          </FormControl>
        </GridItem>
        <GridItem colSpan={{ base: 1, md: 2 }}>
          <FormControl isRequired isInvalid={!!errors.description}>
            <FormLabel>Description</FormLabel>
            <Textarea
              {...register('description', { required: 'Description is required' })}
              placeholder="Enter rollout description"
              resize="vertical"
              data-testid="rollout-description"
            />
            <FormErrorMessage>{errors.description && (errors.description.message as string)}</FormErrorMessage>
          </FormControl>
        </GridItem>
      </Grid>
    </Box>
  );
};

export default CreateRollout;
