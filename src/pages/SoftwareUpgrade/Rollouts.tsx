import React from 'react';
import { Box, Stack, useColorModeValue } from '@chakra-ui/react';
import { ErrorBoundary } from 'react-error-boundary';
import { DataTable } from '../MetricsCollector/components/DataTable';
import { ErrorBoundaryFallback, ErrorBoundaryLogError } from '../../components/errorComponents/ErrorBoundaryFallback';
import RolloutDetails from './components/RolloutDetails';
import useRolloutsColumns from './hooks/useRolloutsColumns';
import { Rollout } from '../../types/softwareController.types';

const Rollouts: React.FC<{ alternativeViewId: string; rollouts: Rollout[]; isRolloutLoading: boolean }> = ({
  alternativeViewId,
  isRolloutLoading,
  rollouts,
}) => {
  const columns = useRolloutsColumns();
  const colorModeValue = useColorModeValue('sm', 'sm-dark');

  return (
    <Stack
      spacing={{
        base: '5',
        lg: '6',
      }}
    >
      <Box
        bg="bg-surface"
        boxShadow={{
          base: 'none',
          md: colorModeValue,
        }}
        px={{
          base: '6',
          md: '8',
        }}
        pt="4"
      >
        <Stack spacing="5">
          <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError as any}>
            <DataTable
              columns={columns}
              data={rollouts}
              renderSubComponent={({ row }) => <RolloutDetails rollout={row.original} />}
              isExpandable={true}
              isLoading={isRolloutLoading}
              version="v2"
              enableFilter={true}
              count={rollouts.length}
              hasEmptyResult={rollouts.length === 0}
              alternativeViewId={alternativeViewId}
            />
          </ErrorBoundary>
        </Stack>
      </Box>
    </Stack>
  );
};

export default Rollouts;
