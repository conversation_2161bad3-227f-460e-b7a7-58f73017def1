import { Box, Text } from '@chakra-ui/react';
import { Deployment } from '../../../types/softwareController.types';
import useDeploymentById from '../hooks/useDeploymentById';
import Loader from '../../../components/loader/Loader';
import QueryError from '../../../components/errorComponents/QueryError';
import { formatInReadableTimeDate } from '../../../utils/formatInReadableTimeData';
import CustomTable from './CustomTable';

const DeploymentDetails = ({ deployment }: { deployment: Deployment }) => {
  const { isLoading, error, data: deploymentDetails } = useDeploymentById(deployment.id);

  if (isLoading) return <Loader />;
  if (error) return <QueryError error={error} />;

  if (!deploymentDetails) {
    return <Text>No data available</Text>;
  }

  // Define columns for the event table
  const eventColumns = [
    // { header: 'Event ID', accessor: 'id' },
    { header: 'Action', accessor: 'action' },
    { header: 'Name', accessor: 'name' },
    // { header: 'Description', accessor: 'description' },
    // { header: 'Artifact Name', accessor: 'artifact_name' },
    // { header: 'Deployment Type', accessor: 'deployment_type' },
    // { header: 'Status', accessor: 'status' },
    { header: 'User', accessor: 'user' },
    { header: 'Created At', accessor: 'created_at' },
  ];

  // Define columns for the device table
  const deviceColumns = [
    { header: 'Device Type', accessor: 'device_type' },
    { header: 'State', accessor: 'state' },
    { header: 'Status', accessor: 'status' },
  ];

  // Flatten events data and add user field
  const flattenedEvents = deploymentDetails.events.map((event: any) => ({
    ...event.details,
    id: event.id,
    action: event.action,
    user: event.user?.user || event.user?.email || 'N/A',
    created_at: formatInReadableTimeDate(event.created_at) || 'N/A',
  }));

  return (
    <Box
      data-testid="deployment-details"
      p="4"
      bg="gray.50"
      borderRadius="md"
      flexDirection="column"
      display="flex"
      flexWrap="wrap"
      gap="10px"
    >
      <Box>
        <Text fontSize="lg" fontWeight="bold" mb="2" data-testid="innerComponent-Devices">
          Devices
        </Text>
        {deploymentDetails?.devices?.length > 0 ? (
          <CustomTable columns={deviceColumns} data={deploymentDetails?.devices} />
        ) : (
          <Text>No Devices available</Text>
        )}
      </Box>
      <Box>
        <Text fontSize="lg" fontWeight="bold" mt="4" data-testid="innerComponent-Events">
          Events
        </Text>
        {flattenedEvents?.length > 0 ? (
          <CustomTable columns={eventColumns} data={flattenedEvents} />
        ) : (
          <Text>No Events available</Text>
        )}
      </Box>
    </Box>
  );
};

export default DeploymentDetails;
