import { ReactNode } from 'react';
import { Box, Text, Card, Flex, useColorModeValue } from '@chakra-ui/react';
import { Rollout } from '../../../types/softwareController.types';
import { formatInReadableTimeDate } from '../../../utils/formatInReadableTimeData';
import useRolloutsById from '../hooks/useRolloutsById';
import Loader from '../../../components/loader/Loader';
import QueryError from '../../../components/errorComponents/QueryError';
import CustomTable from './CustomTable';

const renderKeyValuePairs = (data: any) => {
  return Object.entries(data).map(([key, value]) => {
    let displayValue: ReactNode;

    if (
      (Array.isArray(value) && value.length === 0) ||
      (typeof value === 'object' && value !== null && Object.keys(value).length === 0)
    ) {
      displayValue = 'N/A';
    } else if (typeof value === 'object' && value !== null) {
      displayValue = (
        <Box
          as="pre"
          whiteSpace="pre-wrap"
          wordBreak="break-all"
          p="2"
          bg="gray.50"
          borderRadius="md"
          fontFamily="monospace"
          fontSize="sm"
        >
          {JSON.stringify(value, null, 2)}
        </Box>
      );
    } else if (value == null || value === '') {
      displayValue = 'N/A';
    } else {
      displayValue = String(value);
    }

    // Format date fields
    if (key.endsWith('_at')) {
      return;
    }

    return (
      <Flex key={key} direction="row" paddingBottom="2" wrap="wrap">
        <Text
          data-testid={`innerComponent-${key}`}
          width="185px"
          whiteSpace="normal"
          overflowWrap="break-word"
          wordBreak="break-all"
          fontWeight="semibold"
          paddingRight="2"
        >
          {key}:
        </Text>
        <Text
          data-testid="innerComponent-value"
          maxWidth="280px"
          whiteSpace="normal"
          overflowWrap="break-word"
          wordBreak="break-all"
        >
          {displayValue}
        </Text>
      </Flex>
    );
  });
};

const DetailCard = ({ data }: { data: any }) => {
  const boxShadow = useColorModeValue('sm', 'sm-dark');
  return (
    <Card data-testid="detailCard-card" borderTop="1px solid #e2e2e2" height="100%" overflow="hidden">
      <Box
        as="section"
        bg="bg-surface"
        boxShadow={boxShadow}
        borderRadius="lg"
        p={{ base: '4', md: '6' }}
        height="100%"
      >
        {renderKeyValuePairs(data)}
      </Box>
    </Card>
  );
};

const deploymentColumns = [
  { header: 'Deployment ID', accessor: 'id' },
  { header: 'Name', accessor: 'name' },
  { header: 'Description', accessor: 'description' },
  { header: 'Artifact Name', accessor: 'artifact_name' },
  { header: 'Deployment Type', accessor: 'deployment_type' },
  { header: 'Status', accessor: 'status' },
];

// Define columns for the events table
const eventColumns = [
  { header: 'Action', accessor: 'action' },
  { header: 'User', accessor: 'user' },
  { header: 'Created At', accessor: 'created_at' },
];

const RolloutDetails = ({ rollout }: { rollout: Rollout }) => {
  const { isLoading, error, data: rolloutDetails } = useRolloutsById(rollout.id);

  if (isLoading) return <Loader />;
  if (error) return <QueryError error={error} />;

  if (!rolloutDetails) {
    return <Text>No data available</Text>;
  }
  const renderCards = (data: any[], header: string) => {
    return data.length > 0 ? (
      <Box display="flex" flexWrap="wrap">
        {data.map((item, index) => (
          <Box margin="2" key={index} width="530px">
            <DetailCard data={item} />
          </Box>
        ))}
      </Box>
    ) : (
      <Text>No {header.toLowerCase()} available</Text>
    );
  };

  // Flatten events data and add user field
  const flattenedEvents = rolloutDetails.events.map((event: any) => ({
    action: event.action,
    user: event.user?.user || event.user?.email || 'N/A',
    created_at: formatInReadableTimeDate(event.created_at),
  }));

  return (
    <Box
      data-testid="rollout-details"
      p="4"
      bg="gray.50"
      borderRadius="md"
      flexDirection="column"
      display="flex"
      flexWrap="wrap"
      gap="10px"
    >
      <Box>
        <Text fontSize="lg" fontWeight="bold" mb="2" data-testid="innerComponent-Deployments">
          Deployments
        </Text>
        {rolloutDetails?.deployments.length > 0 ? (
          <CustomTable columns={deploymentColumns} data={rolloutDetails?.deployments} type="deployment" />
        ) : (
          <Text>No Deploymnents available</Text>
        )}
      </Box>
      <Box>
        <Text fontSize="lg" fontWeight="bold" mt="4" data-testid="innerComponent-Events">
          Events
        </Text>
        {flattenedEvents.length > 0 ? (
          <CustomTable columns={eventColumns} data={flattenedEvents} />
        ) : (
          <Text>No events available</Text>
        )}
      </Box>
      <Box>
        <Text fontSize="lg" fontWeight="bold" mt="4" data-testid="innerComponent-Contacts">
          Contacts
        </Text>
        {renderCards(rolloutDetails?.contacts, 'Contact')}
      </Box>
    </Box>
  );
};

export default RolloutDetails;
