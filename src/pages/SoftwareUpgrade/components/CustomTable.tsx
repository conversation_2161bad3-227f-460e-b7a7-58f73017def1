import { Table, Thead, Tbody, Tr, Th, Td, Text, Box, Tooltip, useColorModeValue } from '@chakra-ui/react';
import { useNavigate } from 'react-router-dom';

interface Column {
  header: string;
  accessor: string;
}

interface CustomTableProps {
  columns: Column[];
  data: any[];
  type?: 'deployment' | 'rollout'; // Added type to differentiate between deployment and rollout
}

const CustomTable = ({ columns, data, type }: CustomTableProps) => {
  const boxShadow = useColorModeValue('sm', 'sm-dark');
  const hover = useColorModeValue('gray.200', 'gray.700');
  const navigate = useNavigate();

  const renderCellContent = (value: string, header: string, row: any) => {
    const handleClick = (key: string) => {
      if (type === 'deployment' && key === 'Name') {
        navigate(`/software-upgrade/deployment/${row.id}`);
      }
    };

    const shouldShowTooltip = value && value.length > 40;
    const content = (
      <Box
        as="span"
        _hover={{ bg: header === 'Name' && type === 'deployment' ? hover : '' }}
        color={header === 'Name' && type === 'deployment' ? 'cornflowerblue' : 'inherit'}
        cursor={header === 'Name' && type === 'deployment' ? 'pointer' : 'default'}
        onClick={() => handleClick(header)}
        isTruncated
        maxWidth="40ch"
        overflow="hidden"
        whiteSpace="nowrap"
        p="1"
      >
        {value}
      </Box>
    );
    return shouldShowTooltip ? <Tooltip label={value}>{content}</Tooltip> : content;
  };

  const visibleColumns = columns.filter((column) => !(type === 'deployment' && column.accessor === 'id'));

  return (
    <Box
      as="section"
      bg="bg-surface"
      boxShadow={boxShadow}
      mt="2"
      borderRadius="lg"
      p={{ base: '4', md: '6' }}
      height="100%"
    >
      <Table variant="simple" size="sm" sx={{ tableLayout: 'fixed' }}>
        <Thead>
          <Tr>
            {visibleColumns.map((column) => (
              <Th key={column.accessor}>
                {column.header === 'Deployment ID' && type === 'deployment' ? 'Name' : column.header}
              </Th>
            ))}
          </Tr>
        </Thead>
        <Tbody>
          {data.length > 0 ? (
            data.map((row, rowIndex) => (
              <Tr key={rowIndex}>
                {visibleColumns.map((column) => (
                  <Td key={column.accessor} isTruncated>
                    {row[column.accessor] != null
                      ? renderCellContent(row[column.accessor].toString(), column.header, row)
                      : 'N/A'}
                  </Td>
                ))}
              </Tr>
            ))
          ) : (
            <Tr>
              <Td colSpan={columns.length} textAlign="center">
                <Text>No data available</Text>
              </Td>
            </Tr>
          )}
        </Tbody>
      </Table>
    </Box>
  );
};

export default CustomTable;
