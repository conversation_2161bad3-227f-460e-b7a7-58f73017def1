import { Box, Stack, useColorModeValue } from '@chakra-ui/react';
import { ErrorBoundary } from 'react-error-boundary';
import { DataTable } from '../MetricsCollector/components/DataTable';
import { ErrorBoundaryFallback, ErrorBoundaryLogError } from '../../components/errorComponents/ErrorBoundaryFallback';
import useDeployment from './hooks/useDeployment';
import DeploymentDetails from './components/DeploymentDetails';
import useDeploymentsColumns from './hooks/useDeploymentColumns';
import { Rollout } from '../../types/softwareController.types';

const Deployments: React.FC<{ alternativeViewId: string; rollouts: Rollout[] }> = ({ alternativeViewId, rollouts }) => {
  const colorModeValue = useColorModeValue('sm', 'sm-dark');
  const columns = useDeploymentsColumns();
  const { isLoading, data: deployments = [] } = useDeployment();

  // Map deployments to include rollout names
  const enrichedDeployments = deployments.map((deployment) => {
    const rollout = rollouts.find((r) => r.id === deployment.rollout_id);
    return {
      ...deployment,
      rollout_name: rollout?.name || '',
    };
  });

  return (
    <Stack spacing={{ base: '5', lg: '6' }}>
      <Box bg="bg-surface" boxShadow={{ base: 'none', md: colorModeValue }} px={{ base: '6', md: '8' }} pt="4">
        <Stack spacing="5">
          <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError as any}>
            <DataTable
              columns={columns}
              data={enrichedDeployments}
              renderSubComponent={({ row }) => <DeploymentDetails deployment={row.original} />}
              isExpandable={true}
              version="v2"
              enableFilter={true}
              isLoading={isLoading}
              count={enrichedDeployments.length}
              hasEmptyResult={enrichedDeployments.length === 0}
              alternativeViewId={alternativeViewId}
            />
          </ErrorBoundary>
        </Stack>
      </Box>
    </Stack>
  );
};

export default Deployments;
