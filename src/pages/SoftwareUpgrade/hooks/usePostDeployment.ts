import { useToast } from '@chakra-ui/react';
import { useMutation } from '@tanstack/react-query';
import { postDeployment } from '../../../services/softwareController';

export default function usePostDeployment() {
  const toast = useToast();

  return useMutation({
    mutationFn: ({
      rollout_id,
      name,
      description,
      artifact_name,
      deployment_type,
    }: {
      name: string;
      description: string;
      rollout_id: string;
      artifact_name: string;
      deployment_type: string;
    }) => postDeployment(name, description, rollout_id, artifact_name, deployment_type),
    onSuccess: (data) => {
      toast({
        title: 'Deployment created.',
        description: 'Deployment has been created successfully.',
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
      return data;
    },
    onError: ({ response }) => {
      toast({
        title: 'Error creating deployment',
        description: response?.data?.detail || 'Something went wrong.',
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
  });
}
