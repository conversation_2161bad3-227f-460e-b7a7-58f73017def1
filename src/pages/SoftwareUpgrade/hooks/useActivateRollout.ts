import { useToast } from '@chakra-ui/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { activateRollout } from '../../../services/softwareController';

export default function useActivateRollout() {
  const toast = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ rollout_id }: { rollout_id: string }) => activateRollout(rollout_id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['rollout'] });
      toast({
        title: 'Rollout activated.',
        description: 'Rollout has been activated successfully.',
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error activating rollout',
        description: error?.message || 'Something went wrong.',
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
  });
}
