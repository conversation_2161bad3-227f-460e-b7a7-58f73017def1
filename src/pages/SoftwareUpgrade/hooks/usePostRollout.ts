import { useToast } from '@chakra-ui/react';
import { useMutation } from '@tanstack/react-query';
import { postRollout } from '../../../services/softwareController';

export default function usePostRollout() {
  const toast = useToast();

  return useMutation({
    mutationFn: ({ name, description }: { name: string; description: string }) => postRollout(name, description),
    onSuccess: (data) => {
      toast({
        title: 'Rollout created.',
        description: 'Rollout has been created successfully.',
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
      return data;
    },
    onError: (error: any) => {
      toast({
        title: 'Error creating rollout',
        description: error?.message || 'Something went wrong.',
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
  });
}
