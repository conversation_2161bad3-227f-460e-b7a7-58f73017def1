import { useMemo, useState } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import useLogin from '../../../hooks/useLogin';
import { AUTH_TOKEN_KEY, READ_WRITE_ACCESS_ROLES } from '../../../data/constants';
import { Deployment } from '../../../types/softwareController.types';
import { Box, Flex, IconButton, Menu, MenuButton, MenuItem, MenuList, Spinner, Text, Tooltip } from '@chakra-ui/react';
import { BsArrowReturnRight, BsThreeDots } from 'react-icons/bs';
import { DeleteIcon } from '@chakra-ui/icons';
import GenericAlertDialog from '../../../components/AlertDialog';
import useDeleteDeployment from './useDeleteDeployment';

type ConfigMenuProps = {
  status: string;
  deployment_id: string;
};

const ConfigMenu = ({ deployment_id }: ConfigMenuProps) => {
  const { mutateAsync: deleteDeployment } = useDeleteDeployment();
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleDeleteConfirm = async (deployment_id: string) => {
    await deleteDeployment({ deployment_id });
    setIsDialogOpen(false);
  };
  return (
    <>
      <Menu>
        <MenuButton
          data-testid="deployment-menu-items"
          onClick={(e) => {
            e.stopPropagation();
          }}
          as={IconButton}
          aria-label="Options"
          icon={<BsThreeDots />}
          variant="outline"
          border="none"
        />
        <MenuList minWidth="110px">
          <MenuItem
            data-testid="delete-deployment"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              setIsDialogOpen(true);
            }}
          >
            <DeleteIcon mr="1rem" />
            Delete
          </MenuItem>
        </MenuList>
      </Menu>
      {isDialogOpen && (
        <GenericAlertDialog
          title="Delete Deployment"
          message={`Are you sure you want to delete the Deployment with deployment id ${deployment_id}? This action cannot be undone.`}
          confirmButtonText="Delete"
          cancelButtonText="Cancel"
          onConfirm={() => handleDeleteConfirm(deployment_id)}
          confirmButtonColorScheme="teal"
          onCancel={() => setIsDialogOpen(false)}
          width="650px"
        />
      )}
    </>
  );
};

export default function useDeploymentsColumns() {
  const { checkNmsDevAccess } = useLogin(AUTH_TOKEN_KEY);
  const checkRoleAccess = checkNmsDevAccess(READ_WRITE_ACCESS_ROLES);

  return useMemo<ColumnDef<Deployment>[]>(
    () => [
      {
        accessorKey: 'name',
        header: 'Name',
        cell: (props) => {
          const name = props.row.original.name || 'N/A';
          return (
            <Flex>
              <BsArrowReturnRight />
              <Text ml="2">{name}</Text>
            </Flex>
          );
        },
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        accessorKey: 'rollout_name',
        header: 'Rollout Name',
        cell: (props) => {
          const rolloutId = props.row.original.rollout_id;
          const rolloutName = props.row.original.rollout_name;
          return rolloutId ? (
            <Box
              href={`/software-upgrade/rollouts/${rolloutId}`}
              onClick={(e: any) => e.stopPropagation()}
              as="a"
              color="cornflowerblue"
              cursor="pointer"
              ml="2"
            >
              {rolloutName}
            </Box>
          ) : (
            'N/A'
          );
        },
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        accessorKey: 'description',
        header: 'Description',
        cell: (props) => {
          const description = props.row.original.description;
          const shouldShowTooltip = description && description.length > 29;
          const content = (
            <Box isTruncated maxWidth="26ch">
              {description}
            </Box>
          );
          return (shouldShowTooltip ? <Tooltip label={description}>{content}</Tooltip> : description) || 'N/A';
        },
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        accessorKey: 'artifact_name',
        header: 'Artifact Name',
        cell: (props) => {
          const artifactName = props.row.original.artifact_name || 'N/A';
          return <Text ml="2">{artifactName}</Text>;
        },
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        accessorKey: 'deployment_type',
        header: 'Deployment Type',
        cell: (props) => {
          const deploymentType = props.row.original.deployment_type || 'N/A';
          return <Text ml="2">{deploymentType}</Text>;
        },
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        accessorKey: 'status',
        header: 'Status',
        cell: (props) => {
          const status = props.row.original.status || 'N/A';
          return <Text ml="2">{status}</Text>;
        },
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        header: '',
        accessorKey: 'status',
        id: 'status',
        enableSorting: false,
        cell: ({ row }) => (
          <>
            {checkRoleAccess && <ConfigMenu status={row.original.status as string} deployment_id={row.original.id} />}
          </>
        ),
      },
    ],
    []
  );
}
