import { useToast } from '@chakra-ui/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { deleteRollout } from '../../../services/softwareController';

export default function useDeleteRollout() {
  const toast = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ rollout_id }: { rollout_id: string }) => deleteRollout(rollout_id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['rollout'] });
      toast({
        title: 'Rollout deleted.',
        description: 'Rollout has been deleted successfully.',
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error deleting rollout',
        description: error?.message || 'Something went wrong.',
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
  });
}
