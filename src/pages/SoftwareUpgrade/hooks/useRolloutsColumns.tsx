import React, { useMemo, useState } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import useLogin from '../../../hooks/useLogin';
import { AUTH_TOKEN_KEY, READ_WRITE_ACCESS_ROLES } from '../../../data/constants';
import { Rollout } from '../../../types/softwareController.types';
import { Flex, IconButton, Menu, MenuButton, MenuItem, MenuList, Spinner, Text } from '@chakra-ui/react';
import { formatInReadableTimeDate } from '../../../utils/formatInReadableTimeData';
import { BsArrowReturnRight, BsThreeDots } from 'react-icons/bs';
import useActivateRollout from './useActivateRollout';
import useDeleteRollout from './useDeleteRollout';
import { CheckIcon, DeleteIcon } from '@chakra-ui/icons';
import GenericAlertDialog from '../../../components/AlertDialog';

type ConfigMenuProps = {
  status: string;
  rollout_id: string;
};

const ConfigMenu = ({ status, rollout_id }: ConfigMenuProps) => {
  const isActiveEnabled = status !== 'active';
  const { mutateAsync: activateRollout, isLoading: isRolloutActivating } = useActivateRollout();
  const { mutateAsync: deleteRollout } = useDeleteRollout();
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleDeleteConfirm = async (rollout_id: string) => {
    await deleteRollout({ rollout_id });
    setIsDialogOpen(false);
  };
  return (
    <>
      <Menu>
        <MenuButton
          data-testid="rollout-menu-items"
          onClick={(e) => {
            e.stopPropagation();
          }}
          as={IconButton}
          aria-label="Options"
          icon={<BsThreeDots />}
          variant="outline"
          border="none"
        />
        <MenuList minWidth="110px">
          <MenuItem
            data-testid="activate-rollout"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              activateRollout({ rollout_id });
            }}
            isDisabled={!isActiveEnabled || isRolloutActivating}
          >
            <CheckIcon mr="1rem" />
            Activate
            {isRolloutActivating && <Spinner />}
          </MenuItem>
          <MenuItem
            data-testid="delete-rollout"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              setIsDialogOpen(true);
            }}
          >
            <DeleteIcon mr="1rem" />
            Delete
          </MenuItem>
        </MenuList>
      </Menu>
      {isDialogOpen && (
        <GenericAlertDialog
          title="Delete rollout"
          message={`Are you sure you want to delete the rollout with rollout id ${rollout_id}? This action cannot be undone.`}
          confirmButtonText="Delete"
          cancelButtonText="Cancel"
          onConfirm={() => handleDeleteConfirm(rollout_id)}
          confirmButtonColorScheme="teal"
          onCancel={() => setIsDialogOpen(false)}
          width="650px"
        />
      )}
    </>
  );
};

export default function useRolloutsColumns() {
  const { checkNmsDevAccess } = useLogin(AUTH_TOKEN_KEY);
  const checkRoleAccess = checkNmsDevAccess(READ_WRITE_ACCESS_ROLES);

  return useMemo<ColumnDef<Rollout>[]>(
    () => [
      {
        accessorKey: 'name',
        header: 'Name',
        cell: (props) => {
          const name = props.row.original.name || 'N/A';
          return (
            <Flex>
              <BsArrowReturnRight />
              <Text ml="2">{name}</Text>
            </Flex>
          );
        },
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        accessorKey: 'description',
        header: 'Description',
        cell: (props) => {
          const description = props.row.original.description || 'N/A';
          return <Text ml="2">{description}</Text>;
        },
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        accessorKey: 'created_at',
        header: 'Created At',
        cell: ({ getValue }) => <>{getValue() ? formatInReadableTimeDate(getValue() as string).toString() : null}</>,
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        accessorKey: 'status',
        header: 'Status',
        cell: (props) => {
          const status = props.row.original.status || 'N/A';
          return <Text ml="2">{status}</Text>;
        },
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        header: '',
        accessorKey: 'config',
        id: 'config',
        enableSorting: false,
        cell: ({ row }) => (
          <>{checkRoleAccess && <ConfigMenu status={row.original.status as string} rollout_id={row.original.id} />}</>
        ),
      },
    ],
    []
  );
}
