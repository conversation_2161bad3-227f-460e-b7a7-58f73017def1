import { useToast } from '@chakra-ui/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { deleteDeployment } from '../../../services/softwareController';

export default function useDeleteDeployment() {
  const toast = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ deployment_id }: { deployment_id: string }) => deleteDeployment(deployment_id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['deployment'] });
      toast({
        title: 'Deployment deleted.',
        description: 'Deployment has been deleted successfully.',
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error deleting deployment',
        description: error?.message || 'Something went wrong.',
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
  });
}
