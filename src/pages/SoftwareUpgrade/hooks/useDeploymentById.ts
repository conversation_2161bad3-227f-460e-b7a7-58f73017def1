import { useQuery } from '@tanstack/react-query';
import { getDeploymentbyId } from '../../../services/softwareController';
import { Deployment } from '../../../types/softwareController.types';

export default function useDeploymentById(deployment_id: string) {
  return useQuery<Deployment, Error>({
    queryKey: ['deployment', deployment_id],
    queryFn: () => getDeploymentbyId(deployment_id),
  });
}
