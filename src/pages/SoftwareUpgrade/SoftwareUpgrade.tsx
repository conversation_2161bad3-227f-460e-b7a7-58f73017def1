import { AddIcon } from '@chakra-ui/icons';
import {
  Box,
  Button,
  Heading,
  Stack,
  Tab,
  Tab<PERSON>ist,
  TabPanel,
  TabPanels,
  Tabs,
  useColorModeValue,
  Icon,
} from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { AUTH_TOKEN_KEY, READ_WRITE_ACCESS_ROLES } from '../../data/constants';
import useLogin from '../../hooks/useLogin';
import Deployments from './Deployments';
import useRollouts from './hooks/useRollouts';
import Rollouts from './Rollouts';

const SoftwareUpgrade = () => {
  const navigate = useNavigate();
  const { tab, id } = useParams();
  const [tabIndex, setTabIndex] = useState(0);
  const [alternativeViewId, setAlternativeViewId] = useState<string>('');
  const { checkApplicationAccess } = useLogin(AUTH_TOKEN_KEY);
  const checkRoleAccess = checkApplicationAccess(READ_WRITE_ACCESS_ROLES);
  const { isLoading: isRolloutLoading, data: { rollouts = [] } = {} } = useRollouts();

  useEffect(() => {
    if (tab === 'rollouts') {
      setTabIndex(0);
      setAlternativeViewId(id || '');
    } else if (tab === 'deployment') {
      setTabIndex(1);
      setAlternativeViewId(id || '');
    }
  }, [tab]);

  const handleTabs = (index: number) => {
    if (index === 0) {
      navigate('/software-upgrade/rollouts');
    } else {
      navigate('/software-upgrade/deployment');
    }
  };
  const colorModeValue = useColorModeValue('sm', 'sm-dark');

  return (
    <>
      <Stack
        spacing="1"
        mb="10"
        direction={{
          base: 'column',
          lg: 'row',
        }}
        justify="space-between"
      >
        <Stack spacing="1">
          <Heading fontWeight="medium">Software Upgrade</Heading>
        </Stack>
        {checkRoleAccess && (
          <Button
            data-testid="create-rollout-button"
            variant="primary"
            leftIcon={<Icon as={AddIcon} marginStart="-1" />}
            onClick={() => navigate('/software-upgrade/rollout/create')}
          >
            Create rollout
          </Button>
        )}
      </Stack>
      <Stack
        spacing={{
          base: '5',
          lg: '6',
        }}
      >
        <Box
          bg="bg-surface"
          boxShadow={{
            base: 'none',
            md: colorModeValue,
          }}
        >
          <Stack spacing="5">
            <Tabs
              isManual
              isFitted
              variant="enclosed-colored"
              orientation="horizontal"
              isLazy
              onChange={(index: number) => handleTabs(index)}
              index={tabIndex}
            >
              <TabList mb="1em">
                <Tab>Rollouts</Tab>
                <Tab>Deployment</Tab>
              </TabList>
              <TabPanels>
                <TabPanel id="rollouts">
                  <Rollouts
                    alternativeViewId={alternativeViewId}
                    isRolloutLoading={isRolloutLoading}
                    rollouts={rollouts}
                  />
                </TabPanel>
                <TabPanel id="deployment">
                  <Deployments alternativeViewId={alternativeViewId} rollouts={rollouts} />
                </TabPanel>
              </TabPanels>
            </Tabs>
          </Stack>
        </Box>
      </Stack>
    </>
  );
};

export default SoftwareUpgrade;
