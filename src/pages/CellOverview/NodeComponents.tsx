import { Box } from '@chakra-ui/react';
import Switch from '../../components/nodeComponents/switch/Switch';
import GcpVmComponents from '../../components/nodes/GcpVmComponents';
import MeshRootComponents from '../../components/nodes/MeshRootComponents';
import ServerComponents from '../../components/nodes/ServerComponents';
import StreetCellComponents from '../../components/nodes/StreetCellComponents';
import { NODE_TYPE } from '../../data/constants';
import { Node } from '../../types/InventoryManager.type';
import ServerVmComponents from '../../components/nodes/ServerVmComponents';
import Firewall from '../../components/nodeComponents/firewall/Firewall';
import Power from '../../components/nodeComponents/Power/Power';
import StreetCellV1_1Components from '../../components/nodes/StreetCellV1_1Components';
import RadioComponent from '../../components/nodes/RadioComponent';
import GDCVComponent from '../../components/nodes/ClusterComponent';
import DuComponent from '../../components/nodes/DuComponent';
import CuupComponent from '../../components/nodes/CuupComponent';
import CucpComponent from '../../components/nodes/CucpComponent';

type NodeComponentsProps = {
  row: any;
  node: Node;
  nodeOpen: boolean;
  caller?: string;
  nodeType: string;
};

type RowOriginalProps = {
  node_id: string;
  row: {
    original: RowOriginalProps;
  };
};

const NodeComponents = ({ row, node, nodeOpen, caller, nodeType }: NodeComponentsProps) => {
  const destructRow: RowOriginalProps = row;
  const queryNodeId = caller === 'Nodes' ? destructRow?.row?.original.node_id : caller === 'Cells' ? node.node_id : '';
  const displayNodeType = () => {
    switch (nodeType) {
      case NODE_TYPE.STREETCELL:
        return <StreetCellComponents nodeOpen={nodeOpen} queryNodeId={queryNodeId} nodeType={nodeType} />;
      case NODE_TYPE.Server:
        return <ServerComponents nodeOpen={nodeOpen} queryNodeId={queryNodeId} />;
      case NODE_TYPE.POWER:
        return (
          <Box py="1rem" px="2rem" marginLeft="8">
            <Power queryNodeId={queryNodeId} />
          </Box>
        );
      case NODE_TYPE.Server_VM:
        return <ServerVmComponents nodeOpen={nodeOpen} queryNodeId={queryNodeId} />;
      case NODE_TYPE.GCP_VM:
        return <GcpVmComponents nodeOpen={nodeOpen} queryNodeId={queryNodeId} />;
      case NODE_TYPE.FIREWALL:
        return (
          <Box py="1rem" px="2rem" marginLeft="8">
            <Firewall queryNodeId={queryNodeId} instanceAddress={null} />
          </Box>
        );
      case NODE_TYPE.GDCV:
      case NODE_TYPE.GKE:
        return <GDCVComponent nodeOpen={nodeOpen} queryNodeId={queryNodeId} compName={nodeType} />;
      case NODE_TYPE.SWITCH:
        return (
          <Box padding={'1rem 2rem'} marginLeft="8">
            <Switch
              bluwirelessInstanceAddress={null}
              switchHaveData={true}
              queryNodeId={queryNodeId}
              nodeType={nodeType}
              caller={caller}
            />
          </Box>
        );
      case NODE_TYPE.AIRSPAN4G:
        return <StreetCellV1_1Components nodeOpen={nodeOpen} queryNodeId={queryNodeId} />;
      case NODE_TYPE.DU:
        return <DuComponent nodeOpen={true} queryNodeId={queryNodeId} nodeType={nodeType} />;

      case NODE_TYPE.CUUP:
        return <CuupComponent nodeOpen={true} queryNodeId={queryNodeId} nodeType={nodeType} />;
      case NODE_TYPE.CUCP:
        return <CucpComponent nodeOpen={true} queryNodeId={queryNodeId} nodeType={nodeType} />;
      case NODE_TYPE.RADIO:
        return <RadioComponent nodeOpen={nodeOpen} queryNodeId={queryNodeId} nodeType={nodeType} />;
      default:
        return <MeshRootComponents nodeOpen={nodeOpen} queryNodeId={queryNodeId} />;
    }
  };

  return <Box data-testid="nodes-nodeComponents">{displayNodeType()}</Box>;
};

export default NodeComponents;
