import { useQuery } from '@tanstack/react-query';
import { getPowerNodeByNodeId } from '../../../../services/orchestrator';
import { PowerNode } from '../../../../types/orchestrator.types';

export default function useGetPowerNodeByNodeId(node_id: string) {
  const { status, isLoading, error, data } = useQuery<PowerNode>(
    ['getPowerNodeByNodeId', node_id],
    () => getPowerNodeByNodeId(node_id),
    {
      enabled: true,
      retry: false,
    }
  );
  return { status, isLoading, error, data };
}
