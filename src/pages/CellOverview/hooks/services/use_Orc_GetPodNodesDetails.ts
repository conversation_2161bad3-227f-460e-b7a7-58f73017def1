import { useQuery } from '@tanstack/react-query';
import { getPodNodeDetails } from '../../../../services/orchestrator';

export default function useGetPodNodeDetails(node_id: string, isComponentOpen?: boolean) {
  const { status, isLoading, error, data } = useQuery(
    ['getPodNodeDetails', node_id],
    () => getPodNodeDetails(node_id),
    {
      refetchInterval: isComponentOpen ? 10000 : false,
      retry: true,
    }
  );
  return { status, isLoading, error, data };
}
