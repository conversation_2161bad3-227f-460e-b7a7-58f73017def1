import { useQuery } from '@tanstack/react-query';
import { getFirewallNodeByNodeId } from '../../../../services/orchestrator';
import { FirewallNode } from '../../../../types/orchestrator.types';

export default function useGetFirewallNodeByNodeId(node_id: string) {
  const { status, isLoading, error, data } = useQuery<FirewallNode>(
    ['getFirewallNodeByNodeId', node_id],
    () => getFirewallNodeByNodeId(node_id),
    {
      enabled: true,
      retry: false,
    }
  );
  return { status, isLoading, error, data };
}
