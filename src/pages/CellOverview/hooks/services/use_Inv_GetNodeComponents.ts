import { useQuery } from '@tanstack/react-query';
import { getNodeComponentsListByNodeId } from '../../../../services/inventoryManager';
import { getNodeComponentByNodeId } from '../../../../services/mocks/getNodeComponentByNodeId';

export default function useGetNodeComponents(nodeId: string, checkForDevMode?: boolean, isComponentOpen?: boolean) {
  const { status, isLoading, error, data } = useQuery(
    ['getNodeComponentsListByNodeId', nodeId],
    () => getNodeComponentsListByNodeId(nodeId),
    //() => Inv.getNodesForCell(nodeId),
    {
      placeholderData: checkForDevMode ? getNodeComponentByNodeId : undefined,
      enabled: checkForDevMode ? false : !!(isComponentOpen || nodeId),
      //refetchInterval: isComponentOpen ? 10000 : false,
      retry: false,
    }
  );
  return { status, isLoading, error, data };
}
