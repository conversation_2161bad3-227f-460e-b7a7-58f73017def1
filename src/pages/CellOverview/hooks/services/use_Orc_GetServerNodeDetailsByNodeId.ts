import { useQuery } from '@tanstack/react-query';
import { getServerNodeDetailsByNodeId } from '../../../../services/orchestrator';
import { boolean } from 'zod';

export default function useGetServerNodeDetailsByNodeId(node_id: string, isComponentOpen?: boolean) {
  const { status, isLoading, error, data } = useQuery(
    ['getServerNodeDetailsByNodeId', node_id],
    () => getServerNodeDetailsByNodeId(node_id),
    {
      //placeholderData: checkForDevMode ? getCellByRefMock : undefined,
      //enabled: checkForDevMode ? false : !!(isComponentOpen || node_id),
      refetchInterval: isComponentOpen ? 10000 : false,
      retry: true,
    }
  );
  return { status, isLoading, error, data };
}
