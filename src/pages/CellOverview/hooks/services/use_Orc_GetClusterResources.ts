import { useQuery } from '@tanstack/react-query';
import { getClusterResourceByNodeId } from '../../../../services/orchestrator';

export default function useGetClusterResourceByNodeId(node_id: string, isComponentOpen?: boolean) {
  const { status, isLoading, error, data } = useQuery(
    ['getClusterResourceByNodeId', node_id],
    () => getClusterResourceByNodeId(node_id),
    {
      refetchInterval: isComponentOpen ? 10000 : false,
      retry: true,
    }
  );
  return { status, isLoading, error, data };
}
