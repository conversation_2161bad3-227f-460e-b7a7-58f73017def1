import { useQuery } from '@tanstack/react-query';
import { getCellByRefMock } from '../../../../services/mocks/getCellByRefMock';
import { get4gNodeCompByNodeId } from '../../../../services/orchestrator';

export default function useGet4gNodeCompByNodeId(
  node_id: string,
  checkForDevMode?: boolean,
  isComponentOpen?: boolean
) {
  const { status, isLoading, error, data } = useQuery(
    ['get4gNodeCompByNodeId', node_id],
    () => get4gNodeCompByNodeId(node_id),
    {
      placeholderData: checkForDevMode ? getCellByRefMock : undefined,
      enabled: checkForDevMode ? false : !!(isComponentOpen || node_id),
      refetchInterval: isComponentOpen ? 10000 : false,
      retry: false,
    }
  );
  return { status, isLoading, error, data };
}
