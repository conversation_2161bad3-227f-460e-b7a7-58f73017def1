import { useQuery } from '@tanstack/react-query';
import { getNodesForCell } from '../../../../services/inventoryManager';
import { getNodeByCellRefMock } from '../../../../services/mocks/getNodeByCellRefMock';

export default function useGetNodesByCellRef(cell_ref: string, checkForDevMode?: boolean, isComponentOpen?: boolean) {
  //const cellRef = cell_ref ? true : false;
  const { status, isLoading, error, data } = useQuery(
    ['getNodesForCell', cell_ref],
    () => getNodesForCell(cell_ref),
    //() => Inv.getNodeByCellRef(cell_ref),
    {
      placeholderData: checkForDevMode ? getNodeByCellRefMock : undefined,
      //enabled: checkForDevMode ? false : !!(isComponentOpen || cell_ref),
      //refetchInterval: isComponentOpen ? 30000 : false,
      refetchInterval: isComponentOpen ? 10000 : false,
      retry: false,
      //enabled: cellRef,
    }
  );
  return { status, isLoading, error, data };
}
