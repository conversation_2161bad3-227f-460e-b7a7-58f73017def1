import { useQuery } from '@tanstack/react-query';
import { getCellByRefMock } from '../../../../services/mocks/getCellByRefMock';
import { getNodeCompByNodeId } from '../../../../services/orchestrator';

export default function useGetNodeCompByNodeId(node_id: string, checkForDevMode?: boolean, isComponentOpen?: boolean) {
  const { status, isLoading, error, data } = useQuery(
    ['getNodeCompByNodeId', node_id],
    () => getNodeCompByNodeId(node_id),
    {
      placeholderData: checkForDevMode ? getCellByRefMock : undefined,
      enabled: checkForDevMode ? false : !!(isComponentOpen || node_id),
      refetchInterval: isComponentOpen ? 10000 : false,
      retry: false,
    }
  );
  return { status, isLoading, error, data };
}
