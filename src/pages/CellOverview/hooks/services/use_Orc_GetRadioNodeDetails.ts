import { useQuery } from '@tanstack/react-query';
import { getRadioNodeDetails } from '../../../../services/orchestrator';

export default function useGetRadioNodeDetails(node_id: string, checkForDevMode?: boolean, isComponentOpen?: boolean) {
  const { status, isLoading, error, data } = useQuery(
    ['getRadioNodeDetails', node_id],
    () => getRadioNodeDetails(node_id),
    {
      refetchInterval: isComponentOpen ? 10000 : false,
      enabled: checkForDevMode ? false : !!(isComponentOpen || node_id),
      retry: false,
    }
  );
  return { status, isLoading, error, data };
}
