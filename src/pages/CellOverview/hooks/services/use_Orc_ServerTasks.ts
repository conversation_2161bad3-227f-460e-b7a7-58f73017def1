import { useQuery } from '@tanstack/react-query';
import {
  createServerTask,
  getServerTasks,
  getServerTaskByTaskId,
  getServerTasksByTaskStatus,
  postServerRadioActions,
  getServerAction,
  postServerDruidActions,
} from '../../../../services/orchestrator';
import _ from 'lodash';
import { useEffect, useState } from 'react';
import { ServerActionGetDataType, ServerActionPostDataType } from '../../../../types/orchestrator.types';

export enum ActionPhase {
  DEVELOPMENT = 'Under development',
  TRAINING = 'Operator training',
  CONFIGURING = 'Initial configuration',
  INTEGRATION = 'Establishing connections',
  COMMISSIONING = 'Going live',
  VERIFICATION = 'Test and measurement',
  UPGRADING = 'Updates to functionality',
  REMEDIAL = 'Addressing defects',
  PRECAUTION = 'Precautionary action',
  SCHEDULED = 'Scheduled maintenance',
  OTHER = 'Other',
}

export enum ServerActionType {
  RESET = 'Reset',
  LOCK = 'Lock',
  UNLOCK = 'Unlock',
}

export default function useCreateServerTask(nodeId: string, taskType: string) {
  const { status, isLoading, error, data } = useQuery(
    ['createServerTask', nodeId, taskType],
    () => createServerTask(nodeId, taskType),
    { enabled: !!taskType }
  );

  return { status, isLoading, error, data };
}

export function useGetPendingOrRunningServerTasks(nodeId: string, taskStatus: string[]) {
  const { status, isLoading, error, data } = useQuery(['getPendingOrRunningServerTasks', nodeId, taskStatus], () =>
    getServerTasksByTaskStatus(nodeId, taskStatus)
  );

  return { status, isLoading, error, data };
}

export function useGetServerTaskByTaskId(nodeId: string, taskId: string, taskStatus?: string) {
  const isRunningOrPending = taskStatus === 'running' || taskStatus === 'pending';
  const { status, isLoading, error, data } = useQuery(
    ['getServerTask', nodeId, taskId],
    () => getServerTaskByTaskId(nodeId, taskId),
    {
      enabled: isRunningOrPending,
      refetchInterval: isRunningOrPending ? 30000 : false,
    }
  );

  return { status, isLoading, error, data };
}

export function useGetServerTasks(nodeId: string) {
  const { status, isLoading, error, data } = useQuery(['getRecentServerTask', nodeId], () => getServerTasks(nodeId));
  return { status, isLoading, error, data };
}

export function usePostActionDruidApiCall(nodeId: string, phase: ActionPhase | '', triggerPostActionAPICall = false) {
  const { status, isLoading, error, data } = useQuery(
    ['postServerActionAPI', nodeId, phase],
    async () => {
      return (await postServerDruidActions(nodeId, phase)) as ServerActionPostDataType[];
    },

    { enabled: triggerPostActionAPICall }
  );
  return { status, isLoading, error, data };
}

export function usePostActionRadioApiCall(
  local_node_id: string,
  phase: ActionPhase | '',
  triggerPostActionAPICall = false,
  actionType: ServerActionType | ''
) {
  const { status, isLoading, error, data } = useQuery(
    ['postServerActionAPI', local_node_id, phase],
    async () => {
      return (await postServerRadioActions(local_node_id, phase, actionType)) as ServerActionPostDataType[];
    },

    { enabled: triggerPostActionAPICall }
  );
  return { status, isLoading, error, data };
}

export function useGetActionApiCall(postActionData: any, triggerGetActionAPICall: boolean) {
  const [shouldRefetch, setShouldRefetch] = useState(false);

  const { status, isLoading, error, data, refetch } = useQuery(
    ['getServerActionAPI', triggerGetActionAPICall, postActionData && postActionData[0]?.uid],
    async () => {
      return (await getServerAction(postActionData)) as ServerActionGetDataType[];
    },

    { enabled: triggerGetActionAPICall }
  );

  useEffect(() => {
    if (
      data &&
      !_.isEmpty(data[0].info) &&
      !_.isEmpty(data[0].info.filter((task) => task.progress === 'Results available'))
    ) {
      setShouldRefetch(false);
    } else {
      setShouldRefetch(true);
    }
  }, [data]);

  useEffect(() => {
    let timerId: NodeJS.Timeout;
    if (shouldRefetch) {
      timerId = setInterval(() => {
        refetch();
      }, 3000);
    }

    return () => {
      if (timerId) {
        clearInterval(timerId);
      }
    };
  }, [shouldRefetch, refetch]);

  return { status, isLoading, error, data, refetch };
}
