import { useQuery } from '@tanstack/react-query';
import { getActiveAlarms } from '../../../../services/metricsCollector';

export default function useMCActiveAlarms(cell_refs?: string[], nodeIds?: string[]) {
  return useQuery(['getActiveAlarm', cell_refs, nodeIds], () => getActiveAlarms(cell_refs, nodeIds), {
    enabled: !!cell_refs || !!nodeIds,
    refetchInterval: 10000,
    retry: false,
  });
}
