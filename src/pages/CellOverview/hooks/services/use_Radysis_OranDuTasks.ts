import { useToast } from '@chakra-ui/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { postOranDuActions } from '../../../../services/orchestrator';

export type ApiResponse = { action: string; [key: string]: any };

function formatNodeType(nodeType: string): string | never {
  switch (nodeType.toUpperCase()) {
    case 'DU':
      return 'DU';
    case 'CUUP':
      return 'CU_UP';
    case 'CUCP':
      return 'CU_CP';
    default:
      throw new Error("Input should be 'DU', 'CU_CP' or 'CU_UP'");
  }
}

export function usePostActionOranDuApiCall(
  deploymentName: string,
  nodeType: string,
  actionType: string,
  setTriggerGetAction: (value: boolean) => void,
  setPostActionData: (value: any) => void
) {
  const toast = useToast();
  const queryClient = useQueryClient();
  const sanitisedNodeType = formatNodeType(nodeType);

  const mutation = useMutation(
    async () => {
      const response = await postOranDuActions(deploymentName, sanitisedNodeType, actionType);
      if (Array.isArray(response)) {
        if (response.length > 0) {
          return response[0] as ApiResponse;
        }
        throw new Error('Empty response array');
      }
      return response as ApiResponse;
    },
    {
      onSuccess: (data) => {
        queryClient.invalidateQueries(['getPodNodeDetails']);
        if (data) {
          toast({
            title: `${data?.action} Action initiated successfully.`,
            status: 'success',
            duration: 5000,
            isClosable: true,
            position: 'top',
          });
          setTriggerGetAction(true);
          setPostActionData(() => data || null);
        }
      },
      onError: (error: any) => {
        toast({
          title: 'Action failed',
          description: error.message || 'An error occurred during the action.',
          status: 'error',
          duration: 5000,
          isClosable: true,
          position: 'top',
        });
      },
    }
  );

  return {
    triggerPostAction: mutation.mutate,
    isMutating: mutation.isLoading,
  };
}
