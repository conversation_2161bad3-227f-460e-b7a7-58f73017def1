import { useQuery } from '@tanstack/react-query';
import { deleteNodeFromNodeList } from '../../../../services/inventoryManager';

export default function useDeleteNodeByNodeId(node_id: string) {
  const { status, isLoading, error, data, refetch } = useQuery(
    ['getServerNodeDetailsByNodeId', node_id],
    () => deleteNodeFromNodeList(node_id),
    {
      //placeholderData: checkForDevMode ? getCellByRefMock : undefined,
      //enabled: checkForDevMode ? false : !!(isComponentOpen || node_id),
      //refetchInterval: isComponentOpen ? 10000 : false,
      retry: true,
    }
  );

  const deleteNodeByNodeId = async (node_id: string) => {
    // Assuming deleteNodeFromNodeList is a function that performs the deletion
    try {
      await deleteNodeFromNodeList(node_id);
      // Refetch data after successful deletion
      refetch();
    } catch (error) {
      // Handle errors, if any
    }
  };

  return { status, isLoading, error, data, refetch, deleteNodeByNodeId };
}
