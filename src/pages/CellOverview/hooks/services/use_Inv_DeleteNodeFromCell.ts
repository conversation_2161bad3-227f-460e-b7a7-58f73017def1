import { useToast } from '@chakra-ui/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { deleteNodeFromCell } from '../../../../services/inventoryManager';

interface Error {
  message: string[];
  statusCode: number;
}
export function useDeleteNodeFromCell(customMsg?: boolean) {
  const queryClient = useQueryClient();
  const toast = useToast();

  const { mutateAsync: deleteNodeFromCellMutation } = useMutation({
    mutationFn: ({
      cell_ref,
      node_id,
    }: {
      // cell_ref: string | [string];
      cell_ref: string;
      node_id: string;
    }) => {
      return deleteNodeFromCell(cell_ref, node_id);
    },
    onSuccess: (_, { cell_ref }) => {
      queryClient.invalidateQueries({ queryKey: ['getNodesForCell'] });
      queryClient.invalidateQueries({ queryKey: ['getNodeData'] });
      queryClient.invalidateQueries({ queryKey: ['getCellList'] });
      toast({
        title: 'Success',
        description: customMsg ? `${cell_ref} has been unlinked successfully.` : `Node has been deleted successfully.`,
        status: 'success',
        duration: 5000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `${(error as AxiosError<Error>)?.response?.data?.message}`,
        status: 'error',
        duration: 5000,
        isClosable: true,
        position: 'top',
      });
    },
  });
  return { deleteNodeFromCellMutation };
}
