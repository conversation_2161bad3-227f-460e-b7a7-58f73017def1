import { useToast } from '@chakra-ui/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { addNodeToCell } from '../../../../services/inventoryManager';

export default function useAddNodeToCell(node_id: string) {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: (cell_ref: string) => addNodeToCell(cell_ref, node_id),
    onSuccess: (_, cell_ref) => {
      queryClient.invalidateQueries({ queryKey: ['getNodeData'] });
      queryClient.invalidateQueries({ queryKey: ['getCellList'] });
      queryClient.invalidateQueries({ queryKey: ['getNodesForCell'] });

      toast({
        title: 'Node Added.',
        description: `${cell_ref} has been linked successfully.`,
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: (error) => {
      toast({
        title: `Error ${error}`,
        description: 'Something went wrong.',
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
  });
}
