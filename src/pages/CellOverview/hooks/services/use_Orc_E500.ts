import { useToast } from '@chakra-ui/toast';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  getE500SoftwareUpdateStatus,
  getE500SoftwareVersions,
  postE500SoftwareVersion,
} from '../../../../services/orchestrator';

const useE500 = () => {
  return {
    getE500SoftwareVersions: useGetE500SoftwareVersions,
    updateE500SoftwareVersion: useUpdateE500SoftwareVersion,
    getE500SoftwareUpdateStatus: useGetE500SoftwareUpdateStatus,
  };
};

export const useGetE500SoftwareVersions = (isOpen: boolean) => {
  const { data, error, isLoading, refetch, isFetching } = useQuery(
    ['getE500SoftwareVersions'],
    () => getE500SoftwareVersions(),
    {
      enabled: isOpen,
    }
  );
  return { data, error, isLoading, refetch, isFetching };
};

export const useUpdateE500SoftwareVersion = () => {
  //const queryClient = useQueryClient();
  const toast = useToast();
  return useMutation({
    mutationFn: (payloadData: any) => postE500SoftwareVersion(payloadData),
    onSuccess: (data, variables) => {
      //queryClient.invalidateQueries({ queryKey: ['getRadioNodeDetails'] });
      toast({
        title: 'E500 updated.',
        description: 'E500 update is in progress.',
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: (error) => {
      console.error('--- API error --- :', error);
      toast({
        title: `Error ${error}`,
        description: 'Something went wrong with the updating the E500.',
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
      throw error;
    },
  });
};

export const useGetE500SoftwareUpdateStatus = (isOpen: boolean, deployment_id: string) => {
  const { data, error, isLoading, refetch, isFetching } = useQuery(
    ['getE500SoftwareUpdateStatus'],
    () => getE500SoftwareUpdateStatus(deployment_id),
    {
      enabled: isOpen,
    }
  );
  return { data, error, isLoading, refetch, isFetching };
};

export default useE500;
