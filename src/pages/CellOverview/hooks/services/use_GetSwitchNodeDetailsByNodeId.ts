import { useQuery } from '@tanstack/react-query';
import { getSwitchNodeDetailsByNodeId } from '../../../../services/orchestrator';
import { SwitchNode } from '../../../../types/orchestrator.types';

export default function useGetSwitchNodeDetailsByNodeId(node_id: string, shouldFetchData: boolean) {
  const queryFunction = () => {
    if (!shouldFetchData) {
      // Return a resolved promise with default data when the query should not be fetched
      return Promise.resolve<SwitchNode>({
        fibrolan_switch: {},
        id: '',
        juniper_switch: {},
        mosolabs_switch: {},
      });
    }
    return getSwitchNodeDetailsByNodeId(node_id);
  };

  const { status, isLoading, error, data } = useQuery<SwitchNode>(
    ['getSwitchNodeDetailsByNodeId', node_id],
    queryFunction,
    {
      enabled: shouldFetchData,
      retry: false,
    }
  );

  // Customize isLoading based on shouldFetchData
  const customIsLoading = shouldFetchData && isLoading;
  return { status, isLoading: customIsLoading, error, data };
}
