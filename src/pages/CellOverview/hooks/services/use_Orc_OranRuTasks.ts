import { useToast } from '@chakra-ui/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { startCase } from 'lodash';
import { postOranRuActions } from '../../../../services/orchestrator';
import { OranRuActionType } from '../../../../components/nodeComponents/du-cuManager/OranRuActionTypes';
import { ActionPhase } from './use_Orc_ServerTasks';

export type ApiResponse = { [key: string]: any };

export function usePostActionOranRuApiCall(
  local_node_id: string,
  phase: ActionPhase | '',
  actionType: OranRuActionType | '',
  setTriggerGetAction: (value: boolean) => void,
  setPostActionData: (value: any) => void
) {
  const toast = useToast();
  const queryClient = useQueryClient();

  const mutation = useMutation(
    async () => {
      const response = await postOranRuActions(local_node_id, phase, actionType);

      // Validate and handle array response
      if (Array.isArray(response) && response.length > 0) {
        return response[0]; // Use the first object in the array
      }
      throw new Error('Invalid response: expected a non-empty array');
    },
    {
      onSuccess: (data) => {
        queryClient.invalidateQueries(['getPodNodeDetails']);
        if (data) {
          const formattedType = startCase(data?.type);
          toast({
            title: `${formattedType} Action initiated successfully.`,
            status: 'success',
            duration: 5000,
            isClosable: true,
            position: 'top',
          });
          setTriggerGetAction(true);
          setPostActionData(() => data || null);
        }
      },
      onError: (error: any) => {
        toast({
          title: 'Action failed',
          description: error.message || 'An error occurred during the action.',
          status: 'error',
          duration: 5000,
          isClosable: true,
          position: 'top',
        });
      },
    }
  );

  return {
    triggerPostAction: mutation.mutate,
    isMutating: mutation.isLoading,
  };
}
