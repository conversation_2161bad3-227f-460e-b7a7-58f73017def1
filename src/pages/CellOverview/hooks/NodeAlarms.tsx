import { Box } from '@chakra-ui/react';
import { TabPanel } from '@chakra-ui/react';
import { TabPanels } from '@chakra-ui/react';
import { Heading } from '@chakra-ui/react';
import { Tab } from '@chakra-ui/react';
import { TabList } from '@chakra-ui/react';
import { Tabs } from '@chakra-ui/react';
import { useParams } from 'react-router-dom';
import Alarms from '../../MetricsCollector/components/Alarms/Alarms';
import { useState, useEffect } from 'react';

const NodeAlarms = ({ nodeId }: { nodeId: string }) => {
  const { tab, id } = useParams();
  // Determine initial index based on URL or default to 0 (Active)
  const initialTabIndex = tab === 'history-alarms' ? 1 : 0;
  const [tabIndex, setTabIndex] = useState(initialTabIndex);

  // Update state if URL changes
  useEffect(() => {
    setTabIndex(tab === 'history-alarms' ? 1 : 0);
  }, [tab]);

  const handleTabsChange = (index: number) => {
    setTabIndex(index);
  };

  const pathname = `/cell-overview/nodes/${nodeId}`;

  return (
    <Tabs isFitted variant="enclosed-colored" orientation="horizontal" index={tabIndex} onChange={handleTabsChange}>
      <TabList>
        <Tab>
          <Heading fontWeight="small" size="lg">
            Active
          </Heading>
        </Tab>
        <Tab>
          <Heading fontWeight="small" size="lg">
            History
          </Heading>
        </Tab>
      </TabList>
      <TabPanels>
        {/* Active Alarms Panel */}
        <TabPanel p={0}>
          <Box bg="bg-surface" p="0" m="0">
            <Alarms nodeId={nodeId} useServerFacets={false} pathname={`${pathname}/alarms/active`} openAlarms={true} />
          </Box>
        </TabPanel>
        {/* History Alarms Panel */}
        <TabPanel p={0}>
          <Box bg="bg-surface" p="0" m="0">
            <Alarms
              nodeId={nodeId}
              useServerFacets={false}
              pathname={`${pathname}/alarms/history`}
              openAlarms={false}
            />
          </Box>
        </TabPanel>
      </TabPanels>
    </Tabs>
  );
};

export default NodeAlarms;
