import { Text, Box, Link } from '@chakra-ui/layout';
import { Tooltip } from '@chakra-ui/tooltip';
import { ColumnDef } from '@tanstack/react-table';
import React from 'react';
//import { Cell as CellType } from '../../../types/InventoryManager.type';
import StatusComponent from '../../../components/icons/StatusIcon';
import { AlarmDetails, Node as NodeType } from '../../../types/InventoryManager.type';
import NodeConfigMenu from '../NodeConfigMenu';
import { getStatusColor } from './useStatus';
import { ExternalLinkIcon } from '@chakra-ui/icons';
import { Flex } from '@chakra-ui/react';
import { BsArrowReturnRight } from 'react-icons/bs';
import CellAlarms from '../CellAlarms';
import Loader from '../../../components/loader/Loader';

export default function useNodeColumns() {
  return React.useMemo<ColumnDef<NodeType, unknown>[]>(
    () => [
      {
        header: 'Node id',
        accessorKey: 'node_id',
        id: 'node_id',
        cell: (props) => {
          const cell_ref = props.row.original.node_id as string;
          return (
            <>
              <Flex>
                <BsArrowReturnRight />
                <Text ml={2}>{cell_ref}</Text>
              </Flex>
            </>
          );
        },
      },
      {
        header: 'Node Type',
        accessorKey: 'node_type',
        id: 'node_type',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        header: 'Site name',
        accessorKey: 'site_name',
        id: 'site_name',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        header: 'Region',
        accessorKey: 'region_name',
        id: 'region_name',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        header: 'Country',
        accessorKey: 'country_code',
        id: 'country_code',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        header: 'Orient',
        accessorKey: 'orientation',
        id: 'orientation',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        header: 'Serial No',
        accessorKey: 'node_serial_no',
        id: 'node_serial_no',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
        cell: ({ getValue }) => {
          const value = getValue() as string;
          const shouldShowTooltip = value && value.length > 14;
          const content = (
            <Box isTruncated maxWidth="20ch">
              {value}
            </Box>
          );
          return shouldShowTooltip ? <Tooltip label={value}>{content}</Tooltip> : content;
        },
      },
      {
        header: 'lat / long',
        accessorKey: ['latitude'],
        id: 'latitude',
        cell: ({ row }) => {
          return (
            <Link
              textAlign="center"
              isExternal
              rel="noopener noreferrer"
              href={`http://www.google.com/maps/place/${row.original.latitude},${row.original.longitude}`}
            >
              {row.original.latitude} / {row.original.longitude}
              <ExternalLinkIcon marginLeft={'0.5'} />
            </Link>
          );
        },
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        header: 'Roles',
        accessorKey: 'roles',
        id: 'roles',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
        accessorFn: (originalRow) => {
          const roles = originalRow?.roles?.toString() || 'N/A';
          return roles;
        },
        cell: ({ getValue }) => {
          const roles = getValue() as string | undefined;
          const shouldShowTooltip = roles && roles.length > 14;
          const content = (
            <Box isTruncated maxWidth="14ch">
              {roles}
            </Box>
          );
          return (shouldShowTooltip ? <Tooltip label={roles}>{content}</Tooltip> : roles) || 'N/A';
        },
      },
      // {
      //   header: 'location_error',
      //   accessorKey: 'location_error',
      //   id: 'location_error',
      // },
      {
        header: 'Lifecycle',
        accessorKey: 'lifecycle',
        id: 'lifecycle',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        header: () => {
          return (
            <Tooltip label="If a node has 0 then it is not associated with any cells">
              <Text>Cells</Text>
            </Tooltip>
          );
        },
        accessorKey: 'cell_count',
        id: 'cell_count',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
        cell: ({ row }) => {
          const value = row.original.cell_refs?.length as number;
          return value;
        },
      },
      {
        header: 'Alarms',
        accessorKey: 'node_alarms',
        id: 'node_alarms',
        cell: (props) => {
          const alarmDetails = props.row.original.node_alarms as AlarmDetails;
          if (alarmDetails) {
            return <CellAlarms alarmDetails={alarmDetails} node_id={props.row.original.node_id} />;
          }
          return <Loader size="md" />;
        },
      },

      {
        header: `Status`,
        accessorKey: 'status',
        id: 'status',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
        cell: (props) => {
          const cellStatus = props.row.original.status as string;
          const cellRef = props.row.original.cell_refs as unknown as string;
          const nodeId = props.row.original.node_id as string;
          const nodeType = props.row.original.node_type as string;
          const node_serial_no = props?.row?.original?.node_serial_no as string;
          return (
            <Box display="flex" justifyContent="space-around">
              <StatusComponent
                dataTestId="cell-main-table-status-icon"
                boxSize="sm"
                color={getStatusColor(cellStatus)}
                status={cellStatus}
                node_id={props.row.original.node_id}
              />{' '}
              <NodeConfigMenu
                dataTestId="cell-main-table-node-config-icon"
                cellStatus={cellStatus}
                cellRef={cellRef}
                nodeId={nodeId}
                nodeType={nodeType}
                node_serial_no={node_serial_no}
              />
              {/* <Link onClick={() => goToCell(cellRef)}>cell link</Link> */}
            </Box>
          );
        },
      },
    ],
    []
  );
}
