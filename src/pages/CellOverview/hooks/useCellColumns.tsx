import React from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { Box, Text } from '@chakra-ui/react';
import { Flex } from '@chakra-ui/layout';
import { AlarmDetails, Cell as CellType, Plmn } from '../../../types/InventoryManager.type';
import StatusComponent from '../../../components/icons/StatusIcon';
import CellConfigMenu from '../CellConfigMenu';
import { getStatusColor } from './useStatus';
import { ReactComponent as FourGIcon } from './../../../assets/icons/4G.svg';
import { ReactComponent as FiveGIcon } from './../../../assets/icons/5G.svg';
import { BsArrowReturnRight } from 'react-icons/bs';
import CellAlarms from '../CellAlarms';

export default function useCellColumns() {
  return React.useMemo<ColumnDef<CellType, unknown>[]>(
    () => [
      {
        header: 'Cell Reference',
        accessorKey: 'cell_ref',
        id: 'cell_ref',
        cell: (props) => {
          const cell_ref = props.row.original.cell_ref as string;

          return (
            <>
              <Flex>
                <BsArrowReturnRight />
                <Text ml="2">{cell_ref}</Text>
              </Flex>
            </>
          );
        },
      },
      {
        header: 'Cell Type',
        accessorKey: 'oran_split',
        id: 'oran_split',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        header: 'Site',
        accessorKey: 'site_name',
        id: 'site_name',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        header: 'Orient',
        accessorKey: 'orientation',
        id: 'orientation',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        header: 'Region',
        accessorKey: 'region_name',
        id: 'region_name',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        header: 'Gen',
        accessorKey: 'ran_type',
        id: 'ran-technology',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
        cell: ({ getValue }) => {
          const value = getValue();
          return (
            <Flex>
              {value === '4G' && <FourGIcon width="30px" height="30px" />}
              {value === '5G' && <FiveGIcon width="30px" height="30px" />}
            </Flex>
          );
        },
      },
      {
        header: 'Band',
        id: 'band',
        accessorFn: (row: CellType) => {
          return row.bands.join(', ');
        },
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
        cell: ({ getValue }) => {
          const bands = (getValue() as string).split(', ');
          return (
            <ul>
              {bands && bands.length > 0
                ? bands.map((band, index) => (
                    <li style={{ listStyleType: 'none' }} key={index}>
                      {band}
                    </li>
                  ))
                : 'N/A'}
            </ul>
          );
        },
      },
      {
        header: 'Operator',
        id: 'operator',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
        accessorFn: (originalRow: CellType): string => {
          const plmns = originalRow.plmns;
          if (!Array.isArray(plmns) || plmns.length === 0) return 'N/A';
          // all operator names into a single string
          const operatorNames = plmns.map((plmn) => plmn.operator || 'Unknown').join('|');
          return operatorNames;
        },
      },
      {
        header: 'Country',
        accessorKey: 'country_code',
        id: 'country_code',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        header: 'Lifecycle',
        accessorKey: 'lifecycle',
        id: 'lifecycle',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        header: 'Alarms',
        accessorKey: 'cell_alarms',
        id: 'cell_alarms',
        cell: (props) => {
          const alarmDetails = props.row.original.cell_alarms as AlarmDetails;
          const cell_ref = props.row.original.cell_ref;

          return <CellAlarms alarmDetails={alarmDetails} cell_ref={cell_ref} />;
        },
      },
      {
        header: `Status`,
        accessorKey: 'status',
        id: 'status',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
        cell: (props) => {
          const cellStatus = props.row.original.status as string;
          const cell_ref = props.row.original.cell_ref as string;
          const country_code = props.row.original.country_code as string;
          return (
            <Box display="flex" justifyContent="space-between" mr="2">
              <StatusComponent
                dataTestId="cell-main-table-status-icon"
                boxSize="sm"
                color={getStatusColor(cellStatus)}
                status={cellStatus}
                cell_ref={cell_ref}
              />{' '}
              <CellConfigMenu
                dataTestId="cell-main-table-cell-config-icon"
                cellStatus={cellStatus}
                cell_ref={cell_ref}
                country_code={country_code}
              />
            </Box>
          );
        },
      },
    ],
    []
  );
}
