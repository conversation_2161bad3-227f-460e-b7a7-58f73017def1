import {
  CRIT<PERSON><PERSON>,
  <PERSON>RO<PERSON>,
  <PERSON><PERSON><PERSON>,
  OK,
  SHUTDOWN,
  TRA<PERSON><PERSON>_CRITICAL_PURPLE,
  TRA<PERSON><PERSON>_DEFAULT,
  TRAFFIC_ERROR_RED,
  TRAFFIC_OK_GREEN,
  TRAFFIC_SHUTDOWN_BLACK,
  TRAFFIC_UNKNOWN_GRAY,
  TRAFFIC_WARNING_ORANGE,
  TRUE,
  UNKNOWN,
  WARNING,
} from '../../../data/constants';

export function getStatusColor(status: string) {
  switch (status) {
    case OK:
      return TRAFFIC_OK_GRE<PERSON>;
    case WARNING:
      return TRAFFIC_WARNING_ORANGE;
    case ERROR:
      return TRAFFIC_ERROR_RED;
    case CRITICAL:
      return TRAFFIC_CRITICAL_PURPLE;
    case SHUTDOWN:
      return TRAFFIC_SHUTDOWN_BLACK;
    case UNKNOWN:
      return TRAFFIC_UNKNOWN_GRAY;
    case TRUE:
      return TRAFFIC_OK_GRE<PERSON>;
    case FALSE:
      return TRAFFIC_SHUTDOWN_BLACK;
    default:
      return TRAFFIC_DEFAULT;
  }
}
