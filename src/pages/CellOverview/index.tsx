import {
  Box,
  Button,
  FormControl,
  FormLabel,
  Heading,
  Icon,
  Stack,
  Switch,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
  useColorModeValue,
} from '@chakra-ui/react';
import { AddIcon } from '@chakra-ui/icons';
import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { AUTH_TOKEN_KEY, LIFE_CYCLE, READ_WRITE_ACCESS_ROLES } from '../../data/constants';
import useLogin from '../../hooks/useLogin';
import Cells from './Cells';
import Nodes from './Nodes';
import CellSummary from './CellSummary';

const CellOverview = () => {
  const navigate = useNavigate();
  const { tab, id } = useParams();
  const [tabIndex, setTabIndex] = useState(0);
  const [urlCellRef, setUrlCellRef] = useState<string>('');
  const [urlNodeId, setUrlNodeId] = useState<string>('');
  const [alternativeViewId, setAlternativeViewId] = useState<string>('');
  const [selectedFilter, setSelectedFilter] = useState<Record<string, string> | undefined>({});
  const [lifecycle, setLifecycle] = useState<LIFE_CYCLE | null>(() => {
    const stored = localStorage.getItem('OperationalOnly');
    return stored === 'true' ? LIFE_CYCLE.OPERATIONAL : null;
  });

  useEffect(() => {
    const opOnly = lifecycle === LIFE_CYCLE.OPERATIONAL;
    localStorage.setItem('OperationalOnly', JSON.stringify(opOnly));
  }, [lifecycle]);

  const handleToggle = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLifecycle(e.target.checked ? LIFE_CYCLE.OPERATIONAL : null);
  };

  const { checkApplicationAccess } = useLogin(AUTH_TOKEN_KEY);
  const checkRoleAccess = checkApplicationAccess(READ_WRITE_ACCESS_ROLES);
  useEffect(() => {
    if (tab === 'cells') {
      setTabIndex(0);
      setUrlCellRef(id || '');
    } else if (tab === 'nodes') {
      setTabIndex(1);
      setUrlNodeId(id || '');
    }
  }, [tab]);

  const handleTabs = (index: number) => {
    if (index === 0) {
      navigate('/cell-overview/cells');
    } else {
      navigate('/cell-overview/nodes');
    }
  };
  const colorModeValue = useColorModeValue('sm', 'sm-dark');

  return (
    <>
      <Stack
        spacing="1"
        direction={{
          base: 'column',
          lg: 'row',
        }}
        justify="space-between"
      >
        <Stack spacing="1">
          <Heading fontWeight="medium">Cell Overview</Heading>
        </Stack>
        {checkRoleAccess && (
          <Button
            data-testid="create-cell-button"
            variant="primary"
            leftIcon={<Icon as={AddIcon} marginStart="-1" />}
            onClick={() =>
              tabIndex === 0 ? navigate('/cell-overview/create') : navigate('/cell-overview/create/node')
            }
          >
            {tabIndex === 0 ? 'Create cell' : 'Create node'}
          </Button>
        )}
      </Stack>

      <Stack
        spacing={{
          base: '5',
          lg: '6',
        }}
      >
        <Box alignSelf="flex-end" my="2">
          <FormControl display="flex" alignItems="center">
            <FormLabel htmlFor="lifecycle-operational" mb="0" fontSize="xl" fontWeight="bold">
              Operational only
            </FormLabel>
            <Switch
              colorScheme="teal"
              size="lg"
              id="lifecycle-operational"
              isChecked={lifecycle === LIFE_CYCLE.OPERATIONAL}
              onChange={handleToggle}
            />
          </FormControl>
        </Box>
        {tabIndex === 0 && <CellSummary setSelectedFilter={setSelectedFilter} lifecycle={lifecycle} />}
      </Stack>

      <Stack
        spacing={{
          base: '5',
          lg: '6',
        }}
      >
        <Box
          bg="bg-surface"
          boxShadow={{
            base: 'none',
            md: colorModeValue,
          }}
        >
          <Stack spacing="5">
            <Tabs
              isManual
              isFitted
              variant="enclosed-colored"
              orientation="horizontal"
              isLazy
              onChange={(index: number) => handleTabs(index)}
              index={tabIndex}
            >
              <TabList>
                <Tab>
                  <Heading fontWeight="medium" size="lg">
                    Cells
                  </Heading>
                </Tab>
                <Tab>
                  <Heading fontWeight="medium" size="lg">
                    Nodes
                  </Heading>
                </Tab>
              </TabList>
              <TabPanels>
                <TabPanel id="cells">
                  <Cells
                    alternativeViewId={alternativeViewId}
                    urlCellRef={urlCellRef}
                    selectedFilter={selectedFilter}
                    lifecycle={lifecycle}
                  />
                </TabPanel>
                <TabPanel id="nodes">
                  <Nodes alternativeViewId={alternativeViewId} urlNodeId={urlNodeId} lifecycle={lifecycle} />
                </TabPanel>
              </TabPanels>
            </Tabs>
          </Stack>
        </Box>
      </Stack>
    </>
  );
};

export default CellOverview;
