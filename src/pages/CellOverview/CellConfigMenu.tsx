import { BellIcon, DeleteIcon, EditIcon, LinkIcon } from '@chakra-ui/icons';
import {
  Box,
  Button,
  IconButton,
  Link,
  Menu,
  MenuButton,
  Menu<PERSON><PERSON>,
  <PERSON>u<PERSON>ist,
  <PERSON>dal,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON><PERSON>utton,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  ModalOverlay,
  Stack,
  Text,
  useDisclosure,
} from '@chakra-ui/react';
import { BsThreeDots } from 'react-icons/bs';
import { useNavigate } from 'react-router-dom';
import StreetCellConfigPortal from '../../components/portal/Portal';
import { AUTH_TOKEN_KEY, READ_WRITE_ACCESS_ROLES } from '../../data/constants';
import useLogin from '../../hooks/useLogin';
import { useDeleteNodeFromCell } from './hooks/services/use_Inv_DeleteNodeFromCell';
import useGetNodesByCellRef from './hooks/services/use_Inv_GetNodesForCell';
import useCellConfig from './hooks/useCellConfig';

type CellConfigMenuProps = {
  dataTestId?: string;
  cellStatus: string;
  cell_ref: string;
  country_code?: string;
};

const CellConfigMenu = (props: CellConfigMenuProps) => {
  const { cellStatus, cell_ref, country_code } = props;
  const navigate = useNavigate();
  const { isOpen, onOpen, onClose } = useDisclosure();

  const { isDeleting, cellDeleted, setCellDeleted, deleteCellHandler } = useCellConfig(cell_ref);
  const cellNodeList = useGetNodesByCellRef(cell_ref ?? '').data;
  const cellNodeListIds = cellNodeList
    ? Object.values(cellNodeList)
        .map((node: any) => node.node_id)
        .join(',')
    : [];

  const hasCellNodes = cellNodeList && cellNodeList.length > 0;
  const { deleteNodeFromCellMutation } = useDeleteNodeFromCell();

  const { checkApplicationAccess } = useLogin(AUTH_TOKEN_KEY);
  const checkRoleAccess = checkApplicationAccess(READ_WRITE_ACCESS_ROLES);

  const handelDeleteCell = async () => {
    onOpen();
    setCellDeleted(false);
  };

  return (
    <>
      {checkRoleAccess && (
        <Menu>
          <MenuButton
            data-testid={props.dataTestId}
            onClick={(e) => {
              e.stopPropagation();
            }}
            as={IconButton}
            aria-label="Options"
            icon={<BsThreeDots />}
            variant="outline"
          />
          <MenuList>
            <MenuItem
              data-testid="edit-plmns"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();

                navigate('/cell-overview/Plmn', { state: { country_code, cell_ref } });
              }}
            >
              <EditIcon mr="1rem" />
              Edit PLMN
            </MenuItem>
            <MenuItem
              data-testid="edit-cell"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                navigate(`/cell-overview/edit`, {
                  state: { cell_ref },
                });
              }}
            >
              <EditIcon mr="1rem" />
              Edit cell
            </MenuItem>
            <MenuItem
              data-testid="delete-cell"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handelDeleteCell();
              }}
            >
              <DeleteIcon mr="1rem" />
              Delete cell
            </MenuItem>
            <MenuItem
              data-testid="view-node"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                navigate(`/cell-overview/nodes/${cellNodeListIds}`);
              }}
            >
              <LinkIcon mr="1rem" color="green" />
              View node
            </MenuItem>
            <MenuItem
              data-testid="alarms-events-cells"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                navigate(`/alarms-and-events/events/${cell_ref}`);
              }}
            >
              <BellIcon mr="1rem" color="green" />
              <Link>View Events</Link>
            </MenuItem>
          </MenuList>
        </Menu>
      )}
      <StreetCellConfigPortal>
        <Modal isOpen={cellDeleted ? false : isOpen} onClose={onClose} size="xl">
          <ModalOverlay />
          <ModalContent data-testid="cell-config-modal">
            <ModalHeader>Delete Cell</ModalHeader>
            <ModalCloseButton />
            <ModalBody>
              <Text fontWeight="semibold">
                Are you sure you want to delete <u>{cell_ref}</u> cell?
              </Text>
              <Text mt="2" textStyle="lg" fontWeight="medium">
                Deleting the cell will remove all its associated nodes.
              </Text>
              {hasCellNodes && (
                <Box mt={4} fontSize="sm">
                  {cellNodeList.map((node: any) => {
                    return (
                      <Box as="section" key={node.node_id}>
                        <Box bg="bg.surface" boxShadow="sm" borderRadius="lg" p={{ base: '4', md: '6' }}>
                          <Stack
                            direction={{ base: 'column', md: 'row' }}
                            spacing={{ base: '5', md: '6' }}
                            justify="space-between"
                          >
                            <Stack spacing="1">
                              <Text textStyle="lg" fontWeight="bold">
                                Node
                              </Text>
                              <Text textStyle="sm" color="fg.muted">
                                {node.node_id}
                              </Text>
                            </Stack>
                            <Box>
                              <Button
                                onClick={() =>
                                  deleteNodeFromCellMutation({
                                    node_id: node.node_id,
                                    cell_ref: cell_ref ?? '',
                                  })
                                }
                              >
                                Remove
                              </Button>
                            </Box>
                          </Stack>
                        </Box>
                      </Box>
                    );
                  })}
                </Box>
              )}
            </ModalBody>
            <ModalFooter>
              <Button colorScheme="brand" mr="3" onClick={onClose}>
                Cancel
              </Button>
              <Button onClick={() => deleteCellHandler(cell_ref)}>Delete</Button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      </StreetCellConfigPortal>
    </>
  );
};

export default CellConfigMenu;
