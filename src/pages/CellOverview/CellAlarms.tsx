import { CellAlarmsProps } from '../../types/InventoryManager.type';
import { Box, Flex, IconButton, Menu, MenuButton, MenuItem, MenuList, Text, Tooltip } from '@chakra-ui/react';
import { NavigateFunction, useNavigate } from 'react-router-dom';
import { ChevronDownIcon, ExternalLinkIcon, WarningTwoIcon } from '@chakra-ui/icons';

const SeverityMenuItem = ({
  label,
  count,
  color,
  navigate,
  id,
}: {
  label: string;
  count: number;
  color: string;
  navigate: NavigateFunction;
  id?: string;
}) => {
  return (
    <MenuItem
      data-testid={`cell-alarms-${label.toLowerCase()}`}
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
        count > 0 && navigate(`/alarms-and-events/alarms/${id}`, { state: { severityCategory: label.toLowerCase() } });
      }}
      justifyContent="space-between"
      cursor={count > 0 ? 'pointer' : 'default'}
    >
      {label}:
      <Box as="span" display="flex">
        <Box
          as="span"
          backgroundColor={count > 0 ? color : 'gray.500'}
          color="white"
          padding="2px 5px"
          borderRadius="5px"
          mr={count === 0 ? '18px' : '2px'}
          width="24px"
          display="flex"
          justifyContent="center"
        >
          {count > 0 ? count : 0}
        </Box>
        {count > 0 && <ExternalLinkIcon marginLeft="0.5" />}
      </Box>
    </MenuItem>
  );
};

const CellAlarms = ({ alarmDetails, cell_ref, node_id }: CellAlarmsProps) => {
  const {
    total = 0,
    severity: {
      major = { count: 0 },
      minor = { count: 0 },
      critical = { count: 0 },
      warning = { count: 0 },
      none = { count: 0 },
    } = {},
  } = alarmDetails;
  const isAlarmDetailsError = Boolean(alarmDetails?.error);
  const navigate = useNavigate();
  const severities = [
    { label: 'Critical', count: critical.count, color: 'red.500' },
    { label: 'Major', count: major.count, color: 'orange.500' },
    { label: 'Minor', count: minor.count, color: 'yellow.500' },
    { label: 'Warning', count: warning.count, color: 'blue.500' },
    { label: 'None', count: none.count, color: 'green.500' },
  ];
  const id = cell_ref || node_id;
  return (
    <Flex minWidth="max-content" alignItems="center" onClick={(e) => e.stopPropagation()} cursor="default">
      <Text textAlign="center" width="8">
        {isAlarmDetailsError ? (
          <Tooltip label={alarmDetails?.error?.message} hasArrow>
            <WarningTwoIcon mb="1" color="orange.500" />
          </Tooltip>
        ) : (
          total
        )}
      </Text>
      {total > 0 && (
        <Menu>
          <MenuButton
            data-testid="cell-alarms-icon"
            as={IconButton}
            icon={<ChevronDownIcon boxSize={5} />}
            bg="none"
            _active={{ bg: 'none' }}
            alignItems="center"
            _hover={{ bg: 'none' }}
            m="0"
            p="0"
            size="xs"
          ></MenuButton>
          <MenuList style={{ width: 'fit-content' }}>
            {severities.map((severity) => (
              <SeverityMenuItem
                key={severity.label}
                label={severity.label}
                count={severity.count}
                color={severity.color}
                navigate={navigate}
                id={id}
              />
            ))}
            <MenuItem
              data-testid="view-all-alarms-menu-item"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                navigate(`/alarms-and-events/alarms/${id}`);
              }}
            >
              View all alarms <ExternalLinkIcon marginLeft="0.5" />
            </MenuItem>
          </MenuList>
        </Menu>
      )}
    </Flex>
  );
};

export default CellAlarms;
