import { Box, Text } from '@chakra-ui/react';
import QueryError from '../../components/errorComponents/QueryError';
import Loader from '../../components/loader/Loader';
import useGetNodesByCellRef from './hooks/services/use_Inv_GetNodesForCell';
import useNodeColumns from './hooks/useNodeColumns';
import useMCActiveAlarms from './hooks/services/use_mc_ActiveAlarms';
import { ErrorBoundaryLogError } from '../../components/errorComponents/ErrorBoundaryFallback';
import { ErrorBoundary } from 'react-error-boundary';
import { DataTable } from '../MetricsCollector/components/DataTable';
import { ErrorBoundaryFallback } from '../../components/errorComponents/ErrorBoundaryFallback';
import NodeComponents from './NodeComponents';
import { useState } from 'react';

export interface NodesProps {
  row?: {
    row: {
      getIsExpanded: () => boolean;
      original: {
        cell_ref: string;
      };
    };
  };
}

const CellNodes = ({ row }: NodesProps) => {
  const refetchData = row?.row?.getIsExpanded();
  const cellRef = row?.row?.original?.cell_ref;
  const nodeColumns = useNodeColumns();
  const [nodeLimit, setNodeLimit] = useState<number>(1000);

  const { status, isLoading, error, data: nodesData } = useGetNodesByCellRef(cellRef ?? '', false, refetchData);
  const nodeIds = nodesData?.map(({ node_id }: { node_id: string }) => node_id);
  const cellRefsArray = cellRef ? [cellRef] : undefined;

  const {
    isLoading: activeAlarmsIsLoading,
    error: ActiveAlarmsError,
    data: activeAlarms,
  } = useMCActiveAlarms(cellRefsArray, nodeIds);

  const mergeAlarmsIntoNodesData = (nodesData: any, alarmData: any) => {
    const updatedNodesData = nodesData?.map((cell: any) => {
      const alarmDetails = alarmData?.node_ids[cell?.node_id] || {};
      return {
        ...cell,
        node_alarms: ActiveAlarmsError ? { error: ActiveAlarmsError } : alarmDetails,
      };
    });

    return updatedNodesData;
  };
  const updatedNodesData = mergeAlarmsIntoNodesData(nodesData, activeAlarms);

  if (isLoading && activeAlarmsIsLoading) return <Loader />;
  if (error) return <QueryError error={error} />;
  if (ActiveAlarmsError) return <QueryError error={ActiveAlarmsError} />;

  if (!nodesData || (Array.isArray(nodesData) && nodesData.length === 0)) {
    return <Text>No data available</Text>;
  }

  const renderNodeComponents = (props: any) => {
    return (
      <NodeComponents
        row={props}
        node={updatedNodesData}
        nodeOpen={true}
        caller="Nodes"
        nodeType={props.row.original.node_type}
      />
    );
  };

  return (
    <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
      <Box ml="4" data-testid="cell-nodes" border="1.5px solid teal" borderRadius="lg" width="99%">
        <DataTable
          isExpandable={true}
          enableFilter={false}
          showSearch={false}
          columns={nodeColumns ?? []}
          data={updatedNodesData}
          isLoading={isLoading}
          defaultPageSize={100}
          renderSubComponent={(props) => renderNodeComponents(props)}
          alternativeViewId={''}
          urlNodeId={''}
          limit={nodeLimit}
          setLimit={setNodeLimit}
          count={updatedNodesData?.length}
          hasEmptyResult={updatedNodesData?.length == 0}
          version={'v2'}
          enablePagination={false}
        />
      </Box>
    </ErrorBoundary>
  );
};

export default CellNodes;
