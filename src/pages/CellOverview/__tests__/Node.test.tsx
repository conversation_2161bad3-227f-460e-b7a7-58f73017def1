import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { fireEvent, render, screen } from '@testing-library/react';
import { ReactNode } from 'react';
import { MemoryRouter } from 'react-router-dom';
import { describe, it } from 'vitest';
import StatusComponent from '../../../components/icons/StatusIcon';
import { getCellListMock } from '../../../services/mocks/getCellListMock';
import { getNodeByCellRefMock } from '../../../services/mocks/getNodeByCellRefMock';
import { DataTable } from '../../MetricsCollector/components/DataTable';
import CellConfigMenu from '../CellConfigMenu';
import CellNodes from '../CellNodes';
import { getStatusColor } from '../hooks/useStatus';

type ChildrenProps = {
  children: ReactNode;
};

// Wrapper for react query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});
const wrapper = ({ children }: ChildrenProps) => (
  <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
);

const columns = [
  {
    header: 'Cell ref',
    accessorKey: 'cell_ref',
    id: 'cell_ref',
  },
  {
    header: 'Cell Type',
    accessorKey: 'oran_split',
    id: 'oran_split',
  },
  {
    header: 'Site name',
    accessorKey: 'site_name',
    id: 'site_name',
  },
  {
    header: 'Orientation',
    accessorKey: 'orientation',
    id: 'orientation',
  },
  {
    header: 'Region Name',
    accessorKey: 'region_name',
    id: 'region_name',
  },
  {
    header: 'Country code',
    accessorKey: 'country_code',
    id: 'country_code',
  },
  {
    header: 'Lifecycle',
    accessorKey: 'lifecycle',
    id: 'lifecycle',
  },
  {
    header: 'Status',
    accessorKey: 'status',
    id: 'status',
    cell: () => {
      const cellStatus = 'COMMISSIONING';
      const cell_ref = 'GBMLBKNS000001';
      return (
        <>
          <StatusComponent
            dataTestId="cell-main-table-status-icon"
            boxSize="sm"
            color={getStatusColor(cellStatus)}
            status={cellStatus}
          />{' '}
          <CellConfigMenu cellStatus={cellStatus} cell_ref={cell_ref} />
        </>
      );
    },
  },
];

const handleTabsChange = () => {
  console.log('handleTabsChange');
};

// Debugging
// screen.debug();
// screen.logTestingPlaygroundURL();

describe.skip('show content for a node', () => {
  it.skip('Show node', async () => {
    render(
      <MemoryRouter>
        {/* <DataTable
          isExpandable={true}
          columns={columns ?? []}
          data={getCellListMock}
          defaultPageSize={50}
        /> */}
        <CellNodes row={getNodeByCellRefMock} />
      </MemoryRouter>,
      { wrapper }
    );
    await screen.findByText(/MMWave/i);
    const node = screen.getByRole('cell', { name: /GBMLBKNS000003/i });
    fireEvent.click(node);

    // await screen.findByText(/GBMLBKNS000003/i);
    // await screen.findAllByText(/Millbrook/i);
    // await screen.findAllByText(/COMMISSIONING/i);
  });
  it.skip('click on node to show loading comp', async () => {
    render(
      <MemoryRouter>
        <DataTable isExpandable={true} columns={columns ?? []} data={getCellListMock} defaultPageSize={50} />
      </MemoryRouter>,
      { wrapper }
    );

    const node = screen.getByRole('cell', {
      name: /GBMLBKNS000001/i,
    });
    fireEvent.click(node);

    await screen.findByText(/Loading.../i);
  });
});
