import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { ReactNode } from 'react';
import { MemoryRouter } from 'react-router-dom';
import { describe, expect, it } from 'vitest';
import StatusComponent from '../../../components/icons/StatusIcon';
import { getCellListMock } from '../../../services/mocks/getCellListMock';
import { DataTable } from '../../MetricsCollector/components/DataTable';
import CellConfigMenu from '../CellConfigMenu';
import CellOverviewIndex from '../Cells';
import { getStatusColor } from '../hooks/useStatus';

type ChildrenProps = {
  children: ReactNode;
};

// Wrapper for react query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});
const wrapper = ({ children }: ChildrenProps) => (
  <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
);

const handleTabsChange = () => {
  console.log('handleTabsChange');
};

const columns = [
  {
    header: 'Cell Reference',
    accessorKey: 'cell_ref',
    id: 'cell_ref',
  },
  {
    header: 'Cell Type',
    accessorKey: 'oran_split',
    id: 'oran_split',
  },
  {
    header: 'Site name',
    accessorKey: 'site_name',
    id: 'site_name',
  },
  {
    header: 'Orientation',
    accessorKey: 'orientation',
    id: 'orientation',
  },
  {
    header: 'Region Name',
    accessorKey: 'region_name',
    id: 'region_name',
  },
  {
    header: 'Country code',
    accessorKey: 'country_code',
    id: 'country_code',
  },
  {
    header: 'Lifecycle',
    accessorKey: 'lifecycle',
    id: 'lifecycle',
  },
  {
    header: 'Status',
    accessorKey: 'status',
    id: 'status',
    cell: () => {
      const cellStatus = 'COMMISSIONING';
      const cell_ref = 'GBMLBKNS000001';
      return (
        <>
          <StatusComponent
            dataTestId="cell-main-table-status-icon"
            boxSize="sm"
            color={getStatusColor(cellStatus)}
            status={cellStatus}
          />{' '}
          <CellConfigMenu cellStatus={cellStatus} cell_ref={cell_ref} />
        </>
      );
    },
  },
];

// Debugging
// screen.debug();
// //This one in the function and before the issue
// screen.logTestingPlaygroundURL();

// Add snapshots when the specs for the views are fully agreed
describe('Cell Table with a list of cells', () => {
  it('Render the table component loading state', async () => {
    render(
      <MemoryRouter>
        <CellOverviewIndex alternativeViewId={''} />
      </MemoryRouter>,
      { wrapper }
    );
    screen.getByText('Loading...');
  });
  // it('Render the table component', async () => {
  //   render(
  //     <MemoryRouter>
  //       <CellOverviewIndex alternativeViewId={''} />
  //     </MemoryRouter>,
  //     { wrapper }
  //   );
  //   await waitFor(() => expect(screen.getByRole('table')));
  // });

  it('Render the table component DataTable', async () => {
    render(
      <MemoryRouter>
        <DataTable isExpandable={true} columns={columns ?? []} data={getCellListMock} defaultPageSize={50} />
      </MemoryRouter>,
      { wrapper }
    );
    await waitFor(() => expect(screen.getByRole('table')));
  });

  //Reimplement when fullscreen is working again
  it.skip('Render fullscreen button', async () => {
    render(<CellOverviewIndex alternativeViewId={''} />);
    await waitFor(() => {
      screen.getByRole('button', { name: /open table in fullscreen mode/i }).click();
      // need to check full screen is enabled.
    });
  });

  it('Render data in table', async () => {
    render(
      <MemoryRouter>
        <DataTable isExpandable={true} columns={columns ?? []} data={getCellListMock} defaultPageSize={50} />
      </MemoryRouter>,
      { wrapper }
    );
    await waitFor(() => {
      const table = screen.queryAllByRole('row');
      expect(table).length.greaterThanOrEqual(5);
    });
  });

  it('Render the status icons', async () => {
    render(
      <MemoryRouter>
        <DataTable isExpandable={true} columns={columns ?? []} data={getCellListMock} defaultPageSize={50} />
      </MemoryRouter>,
      { wrapper }
    );
    await waitFor(() => {
      expect(screen.getAllByTestId('cell-main-table-status-icon')).length.greaterThanOrEqual(4);
    });
  });

  it('Render a specific row', async () => {
    render(
      <MemoryRouter>
        <DataTable isExpandable={true} columns={columns ?? []} data={getCellListMock} defaultPageSize={50} />
      </MemoryRouter>,
      { wrapper }
    );
    await waitFor(() => {
      screen.getByRole('cell', {
        name: /GBMLBKNS000001/i,
      });
      screen.getAllByRole('cell', { name: /GBR/i });
      screen.getAllByRole('cell', { name: /Millbrook/i });
      screen.getAllByRole('cell', { name: /COMMISSIONING/i });
    });
  });

  it.skip('Render popover', async () => {
    render(
      <MemoryRouter>
        <DataTable isExpandable={true} columns={columns ?? []} data={getCellListMock} defaultPageSize={50} />
      </MemoryRouter>,
      { wrapper }
    );
    const popoverIcon = screen.getByTestId('nms-popover');
    fireEvent.click(popoverIcon);
    const popover = screen.getByText(/Legend/i);
    expect(popover).to.exist;
  });
});

describe('Cell table column sort ', () => {
  it('sort oran split column asc', async () => {
    render(
      <MemoryRouter>
        <DataTable isExpandable={true} columns={columns ?? []} data={getCellListMock} defaultPageSize={50} />
      </MemoryRouter>,
      { wrapper }
    );
    const columnToSort = screen.getByRole('columnheader', {
      name: /Cell Type/i,
    });

    fireEvent.click(columnToSort);
    fireEvent.click(columnToSort);
  });

  it('sort oran split column desc', async () => {
    render(
      <MemoryRouter>
        <DataTable isExpandable={true} columns={columns ?? []} data={getCellListMock} defaultPageSize={50} />
      </MemoryRouter>,
      { wrapper }
    );
    const columnToSort = screen.getByRole('columnheader', {
      name: /Cell Type/i,
    });

    fireEvent.click(columnToSort);
  });

  it('sort cell country column asc', async () => {
    render(
      <MemoryRouter>
        <DataTable isExpandable={true} columns={columns ?? []} data={getCellListMock} defaultPageSize={50} />
      </MemoryRouter>,
      { wrapper }
    );
    const columnToSort = screen.getByRole('columnheader', {
      name: /country/i,
    });

    fireEvent.click(columnToSort);
    fireEvent.click(columnToSort);
  });

  it('sort cell country column desc', async () => {
    render(
      <MemoryRouter>
        <DataTable isExpandable={true} columns={columns ?? []} data={getCellListMock} defaultPageSize={50} />
      </MemoryRouter>,
      { wrapper }
    );
    const columnToSort = screen.getByRole('columnheader', {
      name: /country/i,
    });

    fireEvent.click(columnToSort);
  });

  it('sort cell region column asc', async () => {
    render(
      <MemoryRouter>
        <DataTable isExpandable={true} columns={columns ?? []} data={getCellListMock} defaultPageSize={50} />
      </MemoryRouter>,
      { wrapper }
    );
    const columnToSort = screen.getByRole('columnheader', {
      name: /region name/i,
    });

    fireEvent.click(columnToSort);
    fireEvent.click(columnToSort);
  });

  it('sort cell region column desc', async () => {
    render(
      <MemoryRouter>
        <DataTable isExpandable={true} columns={columns ?? []} data={getCellListMock} defaultPageSize={50} />
      </MemoryRouter>,
      { wrapper }
    );
    const columnToSort = screen.getByRole('columnheader', {
      name: /region name/i,
    });

    fireEvent.click(columnToSort);
  });

  it('sort cell lifecycle column asc', async () => {
    render(
      <MemoryRouter>
        <DataTable isExpandable={true} columns={columns ?? []} data={getCellListMock} defaultPageSize={50} />
      </MemoryRouter>,
      { wrapper }
    );
    const columnToSort = screen.getByRole('columnheader', {
      name: /lifecycle/i,
    });

    fireEvent.click(columnToSort);
    fireEvent.click(columnToSort);
  });

  it('sort cell lifecycle column desc', async () => {
    render(
      <MemoryRouter>
        <DataTable isExpandable={true} columns={columns ?? []} data={getCellListMock} defaultPageSize={50} />
      </MemoryRouter>,
      { wrapper }
    );
    const columnToSort = screen.getByRole('columnheader', {
      name: /lifecycle/i,
    });

    fireEvent.click(columnToSort);
  });
});
