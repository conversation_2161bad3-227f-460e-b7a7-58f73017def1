import { Navigate, useLocation } from 'react-router-dom';
import { AUTH_TOKEN_KEY } from '../data/constants';
import useLogin from '../hooks/useLogin';

export const PrivateRoutes = ({ children }: any) => {
  const location = useLocation();
  const { jwtTokenFromLocalStorage: loginToken, checkTokenStatus } = useLogin(AUTH_TOKEN_KEY);

  checkTokenStatus();

  if (!loginToken) {
    return <Navigate to="/login" replace state={{ from: location }} />;
  }

  return children;
};

export default PrivateRoutes;
