import { useToast } from '@chakra-ui/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { resolveAlarm } from '../../../services/metricsCollector';
import { AxiosError } from 'axios';

export default function useResolveAlarm() {
  const queryClient = useQueryClient();
  const toast = useToast();

  const { mutateAsync: resolveAlarmMutation, isLoading: isAlarmResolving } = useMutation({
    mutationFn: resolveAlarm,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['alarmList'] });
      toast({
        title: 'Alarm resolved.',
        description: `${data?.id} has been resolved successfully.`,
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: (error) => {
      toast({
        title: 'Alarm resolving failed.',
        description: `${
          (error as AxiosError<{ detail: string }>)?.response?.data?.detail.includes(
            'alarm is alive in ACP, hence cant be resolved'
          )
            ? '<PERSON><PERSON> is still alive, hence cant be resolved'
            : 'An unexpected error occurred.'
        }`,
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
  });
  return { resolveAlarmMutation, isAlarmResolving };
}
