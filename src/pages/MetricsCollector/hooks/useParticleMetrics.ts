import { useQuery } from '@tanstack/react-query';
import { searchParticleMetrics } from '../../../services/metricsCollector';
import { ParticleMetrics } from '../../../types/metricCollector.type';

type TransformedDataType = {
  time: string[];
  temperature: number[];
  bwFanSpeed: number[];
  asFanSpeed: number[];
};

const endDate = new Date();
const startDate = new Date();

// Subtract 24 hours from the current date to get the start date
startDate.setHours(startDate.getHours() - 24);
//NOTE: Can we remove the below code?
const transformTime = (dateStr: string) => {
  const date = new Date(dateStr);
  return `${date.getDate() + 1}/${date.getMonth()} ${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`;
};

// export function transformData(inputData: ParticleMetrics): TransformedDataType {
//   //   const timeData: string[] = inputData.events.map((event) => {
//   //     const eventDate = new Date(event.date);
//   //     return `${eventDate.getUTCHours().toString().padStart(2, '0')}:${eventDate
//   //       .getUTCMinutes()
//   //       .toString()
//   //       .padStart(2, '0')}`;
//   //   });
//   const timeData = inputData.events.map((event) => transformTime(event.date));
//   return {
//     time: timeData,
//     temperature: inputData.events.map((event) => event.temperature),
//     bwFanSpeed: inputData.events.map((event) => event.bluwireless_fan_speed),
//     asFanSpeed: inputData.events.map((event) => event.airspan_fan_speed),
//   };
// }
export function transformData(inputData: ParticleMetrics): TransformedDataType {
  // Extract and transform the time data from the inputData.events
  const timeData = inputData.events.map((event) => {
    const eventDate = new Date(event.date);
    return `${eventDate.getUTCDate()}/${eventDate.getUTCMonth() + 1} ${eventDate.getUTCHours()}:${eventDate
      .getUTCMinutes()
      .toString()
      .padStart(2, '0')}`;
  });

  return {
    time: timeData,
    temperature: inputData.events.map((event) => event.temperature),
    bwFanSpeed: inputData.events.map((event) => event.bluwireless_fan_speed),
    asFanSpeed: inputData.events.map((event) => event.airspan_fan_speed),
  };
}
const formatDate = (date: Date) => {
  return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date
    .getDate()
    .toString()
    .padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date
    .getMinutes()
    .toString()
    .padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`;
};

const formattedStartDate = formatDate(startDate);
const formattedEndDate = formatDate(endDate);
export default function useParticleMetrics(coreid: string) {
  return useQuery<ParticleMetrics, Error, TransformedDataType>({
    queryKey: ['particleMetrics', { coreid, formattedStartDate, formattedEndDate }],
    queryFn: () =>
      searchParticleMetrics({
        coreid: coreid,
        start_date: formattedStartDate,
        end_date: formattedEndDate,
        limit: 1440,
        offset: 0,
      }),
    select: (data) => transformData(data),
  });
}
