import { useQuery } from '@tanstack/react-query';
import { searchTransportMetric } from '../../../services/metricsCollector';
import { TransporteMetricsSearchParams, TransportMetrics } from '../../../types/metricCollector.type';

export default function useTransportMetrics(queryParams: TransporteMetricsSearchParams) {
  return useQuery<TransportMetrics>(['transportMetrics', { ...queryParams }], () =>
    searchTransportMetric({
      ...queryParams,
    })
  );
}
