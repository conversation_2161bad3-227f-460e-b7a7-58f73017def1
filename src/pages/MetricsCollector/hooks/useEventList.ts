import { useQuery } from '@tanstack/react-query';
import { getEventList } from '../../../services/metricsCollector';
import { Events, EventSearchQueryParams } from '../../../types/metricCollector.type';
import { SelectedFilters } from '../components/ServerFacets/types';

const transformSelectedFilters: any = (eventFacetFilets: SelectedFilters) => {
  const queryParams: any = {};

  for (const category in eventFacetFilets) {
    // Filter out selected facets
    const selectedFacets = Object.entries(eventFacetFilets[category])
      .filter(([facet, isSelected]) => isSelected)
      .map(([facet]) => facet);

    // Add to queryParams if there are any selected facets
    if (selectedFacets.length > 0) {
      queryParams[category] = selectedFacets;
    }
  }

  return queryParams;
};

export default function useEventList(queryParams: EventSearchQueryParams, eventFacetFilters: SelectedFilters = {}) {
  const transformedSelectedFilters = transformSelectedFilters(eventFacetFilters);

  const { limit = 1000, lifecycle, ...restParams } = queryParams || {};

  const params: Record<string, any> = {
    ...transformedSelectedFilters,
    ...restParams,
    limit: 1000,
    offset: limit - 1000,
  };
  if (lifecycle != null) {
    params.lifecycle = lifecycle;
  }

  return useQuery<Events, Error>({
    queryKey: ['events', restParams, transformedSelectedFilters, lifecycle, limit],
    queryFn: () => getEventList(params),
    enabled: true,
  });
}

export function useEventListByAlarmId(alarm_id: string) {
  return useEventList({
    alarm_id: alarm_id,
    desc: 'event_time',
  });
}
