import { useToast } from '@chakra-ui/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { updateAlarm } from '../../../services/metricsCollector';

export default function useUpdateAlarm() {
  const queryClient = useQueryClient();
  const toast = useToast();

  const { mutateAsync: updateAlarmMutation, isLoading: isAlarmUpdating } = useMutation({
    mutationFn: updateAlarm,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['alarmList'] });
      toast({
        title: 'Alarm updated.',
        description: `${data?.id} has been updated successfully.`,
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
  });
  return { updateAlarmMutation, isAlarmUpdating };
}
