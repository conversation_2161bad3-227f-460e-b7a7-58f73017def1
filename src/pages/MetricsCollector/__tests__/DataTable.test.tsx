import { render, screen } from '@testing-library/react';
import { expect } from 'vitest';
import { DataTable } from '../components/DataTable';
import { MemoryRouter } from 'react-router-dom';

type DataRow = { domain: string; sourceName: string };
// Type for columns, restricting accessorKey to keys of DataRow
type DataColumn = {
  header: string;
  accessorKey: keyof DataRow; // restrict to keys of DataRow
  id: string;
};

// Mock data for testing
const columns: DataColumn[] = [
  {
    header: 'Domain',
    accessorKey: 'domain',
    id: 'domain',
  },
  {
    header: 'Source Name',
    accessorKey: 'sourceName',
    id: 'sourceName',
  },
];
const data = [
  { domain: 1, sourceName: 'John <PERSON>e' },
  { domain: 2, sourceName: '<PERSON>' },
];

describe('DataTable', () => {
  it('should render table with correct headers and data', async () => {
    render(
      <MemoryRouter>
        <DataTable columns={columns} data={data} isExpandable />
      </MemoryRouter>
    );

    // Check correct number of header cells
    const headerCells = screen.getAllByRole('columnheader');
    expect(headerCells).toHaveLength(columns.length);

    // Check for specific header content
    const domainHeaders = screen.getAllByText('Domain');
    expect(domainHeaders).toHaveLength(2);
    // Use getAllByText instead of getByText
    const sourceNameElements = screen.getAllByText('Source Name');

    // Check if the elements are present in the document
    sourceNameElements.forEach((element) => {
      expect(element).toBeInTheDocument();
    });
    // Check for correct number of data cells
    const dataCells = screen.getAllByRole('cell');
    expect(dataCells).toHaveLength(data.length * columns.length);
    // Check for specific data content
    data.forEach((row, rowIndex) => {
      columns.forEach((col, colIndex) => {
        const cellIndex = rowIndex * columns.length + colIndex;
        expect(dataCells[cellIndex]).toHaveTextContent(row[col.accessorKey].toString());
      });
    });
  });

  it('should render no results message when data is empty', () => {
    render(
      <MemoryRouter>
        <DataTable
          isExpandable={true}
          columns={columns}
          data={[]}
          hasEmptyResult={true}
          noResultsText={{ title: 'No results found', description: '' }}
          version={'v2'}
        />
      </MemoryRouter>
    );
    const noResultsMessage = screen.getByText('No results found');
    expect(noResultsMessage).toBeInTheDocument();
  });
});
