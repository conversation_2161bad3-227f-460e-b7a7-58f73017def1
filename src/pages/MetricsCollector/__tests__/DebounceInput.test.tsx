import { fireEvent, render, screen } from '@testing-library/react';
import { expect, vi } from 'vitest';
import { DebouncedInput } from '../components/DebounceInput';
describe('DebouncedInput', () => {
  it('should debounce input changes and call the onChange callback', async () => {
    const onChangeMock = vi.fn();
    const removePathParamMock = vi.fn();
    render(<DebouncedInput value="" onChange={onChangeMock} debounce={500} removePathParam={removePathParamMock} />);

    const input = screen.getByRole('searchbox');
    fireEvent.change(input, { target: { value: 'test' } });

    // Wait for debounce timeout to finish
    await new Promise((resolve) => setTimeout(resolve, 600));

    expect(onChangeMock).toHaveBeenCalledTimes(1);
    expect(onChangeMock).toHaveBeenCalledWith('test');
  });
});
