import {
  Box,
  Collapse,
  Flex,
  Heading,
  Icon,
  Skeleton,
  Table,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  Tr,
  useColorModeValue,
} from '@chakra-ui/react';
import { rankItem } from '@tanstack/match-sorter-utils';
import {
  ColumnDef,
  ColumnFiltersState,
  ExpandedState,
  FilterFn,
  flexRender,
  getCoreRowModel,
  getExpandedRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getSortedRowModel,
  PaginationState,
  Row,
  SortingState,
  Updater,
  useReactTable,
  VisibilityState,
} from '@tanstack/react-table';
import * as React from 'react';
import { ReactComponent as CaretDown } from '../../../assets/icons/caret-down.svg';
import { ReactComponent as CaretUp } from '../../../assets/icons/caret-up.svg';
import { ReactComponent as DoubleCaret } from '../../../assets/icons/doubleCaret.svg';
import { DataTablePagination } from './DataTablePagination';
import { DataTableToolbar } from './DataTableToolbar';

interface DataTableProps<TData, TValue> {
  onRowClick?: (row: Row<TData>) => void;
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  pageSizeOptions?: number[];
  defaultPageSize?: number;
  isLoading?: boolean;
  hasEmptyResult?: boolean;
  noResultsText?: {
    title: string;
    description: string;
  };
  hiddenColumnsByDefault?: Record<string, boolean>;
  setLimit?: React.Dispatch<React.SetStateAction<number>>;
  getRowCanExpand?: (row: any) => boolean;
  renderSubComponent?: (props: { row: Row<TData> }) => React.ReactNode;
  onExpandedChange?: (updater: Updater<ExpandedState>) => void;
  isExpandable: boolean;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl';
  enableFilter?: boolean;
  alternativeViewId?: string;
  defaultSortingColumn?: SortingState | undefined;
  urlCellRef?: string;
  urlNodeId?: string;
  limit?: number;
  count?: number;
  setresolveExpandable?: React.Dispatch<React.SetStateAction<boolean>>;
  resolveExpandable?: boolean;
  version?: string;
  selectedFilter?: Record<string, string>;
  showSearch?: boolean;
  caller?: string;
  enablePagination?: boolean;
}

const fuzzyFilter: FilterFn<any> = (row, columnId, value, addMeta) => {
  const values = value.split(',').map((item: string) => item.trim());
  const columnValue = row.getValue(columnId);
  const hiddenColumnsToSearch = ['id', 'rollout_id'];
  const hiddenValues = hiddenColumnsToSearch.map((hiddenColumn) => row.original[hiddenColumn]?.toString() || '');
  const allSearchableValues = [columnValue, ...hiddenValues].filter(Boolean);
  const itemRanks = values.flatMap((val: any) =>
    allSearchableValues.map((searchableValue) => rankItem(searchableValue, val))
  );

  const passed = itemRanks.some((rank: { passed: boolean }) => rank.passed);
  const bestRank = itemRanks.reduce(
    (max: { rank: number }, rank: { rank: number }) => (rank.rank > max.rank ? rank : max),
    itemRanks[0]
  );
  addMeta({
    itemRank: bestRank,
  });
  return passed;
};

export function DataTable<TData, TValue>({
  onRowClick,
  isExpandable = false,
  size = 'sm',
  columns,
  data,
  defaultPageSize = 50,
  pageSizeOptions = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
  noResultsText = {
    title: 'No results found',
    description: 'Try adjusting your search or filter to find what you are looking for.',
  },
  isLoading = false,
  hasEmptyResult = false,
  hiddenColumnsByDefault = {},
  getRowCanExpand = () => false,
  renderSubComponent = () => null,
  onExpandedChange,
  enableFilter = false,
  alternativeViewId,
  defaultSortingColumn = [],
  urlCellRef,
  urlNodeId,
  limit,
  setLimit = () => ({}),
  count,
  resolveExpandable,
  setresolveExpandable = () => ({}),
  version = 'v1',
  selectedFilter = {},
  showSearch = true,
  caller,
  enablePagination = true,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = React.useState<SortingState>(defaultSortingColumn);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
  const [rowSelection, setRowSelection] = React.useState({});
  const [globalFilter, setGlobalFilter] = React.useState('');
  const [expanded, setExpanded] = React.useState<ExpandedState>({});
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>(hiddenColumnsByDefault);
  const [pageCount, setPageCount] = React.useState(count);
  const [{ pageIndex, pageSize }, setPagination] = React.useState<PaginationState>({
    pageIndex: 0,
    pageSize: defaultPageSize,
  });
  const [isLoadingData, setIsLoadingData] = React.useState(false);
  const [displayData, setDisplayData] = React.useState<TData[] | []>(data);

  // Professional color palette
  const tableBg = useColorModeValue('white', 'gray.800');
  const headerBg = useColorModeValue('gray.50', 'gray.700');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const hoverBg = useColorModeValue('teal.50', 'gray.700');
  const expandedBg = useColorModeValue('teal.25', 'gray.650');
  const textColor = useColorModeValue('gray.800', 'gray.100');
  const mutedTextColor = useColorModeValue('gray.600', 'gray.400');
  const sortIconColor = useColorModeValue('teal.500', 'teal.300');
  const expandedBorderColor = useColorModeValue('teal.400', 'teal.300');

  React.useEffect(() => {
    if (data?.length <= 1000) {
      setPagination({ pageIndex: 0, pageSize: defaultPageSize });
    }
    if (count && version === 'v2') {
      setPageCount(count);
    }
  }, [data, count]);

  const pagination = React.useMemo(
    () => ({
      pageIndex,
      pageSize,
    }),
    [pageIndex, pageSize]
  );

  const getDisplayData = React.useCallback(() => {
    const startIndex = pageIndex * pageSize;
    const endIndex = startIndex + pageSize;
    return data.slice(startIndex, endIndex);
  }, [data, pageIndex, pageSize]);

  React.useEffect(() => {
    if (version === 'v1') {
      const isLastPageData = Math.ceil((count || data?.length) / pageSize) === pageIndex + 1;
      const shouldLoad = data.length > 0 && data.length < pageIndex * pageSize + pageSize && !isLastPageData;
      setIsLoadingData(true);
      if (data && !shouldLoad) {
        setDisplayData(getDisplayData());
        setIsLoadingData(false);
      }
    }
  }, [data, getDisplayData, isLoadingData, pageIndex, pageSize, count, version]);

  const handleExpandedChange = (updater: Updater<ExpandedState>) => {
    setExpanded(updater);
    if (onExpandedChange) {
      onExpandedChange(updater);
    }
  };

  const handleRowClick = (row: any, isClickable: boolean) => {
    if (!isClickable) return;

    if (onRowClick) {
      onRowClick(row.original);
    } else {
      row.toggleExpanded();
      if (onExpandedChange) onExpandedChange(row);
    }
  };

  const table = useReactTable({
    data: version === 'v1' ? displayData : data,
    pageCount: version === 'v1' ? count : pageCount,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onGlobalFilterChange: setGlobalFilter,
    globalFilterFn: fuzzyFilter,
    onExpandedChange: isExpandable ? handleExpandedChange : undefined,
    getExpandedRowModel: getExpandedRowModel(),
    getRowCanExpand,
    debugTable: false,
    debugHeaders: false,
    debugColumns: false,
    onRowSelectionChange: setRowSelection,
    enableRowSelection: true,
    onPaginationChange: setPagination,
    manualPagination: true,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      expanded,
      globalFilter,
      pagination,
      rowSelection,
    },
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
  });
  React.useEffect(() => {
    if (resolveExpandable) {
      table.resetExpanded();
      setresolveExpandable(false);
    }
  }, [resolveExpandable]);
  const loading = isLoading || isLoadingData;

  const renderTableData = () => {
    const renderTable = table.getRowModel().rows;
    return version === 'v1' ? renderTable : renderTable.slice(pageIndex * pageSize, pageIndex * pageSize + pageSize);
  };
  return (
    <>
      {showSearch && (
        <DataTableToolbar
          table={table}
          enableFilter={enableFilter}
          alternativeViewId={alternativeViewId}
          hiddenColumnsByDefault={hiddenColumnsByDefault}
          urlCellRef={urlCellRef}
          urlNodeId={urlNodeId}
          setPageCount={setPageCount}
          count={count}
          setLimit={setLimit}
          data={data}
          version={version}
          selectedFilter={selectedFilter}
          showSearch={showSearch}
        />
      )}
      <Box
        overflowX="auto"
        data-testid="dataTable-container"
        mt="4"
        bg={tableBg}
        borderRadius="xl"
        border="1px solid"
        borderColor={borderColor}
        boxShadow="sm"
        _hover={{ boxShadow: 'md' }}
        transition="box-shadow 0.2s ease"
        maxHeight="70vh"
        overflowY="auto"
        position="relative"
      >
        <Table
          variant="simple"
          size={size}
          sx={{
            borderCollapse: 'separate',
            borderSpacing: 0,
          }}
        >
          <Thead bg={headerBg} position="sticky" top="0" zIndex="10" boxShadow="0 2px 4px rgba(0,0,0,0.1)">
            {table.getHeaderGroups().map((headerGroup) => (
              <Tr key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <Th
                      key={header.id}
                      borderBottom="2px solid"
                      borderColor={header.column.getIsSorted() ? sortIconColor : borderColor}
                      colSpan={header.colSpan}
                      color={textColor}
                      fontWeight="600"
                      fontSize="xs"
                      letterSpacing="wide"
                      textTransform="uppercase"
                      py="4"
                      px="6"
                      position="relative"
                      bg={headerBg}
                      _first={{
                        borderTopLeftRadius: 'xl',
                      }}
                      _last={{
                        borderTopRightRadius: 'xl',
                      }}
                      transition="all 0.2s ease"
                    >
                      {header.isPlaceholder ? null : (
                        <Box
                          display="flex"
                          alignItems="center"
                          justifyContent="space-between"
                          sx={header?.column.getCanSort() ? { cursor: 'pointer', userSelect: 'none' } : undefined}
                          onClick={header.column.getToggleSortingHandler()}
                          _hover={header?.column.getCanSort() ? { color: sortIconColor } : undefined}
                          transition="color 0.2s ease"
                        >
                          <Text>{flexRender(header.column.columnDef.header ?? '', header.getContext())}</Text>

                          {header.column.getCanSort() && (
                            <Flex
                              opacity={header.column.getIsSorted() ? 1 : 0.4}
                              ml={2}
                              color={sortIconColor}
                              transition="all 0.2s ease"
                              transform={header.column.getIsSorted() ? 'scale(1.1)' : 'scale(1)'}
                            >
                              {{
                                asc: <Icon as={CaretUp} boxSize={4} />,
                                desc: <Icon as={CaretDown} boxSize={4} />,
                              }[header.column.getIsSorted() as string] ?? <Icon as={DoubleCaret} boxSize={4} />}
                            </Flex>
                          )}
                        </Box>
                      )}
                    </Th>
                  );
                })}
              </Tr>
            ))}
          </Thead>

          <Tbody>
            {loading &&
              Array.from({ length: table.getState().pagination.pageSize }, (_, rowIndex) => (
                <Tr key={rowIndex} data-row={rowIndex}>
                  {table.getHeaderGroups().map((headerGroup) => {
                    return headerGroup.headers.map((header, index) => (
                      <Td key={index} py="4" px="6" borderBottom="1px solid" borderColor={borderColor}>
                        <Skeleton height="20px" borderRadius="md" />
                      </Td>
                    ));
                  })}
                </Tr>
              ))}
            {hasEmptyResult && !loading && (
              <Tr>
                <Td colSpan={columns.length} border="none">
                  <Box
                    display="flex"
                    justifyContent="center"
                    alignItems="center"
                    height="300px"
                    width="100%"
                    flexDirection="column"
                    color={mutedTextColor}
                  >
                    <Heading size={'md'} color={textColor} mb="2">
                      {noResultsText.title}
                    </Heading>
                    {noResultsText.description && <Text>{noResultsText.description}</Text>}
                  </Box>
                </Td>
              </Tr>
            )}

            {!loading &&
              renderTableData().map((row: any, index: number) => {
                const isClickable = isExpandable;
                const isExpanded = row.getIsExpanded();
                return (
                  <React.Fragment key={row.id}>
                    <Tr
                      data-state={row.getIsSelected() && 'selected'}
                      onClick={() => handleRowClick(row, isClickable)}
                      style={{
                        cursor: isClickable ? 'pointer' : 'default',
                        animation: `fadeInUp 0.3s ease ${index * 0.05}s both`,
                      }}
                      _hover={{
                        bg: hoverBg,
                        transform: 'translateX(4px)',
                        boxShadow: '0 4px 12px rgba(59, 130, 246, 0.2)',
                        cursor: isClickable ? 'pointer' : 'default',
                        zIndex: 10,
                        borderLeft: '3px solid',
                        borderLeftColor: 'teal.400',
                      }}
                      _active={{
                        transform: 'translateX(2px)',
                        boxShadow: '0 2px 8px rgba(59, 130, 246, 0.15)',
                      }}
                      borderLeft={isExpanded ? `3px solid ${expandedBorderColor}` : '3px solid transparent'}
                      transition="all 0.2s cubic-bezier(0.4, 0, 0.2, 1)"
                      bg={isExpanded ? expandedBg : 'transparent'}
                      position="relative"
                      sx={{
                        '@keyframes fadeInUp': {
                          '0%': {
                            opacity: 0,
                            transform: 'translateY(10px)',
                          },
                          '100%': {
                            opacity: 1,
                            transform: 'translateY(0)',
                          },
                        },
                      }}
                    >
                      {row.getVisibleCells().map((cell: any) => (
                        <Td
                          key={cell.id}
                          py="4"
                          px="6"
                          borderBottom="1px solid"
                          borderColor={borderColor}
                          color={textColor}
                          fontSize="sm"
                          transition="background-color 0.2s ease"
                        >
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </Td>
                      ))}
                    </Tr>
                    {isExpanded && (
                      <Tr
                        key={`${row.id}-expanded`}
                        borderLeft={`3px solid ${expandedBorderColor}`}
                        bg={expandedBg}
                        sx={{
                          animation: 'slideDown 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                          '@keyframes slideDown': {
                            '0%': {
                              opacity: 0,
                              transform: 'translateY(-10px)',
                              maxHeight: '0px',
                            },
                            '100%': {
                              opacity: 1,
                              transform: 'translateY(0)',
                              maxHeight: '500px',
                            },
                          },
                        }}
                        _hover={{
                          bg: hoverBg,
                          transform: 'translateX(4px)',
                          borderLeft: '3px solid',
                          borderLeftColor: 'teal.400',
                          boxShadow: '0 4px 12px rgba(59, 130, 246, 0.2)',
                        }}
                        transition="all 0.2s cubic-bezier(0.4, 0, 0.2, 1)"
                      >
                        <Td
                          colSpan={row.getVisibleCells().length}
                          py="2"
                          px="4"
                          borderBottom="1px solid"
                          borderColor={borderColor}
                        >
                          {renderSubComponent({ row })}
                        </Td>
                      </Tr>
                    )}
                  </React.Fragment>
                );
              })}
          </Tbody>
        </Table>
      </Box>
      {enablePagination && (
        <DataTablePagination
          setLimit={setLimit}
          table={table}
          pageSizeOptions={pageSizeOptions}
          limit={limit}
          isLoading={loading}
        />
      )}
    </>
  );
}
