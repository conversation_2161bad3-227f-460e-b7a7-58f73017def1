import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, MenuList, MenuOptionGroup, MenuItemOption, Text } from '@chakra-ui/react';
import { FacetDefinition } from './types';
import { FaChevronDown } from 'react-icons/fa';

interface MoreFiltersMenuProps {
  facetsVisibility: { [key: string]: boolean };
  facetsVisibilityActions: {
    setAll: (obj: { [key: string]: boolean }) => void;
  };
  facetsDefinition: FacetDefinition[];
}
export const MoreFiltersMenu: React.FC<MoreFiltersMenuProps> = ({
  facetsVisibility,
  facetsVisibilityActions,
  facetsDefinition, // Add to destructured props
}) => {
  return (
    <Flex position="sticky" mb="4" flexShrink={0} marginLeft="auto" paddingRight="20px" zIndex="1">
      <Menu closeOnSelect={false}>
        <MenuButton as={But<PERSON>} size="md" mt="2">
          <Flex alignItems="center" gap="2">
            <Text> More Filters</Text>
            <FaChevronDown />
          </Flex>
        </MenuButton>
        <Flex position="relative">
          <MenuList>
            <MenuOptionGroup
              type="checkbox"
              value={facetsDefinition
                .filter((facetDef) => facetsVisibility[facetDef.id])
                .map((facetDef) => facetDef.id)}
              onChange={(selectedValues) => {
                const updated = facetsDefinition.reduce(
                  (acc, facetDef) => ({
                    ...acc,
                    [facetDef.id]: selectedValues.includes(facetDef.id),
                  }),
                  {} as { [key: string]: boolean }
                );
                facetsVisibilityActions.setAll(updated);
              }}
            >
              {facetsDefinition.map((facetDef) => (
                <MenuItemOption key={facetDef.id} value={facetDef.id}>
                  {facetDef.header}
                </MenuItemOption>
              ))}
            </MenuOptionGroup>
          </MenuList>
        </Flex>
      </Menu>
    </Flex>
  );
};
