import React, { useEffect } from 'react';
import { <PERSON>, Flex, Button } from '@chakra-ui/react';
import { useMap } from 'react-use';
import { FacetPopOver } from './FacetPopOver';
import { MoreFiltersMenu } from './MoreFiltersMenu';
import { SelectedFilterDisplay } from './SelectedFilterDisplay';
import { FacetDefinition, FacetSummary, SelectedFilters } from './types';
import { getStorageKey } from '../../../../utils/helpers';

interface FacetSummaryProps {
  facetSummary: FacetSummary;
  onApply: (newFilters: SelectedFilters) => void;
  facetsDefinition: FacetDefinition[];
  pathname: string;
}

const ServerFacets = ({ facetSummary, onApply, facetsDefinition, pathname }: FacetSummaryProps) => {
  const [isOpen, isOpenActions] = useMap<{ [category: string]: boolean }>({});
  const [filters, filtersActions] = useMap<SelectedFilters>({});
  const [selectAll, selectAllActions] = useMap<{ [category: string]: boolean }>({});
  const [search, searchActions] = useMap<{ [category: string]: string }>({});

  // Load filters from local storage on mount
  useEffect(() => {
    const filters = localStorage.getItem(getStorageKey(pathname)) || '{}';
    if (filters) {
      Object.keys(JSON.parse(filters)).forEach((key) => {
        filtersActions.set(key, JSON.parse(filters)[key]);
      });
    }
  }, []);

  const [facetsVisibility, facetsVisibilityActions] = useMap<{ [category: string]: boolean }>(
    facetsDefinition.reduce((acc, facet) => {
      acc[facet.id] = facet.visible;
      return acc;
    }, {} as { [key: string]: boolean })
  );

  // Get the visible facet definitions
  const visibleFacets = React.useMemo(() => {
    return facetsDefinition.filter(
      (facet) =>
        facetsVisibility[facet.id] && facetSummary[facet.accessorKey] && facetSummary[facet.accessorKey].length > 0
    );
  }, [facetsDefinition, facetsVisibility, facetSummary]);

  const togglePopover = (category: string) => {
    isOpenActions.set(category, !isOpen[category]);
  };

  const toggleFacet = (category: string, facet: string) => {
    const categoryMap = filters[category] || {};
    categoryMap[facet] = !categoryMap[facet];
    filtersActions.set(category, { ...categoryMap });
  };

  const clearFilters = (category?: string) => {
    if (category) {
      filtersActions.set(category, {});
      selectAllActions.set(category, false);
      searchActions.set(category, '');
      filtersActions.set(category, {});
    } else {
      filtersActions.reset();
      selectAllActions.reset();
      searchActions.reset();
      onApply({});
      localStorage.removeItem(getStorageKey(pathname));
    }
  };

  const handleSearchChange = (category: string, value: string) => {
    searchActions.set(category, value);
  };

  const handleSelectAll = (category: string) => {
    const filteredFacets = getFilteredFacets(category);
    const areAllSelected = filteredFacets.every((f) => filters[category]?.[f.facet] === true);

    const newCategoryFilters = { ...(filters[category] || {}) };
    filteredFacets.forEach(({ facet }) => {
      newCategoryFilters[facet] = !areAllSelected;
    });

    filtersActions.set(category, newCategoryFilters);
    selectAllActions.set(category, !areAllSelected);
  };

  const handleApplyAll = () => {
    onApply(filters);
  };

  const getFilteredFacets = (category: string) => {
    const all = facetSummary[category] || [];
    const term = (search[category] || '').toLowerCase();
    if (!term) return all;
    return all.filter((f) => f.facet?.toLowerCase().includes(term) || `${f.total}`.includes(term));
  };

  return visibleFacets.length > 0 ? (
    <Box mt="0">
      <Flex position="relative" flexWrap="nowrap" alignItems="flex-start" maxWidth="80%" p="2">
        <FacetPopOver
          visibleFacets={visibleFacets}
          filters={filters}
          selectAll={selectAll}
          search={search}
          togglePopover={togglePopover}
          toggleFacet={toggleFacet}
          handleSearchChange={handleSearchChange}
          handleSelectAll={handleSelectAll}
          clearFilters={clearFilters}
          getFilteredFacets={getFilteredFacets}
          isOpen={isOpen}
        />

        <MoreFiltersMenu
          facetsVisibility={facetsVisibility}
          facetsVisibilityActions={facetsVisibilityActions}
          facetsDefinition={facetsDefinition}
        />
      </Flex>

      <SelectedFilterDisplay filters={filters} toggleFacet={toggleFacet} facetsDefinition={facetsDefinition} />

      <Flex justify="start" align="center" gap={5} flexWrap="wrap" mt={4} marginLeft="20px" ml="8">
        <Button colorScheme="blue" size="sm" onClick={handleApplyAll}>
          Apply
        </Button>
        <Button colorScheme="teal" size="sm" onClick={() => clearFilters(undefined)}>
          Clear All
        </Button>
      </Flex>
    </Box>
  ) : null;
};

export default ServerFacets;
