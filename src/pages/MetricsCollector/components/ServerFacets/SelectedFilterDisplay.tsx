import React from 'react';
import { <PERSON>, HStack, Button, Text, Wrap } from '@chakra-ui/react';
import { GiCancel } from 'react-icons/gi';
import { SelectedFilters, FacetDefinition } from './types';

interface SelectedFilterDisplayProps {
  filters: SelectedFilters;
  toggleFacet: (category: string, facet: string) => void;
  facetsDefinition: FacetDefinition[];
}

export const SelectedFilterDisplay: React.FC<SelectedFilterDisplayProps> = ({
  filters,
  toggleFacet,
  facetsDefinition,
}) => {
  // Function to get header from category/accessorKey
  const getCategoryHeader = (category: string) => {
    const facetDef = facetsDefinition.find((fd) => fd.accessorKey === category);
    return facetDef?.header || category;
  };

  return (
    <Box
      mb={4}
      marginLeft="20px"
      border="1px solid gray"
      p={2}
      ml="8"
      borderRadius="md"
      bg="white"
      maxH="150px"
      maxW="75%"
      overflowX="auto"
      overflowY="auto"
      minHeight="50px"
    >
      <Wrap>
        {Object.entries(filters).flatMap(([category, facetMap]) =>
          Object.entries(facetMap)
            .filter(([, isChecked]) => isChecked)
            .map(([facet]) => (
              <HStack key={`${category}-${facet}`} spacing={2} m={1}>
                <Button size="sm" onClick={() => toggleFacet(category, facet)}>
                  <Text mr="2" whiteSpace="nowrap">{`${getCategoryHeader(category)} = ${
                    facet === null ? 'null' : facet === '' ? "' '" : facet
                  }`}</Text>
                  <GiCancel color="red" size="20px" />
                </Button>
              </HStack>
            ))
        )}
      </Wrap>
    </Box>
  );
};
