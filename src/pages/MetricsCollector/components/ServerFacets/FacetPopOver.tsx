import React, { useEffect, useRef, useState } from 'react';
import {
  <PERSON>lex,
  Button,
  Popover,
  PopoverTrigger,
  PopoverContent,
  PopoverArrow,
  PopoverHeader,
  HStack,
  Box,
  Input,
  Divider,
  Icon,
  Text,
  PopoverBody,
  VStack,
  PopoverFooter,
  Tooltip,
  InputGroup,
  InputLeftElement,
  InputRightElement,
  IconButton,
} from '@chakra-ui/react';
import { GiCancel } from 'react-icons/gi';
import { FaFilter } from 'react-icons/fa';
import { IoSearchCircleOutline } from 'react-icons/io5';
import { GrCheckboxSelected, GrCheckbox } from 'react-icons/gr';
import { FacetDefinition, Facet, SelectedFilters } from './types';

interface FacetFiltersProps {
  visibleFacets: FacetDefinition[];
  filters: SelectedFilters;
  selectAll: { [category: string]: boolean };
  search: { [category: string]: string };
  isOpen: { [category: string]: boolean };
  togglePopover: (category: string) => void;
  toggleFacet: (category: string, facet: string) => void;
  handleSearchChange: (category: string, value: string) => void;
  handleSelectAll: (category: string) => void;
  clearFilters: (category?: string) => void;
  getFilteredFacets: (category: string) => Facet[];
}

const TruncatedText = ({ children }: { children: string }) => {
  const textRef = useRef<HTMLParagraphElement>(null);
  const [isTruncated, setIsTruncated] = useState(false);

  useEffect(() => {
    const checkTruncation = () => {
      const element = textRef.current;
      if (element) {
        setIsTruncated(element.scrollWidth > element.clientWidth);
      }
    };

    checkTruncation();
    window.addEventListener('resize', checkTruncation);
    return () => window.removeEventListener('resize', checkTruncation);
  }, [children]);

  return isTruncated ? (
    <Tooltip label={children} placement="top" hasArrow>
      <Text ref={textRef} isTruncated>
        {children}
      </Text>
    </Tooltip>
  ) : (
    <Text ref={textRef} isTruncated>
      {children}
    </Text>
  );
};

export const FacetPopOver: React.FC<FacetFiltersProps> = ({
  visibleFacets,
  filters,
  selectAll,
  search,
  isOpen,
  togglePopover,
  toggleFacet,
  handleSearchChange,
  handleSelectAll,
  clearFilters,
  getFilteredFacets,
}) => {
  return (
    <Flex
      flex="1"
      maxWidth="90%"
      marginLeft="20px"
      marginRight="20px"
      padding="10px"
      overflowX="auto"
      flexWrap="wrap"
      gap={5}
    >
      {visibleFacets.map((facetDef) => {
        const category = facetDef.accessorKey;
        const anyFilterChecked = Object.values(filters[category] || {}).some((checked) => checked);
        return (
          <Popover key={category} placement="bottom" closeOnBlur={true} isLazy={true} boundary="clippingParents">
            <PopoverTrigger>
              <Button
                variant="outline"
                fontWeight="normal"
                size="sm"
                bg={anyFilterChecked ? 'brand.500' : 'white'}
                color="black"
                _hover={{
                  color: 'white',
                  bg: 'brand.400',
                }}
                onClick={() => togglePopover(category)}
                leftIcon={<FaFilter color="teal" size="15" />}
              >
                {facetDef.header}
              </Button>
            </PopoverTrigger>
            <PopoverContent w="400px" h="400px" pt={2}>
              <PopoverArrow
                bg="white"
                borderTop="1px solid"
                borderLeft="1px solid"
                borderColor="teal.500"
                boxSize="14px"
              />
              <PopoverHeader bg="gray.50" top="0" zIndex="2">
                <HStack spacing={2}>
                  <Box flex="1">
                    <HStack>
                      <InputGroup
                        borderWidth="1px"
                        borderRadius="md"
                        borderColor="blue.500"
                        _hover={{ borderColor: 'blue.500' }}
                        _focus={{
                          borderColor: 'blue.500',
                        }}
                      >
                        <InputLeftElement pointerEvents="none">
                          <IconButton
                            aria-label="Search"
                            icon={<IoSearchCircleOutline size="30px" color="teal" />}
                            size="sm"
                            variant="ghost"
                          />
                        </InputLeftElement>
                        {search[category] && (
                          <InputRightElement>
                            <IconButton
                              aria-label="Clear input"
                              icon={<GiCancel color="red" size="20px" />}
                              size="sm"
                              variant="ghost"
                              onClick={() => {
                                handleSearchChange(category, '');
                              }}
                              _hover={{ color: 'red.500' }}
                              boxSize="6"
                            />
                          </InputRightElement>
                        )}
                        <Input
                          size="md"
                          placeholder={`Search ${facetDef.header}`}
                          value={search[category] || ''}
                          onChange={(e) => handleSearchChange(category, e.target.value)}
                          borderColor="blue.500"
                          _hover={{ borderColor: 'blue.500' }}
                          _focus={{
                            borderColor: 'blue.500',
                          }}
                        />
                      </InputGroup>
                    </HStack>
                  </Box>
                </HStack>
                <Divider my={2} />
                <Box display="flex" justifyContent="start" mb={1}>
                  <Button
                    variant="ghost"
                    width="100%"
                    borderRadius="0"
                    fontWeight="normal"
                    color="gray.800"
                    bg={'brand.100'}
                    _hover={{
                      color: 'white',
                      bg: 'brand.300',
                    }}
                    onClick={() => handleSelectAll(category)}
                  >
                    <Flex minWidth="100%" gap="2" py="2">
                      <Box>
                        <Icon
                          color={'brand.500'}
                          as={
                            category in selectAll ? (selectAll[category] ? GrCheckboxSelected : GrCheckbox) : GrCheckbox
                          }
                        />
                      </Box>
                      <Box>
                        <Text>Select All</Text>
                      </Box>
                    </Flex>
                  </Button>
                </Box>
              </PopoverHeader>

              <PopoverBody overflowX="auto" overflowY="auto" position="relative" pt={0}>
                <VStack align="start" spacing={1}>
                  {getFilteredFacets(category).map(({ facet, total }) => (
                    <HStack key={facet} spacing={2} w="100%" justifyContent="space-between" alignItems="center">
                      <Button
                        variant="ghost"
                        width="100%"
                        borderRadius="0"
                        fontWeight="normal"
                        color="gray.800"
                        _hover={{
                          color: 'white',
                          bg: 'brand.200',
                        }}
                        bg={filters[category]?.[facet] === true ? 'brand.200' : 'white'}
                        onClick={() => toggleFacet(category, facet)}
                      >
                        <Flex minWidth="100%" py="2">
                          <Box width="10%" flexShrink={0}>
                            <Icon
                              ml="20px"
                              color={'brand.500'}
                              as={filters[category]?.[facet] === true ? GrCheckboxSelected : GrCheckbox}
                            />
                          </Box>

                          <Box ml="2" width="80%" overflow="hidden" textAlign="left">
                            <TruncatedText>{facet === null ? 'null' : facet === '' ? "' '" : facet}</TruncatedText>
                          </Box>

                          <Box width="10%" flexShrink={0} textAlign="right">
                            <Text>{total}</Text>
                          </Box>
                        </Flex>
                      </Button>
                    </HStack>
                  ))}
                </VStack>
              </PopoverBody>

              <PopoverFooter>
                <Flex justifyContent="flex-end">
                  <Button colorScheme="teal" size="sm" onClick={() => clearFilters(category)}>
                    Clear
                  </Button>
                </Flex>
              </PopoverFooter>
            </PopoverContent>
          </Popover>
        );
      })}
    </Flex>
  );
};
