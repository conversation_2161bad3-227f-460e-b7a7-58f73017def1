import {
  Icon,
  Input,
  InputGroup,
  InputLeftElement,
  InputRightElement,
  IconButton,
  useColorModeValue,
} from '@chakra-ui/react';
import React from 'react';
import { useParams } from 'react-router';
import { GiCancel } from 'react-icons/gi';
import { IoSearchCircleOutline } from 'react-icons/io5';

// A debounced input react component
export function DebouncedInput({
  value: initialValue,
  onChange,
  debounce = 300,
  testId,
  icon,
  removePathParam,
  ...props
}: {
  value: string | number | undefined;
  onChange: (value: string | number) => void;
  debounce?: number;
  testId?: string;
  icon?: null | string;
  removePathParam?: (id?: string) => void;
} & Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange'>) {
  const [value, setValue] = React.useState(initialValue);
  const { id } = useParams();

  // Professional color palette
  const inputBg = useColorModeValue('white', 'gray.700');
  const inputColor = useColorModeValue('gray.800', 'gray.100');
  const placeholderColor = useColorModeValue('gray.500', 'gray.400');
  const searchIconColor = useColorModeValue('blue.500', 'blue.300');
  const clearIconColor = useColorModeValue('red.500', 'red.400');

  React.useEffect(() => {
    setValue(initialValue);
  }, [initialValue]);

  React.useEffect(() => {
    const timeout = setTimeout(() => {
      if (value && value !== undefined) {
        onChange(value);
      } else {
        onChange('');
      }
    }, debounce);

    return () => clearTimeout(timeout);
  }, [value, initialValue]);

  return (
    <InputGroup>
      <InputLeftElement pointerEvents="none" h="100%">
        <Icon as={IoSearchCircleOutline} boxSize="5" color={searchIconColor} transition="color 0.2s ease" />
      </InputLeftElement>

      {value && (
        <InputRightElement h="100%">
          <IconButton
            aria-label="Clear input"
            icon={<GiCancel size="16px" />}
            size="xs"
            variant="ghost"
            onClick={() => {
              setValue('');
              onChange('');
              if (id) removePathParam?.(id);
            }}
            color={clearIconColor}
            _hover={{
              color: 'red.600',
              bg: 'red.50',
              transform: 'scale(1.1)',
            }}
            _active={{ transform: 'scale(0.95)' }}
            borderRadius="full"
            transition="all 0.2s ease"
          />
        </InputRightElement>
      )}

      <Input
        data-testid={testId && testId}
        type={'search'}
        {...props}
        value={value}
        bg={inputBg}
        color={inputColor}
        border="none"
        _placeholder={{ color: placeholderColor }}
        _focus={{
          outline: 'none',
          boxShadow: 'none',
        }}
        sx={{
          '&::-webkit-search-cancel-button': {
            display: 'none',
          },
        }}
        onChange={(e) => {
          setValue(e.target.value);
          if (id && e.target.value === '') {
            onChange(e.target.value);
            removePathParam && removePathParam(id);
          }
        }}
        size="md"
        width="100%"
        pl="10"
        pr={value ? '10' : '3'}
        py="2"
        borderRadius="lg"
        fontSize="sm"
        fontWeight="400"
        transition="all 0.2s ease"
      />
    </InputGroup>
  );
}
