import { <PERSON><PERSON>, <PERSON>lex, <PERSON><PERSON>, <PERSON>u<PERSON>utton, MenuItemOption, MenuList, MenuOptionGroup, Text } from '@chakra-ui/react';
import { Table } from '@tanstack/react-table';
import { FaChevronDown } from 'react-icons/fa';

const DisplayColumns = ({ table }: { table: Table<any> }) => {
  return (
    <Menu closeOnSelect={false}>
      <MenuButton as={Button} display="flex" alignItems="center" gap="2">
        <Flex alignItems="center" gap="2">
          <Text>Customize Columns</Text>
          <FaChevronDown />
        </Flex>
      </MenuButton>
      <Flex overflow="overflow-x" position="relative">
        <MenuList>
          <MenuOptionGroup defaultValue={[...table.getVisibleFlatColumns().map((col) => col.id)]} type="checkbox">
            {table
              .getAllColumns()
              .filter(
                (column) =>
                  column.getCanHide() &&
                  (typeof column.columnDef.header === 'string' || column.columnDef.header instanceof String)
              )
              .map((column, index) => {
                return (
                  <MenuItemOption
                    key={`${index}-${column.id}`}
                    {...{
                      isChecked: column.getIsVisible(),
                      onClick: column.getToggleVisibilityHandler(),
                    }}
                    value={column.id}
                  >
                    <>{column.columnDef.header}</>
                  </MenuItemOption>
                );
              })}
          </MenuOptionGroup>
        </MenuList>
      </Flex>
    </Menu>
  );
};

export default DisplayColumns;
