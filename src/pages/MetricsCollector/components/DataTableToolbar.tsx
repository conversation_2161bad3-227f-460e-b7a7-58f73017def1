import { Box, Button, Flex, Text, useColorModeValue } from '@chakra-ui/react';
import { Column, Table } from '@tanstack/react-table';
import { useEffect, useState } from 'react';
import { DataTableFacetedFilter } from './DataTableFacets';
import { DebouncedInput } from './DebounceInput';
import _ from 'lodash';
import DisplayColumns from './DisplayColumns';
import { MdAutorenew } from 'react-icons/md';
import { useNavigate, useLocation } from 'react-router';
import { getStorageKey } from '../../../utils/helpers';

interface DataTableToolbarProps<TData> {
  table: Table<TData>;
  enableFilter?: boolean;
  alternativeViewId?: string;
  hiddenColumnsByDefault?: any;
  urlCellRef?: string;
  urlNodeId?: string;
  setPageCount?: any;
  count?: number;
  setLimit?: any;
  data?: any;
  version?: string;
  selectedFilter?: Record<string, string>;
  showSearch?: boolean;
}

interface MenuItemOptionProps {
  label: string;
  value: string;
}

function createFacetedFilters<TData>(
  table: Table<TData>,
  hiddenColumnsByDefault: string[]
): Record<string, MenuItemOptionProps[]> {
  const facetedFilters: Record<string, MenuItemOptionProps[]> = {};

  table.getAllColumns().forEach((column: Column<TData>) => {
    if (column.getIsVisible() && column.columnDef.filterFn !== 'auto') {
      const uniqueValues = column.getFacetedUniqueValues ? column.getFacetedUniqueValues() : new Map();
      const filteredUniqueValues = _.chain(Array.from(uniqueValues))
        .filter(([key]) => !_.isUndefined(key) && !_.isEmpty(key) && key !== 'N/A' && !_.isObject(key))
        .value();

      // sort them alphabetically
      filteredUniqueValues.sort((a, b) => a[0].localeCompare(b[0]));

      facetedFilters[column.id] = _.chain(filteredUniqueValues)
        .map(([value]) => ({
          label: _.isString(value) ? value : 'Undefined',
          value: _.isString(value) ? value : 'undefined',
        }))
        .value() as MenuItemOptionProps[];
    }
  });
  return facetedFilters;
}

export function DataTableToolbar<TData>({
  table,
  enableFilter = false,
  hiddenColumnsByDefault = [],
  alternativeViewId,
  urlCellRef,
  urlNodeId,
  setPageCount,
  count,
  setLimit,
  data,
  version,
  selectedFilter = {},
  showSearch,
}: DataTableToolbarProps<TData>) {
  const tabName = (table.options.columns[0] as Column<TData>).id;

  // Professional color palette
  const bg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const inputBg = useColorModeValue('white', 'gray.700');
  const inputBorderColor = useColorModeValue('gray.300', 'gray.600');
  const refreshButtonBg = useColorModeValue('blue.500', 'blue.400');
  const refreshButtonHoverBg = useColorModeValue('blue.600', 'blue.500');

  const resetUrlToDefaultValue = (): void => {
    if (history.state && history.state.usr) {
      history.state.usr.resetValue = true;
    }
  };

  const removeLocationAttribute = (): void => {
    resetUrlToDefaultValue();
    window.history.replaceState({}, '', `${location.pathname}`);
    localStorage.removeItem(getStorageKey(location.pathname));
  };

  const navigate = useNavigate();
  const location = useLocation();

  const [inputValue, setInputValue] = useState<string>('');

  useEffect(() => {
    const savedSearchValue = localStorage.getItem(getStorageKey(location.pathname));

    if (savedSearchValue) {
      setInputValue(savedSearchValue);
      table.setGlobalFilter(savedSearchValue);
    }
  }, [alternativeViewId, urlCellRef, urlNodeId, tabName, table]);

  useEffect(() => {
    if (inputValue) {
      localStorage.setItem(getStorageKey(location.pathname), inputValue);
    } else {
      localStorage.removeItem(getStorageKey(location.pathname));
    }
  }, [inputValue, alternativeViewId, urlCellRef, urlNodeId, tabName]);

  useEffect(() => {
    if (version === 'v2') {
      if (table.getState().columnFilters?.length) {
        setPageCount(table.getFilteredRowModel().rows.length);
      } else {
        setPageCount(count);
      }
    }
  }, [table.getFilteredRowModel().rows.length, data, table, version]);

  return (
    <Box
      data-testid="data_table_toolbar"
      mt="4"
      p="4"
      bg={bg}
      borderRadius="lg"
      border="1px solid"
      borderColor={borderColor}
      boxShadow="sm"
      _hover={{ boxShadow: 'md' }}
      transition="box-shadow 0.2s ease"
    >
      <Flex direction="column" justifyItems="center" alignItems="center" gap="4">
        <Flex direction="column" gap={4} w="100%">
          {showSearch && (
            <Flex justify="center" align="center" gap={4} w="100%" flexWrap="wrap">
              <Box
                alignSelf="end"
                border="1px solid"
                borderColor={inputBorderColor}
                borderRadius="lg"
                width="300px"
                bg={inputBg}
                _hover={{ borderColor: 'blue.300' }}
                _focusWithin={{
                  borderColor: 'blue.400',
                  boxShadow: '0 0 0 1px blue.400',
                }}
                transition="all 0.2s ease"
              >
                <DebouncedInput
                  value={table.getState().globalFilter || inputValue}
                  onChange={(value) => {
                    setInputValue(String(value));
                    table.setGlobalFilter(String(value));
                  }}
                  removePathParam={removeLocationAttribute}
                  placeholder="Search all columns..."
                />
              </Box>
              <Box
                alignSelf="end"
                borderRadius="lg"
                _hover={{ transform: 'translateY(-1px)' }}
                transition="transform 0.2s ease"
              >
                <DisplayColumns table={table} />
              </Box>
              <Box alignSelf="end" _hover={{ transform: 'translateY(-1px)' }} transition="transform 0.2s ease">
                <Button
                  size="md"
                  leftIcon={<MdAutorenew />}
                  bg={refreshButtonBg}
                  color="white"
                  _hover={{
                    bg: refreshButtonHoverBg,
                    transform: 'translateY(-1px)',
                    boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3)',
                  }}
                  _active={{
                    transform: 'translateY(0)',
                    boxShadow: '0 2px 8px rgba(59, 130, 246, 0.3)',
                  }}
                  borderRadius="lg"
                  fontWeight="500"
                  transition="all 0.2s ease"
                  onClick={() => window.location.reload()}
                >
                  Refresh
                </Button>
              </Box>
            </Flex>
          )}

          {enableFilter && (
            <Flex direction="row" alignItems="center" gap={3} flexWrap="wrap">
              {table
                .getAllColumns()
                .filter((column) => column.getCanFilter())
                .map((column, index) => {
                  const columnMeta = column.columnDef.meta as any;
                  const facetedUniqueValues = column.getFacetedUniqueValues();
                  const uniqueValues = Array.from(facetedUniqueValues.keys())
                    .filter((value) => value !== '' && value !== null && value !== undefined)
                    .sort()
                    .slice(0, 10);

                  const filters = uniqueValues.map((value) => ({
                    label: value as string,
                    value: value as string,
                  }));

                  return (
                    <Box key={column.id} _hover={{ transform: 'translateY(-1px)' }} transition="transform 0.2s ease">
                      <DataTableFacetedFilter
                        column={column}
                        title={columnMeta?.displayName ?? column.id}
                        filters={filters}
                        selectedFilter={selectedFilter}
                        isFiltered={!!column.getFilterValue()}
                      />
                    </Box>
                  );
                })}
            </Flex>
          )}
        </Flex>
      </Flex>
    </Box>
  );
}
