import {
  Box,
  Button,
  Divider,
  Flex,
  Icon,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Text,
  InputGroup,
  InputLeftElement,
  InputRightElement,
  IconButton,
  VStack,
  PopoverArrow,
  PopoverHeader,
  PopoverBody,
  PopoverFooter,
  HStack,
  Input,
  Tooltip,
  useColorModeValue,
} from '@chakra-ui/react';
import { Column } from '@tanstack/react-table';
import * as React from 'react';
import { FaFilter } from 'react-icons/fa';
import { GrCheckbox, GrCheckboxSelected } from 'react-icons/gr';
import { useLocation } from 'react-router-dom';
import { getStorageKey } from '../../../utils/helpers';
import { GiCancel } from 'react-icons/gi';
import { IoSearchCircleOutline } from 'react-icons/io5';
import { useState } from 'react';
import { useRef } from 'react';
import { useEffect } from 'react';

interface DataTableFacetedFilterProps<TData, TValue> {
  column?: Column<TData, TValue>;
  title?: string;
  filters: {
    label: string;
    value: string;
    icon?: React.ComponentType<{ className?: string }>;
  }[];
  selectedFilter?: Record<string, string>;
  isFiltered?: boolean;
}

export function DataTableFacetedFilter<TData, TValue>({
  column,
  title = '',
  filters,
  selectedFilter,
  isFiltered = false,
}: DataTableFacetedFilterProps<TData, TValue>) {
  const location = useLocation();
  const { severityCategory = '' } = location.state || {};
  const pathname = location.pathname;
  const facets = column?.getFacetedUniqueValues() ?? new Map<string, number>();

  // Professional color palette
  const bg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const buttonBg = useColorModeValue('white', 'gray.700');
  const buttonHoverBg = useColorModeValue('blue.50', 'gray.600');
  const buttonActiveBg = useColorModeValue('blue.500', 'blue.400');
  const textColor = useColorModeValue('gray.800', 'gray.100');
  const mutedTextColor = useColorModeValue('gray.600', 'gray.400');
  const searchIconColor = useColorModeValue('blue.500', 'blue.300');
  const inputBg = useColorModeValue('white', 'gray.700');
  const headerBg = useColorModeValue('gray.50', 'gray.700');
  const selectedBg = useColorModeValue('blue.100', 'blue.800');

  const [selectedValues, setSelectedValues] = React.useState<Set<string>>(
    new Set(column?.getFilterValue() as string[])
  );
  const serializedFilter = React.useMemo(() => JSON.stringify(selectedFilter), [selectedFilter]);
  const [searchQuery, setSearchQuery] = React.useState('');
  const initialFocusRef = React.useRef<HTMLInputElement>(null);

  // Load persisted filters from local storage when the pathname or column changes.
  React.useEffect(() => {
    const persistedFilters = JSON.parse(localStorage.getItem(getStorageKey(pathname)) || '{}');
    if (persistedFilters[title]) {
      setSelectedValues(new Set(persistedFilters[title]));
      column?.setFilterValue(persistedFilters[title]);
    } else {
      setSelectedValues(new Set());
      column?.setFilterValue(undefined);
    }
  }, [pathname, column, isFiltered]);

  // Manage changes in the severity category or selected filter.
  React.useEffect(() => {
    let filterValue = null;
    const currentFilter = JSON.parse(serializedFilter);

    if (title === 'Severity' && severityCategory) {
      filterValue = severityCategory;
    } else if (currentFilter && Object.keys(currentFilter).length) {
      selectedValues.clear();
      setSelectedValues(new Set());
      if (title === 'Region' && currentFilter['region_name']) {
        filterValue = currentFilter['region_name'];
      } else {
        column?.setFilterValue(undefined);
      }
      if (title === 'Status' && currentFilter['status']) {
        filterValue = currentFilter['status'];
      } else {
        column?.setFilterValue(undefined);
      }

      if (filterValue !== null) {
        selectedValues.clear();
        selectedValues.add(filterValue);
        setSelectedValues(selectedValues);
        const newFilterValues = Array.from(selectedValues);
        column?.setFilterValue(newFilterValues.length > 0 ? newFilterValues : undefined);
      }
    }
  }, [title, severityCategory, serializedFilter]);

  // Fix the useEffect for filter synchronization
  React.useEffect(() => {
    const persistedFilters = JSON.parse(localStorage.getItem(getStorageKey(pathname)) || '{}');
    const storedValues = persistedFilters[title] || [];

    // Only update if values actually changed
    if (JSON.stringify(storedValues) !== JSON.stringify(Array.from(selectedValues))) {
      setSelectedValues(new Set(storedValues));
      column?.setFilterValue(storedValues.length > 0 ? storedValues : undefined);
    }
  }, [pathname, title, serializedFilter]);

  const handleFilterSelection = (filterValue: string) => {
    const newSelectedValues = new Set(selectedValues);
    const wasSelected = newSelectedValues.has(filterValue);

    if (wasSelected) {
      newSelectedValues.delete(filterValue);
    } else {
      newSelectedValues.add(filterValue);
    }

    setSelectedValues(newSelectedValues);

    const filterArray = Array.from(newSelectedValues);
    column?.setFilterValue(filterArray.length > 0 ? filterArray : undefined);

    const persistedFilters = JSON.parse(localStorage.getItem(getStorageKey(pathname)) || '{}');
    if (filterArray.length > 0) {
      persistedFilters[title] = filterArray;
    } else {
      delete persistedFilters[title];
    }
    localStorage.setItem(getStorageKey(pathname), JSON.stringify(persistedFilters));
  };

  const handleClearFilter = () => {
    setSelectedValues(new Set());
    column?.setFilterValue(undefined);

    const persistedFilters = JSON.parse(localStorage.getItem(getStorageKey(pathname)) || '{}');
    delete persistedFilters[title];
    localStorage.setItem(getStorageKey(pathname), JSON.stringify(persistedFilters));
  };

  const handleSelectAll = () => {
    const allValues = filters.map((f) => f.value);
    const newSelectedValues = selectedValues.size === allValues.length ? new Set<string>() : new Set(allValues);

    setSelectedValues(newSelectedValues);
    column?.setFilterValue(Array.from(newSelectedValues));

    const persistedFilters = JSON.parse(localStorage.getItem(getStorageKey(pathname)) || '{}');
    persistedFilters[title] = Array.from(newSelectedValues);
    localStorage.setItem(getStorageKey(pathname), JSON.stringify(persistedFilters));
  };

  const filteredFilters = React.useMemo(() => {
    if (!searchQuery) return filters;
    return filters.filter((f) => f.label.toLowerCase().includes(searchQuery.toLowerCase()));
  }, [filters, searchQuery]);

  return (
    <Popover
      placement="bottom-start"
      closeOnBlur={true}
      closeOnEsc={true}
      isLazy={true}
      matchWidth={true}
      initialFocusRef={initialFocusRef}
    >
      {({ isOpen, onClose }) => {
        return (
          <>
            <PopoverTrigger>
              <Button
                variant="outline"
                size="sm"
                fontWeight="500"
                bg={selectedValues.size > 0 ? buttonActiveBg : buttonBg}
                color={selectedValues.size > 0 ? 'white' : textColor}
                borderColor={selectedValues.size > 0 ? buttonActiveBg : borderColor}
                _hover={{
                  bg: selectedValues.size > 0 ? 'blue.600' : buttonHoverBg,
                  borderColor: 'blue.300',
                  transform: 'translateY(-2px) scale(1.02)',
                  boxShadow: '0 4px 12px rgba(59, 130, 246, 0.2)',
                }}
                _active={{
                  transform: 'translateY(-1px) scale(1.01)',
                }}
                transition="all 0.2s cubic-bezier(0.4, 0, 0.2, 1)"
                borderRadius="md"
              >
                <Icon as={FaFilter} size="12" color="teal.500" />
                <Text ml="2" fontSize="sm">
                  {title}
                </Text>
                {selectedValues.size > 0 && (
                  <Box
                    ml="2"
                    px="2"
                    py="1"
                    bg="white"
                    color={buttonActiveBg}
                    borderRadius="full"
                    fontSize="xs"
                    fontWeight="600"
                    minW="20px"
                    textAlign="center"
                  >
                    {selectedValues.size}
                  </Box>
                )}
              </Button>
            </PopoverTrigger>

            <PopoverContent
              w="400px"
              maxH="500px"
              bg={bg}
              borderColor={borderColor}
              boxShadow="xl"
              borderRadius="lg"
              overflow="hidden"
              zIndex={isOpen ? 'popover' : 'auto'}
            >
              <PopoverArrow bg={bg} borderColor={borderColor} />

              <PopoverHeader bg={headerBg} borderColor={borderColor} borderBottomWidth="1px" p="4">
                <VStack spacing="3" align="stretch">
                  <HStack justify="space-between">
                    <Text fontSize="md" fontWeight="600" color={textColor}>
                      Filter by {title}
                    </Text>
                    <IconButton
                      aria-label="Close filter"
                      icon={<GiCancel size="16px" />}
                      size="sm"
                      variant="ghost"
                      onClick={onClose}
                      color={mutedTextColor}
                      _hover={{
                        color: 'red.500',
                        bg: 'red.50',
                        transform: 'scale(1.1)',
                      }}
                      _active={{
                        transform: 'scale(0.95)',
                      }}
                      transition="all 0.2s ease"
                      borderRadius="full"
                    />
                  </HStack>

                  <InputGroup size="sm">
                    <InputLeftElement>
                      <Icon as={IoSearchCircleOutline} color={searchIconColor} boxSize="4" />
                    </InputLeftElement>
                    <Input
                      ref={initialFocusRef}
                      placeholder={`Search ${title.toLowerCase()}...`}
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      bg={inputBg}
                      borderColor={borderColor}
                      _hover={{ borderColor: 'blue.300' }}
                      _focus={{
                        borderColor: 'blue.400',
                        boxShadow: '0 0 0 1px blue.400',
                      }}
                      borderRadius="md"
                      fontSize="sm"
                    />
                    {searchQuery && (
                      <InputRightElement>
                        <IconButton
                          aria-label="Clear search"
                          icon={<GiCancel size="14px" />}
                          size="xs"
                          variant="ghost"
                          onClick={() => setSearchQuery('')}
                          color={mutedTextColor}
                          _hover={{
                            color: 'red.500',
                            transform: 'scale(1.1)',
                          }}
                          _active={{
                            transform: 'scale(0.95)',
                          }}
                          transition="all 0.2s ease"
                          borderRadius="full"
                        />
                      </InputRightElement>
                    )}
                  </InputGroup>

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleSelectAll}
                    color={textColor}
                    _hover={{
                      bg: buttonHoverBg,
                      transform: 'translateX(2px)',
                    }}
                    _active={{
                      transform: 'translateX(1px)',
                    }}
                    justifyContent="flex-start"
                    leftIcon={
                      <Icon
                        as={selectedValues.size === filters.length ? GrCheckboxSelected : GrCheckbox}
                        color={searchIconColor}
                      />
                    }
                    transition="all 0.2s ease"
                    borderRadius="md"
                  >
                    Select All ({filters.length})
                  </Button>
                </VStack>
              </PopoverHeader>

              <PopoverBody p="0" overflowY="auto" maxH="300px">
                <VStack spacing="0" align="stretch">
                  {filteredFilters.map((filter) => {
                    const isSelected = selectedValues.has(filter.value);
                    const count = facets.get(filter.value) || 0;

                    return (
                      <Button
                        key={filter.value}
                        variant="ghost"
                        size="sm"
                        onClick={() => handleFilterSelection(filter.value)}
                        bg={isSelected ? selectedBg : 'transparent'}
                        color={textColor}
                        _hover={{
                          bg: isSelected ? 'blue.200' : buttonHoverBg,
                          transform: 'translateX(4px)',
                          borderLeft: '3px solid',
                          borderLeftColor: 'blue.400',
                        }}
                        _active={{
                          transform: 'translateX(2px)',
                        }}
                        justifyContent="space-between"
                        borderRadius="0"
                        px="4"
                        py="3"
                        h="auto"
                        fontWeight="normal"
                        transition="all 0.2s cubic-bezier(0.4, 0, 0.2, 1)"
                      >
                        <HStack spacing="3" flex="1">
                          <Icon
                            as={isSelected ? GrCheckboxSelected : GrCheckbox}
                            color={searchIconColor}
                            flexShrink={0}
                          />
                          <TruncatedText>{filter.label}</TruncatedText>
                        </HStack>
                        <Text
                          fontSize="xs"
                          color={mutedTextColor}
                          bg={headerBg}
                          px="2"
                          py="1"
                          borderRadius="full"
                          minW="6"
                          textAlign="center"
                        >
                          {count}
                        </Text>
                      </Button>
                    );
                  })}
                </VStack>
              </PopoverBody>

              <PopoverFooter bg={headerBg} borderColor={borderColor} borderTopWidth="1px" p="3">
                <HStack justify="space-between">
                  <Text fontSize="xs" color={mutedTextColor}>
                    {selectedValues.size} of {filters.length} selected
                  </Text>
                  <HStack spacing="2">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={handleClearFilter}
                      isDisabled={selectedValues.size === 0}
                      color={mutedTextColor}
                      _hover={{
                        color: textColor,
                        bg: buttonHoverBg,
                        transform: 'translateY(-1px)',
                      }}
                      _active={{
                        transform: 'translateY(0)',
                      }}
                      transition="all 0.2s ease"
                      borderRadius="md"
                    >
                      Clear
                    </Button>
                    <Button
                      size="sm"
                      colorScheme="blue"
                      onClick={onClose}
                      _hover={{
                        transform: 'translateY(-1px)',
                        boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3)',
                      }}
                      _active={{
                        transform: 'translateY(0)',
                      }}
                      transition="all 0.2s ease"
                      borderRadius="md"
                    >
                      Apply
                    </Button>
                  </HStack>
                </HStack>
              </PopoverFooter>
            </PopoverContent>
          </>
        );
      }}
    </Popover>
  );
}

const TruncatedText = ({ children }: { children: string }) => {
  const textRef = useRef<HTMLParagraphElement>(null);
  const [isTruncated, setIsTruncated] = useState(false);

  useEffect(() => {
    const checkTruncation = () => {
      const element = textRef.current;
      if (element) {
        setIsTruncated(element.scrollWidth > element.clientWidth);
      }
    };

    checkTruncation();
    window.addEventListener('resize', checkTruncation);
    return () => window.removeEventListener('resize', checkTruncation);
  }, [children]);

  return isTruncated ? (
    <Tooltip label={children} placement="top" hasArrow>
      <Text ref={textRef} isTruncated>
        {children}
      </Text>
    </Tooltip>
  ) : (
    <Text ref={textRef} isTruncated>
      {children}
    </Text>
  );
};
