import { FacetDefinition } from '../ServerFacets/types';

export const eventFacetsDefinition: FacetDefinition[] = [
  {
    header: 'Country Code',
    accessorKey: 'country_code',
    id: 'country_code',
    visible: true,
  },
  {
    header: 'Region Name',
    accessorKey: 'region_code',
    id: 'region_code',
    visible: true,
  },
  {
    header: 'Lifecycle',
    accessorKey: 'lifecycle',
    id: 'lifecycle',
    visible: true,
  },
  {
    header: 'Event Name',
    accessorKey: 'event_name',
    id: 'event_name',
    visible: true,
  },
  {
    header: 'Street Cell Id',
    accessorKey: 'streetCellId',
    id: 'streetCellId',
    visible: false,
  },
  {
    header: 'Cause',
    accessorKey: 'cause',
    id: 'cause',
    visible: false,
  },
  {
    header: 'Event Type',
    accessorKey: 'eventType',
    id: 'eventType',
    visible: false,
  },
  {
    header: 'Object Type',
    accessorKey: 'object_type',
    id: 'object_type',
    visible: true,
  },
  {
    header: 'Object Id',
    accessorKey: 'object_id',
    id: 'object_id',
    visible: true,
  },
  {
    header: 'Severity',
    accessorKey: 'severity',
    id: 'severity',
    visible: true,
  },

  {
    header: 'Priority',
    accessorKey: 'priority',
    id: 'priority',
    visible: false,
  },
  {
    header: 'Reporting Entity Name',
    accessorKey: 'reporting_entity_name',
    id: 'reporting_entity_name',
    visible: false,
  },

  {
    header: 'Source Name',
    accessorKey: 'source_name',
    id: 'source_name',
    visible: false,
  },
  {
    header: 'Problem',
    accessorKey: 'specific_problem',
    id: 'specific_problem',
    visible: false,
  },

  {
    header: 'Type',
    accessorKey: 'type',
    id: 'type',
    visible: false,
  },

  {
    header: 'URI',
    accessorKey: 'uri',
    id: 'uri',
    visible: false,
  },
];
