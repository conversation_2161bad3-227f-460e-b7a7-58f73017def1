import * as React from 'react';
import { Box, Code } from '@chakra-ui/react';
import { ColumnDef, Row } from '@tanstack/react-table';
import { useNavigate } from 'react-router-dom';
import { useColorModeValue as mode } from '@chakra-ui/react';
import { EventHeader } from '../../../../types/metricCollector.type';
import EventColumns from './Columns';

// Shared transformEvent function
export const transformEvents = (eventList: any[] = []): EventHeader[] => {
  return eventList.map((event) => ({
    ...event.header,
    objectId: event?.data?.object_id,
    specificProblem: event?.data?.specific_problem,
    severity: event?.data?.severity,
    nodeId: event?.data?.inventory?.node?.node_id,
    nodeType: event?.data?.inventory?.node?.node_type,
    cell_refs: Array.isArray(event?.data?.inventory?.node?.cell_refs)
      ? event?.data?.inventory?.node?.cell_refs?.toString()
      : event?.data?.inventory?.node?.cell_refs,
    nodeSiteName: event?.data?.inventory?.node?.site_name,
    uri: event?.data?.uri,
    type: event?.data?.type,
    cause: event?.data?.cause,
    trendIndication: event?.data?.trend_indication,
    subRows: event.data,
  }));
};

// Shared hidden columns configuration
export const getHiddenColumnsByDefault = () => ({
  reporting_entity_name: false,
  domain: false,
  uri: false,
  type: false,
  cause: false,
  priority: false,
  event_type: false,
  trend_indication: false,
  node_type: false,
  object_id: false,
  nodeType: false,
});

// Shared columns definition
export const getEventColumns = (): ColumnDef<EventHeader>[] => EventColumns();

// Shared JsonKeyClickable component
export const JsonKeyClickable = ({ data }: { data: any }) => {
  const navigate = useNavigate();

  const handleClick = (key: any, value: any) => {
    if (key === 'nodeId') {
      navigate(`/cell-overview/nodes/${value}`);
    } else if (key === 'cell_refs') {
      navigate(`/cell-overview/cells/${value}`);
    }
  };

  const handleKeyDown = (event: any, key: any, value: any) => {
    if (event.key === 'Enter' && (key === 'nodeId' || key === 'cell_refs')) {
      handleClick(key, value);
    }
  };

  const renderFilesValue = (key: string, value: any) => (
    <a href={value} key={key} target="_blank" rel="noopener noreferrer" style={{ color: 'cornflowerblue' }}>
      {value}
    </a>
  );

  return (
    <div style={{ whiteSpace: 'pre-wrap' }}>
      {Object.entries(data).map(([key, value]) => {
        let cellRefIds;
        if (key === 'cell_refs' && typeof value === 'string') {
          cellRefIds = value.split(',');
        }
        if (key === 'subRows' && (value as any).monitoredAttributes) {
          return (
            <div key={key}>
              {key}:
              <div style={{ paddingLeft: '1rem' }}>
                {(value as any).monitoredAttributes.map((attr: any, index: number) => (
                  <div key={index}>
                    {Object.entries(attr).map(([attrKey, attrValue]) => (
                      <div key={attrKey}>
                        {attrKey}:
                        {attrKey === 'filesLocation'
                          ? renderFilesValue(attrKey, attrValue)
                          : JSON.stringify(attrValue, null, 2)}
                      </div>
                    ))}
                  </div>
                ))}
              </div>
            </div>
          );
        }
        return (
          <div key={key}>
            {key}:
            {cellRefIds ? (
              cellRefIds.map((id, index) => (
                <React.Fragment key={id}>
                  <Box
                    as="span"
                    _hover={{ bg: mode('gray.200', 'gray.700') }}
                    _activeLink={{ bg: 'gray.700', color: 'white' }}
                    color="cornflowerblue"
                    cursor="pointer"
                    onClick={() => handleClick(key, id)}
                    onKeyDown={(event: any) => handleKeyDown(event, key, id)}
                    role="button"
                    tabIndex={0}
                    p="1"
                  >
                    {id}
                  </Box>
                  {index < cellRefIds.length - 1 ? ',' : ''}
                </React.Fragment>
              ))
            ) : (
              <Box
                as="span"
                _hover={{ bg: mode('gray.200', 'gray.700') }}
                _activeLink={{ bg: 'gray.700', color: 'white' }}
                color={key === 'nodeId' || key === 'cell_refs' ? 'cornflowerblue' : 'inherit'}
                cursor={key === 'nodeId' || key === 'cell_refs' ? 'pointer' : 'default'}
                onClick={() => handleClick(key, value)}
                onKeyDown={(event: any) => handleKeyDown(event, key, value)}
                role="button"
                tabIndex={0}
                p="1"
              >
                {JSON.stringify(value, null, 2)}
              </Box>
            )}
          </div>
        );
      })}
    </div>
  );
};

// Shared renderSubComponent
export const useRenderSubComponent = () => {
  return React.useCallback(({ row }: { row: Row<EventHeader> }) => {
    return (
      <Box maxWidth="100vw">
        <pre style={{ fontSize: '10px' }}>
          <Code size="md" variant="outline" style={{ padding: '1rem' }}>
            <JsonKeyClickable data={row?.original} />
          </Code>
        </pre>
      </Box>
    );
  }, []);
};
