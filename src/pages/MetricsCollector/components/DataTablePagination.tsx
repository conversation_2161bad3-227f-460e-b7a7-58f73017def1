import { Box, Button, ButtonGroup, HStack, Select, Text, useColorModeValue } from '@chakra-ui/react';
import { Table } from '@tanstack/react-table';

interface DataTablePaginationProps<TData> {
  table: Table<TData>;
  pageSizeOptions?: number[];
  isPreviousData?: boolean;
  setLimit?: React.Dispatch<React.SetStateAction<number>>;
  limit?: number;
  isLoading?: boolean;
}

export function DataTablePagination<TData>({
  table,
  pageSizeOptions = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
  setLimit = () => ({}),
  limit,
  isLoading = false,
}: DataTablePaginationProps<TData>) {
  const totalRecords = (table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize;

  // Professional color palette
  const bg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.700', 'gray.200');
  const mutedTextColor = useColorModeValue('gray.600', 'gray.400');
  const buttonBg = useColorModeValue('white', 'gray.700');
  const buttonHoverBg = useColorModeValue('blue.50', 'gray.600');
  const buttonActiveBg = useColorModeValue('blue.100', 'gray.500');
  const selectBg = useColorModeValue('white', 'gray.700');

  const handleNext = (table: any, totalRecords: number) => {
    if (setLimit && limit && totalRecords >= limit) {
      setLimit(limit + table.getState().pagination.pageSize);
    }
    table.nextPage();
  };

  const totalPages = table.getPageCount();

  return (
    <Box
      mt="6"
      mb="4"
      p="4"
      bg={bg}
      borderRadius="lg"
      border="1px solid"
      borderColor={borderColor}
      boxShadow="sm"
      transition="all 0.2s ease"
    >
      <HStack justify="space-between" spacing="4" flexWrap="wrap">
        <HStack spacing="4" align="center">
          <Text fontSize="sm" color={mutedTextColor} fontWeight="500">
            Rows per page:
          </Text>
          <Select
            size="sm"
            w="auto"
            value={table.getState().pagination.pageSize}
            onChange={(e) => {
              table.setPageSize(Number(e.target.value));
            }}
            bg={selectBg}
            borderColor={borderColor}
            color={textColor}
            borderRadius="md"
            _hover={{ borderColor: 'blue.300' }}
            _focus={{
              borderColor: 'blue.400',
              boxShadow: '0 0 0 1px blue.400',
              outline: 'none',
            }}
            transition="all 0.2s ease"
          >
            {pageSizeOptions.map((pageSize) => (
              <option key={pageSize} value={pageSize}>
                {pageSize}
              </option>
            ))}
          </Select>
        </HStack>

        <HStack spacing="3" align="center">
          <Text fontSize="sm" color={textColor} fontWeight="500">
            Page {table.getState().pagination.pageIndex + 1} of {totalPages || 1}
          </Text>

          <ButtonGroup
            spacing="2"
            justifyContent="space-between"
            width={{
              base: 'full',
              md: 'auto',
            }}
            variant="outline"
            size="sm"
          >
            <Button
              onClick={() => table.previousPage()}
              isDisabled={table.getState().pagination.pageIndex === 0 || isLoading}
              bg={buttonBg}
              borderColor={borderColor}
              color={textColor}
              _hover={{
                bg: buttonHoverBg,
                borderColor: 'blue.300',
                transform: 'translateY(-1px)',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
              }}
              _active={{
                bg: buttonActiveBg,
                transform: 'translateY(0)',
              }}
              _disabled={{
                opacity: 0.5,
                cursor: 'not-allowed',
                _hover: {
                  bg: buttonBg,
                  transform: 'none',
                  boxShadow: 'none',
                },
              }}
              transition="all 0.2s ease"
              borderRadius="md"
              fontWeight="500"
            >
              Previous
            </Button>
            <Button
              onClick={() => handleNext(table, totalRecords)}
              isDisabled={totalPages === table.getState().pagination.pageIndex + 1 || isLoading}
              bg={buttonBg}
              borderColor={borderColor}
              color={textColor}
              _hover={{
                bg: buttonHoverBg,
                borderColor: 'blue.300',
                transform: 'translateY(-1px)',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
              }}
              _active={{
                bg: buttonActiveBg,
                transform: 'translateY(0)',
              }}
              _disabled={{
                opacity: 0.5,
                cursor: 'not-allowed',
                _hover: {
                  bg: buttonBg,
                  transform: 'none',
                  boxShadow: 'none',
                },
              }}
              transition="all 0.2s ease"
              borderRadius="md"
              fontWeight="500"
            >
              Next
            </Button>
          </ButtonGroup>
        </HStack>
      </HStack>
    </Box>
  );
}
