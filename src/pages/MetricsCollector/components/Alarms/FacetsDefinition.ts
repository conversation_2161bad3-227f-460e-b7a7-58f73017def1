import { FacetDefinition } from '../ServerFacets/types';

export const alarmFacetsDefinition: FacetDefinition[] = [
  {
    header: 'Country Code',
    accessorKey: 'country_code',
    id: 'country_code',
    visible: true,
  },
  {
    header: 'Region Name',
    accessorKey: 'region_code',
    id: 'region_code',
    visible: true,
  },
  {
    header: 'Lifecycle',
    accessorKey: 'lifecycle',
    id: 'lifecycle',
    visible: true,
  },
  {
    header: 'Event Name',
    accessorKey: 'event_name',
    id: 'event_name',
    visible: true,
  },
  {
    header: 'Alarm Id',
    accessorKey: 'id',
    id: 'id',
    visible: true,
  },
  {
    header: 'Problem',
    accessorKey: 'specific_problem',
    id: 'specific_problem',
    visible: false,
  },
  {
    header: 'Cell Refs',
    accessorKey: 'cell_refs',
    id: 'cell_refs',
    visible: true,
  },
  {
    header: 'Node Id',
    accessorKey: 'node_id',
    id: 'node_id',
    visible: true,
  },
  {
    header: 'Status',
    accessorKey: 'status',
    id: 'status',
    visible: false,
  },
  {
    header: 'Severity',
    accessorKey: 'severity',
    id: 'severity',
    visible: true,
  },
  {
    header: 'Cause',
    accessorKey: 'cause',
    id: 'cause',
    visible: false,
  },
  // TODO: filters dont work for event_id
  // {
  //   header: 'Event Id',
  //   accessorKey: 'event_id',
  //   id: 'event_id',
  //   filterKey: 'event_id',
  //   visible: false,
  // },
  {
    header: 'Object Type',
    accessorKey: 'object_type',
    id: 'object_type',
    visible: true,
  },
  {
    header: 'Object Id',
    accessorKey: 'object_id',
    id: 'object_id',
    visible: true,
  },
  {
    header: 'PLMN',
    accessorKey: 'plmns',
    id: 'plmns',
    visible: false,
  },
  {
    header: 'Reporting Entity Name',
    accessorKey: 'reporting_entity_name',
    id: 'reporting_entity_name',
    visible: false,
  },
  {
    header: 'Source Name',
    accessorKey: 'source_name',
    id: 'source_name',
    visible: false,
  },
  {
    header: 'Reported By',
    accessorKey: 'system_dn',
    id: 'system_dn',
    visible: false,
  },
  {
    header: 'Type',
    accessorKey: 'type',
    id: 'type',
    visible: false,
  },
];
