import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useSearchParams } from 'react-router-dom';
import { getAlarmList } from '../../../../../services/metricsCollector';
import { AlarmElement } from '../../../../../types/metricCollector.type';
import { SelectedFilters } from '../../ServerFacets/types';
import { useUserInfo } from './useUserInfo';
import { alarmFacetsDefinition } from '../FacetsDefinition';
import { LIFE_CYCLE } from '../../../../../data/constants';

export const useAlarmTableData = (
  filters: SelectedFilters,
  limit: number,
  openAlarms?: boolean,
  enabled = true,
  lifecycle?: LIFE_CYCLE | null
) => {
  const [searchParams] = useSearchParams();
  const checkForDevMode = process.env.NODE_ENV === 'development' && searchParams.get('devMode') === 'true';
  const { userFullName } = useUserInfo();
  const [alarmList, setAlarmList] = useState<AlarmElement[]>([]);

  // replace key in filters with the appropriate filterKey fetch from alarmFacetsDefinition
  const filtersWithFilterKey: SelectedFilters = Object.entries(filters).reduce<SelectedFilters>(
    (result, [key, value]) => {
      const facetDef = alarmFacetsDefinition.find((facet) => facet.accessorKey === key);
      const filterKey = facetDef?.filterKey || key;

      return {
        ...result,
        [filterKey]: value,
      };
    },
    {}
  );

  // Combine the passed 'enabled' flag with the dev mode check
  const isQueryEnabled = enabled && !checkForDevMode;

  // Fetch alarm data
  const { isLoading, data: alarms } = useQuery(
    ['alarmList', filtersWithFilterKey, limit, openAlarms, lifecycle],
    () => getAlarmList(limit, filtersWithFilterKey as SelectedFilters, openAlarms, lifecycle),
    {
      enabled: isQueryEnabled,
      refetchInterval: 30000,
      select: (data) => {
        const { count, alarms } = data;
        return alarms.map(
          ({
            id,
            internal_id,
            system_dn,
            cause,
            additional_text,
            proposed_repair_actions,
            type,
            severity,
            status,
            created,
            updated,
            expires,
            specific_problem,
            inventory,
            ...rest
          }) => ({
            id,
            internal_id,
            system_dn,
            cause,
            additional_text,
            proposed_repair_actions,
            type,
            severity,
            status,
            created,
            updated,
            expires,
            specific_problem,
            inventory,
            count,
            subRows: [
              { ...rest, userFullName } as AlarmElement & {
                userFullName: string;
              },
            ],
          })
        );
      },
    }
  );

  // Update alarm list when data changes
  useEffect(() => {
    if (isQueryEnabled && alarms) {
      setAlarmList(alarms);
    } else if (!isQueryEnabled) {
      setAlarmList([]);
    }
  }, [alarms, isQueryEnabled]);

  return {
    alarmList,
    isLoading: isQueryEnabled && isLoading,
  };
};
