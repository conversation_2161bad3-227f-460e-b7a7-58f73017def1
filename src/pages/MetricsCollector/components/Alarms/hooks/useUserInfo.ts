import { AUTH_TOKEN_KEY } from '../../../../../data/constants';
import useLogin from '../../../../../hooks/useLogin';

export const useUserInfo = () => {
  const { decodeJwtToken } = useLogin(AUTH_TOKEN_KEY);
  const decodedToken = decodeJwtToken();

  const firstName = decodedToken?.FirstName ?? '';
  const lastName = decodedToken?.LastName ?? '';
  const userFullName = `${firstName} ${lastName}`;

  return {
    firstName,
    lastName,
    userFullName,
  };
};
