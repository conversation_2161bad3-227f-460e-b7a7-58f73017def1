import React from 'react';
import {
  Box,
  ButtonGroup,
  Editable,
  EditablePreview,
  EditableTextarea,
  IconButton,
  Tooltip,
  useColorModeValue,
  useEditableControls,
} from '@chakra-ui/react';
import { CheckIcon, CloseIcon } from '@chakra-ui/icons';
import useUpdateAlarm from '../../hooks/useUpdateAlarm';

interface CommentProps {
  alarm_id: string;
  details: string | null;
  isWriteAccess: boolean;
}

export const CommentSection: React.FC<CommentProps> = ({ alarm_id, details, isWriteAccess }) => {
  const { updateAlarmMutation } = useUpdateAlarm();
  const hoverBackgroundColor = useColorModeValue('gray.200', 'gray.700');

  const EditableControls = () => {
    const { isEditing, getSubmitButtonProps, getCancelButtonProps } = useEditableControls();

    return isEditing ? (
      <ButtonGroup display="flex" justifyContent="end" size="sm" spacing={2} mt={2}>
        <IconButton aria-label="save comment" icon={<CheckIcon />} {...getSubmitButtonProps()} />
        <IconButton aria-label="cancel edit" icon={<CloseIcon boxSize={3} />} {...getCancelButtonProps()} />
      </ButtonGroup>
    ) : null;
  };

  const handleSubmit = (value: string) => {
    updateAlarmMutation({
      alarm_id: alarm_id ?? '',
      alarm: { details: value },
    });
  };

  return (
    <Editable
      defaultValue={details ?? ''}
      onSubmit={handleSubmit}
      submitOnBlur={false}
      selectAllOnFocus={false}
      placeholder="Add comment here"
      isDisabled={!isWriteAccess}
    >
      <Tooltip label="Click to edit" isDisabled={!isWriteAccess} shouldWrapChildren={true}>
        <EditablePreview
          py="2"
          px="4"
          _hover={{
            background: hoverBackgroundColor,
          }}
        />
      </Tooltip>
      <EditableTextarea />
      <EditableControls />
    </Editable>
  );
};
