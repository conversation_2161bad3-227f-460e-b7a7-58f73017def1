import { ColumnDef } from '@tanstack/react-table';
import { AlarmElement } from '../../../../types/metricCollector.type';
import { Box, Flex, Text, Tooltip } from '@chakra-ui/react';
import { BsArrowReturnRight } from 'react-icons/bs';
import Chip from '../../../../components/chip/Chip';
import { formatInReadableTimeDate } from '../../../../utils/formatInReadableTimeData';
import { CheckIcon } from '@chakra-ui/icons';
import { FaBell, FaHardHat } from 'react-icons/fa';
import { TbBellRingingFilled } from 'react-icons/tb';
import { MdFactory, MdHistory } from 'react-icons/md';
import { FaTools, FaCheckCircle, FaTimesCircle } from 'react-icons/fa';
import { TimeElapsed } from '../../../../components/nodeComponents/server/acp/AcpCard';

// Mapping of status types to icons with descriptive colors and configurations
const lifecycleStatusIcons = (lifecycle: string) => {
  const iconConfig = {
    FACTORY: {
      icon: MdFactory,
      color: '#4A5568',
      size: 24,
      tooltip: 'In Factory',
    },
    STAGING: {
      icon: FaHardHat,
      color: '#ED8936',
      size: 24,
      tooltip: 'Staging in Progress',
    },
    COMMISSIONING: {
      icon: FaTools,
      color: '#4299E1',
      size: 24,
      tooltip: 'Commissioning Phase',
    },
    OPERATIONAL: {
      icon: FaCheckCircle,
      color: '#48BB78',
      size: 24,
      tooltip: 'Fully Operational',
    },
    DECOMMISSIONED: {
      icon: FaTimesCircle,
      color: '#E53E3E',
      size: 24,
      tooltip: 'Decommissioned',
    },
  };

  return iconConfig[lifecycle as keyof typeof iconConfig];
};

export default function AlarmColumns(): ColumnDef<AlarmElement>[] {
  return [
    {
      id: 'expander',
      cell: (props) => {
        const expander_data = props.row.original.expander as string;
        return (
          <>
            <Flex>
              <BsArrowReturnRight />
              <Text ml={2}>{expander_data}</Text>
            </Flex>
          </>
        );
      },
    },
    {
      header: 'Id',
      accessorKey: 'id',
    },
    {
      header: 'Event Name',
      accessorKey: 'event_name',
      id: 'eventName',
      accessorFn: (originalRow) => {
        // if subRows is defined and has at least one elem
        if (originalRow.subRows && originalRow.subRows?.length > 0) {
          return originalRow.subRows[0].event_name || 'N/A';
        }
        return 'N/A';
      },
      cell: ({ getValue }) => {
        const value = getValue() as string;
        const shouldShowTooltip = value && value.length > 30;
        const content = (
          <Box isTruncated maxWidth="30ch">
            {value}
          </Box>
        );
        return shouldShowTooltip ? <Tooltip label={value}>{content}</Tooltip> : content;
      },
    },
    {
      header: 'Type',
      accessorKey: 'type',
      id: 'alarmsType',
      cell: ({ getValue }) => {
        const value = getValue() as string;
        const shouldShowTooltip = value && value.length > 30;
        const content = (
          <Box isTruncated maxWidth="30ch">
            {value}
          </Box>
        );
        return shouldShowTooltip ? <Tooltip label={value}>{content}</Tooltip> : content;
      },
    },
    {
      header: 'Severity',
      accessorKey: 'severity',
      cell: ({ getValue }) => <Chip statusText={getValue() as string} hasStatusLight />,
    },
    {
      header: 'Status',
      accessorKey: 'status',
      cell: ({ getValue }) => {
        const status = getValue() as string;
        let icon = null;
        let label = '';
        if (status === 'new') {
          icon = <FaBell color="red" size="20" />;
          label = 'New';
        } else if (status === 'acknowledged') {
          icon = <CheckIcon color="green.500" />;
          label = 'acknowledged';
        } else if (status === 'updated') {
          icon = <TbBellRingingFilled color="red" size="20" />;
          label = 'Updated';
        }
        return (
          <Box display="flex" justifyContent="center" alignItems="center">
            <Tooltip label={label} hasArrow>
              <span>{icon}</span>
            </Tooltip>
          </Box>
        );
      },
    },
    {
      header: 'Created',
      accessorKey: 'created',
      cell: ({ getValue }) => <>{getValue() ? formatInReadableTimeDate(getValue() as string).toString() : null}</>,
    },
    {
      accessorKey: 'updated',
      header: 'Updated',
      cell: ({ row }) => {
        const updatedDate = row.original.updated;
        const createdDate = row.original.created;

        return (
          <Box position="relative" display="flex" flexDirection="row" alignItems="center">
            <Box width="25px" marginRight="10px" display="flex" justifyContent="center">
              {updatedDate !== createdDate && (
                <Tooltip
                  label={`Created At: ${createdDate ? formatInReadableTimeDate(createdDate.toString()) : 'N/A'}`}
                  hasArrow
                  placement="top"
                >
                  <Box cursor="pointer">
                    <MdHistory size="25px" color="teal" />
                  </Box>
                </Tooltip>
              )}
            </Box>
            <Text>{updatedDate ? formatInReadableTimeDate(updatedDate.toString()) : 'N/A'}</Text>
          </Box>
        );
      },
    },
    {
      id: 'opened_for',
      accessorKey: 'opened_for',
      header: 'Opened For',
      cell: ({ row }) => {
        const createdDate = row.original.created;
        return <TimeElapsed key={row.original.id} initialUptime={createdDate?.toString() || 'N/A'} />;
      },
    },
    {
      header: 'Object Type',
      accessorKey: 'object_type',
      id: 'objectType',
      accessorFn: (originalRow) => {
        // if subRows is defined and has at least one elem
        if (originalRow.subRows && originalRow.subRows?.length > 0) {
          return originalRow.subRows[0].object_type || 'N/A';
        }
        return 'N/A';
      },
    },
    {
      header: 'Roles',
      accessorKey: 'inventory',
      id: 'roles',
      accessorFn: (originalRow) => {
        const node = originalRow?.inventory?.node || {};
        const roles = node?.roles?.toString().split(',').join(', ') || 'N/A';
        return roles;
      },
      cell: ({ getValue }) => {
        const roles = getValue() as string | undefined;
        const shouldShowTooltip = roles && roles.length > 29;
        const content = (
          <Box isTruncated maxWidth="26ch">
            {roles}
          </Box>
        );
        return (shouldShowTooltip ? <Tooltip label={roles}>{content}</Tooltip> : roles) || 'N/A';
      },
    },
    {
      header: 'Country Code',
      accessorFn: (originalRow) => {
        const cells = originalRow.inventory?.cells || [];
        return Array.from(new Set(cells.map((cell: any) => cell.country_code))).join(', ') || 'N/A';
      },
      id: 'country_code',
      cell: ({ getValue }) => {
        const countryCodes = getValue() as string;
        const shouldShowTooltip = countryCodes.length > 29;
        const content = (
          <Box isTruncated maxWidth="26ch">
            {countryCodes}
          </Box>
        );
        return shouldShowTooltip ? <Tooltip label={countryCodes}>{content}</Tooltip> : countryCodes;
      },
      filterFn: (row, id, value) => {
        const countryCodes = row.getValue(id) as string;
        return countryCodes.split(', ').some((code) => value.includes(code));
      },
    },
    {
      header: 'Region Name',
      accessorFn: (originalRow) => {
        const cells = originalRow.inventory?.cells || [];
        return Array.from(new Set(cells.map((cell: any) => cell.region_name))).join(', ') || 'N/A';
      },
      id: 'region_name',
      cell: ({ getValue }) => {
        const regionNames = getValue() as string;
        const shouldShowTooltip = regionNames.length > 29;
        const content = (
          <Box isTruncated maxWidth="26ch">
            {regionNames}
          </Box>
        );
        return shouldShowTooltip ? <Tooltip label={regionNames}>{content}</Tooltip> : regionNames;
      },
      filterFn: (row, id, value) => {
        const regionNames = row.getValue(id) as string;
        return regionNames.split(', ').some((name) => value.includes(name));
      },
    },
    {
      header: 'Object Id',
      accessorKey: 'object_id',
      id: 'objectId',
      accessorFn: (originalRow) => {
        if (originalRow.subRows && originalRow.subRows?.length > 0) {
          return originalRow.subRows[0].object_id || 'N/A';
        }
        return 'N/A';
      },
      cell: ({ getValue }) => {
        const value = getValue() as string;
        const shouldShowTooltip = value && value.length > 20;
        const content = (
          <Box isTruncated maxWidth="20ch">
            {value}
          </Box>
        );
        return shouldShowTooltip ? <Tooltip label={value}>{content}</Tooltip> : content;
      },
    },
    {
      header: 'Node Id',
      accessorKey: 'inventory',
      id: 'alarmNodeId',
      accessorFn: (originalRow) => {
        return originalRow.inventory?.node?.node_id || 'N/A';
      },
    },
    {
      header: 'Node Type',
      accessorKey: 'inventory',
      id: 'alarmNodeType',
      accessorFn: (originalRow) => {
        return originalRow?.inventory?.node?.node_type || 'N/A';
      },
    },
    {
      header: 'Cell Reference',
      accessorKey: 'inventory',
      id: 'alarm_cell_refs',
      accessorFn: (originalRow) => {
        const node = originalRow?.inventory?.node || {};
        const cell_refs = node?.cell_ids?.toString() || node?.cell_refs?.toString() || 'N/A';
        return cell_refs;
      },
      cell: ({ getValue }) => {
        // check if cell_refs is an empty array or undefined and return a placeholder
        const cellRefs = getValue() as string | undefined;
        const shouldShowTooltip = cellRefs && cellRefs.length > 20;
        const content = (
          <Box isTruncated maxWidth="20ch">
            {cellRefs}
          </Box>
        );
        return (shouldShowTooltip ? <Tooltip label={cellRefs}>{content}</Tooltip> : cellRefs) || 'N/A';
      },
    },
    {
      header: 'Site Name',
      accessorKey: 'inventory',
      id: 'alarmSiteName',
      accessorFn: (originalRow) => {
        return originalRow?.inventory?.node?.site_name || 'N/A';
      },
    },
    {
      header: 'Lifecycle',
      id: 'lifecycle',
      accessorKey: 'inventory',
      accessorFn: (originalRow) => {
        const node = originalRow?.inventory?.node || {};
        const lifecycle = node?.lifecycle || 'N/A';
        return lifecycle;
      },
      cell: ({ getValue }) => {
        const lifecycle = getValue() as string;
        const config = lifecycleStatusIcons(lifecycle);
        return config ? (
          <Text>{lifecycle}</Text>
        ) : (
          // TODO: We need this in future to show the icon
          // <Box position="relative" display="inline-block">
          //   <Tooltip label={config.tooltip} hasArrow>
          //     <Box display="flex" alignItems="center">
          //       <config.icon size={config.size} color={config.color} />
          //     </Box>
          //   </Tooltip>
          // </Box>
          'N/A'
        );
      },
    },
    {
      header: 'Problem',
      accessorKey: 'specific_problem',
      cell: ({ getValue }) => {
        const value = getValue() as string;
        const shouldShowTooltip = value && value.length > 20;
        const content = (
          <Box isTruncated maxWidth="20ch">
            {value}
          </Box>
        );
        return shouldShowTooltip ? <Tooltip label={value}>{content}</Tooltip> : content;
      },
    },
  ];
}
