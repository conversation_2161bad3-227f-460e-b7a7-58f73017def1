import React from 'react';
import {
  <PERSON><PERSON>,
  Divider,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  <PERSON>dal<PERSON>ooter,
  <PERSON>dalHeader,
  ModalOverlay,
  Textarea,
} from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import Loader from '../../../../components/loader/Loader';
import { AlarmElement } from '../../../../types/metricCollector.type';

// Form validation schema
const schema = z.object({
  resolution: z.string().min(1, { message: 'Resolution comment is required' }),
});
type ResolutionSchema = z.infer<typeof schema>;

interface ResolutionModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedAlarm: AlarmElement | null;
  resolveAlarmMutation: any; // Replace with proper type
  setResolveExpandable: React.Dispatch<React.SetStateAction<boolean>>;
}

export const ResolutionModal: React.FC<ResolutionModalProps> = ({
  isOpen,
  onClose,
  selectedAlarm,
  resolveAlarmMutation,
  setResolveExpandable,
}) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<ResolutionSchema>({
    resolver: zodResolver(schema),
  });

  const onSubmit = async ({ resolution }: ResolutionSchema) => {
    if (!selectedAlarm) return;

    await resolveAlarmMutation({
      alarm_id: String(selectedAlarm.id),
      alarm: { resolution },
    });

    onClose();
    reset();
    setResolveExpandable(true);
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl">
      <ModalOverlay />
      <ModalContent>
        <form onSubmit={handleSubmit(onSubmit)}>
          <ModalHeader>Resolution comments</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            {resolveAlarmMutation.isLoading ? (
              <Loader />
            ) : (
              <FormControl isInvalid={!!errors.resolution}>
                <FormLabel>Comments</FormLabel>
                <Textarea placeholder="Add comments here" size="sm" {...register('resolution')} />
                <FormErrorMessage>{errors.resolution?.message}</FormErrorMessage>
              </FormControl>
            )}
          </ModalBody>

          <Divider mt="2rem" />
          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onClose}>
              Close
            </Button>
            <Button colorScheme="blue" type="submit" isLoading={resolveAlarmMutation.isLoading}>
              Submit
            </Button>
          </ModalFooter>
        </form>
      </ModalContent>
    </Modal>
  );
};
