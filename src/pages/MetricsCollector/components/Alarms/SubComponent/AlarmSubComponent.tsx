import React, { useState } from 'react';
import { Box, Button, Card, CardBody, CardHeader, Flex, Grid, GridItem, Heading } from '@chakra-ui/react';
import { AlarmElement } from '../../../../../types/metricCollector.type';
import { AUTH_TOKEN_KEY, READ_WRITE_ACCESS_ROLES } from '../../../../../data/constants';
import useLogin from '../../../../../hooks/useLogin';
import { ProblemDetailsSection } from './sections/ProblemDetailsSection';
import { CellInfoSection } from './sections/CellInfoSection';
import { SiteInfoSection } from './sections/SiteInfoSection';
import { CommentsSection } from './sections/CommentsSection';
import { ActionsSection } from './sections/ActionsSection';

interface AlarmSubComponentProps {
  row: { original: AlarmElement };
  onShowEventsForAlarm: (alarm: AlarmElement) => void;
  onResolveAlarm: (alarm: AlarmElement) => void;
  onAcknowledgeAlarm: (alarm: AlarmElement) => void;
}

export const AlarmSubComponent: React.FC<AlarmSubComponentProps> = ({
  row,
  onShowEventsForAlarm,
  onResolveAlarm,
  onAcknowledgeAlarm,
}) => {
  const [toggledRows, setToggledRows] = useState(true);
  const { checkApplicationAccess } = useLogin(AUTH_TOKEN_KEY);
  const isWriteAccess = checkApplicationAccess(READ_WRITE_ACCESS_ROLES);

  return (
    <Card boxShadow="lg" width="90%" border="1px solid teal" ml="8">
      <CardHeader data-testid="alarm-subcomponent-header" p="2">
        <Heading size="md">
          {row.original.id} - {row.original.subRows?.[0]?.event_name}
        </Heading>
      </CardHeader>
      <CardBody data-testid="alarm-subcomponent-body" width="100%" p="1" m="0">
        <Grid templateColumns="70% 30%" p="2" gap="2" width="100%" data-testid="alarm-subcomponent-grid">
          {/* Left Side - Problem Details + Cell/Site Info */}

          <GridItem colSpan={1}>
            <Flex direction="column" height="100%" gap="2" width="100%">
              <Box flex="1">
                <ProblemDetailsSection row={row} />
              </Box>

              <Grid templateColumns="35% 64%" gap="2">
                <GridItem>
                  <CellInfoSection
                    inventory={row.original.inventory}
                    toggledRows={toggledRows}
                    setToggledRows={setToggledRows}
                  />
                </GridItem>
                <GridItem>
                  <SiteInfoSection inventory={row.original.inventory} />
                </GridItem>
              </Grid>
            </Flex>
          </GridItem>

          {/* Right Side - Comments and Actions */}

          <GridItem>
            <Flex direction="column" gap="2">
              <CommentsSection
                alarmId={row.original.id.toString()}
                details={row.original.subRows?.[0]?.details ?? null}
                isWriteAccess={isWriteAccess}
              />

              <ActionsSection
                status={row.original.status}
                alarm={row.original}
                acknowledger={row.original.subRows?.[0]?.acknowledger ?? null}
                acknowledged={row.original.subRows?.[0]?.acknowledged ?? null}
                resolver={row.original.subRows?.[0]?.resolver ?? null}
                resolved={row.original.subRows?.[0]?.resolved ?? null}
                isWriteAccess={isWriteAccess}
                onAcknowledgeAlarm={onAcknowledgeAlarm}
                onResolveAlarm={onResolveAlarm}
              />
            </Flex>
          </GridItem>
        </Grid>

        <Box display="flex" justifyContent="center">
          {/* View Related Events Button */}
          <Button
            mt="2"
            mb="2"
            colorScheme="teal"
            onClick={() => onShowEventsForAlarm(row.original)}
            data-testid="view-related-events-button"
            variant="primary"
          >
            View related events
          </Button>
        </Box>
      </CardBody>
    </Card>
  );
};
