import React from 'react';
import { Box, FormControl, Heading } from '@chakra-ui/react';
import { CommentSection } from './CommentSection';

interface CommentsSectionProps {
  alarmId: string;
  details: string | null;
  isWriteAccess: boolean;
}

export const CommentsSection: React.FC<CommentsSectionProps> = ({ alarmId, details, isWriteAccess }) => {
  return (
    <Box p={4} borderWidth="1px" borderRadius="md">
      <Heading size="sm" mb={3}>
        Comments
      </Heading>
      <Box minH="120px">
        <FormControl mb={4}>
          <CommentSection alarm_id={alarmId} details={details} isWriteAccess={isWriteAccess} />
        </FormControl>
      </Box>
    </Box>
  );
};
