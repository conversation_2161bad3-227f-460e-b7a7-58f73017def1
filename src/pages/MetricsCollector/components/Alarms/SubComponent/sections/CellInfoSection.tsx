import React from 'react';
import { Box, Button, Divider, Heading, Link, Text, useColorModeValue } from '@chakra-ui/react';
import { UnstyledTable } from '../../../../../../components/nodeComponents/airspan/utils';
import { ArrowForwardIcon } from '@chakra-ui/icons';
import { HiOutlineExternalLink } from 'react-icons/hi';

interface CellInfoSectionProps {
  inventory: any;
  toggledRows: boolean;
  setToggledRows: React.Dispatch<React.SetStateAction<boolean>>;
}

export const CellInfoSection: React.FC<CellInfoSectionProps> = ({ inventory, toggledRows, setToggledRows }) => {
  const hoverBackgroundColor = useColorModeValue('gray.200', 'gray.700');
  const cellRefs = inventory?.node?.cell_refs || inventory?.node?.cell_ids;

  const linkProps = {
    _hover: { textDecoration: 'none', bg: hoverBackgroundColor },
    _activeLink: { bg: 'gray.700', color: 'white' },
    color: 'brand.600',
    isExternal: false,
    rel: 'noopener noreferrer',
  };

  // Render cell references with optional toggling for large lists
  const renderCellRefsLinks = () => {
    if (!cellRefs || cellRefs.length === 0) {
      return 'N/A';
    }

    const displayLength = toggledRows && cellRefs.length > 2 ? 2 : cellRefs.length;

    return (
      <>
        {cellRefs.slice(0, displayLength).map((val: string, index: number) => (
          <span
            key={index}
            role="button"
            tabIndex={0}
            style={{
              cursor: 'pointer',
              display: 'inline-block',
              transition: 'all 0.2s',
              backgroundColor: 'brand.500', // Light brand color background
              borderColor: 'brand.500',
              fontWeight: '800',
            }}
          >
            <Link
              {...linkProps}
              href={`/cell-overview/cells/${val}`}
              display="flex"
              alignItems="center"
              gap={1}
              color="brand.700"
              textDecoration="underline"
              _hover={{
                textDecoration: 'none',
                color: 'brand.800',
              }}
              mr="2"
            >
              {val}
              <HiOutlineExternalLink size="20" />
            </Link>
          </span>
        ))}
        {toggledRows && cellRefs.length > 2 && '...'}

        {cellRefs.length > 2 && (
          <Button
            mt={2}
            display="block"
            _hover={{ textDecoration: 'none', bg: hoverBackgroundColor }}
            borderRadius={{ lg: 'lg' }}
            bgColor="brand.100"
            size="sm"
            onClick={() => setToggledRows((prev) => !prev)}
          >
            {toggledRows ? 'More' : 'Less'}
          </Button>
        )}
      </>
    );
  };

  const data = {
    ['Lifecycle']: inventory?.node?.lifecycle || 'N/A',
    ['Cell Refs']: renderCellRefsLinks(),
    ['Node Id']: inventory?.node?.node_id ? (
      <span
        role="button"
        tabIndex={0}
        style={{
          cursor: 'pointer',
          display: 'inline-block',
          transition: 'all 0.2s',
          backgroundColor: 'brand.500',
          borderColor: 'brand.500',
          fontWeight: '800',
        }}
      >
        <Link
          {...linkProps}
          href={`/cell-overview/nodes/${inventory.node.node_id}`}
          display="flex"
          alignItems="center"
          gap={1}
          color="brand.700"
          textDecoration="underline"
          _hover={{
            textDecoration: 'none',
            color: 'brand.800',
          }}
        >
          {inventory.node.node_id}
          <HiOutlineExternalLink size="20" />
        </Link>
      </span>
    ) : (
      'N/A'
    ),
  };

  return (
    <Box p={4} borderWidth="1px" borderRadius="md" h="100%">
      <Heading size="md" mb={3}>
        Cell Info
      </Heading>

      <Divider />

      <Box mt={2}>
        <UnstyledTable tableData={data} firstColWidth="15%" />
      </Box>
    </Box>
  );
};
