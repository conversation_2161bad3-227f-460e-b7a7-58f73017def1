import React from 'react';
import {
  Box,
  Button,
  Flex,
  Heading,
  Stat,
  StatGroup,
  StatHelpText,
  StatLabel,
  StatNumber,
  VStack,
  Divider,
} from '@chakra-ui/react';
import { AtSignIcon } from '@chakra-ui/icons';
import { formatInReadableTimeDate } from '../../../../../../utils/formatInReadableTimeData';
import { AlarmElement } from '../../../../../../types/metricCollector.type';

interface ActionsSectionProps {
  status: string;
  alarm: AlarmElement;
  acknowledger: string | null;
  acknowledged: string | null;
  resolver: string | null;
  resolved: string | null;
  isWriteAccess: boolean;
  onAcknowledgeAlarm: (alarm: AlarmElement) => void;
  onResolveAlarm: (alarm: AlarmElement) => void;
}

export const ActionsSection: React.FC<ActionsSectionProps> = ({
  status,
  alarm,
  acknowledger,
  acknowledged,
  resolver,
  resolved,
  isWriteAccess,
  onAcknowledgeAlarm,
  onResolveAlarm,
}) => {
  const isAcknowledgeDisabled = !['new', 'updated'].includes(status) || !isWriteAccess;
  const isResolveDisabled = !['new', 'acknowledged', 'updated'].includes(status) || !isWriteAccess;

  return (
    <Box p={4} borderWidth="1px" borderRadius="md">
      <Heading size="sm" textTransform="uppercase" mt={2}>
        Actions
      </Heading>
      <VStack direction="column" gap={2} alignItems="flex-start">
        <Flex justifyContent="flex-start" mt="2">
          <Button
            width="100%"
            colorScheme="teal"
            isDisabled={isAcknowledgeDisabled || !isWriteAccess}
            onClick={() => onAcknowledgeAlarm(alarm)}
          >
            Acknowledge
          </Button>
          {acknowledger !== null && (
            <Box mr="4" ml="4">
              <StatGroup>
                <Stat>
                  <StatLabel>{status === 'updated' ? 'Previously acknowledged by:' : 'Acknowledged by:'}</StatLabel>
                  <StatNumber fontSize="md">{acknowledger}</StatNumber>
                  <StatHelpText>
                    <AtSignIcon color="teal" />
                    {formatInReadableTimeDate(acknowledged ?? '')}
                  </StatHelpText>
                </Stat>
              </StatGroup>
            </Box>
          )}
        </Flex>
        <Divider />
        <Flex justifyContent="flex-start">
          <Button
            width="100%"
            colorScheme="teal"
            isDisabled={isResolveDisabled || !isWriteAccess}
            onClick={() => onResolveAlarm(alarm)}
          >
            Resolved
          </Button>

          {resolver !== null && (
            <Box mt="4" ml="4">
              <StatGroup>
                <Stat>
                  <StatLabel>Resolved by:</StatLabel>
                  <StatNumber fontSize="md">{resolver}</StatNumber>
                  <StatHelpText>
                    <AtSignIcon color="teal" />
                    {formatInReadableTimeDate(resolved ?? '')}
                  </StatHelpText>
                </Stat>
              </StatGroup>
            </Box>
          )}
        </Flex>
      </VStack>
    </Box>
  );
};
