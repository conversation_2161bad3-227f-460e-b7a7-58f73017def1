import React from 'react';
import { Box, Heading, Text, UnorderedList, ListItem, Divider, Flex } from '@chakra-ui/react';
import { AlarmElement } from '../../../../../../types/metricCollector.type';
import { UnstyledTable } from '../../../../../../components/nodeComponents/airspan/utils';

interface ProblemDetailsSectionProps {
  row: { original: AlarmElement };
}

export const ProblemDetailsSection: React.FC<ProblemDetailsSectionProps> = ({ row }) => {
  const { system_dn, additional_text, cause, specific_problem, proposed_repair_actions } = row.original;
  const { object_type, object_id } = row.original.subRows?.[0] ?? {};

  // Parse and display repair actions with proper formatting
  const renderProposedRepairs = (repairActions: string[] = []) => {
    if (!repairActions.length) return null;

    const repairText = repairActions.join('\n');
    const repairs = repairText.split('\n');
    const hasNoBulletActions = repairs.some((action) => /^\d+\)/.test(action));

    return repairs.map((repair, index) => {
      const noBulletStyle = /^\d+\)/.test(repair);
      return (
        <UnorderedList key={index} style={{ marginInlineStart: hasNoBulletActions ? '0px' : undefined }}>
          <ListItem
            style={{
              listStyleType: noBulletStyle ? 'none' : 'initial',
            }}
          >
            {repair}
          </ListItem>
        </UnorderedList>
      );
    });
  };

  const data = {
    ['Problem Details']: (
      <Text as="span" wordBreak="break-word" whiteSpace="normal" fontSize="md">
        {specific_problem} {specific_problem && additional_text && ' - '} {additional_text}
      </Text>
    ),
    ['Reported By']: system_dn,
    ['Cause']: cause,
    ['Proposed Repairs']: (
      <Flex flexDirection="row">
        <Box as="span">{renderProposedRepairs(proposed_repair_actions)}</Box>
      </Flex>
    ),
  };

  return (
    <Box h="100%" p={4} borderWidth="1px" borderRadius="md">
      <Heading size="md" mb={4}>
        {(object_type || object_id) && (
          <Text as="span" fontWeight="bold" m="2">
            {object_type}
            {object_type && object_id && ' - '}
            {object_id}
          </Text>
        )}
      </Heading>
      <Divider />
      <Box ml="4" mt={2}>
        <UnstyledTable tableData={data} firstColWidth="15%" />
      </Box>
    </Box>
  );
};
