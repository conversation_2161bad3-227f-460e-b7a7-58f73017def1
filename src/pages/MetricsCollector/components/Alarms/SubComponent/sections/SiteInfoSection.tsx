import React from 'react';
import { Box, Divider, Heading, Link, Text, useColorModeValue } from '@chakra-ui/react';
import { UnstyledTable } from '../../../../../../components/nodeComponents/airspan/utils';

interface SiteInfoSectionProps {
  inventory: any;
}

export const SiteInfoSection: React.FC<SiteInfoSectionProps> = ({ inventory }) => {
  const hoverBackgroundColor = useColorModeValue('gray.200', 'gray.700');

  const linkProps = {
    _hover: { textDecoration: 'none', bg: hoverBackgroundColor },
    _activeLink: { bg: 'gray.700', color: 'white' },
    color: 'brand.600',
    isExternal: true,
    rel: 'noopener noreferrer',
  };

  const data = {
    ['Site Name']: inventory?.node?.site_name || 'N/A',
    ['Site Address']: inventory?.node?.site_address || 'N/A',
    ['Country Name']: inventory?.node?.country_name || 'N/A',
    ['Region Name']: inventory?.node?.region_name || 'N/A',
    ['Latitude/Longitude']:
      inventory?.node?.latitude && inventory?.node?.longitude ? (
        <Link
          {...linkProps}
          href={`http://www.google.com/maps/place/${inventory.node.latitude},${inventory.node.longitude}`}
        >
          {inventory.node.latitude}/{inventory.node.longitude}
        </Link>
      ) : (
        'N/A'
      ),
  };

  return (
    <Box p={4} borderWidth="1px" borderRadius="md" h="100%">
      <Heading size="md" mb={3}>
        Site Info
      </Heading>

      <Divider />

      <Box mt={2}>
        <UnstyledTable tableData={data} firstColWidth="15%" />
      </Box>
    </Box>
  );
};
