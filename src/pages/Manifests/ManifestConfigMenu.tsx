import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>u<PERSON>utton, <PERSON>u<PERSON><PERSON>, MenuList } from '@chakra-ui/react';
import { BsThreeDots } from 'react-icons/bs';
import { LinkIcon } from '@chakra-ui/icons';
import { useNavigate } from 'react-router-dom';

const ManifestConfigMenu = ({ node_id }: { node_id: string }) => {
  const navigate = useNavigate();

  return (
    <>
      <Menu data-testid="manifest-menu-items">
        <MenuButton
          data-testid="manifest-menu-items-button"
          onClick={(e) => {
            e.stopPropagation();
          }}
          as={IconButton}
          aria-label="Options"
          icon={<BsThreeDots />}
          variant="outline"
          border="none"
        />
        <MenuList minWidth="110px">
          <MenuItem
            data-testid="view-nodes"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              navigate(`/cell-overview/nodes/${node_id}`);
            }}
          >
            <LinkIcon mr="1rem" color="green" />
            View node
          </MenuItem>
        </MenuList>
      </Menu>
    </>
  );
};

export default ManifestConfigMenu;
