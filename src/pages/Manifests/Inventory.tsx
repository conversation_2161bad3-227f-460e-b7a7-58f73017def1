import { CheckIcon, CloseIcon } from '@chakra-ui/icons';
import {
  Bad<PERSON>,
  Box,
  Collapse,
  Container,
  Heading,
  Link,
  List,
  ListItem,
  SimpleGrid,
  Stack,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
  Text,
} from '@chakra-ui/react';
import React from 'react';
import { RefreshCellInventoryResponse } from '../../types/orchestrator.types';

interface TabData {
  label: string;
  content: Array<string | Record<string, string>>;
}

interface DataTabsProps {
  data?: RefreshCellInventoryResponse;
}

const Inventory: React.FC<DataTabsProps> = ({ data }) => {
  const invalidStreetCells = data?.detail?.format_data_process?.['Invalid Street Cells'] || [];
  const rejectedStreetCells = data?.detail?.post_manifest_inventory_manager_process?.['Rejected Street Cells'] || [];

  const mappedData = [...invalidStreetCells, ...rejectedStreetCells];

  const [show, setShow] = React.useState<Record<number, boolean>>({});
  const inventoryList: TabData[] = [
    {
      label: 'Accepted Street Cells',
      content: data?.detail?.post_manifest_inventory_manager_process['Accepted Street Cells'] ?? [],
    },
    {
      label: 'Rejected Street Cells',
      content: mappedData ?? [],
    },
  ];
  const stats = [
    {
      label: 'Total cells',
      value: data?.detail?.collect_data_process?.['Number of cells gathered'] ?? 0,
    },
    {
      label: 'Accepted cells',
      value: data?.detail?.post_manifest_inventory_manager_process['Accepted Street Cells']?.length,
    },
    {
      label: 'Rejected cells',
      value: mappedData?.length,
    },
  ];
  const handleToggle = (index: number) => {
    setShow((prevState) => ({
      ...prevState,
      [index]: !prevState[index],
    }));
  };

  return (
    <>
      <Box as="section" pb={{ base: '4', md: '8' }}>
        <Container px={0}>
          <SimpleGrid columns={{ base: 1, md: 3 }} gap={{ base: '5', md: '6' }}>
            {stats.map(({ label, value }) => (
              <Box
                key={value}
                px={{ base: '4', md: '6' }}
                py={{ base: '5', md: '6' }}
                bg="accent"
                color={'inverted'}
                borderRadius="lg"
                boxShadow="lg"
              >
                <Stack>
                  <Text textStyle="sm" color="fg.muted">
                    {label}
                  </Text>
                  <Heading size={{ base: 'sm', md: 'lg' }}>{value}</Heading>
                </Stack>
              </Box>
            ))}
          </SimpleGrid>
        </Container>
      </Box>
      <Tabs>
        <TabList>
          {inventoryList?.map((tab, index) => (
            <Tab key={index}>
              {index === 1 ? <CloseIcon mr="2" color={'red'} /> : <CheckIcon mr="2" color={'green'} />}
              {tab.label}
            </Tab>
          ))}
        </TabList>
        <TabPanels>
          {inventoryList?.map((tab, tabIndex) => (
            <TabPanel p={4} key={tabIndex}>
              <List spacing={1}>
                {typeof tab.content[0] === 'string'
                  ? tab.content.map((cell, index) => (
                      <ListItem key={index.toString()}>
                        <Badge
                          borderRadius="full"
                          px="2"
                          letterSpacing={'0.03rem'}
                          border="1px solid"
                          borderColor={'brand.500'}
                          textTransform="capitalize"
                          fontSize="0.8em"
                          lineHeight={'1.2rem'}
                          colorScheme="black"
                        >
                          <CheckIcon mr="2" color="green" />
                          {typeof cell === 'string' ? cell : null}
                        </Badge>
                      </ListItem>
                    ))
                  : tab.content.map((cell, index) => (
                      <ListItem key={index.toString()}>
                        <Link textDecoration={'underline'} onClick={() => handleToggle(index)} color="red.500" href="#">
                          {typeof cell !== 'string' ? Object.keys(cell)[0] : null}
                        </Link>
                        <Collapse in={show[index]}>
                          <Box p="10px" mb="4" border={'1px solid red'} rounded="md" shadow="md">
                            {typeof cell !== 'string' ? Object.values(cell)[0] : null}
                          </Box>
                        </Collapse>
                      </ListItem>
                    ))}
              </List>
            </TabPanel>
          ))}
        </TabPanels>
      </Tabs>
    </>
  );
};

export default Inventory;
