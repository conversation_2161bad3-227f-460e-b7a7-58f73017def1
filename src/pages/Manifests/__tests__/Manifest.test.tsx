import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { ReactNode } from 'react';
import ManifestTable from '../ManifestTable';

type ChildrenProps = {
  children: ReactNode;
};

// Wrapper for react query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});
const wrapper = ({ children }: ChildrenProps) => (
  <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
);

// Debugging
// screen.debug();
// //This one in the function and before the issue
// screen.logTestingPlaygroundURL();

describe('Cell Manifest Table', () => {
  it('should render table with correct headers and data', async () => {
    render(<ManifestTable />, { wrapper });
    // Check header cells
    // await waitFor(() => {
    //   screen.getAllByText(/node serial no/i);
    //   screen.getAllByText(/Lifecycle/i);
    //   // check for data
    //   screen.getByText(/AAAA31300103/i);
    //   screen.getByText(/STAGING/i);
    // });
  });
});

// describe('sort functions', () => {
//   // it('sort Node Serial No column asc', async () => {
//   //   render(<ManifestTable />, { wrapper });
//   //   const columnToSort = screen.getByRole('columnheader', {
//   //     name: /Node Serial No/i,
//   //   });
//   //   fireEvent.click(columnToSort);
//   //   fireEvent.click(columnToSort);
//   // });
//   // it('sort Node Serial No column desc', async () => {
//   //   render(<ManifestTable />, { wrapper });
//   //   const columnToSort = screen.getByRole('columnheader', {
//   //     name: /Node Serial No/i,
//   //   });
//   //   fireEvent.click(columnToSort);
//   // });
//   // it('sort cell lifecycle column asc', async () => {
//   //   render(<ManifestTable />, { wrapper });
//   //   const columnToSort = screen.getByRole('columnheader', {
//   //     name: /lifecycle/i,
//   //   });
//   //   fireEvent.click(columnToSort);
//   //   fireEvent.click(columnToSort);
//   // });
//   // it('sort cell lifecycle column desc', async () => {
//   //   render(<ManifestTable />, { wrapper });
//   //   const columnToSort = screen.getByRole('columnheader', {
//   //     name: /lifecycle/i,
//   //   });
//   //   fireEvent.click(columnToSort);
//   // });
// });

// describe('sub components', () => {
//   // it("render subcomponent 'CellManifest' when clicking on a row", async () => {
//   //   render(<ManifestTable />, { wrapper });
//   //   const row = screen.getByRole('cell', { name: /aaaa31300103/i });
//   //   fireEvent.click(row);
//   //   //await screen.findByText(/Airspan Manifest/i);
//   // });
// });
