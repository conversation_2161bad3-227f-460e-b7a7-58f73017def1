import { AddIcon } from '@chakra-ui/icons';
import {
  Box,
  Button,
  Heading,
  Icon,
  Stack,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
  useColorModeValue,
  Link,
} from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import { useNavigate, useParams, Link as RouterLink } from 'react-router-dom';
import ManifestTable from './ManifestTable';
import VersionManifests from './VersionManifests';

const ManifestOverview = () => {
  const navigate = useNavigate();
  const { tab } = useParams();
  const [tabIndex, setTabIndex] = useState(0);

  useEffect(() => {
    if (tab === 'device-manifests') {
      setTabIndex(0);
    } else if (tab === 'version-manifests') {
      setTabIndex(1);
    }
  }, [tab]);

  const handleTabs = (index: number) => {
    if (index === 0) {
      navigate('/manifest-overview/device-manifests');
    } else {
      navigate('/manifest-overview/version-manifests');
    }
  };

  const colorModeValue = useColorModeValue('sm', 'sm-dark');

  return (
    <>
      <Stack
        spacing="1"
        direction={{
          base: 'column',
          lg: 'row',
        }}
        justify="space-between"
      >
        <Stack spacing="1">
          <Heading fontWeight="medium">Manifest Overview</Heading>
        </Stack>
        <Link as={RouterLink} to="/manifest-overview/add-network-manifest">
          <Button data-testid="create-cell-button" variant="primary" leftIcon={<Icon as={AddIcon} marginStart="-1" />}>
            Add Network Manifest
          </Button>
        </Link>
      </Stack>

      <Stack
        spacing={{
          base: '5',
          lg: '6',
        }}
      >
        <Box
          bg="bg-surface"
          boxShadow={{
            base: 'none',
            md: colorModeValue,
          }}
        >
          <Stack spacing="5">
            <Tabs
              isManual
              isFitted
              variant="enclosed-colored"
              orientation="horizontal"
              isLazy
              onChange={(index: number) => handleTabs(index)}
              index={tabIndex}
            >
              <TabList mb="1em">
                <Tab>Device Manifests</Tab>
                <Tab>Version Manifests</Tab>
              </TabList>
              <TabPanels>
                <TabPanel id="device-manifests">
                  <ManifestTable />
                </TabPanel>
                <TabPanel id="version-manifests">
                  <VersionManifests />
                </TabPanel>
              </TabPanels>
            </Tabs>
          </Stack>
        </Box>
      </Stack>
    </>
  );
};

export default ManifestOverview;
