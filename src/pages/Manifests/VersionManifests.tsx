import { Box, Container, Flex, Heading, Stack, Text, useColorModeValue } from '@chakra-ui/react';
import { useQuery } from '@tanstack/react-query';
import React from 'react';
import { ColumnDef } from '@tanstack/react-table';
import Loader from '../../components/loader/Loader';
import { DataTable } from '../MetricsCollector/components/DataTable';
import { getVersionDevicesData } from '../../services/inventoryManager';
import { ErrorBoundary } from 'react-error-boundary';
import { ErrorBoundaryFallback, ErrorBoundaryLogError } from '../../components/errorComponents/ErrorBoundaryFallback';
import QueryError from '../../components/errorComponents/QueryError';

const VersionDeviceTable = () => {
  const [versionLimit, setVersionLimit] = React.useState<number>(1000);

  const {
    isLoading,
    error,
    data: versionDevicesData,
  } = useQuery({
    queryKey: ['getVersionDevicesData', 'versionLimit'],
    queryFn: () => getVersionDevicesData(versionLimit),
  });
  const bgColor = useColorModeValue('gray.100', 'gray.800');
  const boxShadow = useColorModeValue('sm', 'sm-dark');

  const columns = React.useMemo<ColumnDef<any>[]>(
    () => [
      {
        header: 'Node Serial No',
        accessorKey: 'node_serial_no',
        id: 'node_serial_no',
        cell: (props) => {
          const node_serial_no = props.row.original.node_serial_no;
          return (
            <Flex>
              {/* Need to revert back when we have data to show */}
              {/* <BsArrowReturnRight /> */}
              <Text ml="2">{node_serial_no}</Text>
            </Flex>
          );
        },
      },
      {
        header: 'Software Version',
        accessorKey: 'version',
        accessorFn: (originalRow) => originalRow?.version || '',
        id: 'version',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        header: 'Node Type',
        accessorKey: 'node_type',
        id: 'node_type',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        header: 'Component Type',
        accessorKey: 'component_type',
        id: 'component_type',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        header: 'Lifecycle',
        accessorKey: 'lifecycle',
        id: 'lifecycle',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        header: 'Country',
        accessorKey: 'country_name',
        id: 'country_name',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        header: 'Region',
        accessorKey: 'region_name',
        id: 'region_name',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        header: 'Site Name',
        accessorKey: 'site_name',
        id: 'site_name',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
    ],
    []
  );

  if (isLoading) return <Loader />;
  if (error) return <QueryError error={error} />;
  return (
    <Flex
      px={{
        base: '4',
        md: '8',
      }}
      flexDirection="column"
      mt="2"
    >
      <Stack
        spacing="4"
        direction={{
          base: 'column',
          lg: 'row',
        }}
        justify="center"
      >
        <Stack spacing="1" data-testid="Version-heading">
          <Heading fontWeight="medium">Device Version Information</Heading>
        </Stack>
      </Stack>
      <Stack
        spacing={{
          base: '5',
          lg: '6',
        }}
      >
        <Box bg={bgColor}>
          <Container
            py={{
              base: '4',
              md: '0',
            }}
            px={{
              base: '0',
              md: 0,
            }}
          >
            <Box bg="bg-surface" boxShadow={boxShadow}>
              <Stack spacing="5">
                <Box
                  px={{
                    base: '4',
                    md: '6',
                  }}
                  pt="5"
                  data-testid="version-manifest-container"
                />
                <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError as any}>
                  <DataTable
                    enableFilter={true}
                    isExpandable={false}
                    columns={columns}
                    defaultPageSize={100}
                    count={versionDevicesData?.length || 0}
                    data={versionDevicesData || []}
                    limit={versionLimit}
                    setLimit={setVersionLimit}
                  />
                </ErrorBoundary>
              </Stack>
            </Box>
          </Container>
        </Box>
      </Stack>
    </Flex>
  );
};

export default VersionDeviceTable;
