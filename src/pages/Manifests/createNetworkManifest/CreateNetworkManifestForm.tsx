import React from 'react';
import { useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { useColorModeValue, useToast } from '@chakra-ui/react';
import { useMutation } from '@tanstack/react-query';
import {
  Box,
  FormControl,
  FormLabel,
  Input,
  Select,
  Textarea,
  Button,
  FormErrorMessage,
  SimpleGrid,
  Flex,
  Divider,
  Text,
} from '@chakra-ui/react';
import useRegionList from '../../SiteManager/hooks/useRegionList';
import { NetworkManifestForm, networkManifestSchema } from './schema';
import { zodResolver } from '@hookform/resolvers/zod';
import { createNetworkManifest } from '../../../services/inventoryManager';

const CreateNetworkManifestForm = () => {
  const navigate = useNavigate();
  const toast = useToast();

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    watch,
    reset,
  } = useForm<NetworkManifestForm>({
    resolver: zodResolver(networkManifestSchema),
    mode: 'onChange',
  });

  const { mutateAsync: postNetworkManifest, isLoading } = useMutation(createNetworkManifest);

  const { data: regionList = [] } = useRegionList();
  const boxShadow = useColorModeValue('sm', 'sm-dark');

  const onSubmit = async (data: NetworkManifestForm) => {
    try {
      const transformedData = { ...data };

      switch (data.credential_type) {
        case 'v2c':
          transformedData.credentials = {
            username: 'v2c',
            auth: data.community_string || '',
            priv: '',
          };
          break;
        case 'v3 auth-only':
          if (transformedData.credentials) {
            transformedData.credentials.priv = '';
            transformedData.credentials.priv_protocol = undefined;
          }
          break;
        case 'v3 auth-priv':
          // No transformation needed
          break;
        default:
          break;
      }

      delete transformedData.community_string;

      await postNetworkManifest(transformedData);
      toast({
        title: 'Network Manifest created',
        description: 'The network manifest has been successfully created.',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
      reset();
      navigate('/manifest-overview/device-manifests');
    } catch (error) {
      toast({
        title: 'Error',
        description: 'There was an error creating the network manifest.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  // Watch the credential_type field to conditionally display credential fields
  const credentialType = watch('credential_type');

  // Watch the node_type field to conditionally display component_type options
  const selectedNodeType = watch('node_type');

  // Determine component_type options based on node_type
  const getComponentTypeOptions = () => {
    switch (selectedNodeType) {
      case 'Switch':
        return [
          { value: 'FIBROLAN', label: 'Fibrolan' },
          { value: 'JUNIPER', label: 'Juniper' },
          { value: 'MOSOLABS', label: 'Mosolabs' },
        ];
      case 'Firewall':
        return [{ value: 'JUNIPER', label: 'Juniper' }];
      default:
        return [];
    }
  };

  return (
    <Box
      as="form"
      marginX="auto"
      width="full"
      maxW={{ lg: '8xl' }}
      bg="bg-surface"
      boxShadow={boxShadow}
      onSubmit={handleSubmit(onSubmit)}
      borderRadius="lg"
      p="8"
    >
      {/* Section 1: Basic Information */}
      <Box mb="6">
        <Text fontSize="lg" fontWeight="semibold" mb="4">
          Basic Information
        </Text>
        <SimpleGrid columns={3} spacing="3">
          <FormControl isInvalid={!!errors.name} isRequired>
            <FormLabel>Name</FormLabel>
            <Input placeholder="Name" {...register('name')} />
            <FormErrorMessage>{errors.name?.message}</FormErrorMessage>
          </FormControl>
          <FormControl isInvalid={!!errors.serial_no} isRequired>
            <FormLabel>Serial Number</FormLabel>
            <Input placeholder="Serial Number" {...register('serial_no')} />
            <FormErrorMessage>{errors.serial_no?.message}</FormErrorMessage>
          </FormControl>

          <FormControl>
            <FormLabel>Description</FormLabel>
            <Textarea placeholder="Description" {...register('description')} />
          </FormControl>
        </SimpleGrid>
      </Box>

      {/* Section 2: Node Details */}
      <Box mb="6">
        <Text fontSize="lg" fontWeight="semibold" mb="4">
          Node Details
        </Text>
        <SimpleGrid columns={5} spacing="3">
          <FormControl isInvalid={!!errors.node_type} isRequired>
            <FormLabel>Node Type</FormLabel>
            <Select placeholder="Select Node Type" {...register('node_type')}>
              <option value="Switch">Switch</option>
              <option value="Firewall">Firewall</option>
            </Select>
            <FormErrorMessage>{errors.node_type?.message}</FormErrorMessage>
          </FormControl>

          <FormControl isInvalid={!!errors.component_type} isRequired>
            <FormLabel>Component Type</FormLabel>
            <Select placeholder="Select Component Type" {...register('component_type')}>
              {getComponentTypeOptions().map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </Select>
            <FormErrorMessage>{errors.component_type?.message}</FormErrorMessage>
          </FormControl>

          <FormControl isInvalid={!!errors.model} isRequired>
            <FormLabel>Model</FormLabel>
            <Input placeholder="Model" {...register('model')} />
            <FormErrorMessage>{errors.model?.message}</FormErrorMessage>
          </FormControl>

          <FormControl isInvalid={!!errors.version} isRequired>
            <FormLabel>Version</FormLabel>
            <Input placeholder="Version" {...register('version')} />
            <FormErrorMessage>{errors.version?.message}</FormErrorMessage>
          </FormControl>

          <FormControl isInvalid={!!errors.ip_address} isRequired>
            <FormLabel>IP Address</FormLabel>
            <Input placeholder="IP Address" {...register('ip_address')} />
            <FormErrorMessage>{errors.ip_address?.message}</FormErrorMessage>
          </FormControl>
        </SimpleGrid>
      </Box>

      {/* Section 3: Location */}
      <Box mb="6" mt="10">
        <Text fontSize="lg" fontWeight="semibold" mb="4">
          Location
        </Text>
        <SimpleGrid columns={3} spacing="3">
          <FormControl>
            <FormLabel>Location</FormLabel>
            <Input placeholder="Location" {...register('location')} />
          </FormControl>

          <FormControl isInvalid={!!errors.region_code} isRequired>
            <FormLabel>Region Code</FormLabel>
            <Select placeholder="Select Region Code" {...register('region_code')}>
              {regionList.map(({ region_id, region_name, region_code, country_name }) => (
                <option key={region_id} value={region_code}>
                  {region_code} ({region_name}) - {country_name}
                </option>
              ))}
            </Select>
            <FormErrorMessage>{errors.region_code?.message}</FormErrorMessage>
          </FormControl>
        </SimpleGrid>
      </Box>

      {/* <Divider my="10" borderColor="gray.400" /> */}

      {/* Section 4: Credentials */}
      <Box mb="6" mt="10">
        <Flex alignItems="flex-start" mb="4" width="100%">
          <Text fontSize="lg" fontWeight="semibold" mr="4" mt="2">
            Credentials
          </Text>

          <FormControl isInvalid={!!errors.credential_type} width="auto" flex="1" marginLeft="auto" isRequired>
            <Box display="flex" justifyContent="flex-start" flexDirection="row" width="100%">
              <Flex justifyContent="center" flexDirection="column" width="100%">
                <Select placeholder="Select Security Type" {...register('credential_type')} width="40%">
                  <option value="v2c">v2c</option>
                  <option value="v3 auth-only">v3 auth-only</option>
                  <option value="v3 auth-priv">v3 auth-priv</option>
                </Select>
                <FormErrorMessage ml="4">{errors.credential_type?.message}</FormErrorMessage>
              </Flex>
            </Box>
          </FormControl>
        </Flex>
        <SimpleGrid columns={5} spacing="3" mt="10">
          {/* V2c fields */}
          {credentialType === 'v2c' && (
            <>
              <FormControl isInvalid={!!errors.community_string} isRequired>
                <FormLabel>Community String</FormLabel>
                <Input placeholder="Community String" {...register('community_string')} />
                <FormErrorMessage>{errors.community_string?.message}</FormErrorMessage>
              </FormControl>
            </>
          )}

          {/* v3 auth-only and v3 auth-priv fields */}
          {(credentialType === 'v3 auth-only' || credentialType === 'v3 auth-priv') && (
            <>
              <FormControl isInvalid={!!errors.credentials?.username} isRequired>
                <FormLabel>Username</FormLabel>
                <Input placeholder="Username" {...register('credentials.username')} />
                <FormErrorMessage>{errors.credentials?.username?.message}</FormErrorMessage>
              </FormControl>

              <FormControl isInvalid={!!errors.credentials?.auth} isRequired>
                <FormLabel>Auth Password</FormLabel>
                <Input type="password" placeholder="Auth Password" {...register('credentials.auth')} />
                <FormErrorMessage>{errors.credentials?.auth?.message}</FormErrorMessage>
              </FormControl>

              <FormControl isInvalid={!!errors.credentials?.auth_protocol} isRequired>
                <FormLabel>Auth Protocol</FormLabel>
                <Select {...register('credentials.auth_protocol')}>
                  <option value="SHA">SHA</option>
                  <option value="SHA192">SHA192</option>
                  <option value="SHA256">SHA256</option>
                  <option value="MD5">MD5</option>
                </Select>
                <FormErrorMessage>{errors.credentials?.auth_protocol?.message}</FormErrorMessage>
              </FormControl>
            </>
          )}

          {/* v3 auth-priv only fields */}
          {credentialType === 'v3 auth-priv' && (
            <>
              <FormControl isInvalid={!!errors.credentials?.priv} isRequired>
                <FormLabel>Priv Password</FormLabel>
                <Input type="password" placeholder="Priv Password" {...register('credentials.priv')} />
                <FormErrorMessage>{errors.credentials?.priv?.message}</FormErrorMessage>
              </FormControl>

              <FormControl isInvalid={!!errors.credentials?.priv_protocol} isRequired>
                <FormLabel>Priv Protocol</FormLabel>
                <Select {...register('credentials.priv_protocol')}>
                  <option value="AES">AES</option>
                  <option value="AES192">AES192</option>
                  <option value="AES256">AES256</option>
                  <option value="DES">DES</option>
                  <option value="3DES">3DES</option>
                </Select>
                <FormErrorMessage>{errors.credentials?.priv_protocol?.message}</FormErrorMessage>
              </FormControl>
            </>
          )}
        </SimpleGrid>
      </Box>

      <Divider my="6" />

      <Flex direction="row-reverse" justifyContent="space-between" gap="4">
        <Button type="submit" colorScheme="teal" isLoading={isLoading} isDisabled={!isValid}>
          Create
        </Button>
        <Button colorScheme="teal" variant="outline" onClick={() => navigate('/manifest-overview/device-manifests')}>
          Cancel
        </Button>
      </Flex>
    </Box>
  );
};

export default CreateNetworkManifestForm;
