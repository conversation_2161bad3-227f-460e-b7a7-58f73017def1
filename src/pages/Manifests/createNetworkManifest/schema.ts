import { z } from 'zod';

const credentialTypes = ['v2c', 'v3 auth-only', 'v3 auth-priv'] as const;

export const networkManifestSchema = z
  .object({
    serial_no: z.string().min(1, { message: 'Serial Number is required' }),
    name: z.string().regex(/^[a-zA-Z0-9-]+$/, 'Name can only contain letters, numbers, and hyphens'),
    description: z.string().optional(),
    node_type: z.string().min(1, { message: 'Node Type is required' }),
    component_type: z.string().min(1, { message: 'Component Type is required' }),
    model: z.string().min(1, { message: 'Model is required' }),
    version: z.string().min(1, { message: 'Version is required' }),
    ip_address: z.string().refine(
      (val) => {
        const octets = val.split('.');
        if (octets.length !== 4) return false; // Must have 4 octets

        return octets.every((octet) => {
          const num = parseInt(octet, 10);
          return num >= 0 && num <= 255 && octet === num.toString(); // Avoid leading zeros
        });
      },
      {
        message: 'Invalid IP address. Each octet must be between 0 and 255.',
      }
    ),
    location: z.string().optional(),
    region_code: z.string().min(1, { message: 'Region Code is required' }),
    access_method: z.literal('snmp').default('snmp'),
    credential_type: z.string().min(1, { message: 'Security Type is required' }),
    community_string: z.string().optional(),
    credentials: z
      .object({
        username: z.string().optional(),
        auth: z.string().optional(),
        priv: z.string().optional(),
        auth_protocol: z
          .enum(['SHA', 'SHA192', 'SHA256', 'MD5'], {
            errorMap: () => ({ message: 'Invalid Auth Protocol. Allowed: SHA, SHA192, SHA256, MD5' }),
          })
          .optional(),
        priv_protocol: z
          .enum(['AES', 'AES192', 'AES256', 'DES', '3DES'], {
            errorMap: () => ({ message: 'Invalid Priv Protocol. Allowed: AES, AES192, AES256, DES, 3DES' }),
          })
          .optional(),
      })
      .optional()
      .default({}),
  })
  .superRefine((data, ctx) => {
    switch (data.credential_type) {
      case 'v2c':
        if (!data.community_string || data.community_string.trim() === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['community_string'],
            message: 'Community String is required for v2c',
          });
        }
        break;
      case 'v3 auth-only':
        if (!data.credentials?.username || data.credentials.username.trim() === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['credentials', 'username'],
            message: 'Username is required for v3 auth-only',
          });
        }
        if (!data.credentials?.auth || data.credentials.auth.trim() === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['credentials', 'auth'],
            message: 'Auth is required for v3 auth-only',
          });
        }
        if (!data.credentials?.auth_protocol) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['credentials', 'auth_protocol'],
            message: 'Auth Protocol is required for v3 auth-only',
          });
        }
        break;
      case 'v3 auth-priv':
        if (!data.credentials?.username || data.credentials.username.trim() === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['credentials', 'username'],
            message: 'Username is required for v3 auth-priv',
          });
        }
        if (!data.credentials?.auth || data.credentials.auth.trim() === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['credentials', 'auth'],
            message: 'Auth is required for v3 auth-priv',
          });
        }
        if (!data.credentials?.priv || data.credentials.priv.trim() === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['credentials', 'priv'],
            message: 'Priv is required for v3 auth-priv',
          });
        }
        if (!data.credentials?.auth_protocol) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['credentials', 'auth_protocol'],
            message: 'Auth Protocol is required for v3 auth-priv',
          });
        }
        if (!data.credentials?.priv_protocol) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['credentials', 'priv_protocol'],
            message: 'Priv Protocol is required for v3 auth-priv',
          });
        }
        break;
      default:
        break;
    }
  });

export type NetworkManifestForm = z.infer<typeof networkManifestSchema>;
