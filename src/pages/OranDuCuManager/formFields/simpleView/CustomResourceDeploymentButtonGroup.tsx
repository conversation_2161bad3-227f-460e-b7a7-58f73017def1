import React from 'react';
import { Flex, Button } from '@chakra-ui/react';
import { useNavigate } from 'react-router-dom';
import Loader from '../../../../components/loader/Loader';
import { ChevronRightIcon } from '@chakra-ui/icons';
import useLogin from '../../../../hooks/useLogin';
import { AUTH_TOKEN_KEY, READ_WRITE_ACCESS_ROLES } from '../../../../data/constants';

interface CustomResourceDeploymentButtonGroupProps {
  deploymentExists: boolean;
  isCreating: boolean;
  IsDeploymentSuccess: boolean;
  isDeleting: boolean;
  selectedOption: any;
  clusterList: any;
  methods: any;
  onSubmit: any;
  deployment_type?: string;
  onOpen: () => void;
}

const CustomResourceDeploymentButtonGroup: React.FC<CustomResourceDeploymentButtonGroupProps> = ({
  deploymentExists,
  isCreating,
  IsDeploymentSuccess,
  isDeleting,
  selectedOption,
  clusterList,
  methods,
  onSubmit,
  deployment_type,
  onOpen,
}) => {
  const navigate = useNavigate();

  const { checkNmsDevAccess } = useLogin(AUTH_TOKEN_KEY);
  const checkRoleAccess = checkNmsDevAccess(READ_WRITE_ACCESS_ROLES);

  if (isCreating || isDeleting) {
    return <Loader />;
  }

  if (IsDeploymentSuccess && deployment_type === 'new') {
    return (
      <Flex justifyContent="right" mb="4">
        <Button
          variant="primary"
          type="button"
          mt="8"
          mb="4"
          isLoading={isCreating}
          onClick={() =>
            navigate('/oran-du-cu-manager/simple-view/interfaces', {
              state: { selectedOption, deploymentExists, deployment_type, clusterList },
            })
          }
        >
          Interface
          <ChevronRightIcon boxSize={6} />
        </Button>
      </Flex>
    );
  }

  if (!deploymentExists && deployment_type === 'new' && checkRoleAccess) {
    return (
      <Flex justifyContent="center" mb="4">
        <Button
          data-testid="create-deployment-button"
          variant="primary"
          type="submit"
          mt="8"
          mb="4"
          isLoading={isCreating}
          onClick={() => {
            methods.handleSubmit(onSubmit)();
          }}
        >
          Create a deployment
        </Button>
      </Flex>
    );
  }

  if (deploymentExists && deployment_type === 'edit') {
    return (
      <Flex justifyContent="left" mt="4" mb="4">
        {checkRoleAccess && (
          <Button colorScheme="red" onClick={onOpen}>
            Delete
          </Button>
        )}
      </Flex>
    );
  }

  return null;
};

export default CustomResourceDeploymentButtonGroup;
