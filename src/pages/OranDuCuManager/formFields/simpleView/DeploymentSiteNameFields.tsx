import React from 'react';
import { Flex } from '@chakra-ui/react';
import InputFormField from '../../../../components/form/oranDuCuManager/input/InputFormField';

type DeploymentSiteNameFieldsProps = {
  isDisabled: boolean;
  createCustomResourcesForSiteIsSuccess: boolean;
};

const DeploymentSiteNameFields: React.FC<DeploymentSiteNameFieldsProps> = ({
  isDisabled,
  createCustomResourcesForSiteIsSuccess,
}) => {
  return (
    <Flex justifyContent="center" mb="4">
      <InputFormField
        name="du_site_name"
        label="DU site name"
        tooltip="Name of the DU site"
        isDisabled={isDisabled || createCustomResourcesForSiteIsSuccess}
      />
      <InputFormField
        name="cucp_site_name"
        label="CUCP site name"
        mx="8"
        tooltip="Name of the CUCP site"
        isDisabled={isDisabled || createCustomResourcesForSiteIsSuccess}
      />
      <InputFormField
        name="cuup_site_name"
        label="CUUP site name"
        tooltip="Name of the CUUP site"
        isDisabled={isDisabled || createCustomResourcesForSiteIsSuccess}
      />
    </Flex>
  );
};

export default DeploymentSiteNameFields;
