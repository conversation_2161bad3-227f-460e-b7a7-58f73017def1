import React from 'react';
import { Flex } from '@chakra-ui/react';
import InputFormField from '../../../../components/form/oranDuCuManager/input/InputFormField';

type DeploymentNameFieldProps = {
  deploymentType: string;
  createCustomResourcesForSiteIsSuccess: boolean;
  isDisabled: boolean;
  cellRef?: string;
};

const DeploymentNameField: React.FC<DeploymentNameFieldProps> = ({
  deploymentType,
  createCustomResourcesForSiteIsSuccess,
  isDisabled,
  cellRef,
}) => {
  const shouldDisableInput =
    deploymentType === 'edit' || createCustomResourcesForSiteIsSuccess || isDisabled || Boolean(cellRef);
  return (
    <Flex justifyContent="center" mb="4">
      <InputFormField
        name="deployment_name"
        label="Deployment"
        tooltip="Name of the deployment site"
        defaultValue={cellRef || ''}
        isDisabled={shouldDisableInput}
      />
    </Flex>
  );
};

export default DeploymentNameField;
