import React from 'react';
import { Stack, useColorModeValue } from '@chakra-ui/react';
import InputFormField from '../../../../components/form/oranDuCuManager/input/InputFormField';

type MoranDeploymentSiteAndClustersProps = {
  isDisabled: boolean;
  createCustomResourcesForSiteIsSuccess: boolean;
  clusterList: any;
};

const MoranDeploymentSiteAndClusters: React.FC<MoranDeploymentSiteAndClustersProps> = ({
  isDisabled,
  createCustomResourcesForSiteIsSuccess,
  clusterList,
}) => {
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  return (
    <Stack
      spacing="6"
      direction="row"
      align="flex-start"
      borderRadius="4"
      boxShadow="lg"
      border={borderColor}
      p="8"
      marginTop="4"
      marginBottom="12"
    >
      <InputFormField
        name="deployment_params.cu2.cucp_site_name2"
        label="MORAN CUCP site name"
        tooltip="Name of the MORAN CUCP site"
        isDisabled={isDisabled || createCustomResourcesForSiteIsSuccess}
      />
      <InputFormField
        name="deployment_params.cu2.cuup_site_name2"
        label="MORAN CUUP site name"
        tooltip="Name of the MORAN CUUP site"
        isDisabled={isDisabled || createCustomResourcesForSiteIsSuccess}
      />
      <InputFormField
        name="deployment_params.cu2.f1_ip2"
        label="MORAN F1 ip"
        tooltip="MORAN F1 ip address"
        isDisabled={isDisabled || createCustomResourcesForSiteIsSuccess}
      />
      <InputFormField
        name="deployment_params.cu2.e1_ip2"
        label="MORAN E1 ip"
        tooltip="MORAN E1 ip address"
        isDisabled={isDisabled || createCustomResourcesForSiteIsSuccess}
      />
    </Stack>
  );
};

export default MoranDeploymentSiteAndClusters;
