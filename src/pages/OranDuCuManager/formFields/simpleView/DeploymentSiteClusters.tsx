import React from 'react';
import { Flex } from '@chakra-ui/react';
import SelectForm<PERSON>ield from '../../../../components/form/oranDuCuManager/select/SelectFormField';

type DeploymentSiteClustersProps = {
  isDisabled: boolean;
  createCustomResourcesForSiteIsSuccess: boolean;
  clusterList: any;
};

const DeploymentSiteClusters: React.FC<DeploymentSiteClustersProps> = ({
  isDisabled,
  createCustomResourcesForSiteIsSuccess,
  clusterList,
}) => {
  return (
    <Flex justifyContent="center" mb="4">
      <SelectFormField
        isDisabled={isDisabled || createCustomResourcesForSiteIsSuccess}
        name="du_cluster"
        label="DU Cluster"
        options={[...clusterList]
          .sort((a, b) => a.id - b.id)
          .map((cluster: any) => ({
            value: cluster.id,
            label: `${cluster.id.toString()}:${cluster.cluster_name} - ${cluster.description} - ${
              cluster.software_release
            }`,
          }))}
        tooltip="DU Cluster"
      />
      <SelectFormField
        isDisabled={isDisabled || createCustomResourcesForSiteIsSuccess}
        name="cucp_cluster"
        label="CUCP Cluster"
        options={[...clusterList]
          .sort((a, b) => a.id - b.id)
          .map((cluster: any) => ({
            value: cluster.id,
            label: `${cluster.id.toString()}:${cluster.cluster_name} - ${cluster.description}  - ${
              cluster.software_release
            }`,
          }))}
        mx="8"
        tooltip="CUCP Cluster"
      />
      <SelectFormField
        isDisabled={isDisabled || createCustomResourcesForSiteIsSuccess}
        name="cuup_cluster"
        label="CUUP Cluster"
        options={[...clusterList]
          .sort((a, b) => a.id - b.id)
          .map((cluster: any) => ({
            value: cluster.id,
            label: `${cluster.id.toString()}:${cluster.cluster_name} - ${cluster.description}  - ${
              cluster.software_release
            }`,
          }))}
        tooltip="CUUP Cluster"
      />
    </Flex>
  );
};

export default DeploymentSiteClusters;
