import React, { useState } from 'react';
import { Flex, Button, Text, Divider, Box } from '@chakra-ui/react';
import { useNavigate } from 'react-router';
import { ChevronRightIcon } from '@chakra-ui/icons';
import useLogin from '../../../../hooks/useLogin';
import { AUTH_TOKEN_KEY, READ_WRITE_ACCESS_ROLES } from '../../../../data/constants';

type PodsDeploymentButtonGroupProps = {
  deploymentExists: any;
  DeploymentCustomResourcesData: any;
  handleActivate: () => void;
  handleDeactivate: () => void;
  onOpen: () => void;
  isOpen: boolean;
  onClose: () => void;
};

const PodsDeploymentButtonGroup: React.FC<PodsDeploymentButtonGroupProps> = ({
  deploymentExists,
  DeploymentCustomResourcesData,
  handleActivate,
  handleDeactivate,
  onOpen,
  isOpen,
  onClose,
}) => {
  const navigate = useNavigate();

  const { checkNmsDevAccess } = useLogin(AUTH_TOKEN_KEY);
  const checkRoleAccess = checkNmsDevAccess(READ_WRITE_ACCESS_ROLES);

  const doesDeploymentHaveCrs = (data: any) => {
    return data?.du?.length > 0 && data?.cucp?.length > 0 && data?.cuup?.length > 0;
  };

  const areDeploymentCrsEnabled = (data: any) => {
    const allDuEnabled = data?.du.every((element: any) => element.enabled === 'True');
    const allCucpEnabled = data?.cucp.every((element: any) => element.enabled === 'True');
    const allCuupEnabled = data?.cuup.every((element: any) => element.enabled === 'True');

    return allDuEnabled && allCucpEnabled && allCuupEnabled;
  };

  if (!deploymentExists.exists) {
    return (
      <Flex justifyContent="center" mb="4">
        <Text>Deployment does not exist</Text>
      </Flex>
    );
  }

  return (
    <>
      <Divider />
      <Flex justifyContent="space-between" alignItems="center" py="4">
        {checkRoleAccess && (
          <Button colorScheme="red" onClick={onOpen}>
            Delete
          </Button>
        )}

        {checkRoleAccess && (
          <Box flex="1" textAlign="center">
            {doesDeploymentHaveCrs(DeploymentCustomResourcesData) &&
            !areDeploymentCrsEnabled(DeploymentCustomResourcesData) ? (
              <Button variant="primary" onClick={handleActivate}>
                Activate
              </Button>
            ) : null}
            {doesDeploymentHaveCrs(DeploymentCustomResourcesData) &&
            areDeploymentCrsEnabled(DeploymentCustomResourcesData) ? (
              <Button variant="primary" onClick={handleDeactivate}>
                Deactivate
              </Button>
            ) : null}
          </Box>
        )}

        {true && (
          <Button
            variant="primary"
            ml="auto"
            onClick={() =>
              navigate('/oran-du-cu-manager/simple-view', {
                state: {},
              })
            }
          >
            Finish
            <ChevronRightIcon boxSize={6} />
          </Button>
        )}
      </Flex>
    </>
  );
};

export default PodsDeploymentButtonGroup;
