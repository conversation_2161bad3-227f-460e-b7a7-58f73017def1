import React from 'react';
import { Flex } from '@chakra-ui/react';
import InputForm<PERSON>ield from '../../../../components/form/oranDuCuManager/input/InputFormField';
import SelectFormField from '../../../../components/form/oranDuCuManager/select/SelectFormField';

type DeploymentF1E1FieldsProps = {
  isDisabled: boolean;
  createCustomResourcesForSiteIsSuccess: boolean;
};

const DeploymentF1E1Fields: React.FC<DeploymentF1E1FieldsProps> = ({
  isDisabled,
  createCustomResourcesForSiteIsSuccess,
}) => {
  const ruVenderList = [
    {
      value: 'Pal',
      label: 'Pal',
    },
    {
      value: 'EdgeQ',
      label: 'EdgeQ',
    },
    {
      value: 'Airspan',
      label: 'Airspan',
    },
  ];
  return (
    <Flex justifyContent="center" mb="4">
      <InputFormField
        name="f1_ip"
        label="F1 ip"
        tooltip="F1 ip address"
        isDisabled={isDisabled || createCustomResourcesForSiteIsSuccess}
      />
      <InputFormField
        name="e1_ip"
        label="E1 ip"
        tooltip="E1 ip address"
        isDisabled={isDisabled || createCustomResourcesForSiteIsSuccess}
        mx="8"
      />
      <SelectFormField
        isDisabled={isDisabled || createCustomResourcesForSiteIsSuccess}
        name="ru_vendor"
        label="RU Vendor"
        options={ruVenderList.map((vendor: any) => ({
          value: vendor.value,
          label: vendor.label,
        }))}
        tooltip="RU Vendor"
      />
    </Flex>
  );
};

export default DeploymentF1E1Fields;
