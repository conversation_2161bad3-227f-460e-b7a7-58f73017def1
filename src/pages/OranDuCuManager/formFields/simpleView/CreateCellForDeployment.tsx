import { <PERSON><PERSON>, But<PERSON>, Flex, Divider, useToast } from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import useRegionList from '../../../SiteManager/hooks/useRegionList';
import useSiteList from '../../../SiteManager/hooks/useSiteList';
import { constructRegionSites } from '../../../../components/cellCreation/utils';
import RegionFields from '../../../../components/cellCreation/formFields/RegionFields';
import SiteFields from '../../../../components/cellCreation/formFields/SiteFields';
import PlacementField from '../../../../components/cellCreation/formFields/PlacementField';
import CellReferenceFields from '../../../../components/cellCreation/formFields/CellReferenceFields';
import useGetCellRef from '../../../../components/cellCreation/hooks/useGetCellRef';
import { ChevronRightIcon } from '@chakra-ui/icons';
import { useNavigate } from 'react-router';
import useLogin from '../../../../hooks/useLogin';
import { AUTH_TOKEN_KEY, LIFE_CYCLE, ORIENTATION, READ_WRITE_ACCESS_ROLES } from '../../../../data/constants';
import { useMutation } from '@tanstack/react-query';
import { postCell } from '../../../../services/inventoryManager';
import { CellRequest } from '../../../../types/InventoryManager.type';
import { useState } from 'react';
import { z } from 'zod';

const CreateCellForDeployment = () => {
  const cell_ref = '';
  const oran_split = '';
  const isEdit = false;
  const { data: regions = [] } = useRegionList();
  const { data: sites = [] } = useSiteList();

  const sortedRegions = [...regions].sort((a, b) => a.region_name.localeCompare(b.region_name));
  const sortedSites = [...sites].sort((a, b) => a.name.localeCompare(b.name));

  const { regions: regionsData, regionSites: regionSitesData } = constructRegionSites(sortedRegions, sortedSites);
  const [isCellCreated, setIsCellCreated] = useState(false);

  const toast = useToast();
  const navigate = useNavigate();

  const { checkNmsDevAccess } = useLogin(AUTH_TOKEN_KEY);
  const checkRoleAccess = checkNmsDevAccess(READ_WRITE_ACCESS_ROLES);

  const placements = [
    { value: 'Indoor', label: 'Indoor' },
    { value: 'Outdoor', label: 'Outdoor' },
  ].sort((a, b) => a.label.localeCompare(b.label));

  const schema = z.object({
    region: z.string().nonempty({ message: 'Region is required' }),
    site: z.string().nonempty({ message: 'Site is required' }),
    placement: z.string().nonempty({ message: 'Placement is required' }),
  });

  const cellForm = useForm<any>({
    defaultValues: {
      region: '',
      site: '',
      placement: '',
    },
    resolver: zodResolver(schema),
  });

  const {
    control,
    watch,
    reset,
    formState: { errors },
  } = cellForm;

  const region = watch('region');
  const site = watch('site');
  const placement = watch('placement');

  const { data: generatedCellRef = '', refetchCellRef } = useGetCellRef(
    Number(site),
    isEdit,
    cell_ref,
    oran_split,
    placement
  );

  const { mutateAsync: cellMutation, isLoading: isCellPosting } = useMutation({
    mutationFn: postCell,
  });

  const handleUpdateCellMutation = (cell: { cell_ref: string; cell: { site_id: number } }) => {
    console.log('Update cell mutation:', cell);
  };

  const handleCreateCellForDeployment = async () => {
    const postCellData: CellRequest = {
      cell_ref: generatedCellRef,
      oran_split: 'ORAN',
      site_id: site,
      region: region,
      orientation: 'UNK' as ORIENTATION,
      placement: placement,
      lifecycle: 'PLANNING' as LIFE_CYCLE,
      ran_type: '5G',
    };

    await cellMutation(postCellData, {
      onSuccess: (variables) => {
        setIsCellCreated(true);
        toast({
          title: `Cell  ${variables?.cell_ref} created successfully.`,
          status: 'success',
          duration: 5000,
          isClosable: true,
          position: 'top',
        });
      },
      onError: (error, variables) => {
        setIsCellCreated(false);
        if ((error as { response: { status: number } })?.response.status === 409) {
          toast({
            title: `Cell  ${variables?.cell_ref} already exist. Please regenerate.`,
            status: 'error',
            duration: 5000,
            isClosable: true,
            position: 'top',
          });
        }
      },
    });
  };

  return (
    <>
      <Stack
        spacing="6"
        direction={{
          base: 'column',
          md: 'row',
        }}
      >
        {/* Region Dropdown */}
        <RegionFields form={cellForm} regions={regionsData} isEdit={isEdit} />

        {/* Site Dropdown */}
        <SiteFields
          form={cellForm}
          regionSites={regionSitesData}
          isEdit={isEdit}
          updateCellMutation={handleUpdateCellMutation}
          checkRoleAccess={checkRoleAccess}
          onCreateOpen={() => handleCreateCellForDeployment()}
        />
      </Stack>
      <Stack
        spacing="6"
        direction={{
          base: 'column',
          md: 'row',
        }}
      >
        <PlacementField form={cellForm} isEdit={isEdit} placements={placements} />

        <CellReferenceFields
          form={cellForm}
          isEdit={isEdit}
          generatedCellRef={generatedCellRef}
          refetchCellRef={refetchCellRef}
        />
      </Stack>
      <Divider />
      <Flex justifyContent="space-between" alignItems="center" width="100%" mb="4">
        <Flex flex="1" />
        <Flex justifyContent="center" flex="1">
          <Button
            data-testid="create-deployment-button"
            variant="primary"
            type="submit"
            mt="8"
            mb="4"
            isDisabled={!region || !site || !placement || !generatedCellRef || isCellCreated}
            isLoading={isCellPosting}
            onClick={handleCreateCellForDeployment}
          >
            Create cell
          </Button>
        </Flex>

        <Flex justifyContent="flex-end" flex="1">
          <Button
            variant="primary"
            type="button"
            mt="8"
            mb="4"
            isDisabled={!isCellCreated}
            onClick={() => {
              reset();
              navigate('/oran-du-cu-manager/simple-view/deployment-use-cases', {
                state: { region, site, placement, generatedCellRef, deployment_type: 'new' },
              });
            }}
          >
            RF use case
            <ChevronRightIcon boxSize={6} />
          </Button>
        </Flex>
      </Flex>
    </>
  );
};

export default CreateCellForDeployment;
