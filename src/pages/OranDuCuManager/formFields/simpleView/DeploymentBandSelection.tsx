import React from 'react';
import { Flex } from '@chakra-ui/react';
import InputForm<PERSON>ield from '../../../../components/form/oranDuCuManager/input/InputFormField';
import SelectFormField from '../../../../components/form/oranDuCuManager/select/SelectFormField';

type DeploymentBandSelectionProps = {
  isDisabled: boolean;
  createCustomResourcesForSiteIsSuccess: boolean;
  bandOptions: any;
  onBandSelection: any;
  bandwidthOptions: any;
  validArfcnsData: any;
};

const DeploymentBandSelection: React.FC<DeploymentBandSelectionProps> = ({
  isDisabled,
  createCustomResourcesForSiteIsSuccess,
  bandOptions,
  onBandSelection,
  bandwidthOptions,
  validArfcnsData,
}) => {
  return (
    <Flex justifyContent="center" mb="4">
      <SelectFormField
        isDisabled={isDisabled || createCustomResourcesForSiteIsSuccess}
        name="bands"
        label="Band"
        options={bandOptions}
        onChangeHandler={(selectedBand: string) => {
          onBandSelection?.(selectedBand);
        }}
        tooltip="Band"
      />
      <SelectFormField
        isDisabled={isDisabled || createCustomResourcesForSiteIsSuccess}
        name="bandwidths"
        label="Bandwidth"
        options={bandwidthOptions.map((bandwidth: string) => ({
          value: bandwidth,
          label: bandwidth,
        }))}
        tooltip="Bandwidth"
        mx="8"
      />
      <InputFormField
        isDisabled={isDisabled || createCustomResourcesForSiteIsSuccess}
        name="arfcns"
        label="ARFCN"
        options={validArfcnsData}
        tooltip={`ARFCN values ${validArfcnsData}`}
      />
    </Flex>
  );
};

export default DeploymentBandSelection;
