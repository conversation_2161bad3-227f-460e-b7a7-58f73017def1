import { ChevronRightIcon } from '@chakra-ui/icons';
import {
  <PERSON>,
  Button,
  Divider,
  Flex,
  Stack,
  Switch,
  Table,
  Text,
  Thead,
  Tooltip,
  Tr,
  Td,
  Th,
  Tbody,
} from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useNavigate } from 'react-router';
import { z } from 'zod';
import InputFormField from '../../../../components/form/oranDuCuManager/input/InputFormField';
import SelectFormField, {
  UseCaseOption,
  Option,
} from '../../../../components/form/oranDuCuManager/select/SelectFormField';
import useRfUseCases from '../../hooks/services/useRfUseCases';

type DeploymentParams = {
  rf_use_case: string;
  ssb_arfcns: string;
  dh_fh_vlans: string;
};

type DeploymentFormSchema = {
  deployment_params: DeploymentParams;
};

type DeploymentUseCasesFormProps = {
  data: {
    generatedCellRef?: string;
  };
};

const DeploymentUseCasesForm = ({ data }: DeploymentUseCasesFormProps) => {
  const navigate = useNavigate();
  const { getRfUseCases } = useRfUseCases();
  const [selectedUseCaseObj, setSelectedUseCaseObj] = useState<UseCaseOption | null>(null);
  const [isMoranDeployment, setIsMoranDeployment] = useState(false);

  const schema = z.object({
    deployment_params: z.object({
      rf_use_case: z
        .string()
        .min(1, { message: 'Use case is required' })
        .refine((val) => val !== 'Select...', {
          message: 'Please select a valid use case',
        }),

      ssb_arfcns: z
        .string()
        .nonempty({ message: 'SSB ARFCNs are required and must be a number' })
        .refine(
          (val) => {
            if (!val || val.trim() === '') return true;
            return /^\d+(\s*,\s*\d+)*$/.test(val.trim());
          },
          {
            message:
              'SSB ARFCNs must contain only numbers (e.g., 12345) or comma-separated numbers (e.g., 12345, 67890)',
          }
        )
        .refine(
          (val) => {
            if (!isMoranDeployment) return true;
            const arfcnCount = val.split(',').filter((v) => v.trim()).length;
            return arfcnCount === 2;
          },
          {
            message: 'MORAN deployment requires exactly 2 SSB ARFCN values (e.g., 123456, 789012)',
          }
        ),

      dh_fh_vlans: z
        .string()
        .nonempty({ message: 'DU FH VLANs are required and must be a number' })
        .refine(
          (val) => {
            if (!val || val.trim() === '') return true;
            return /^\d+(\s*,\s*\d+)*$/.test(val.trim());
          },
          {
            message:
              'DU FH VLANs must contain only numbers (e.g., 12345) or comma-separated numbers (e.g., 12345, 67890)',
          }
        ),
    }),
  });

  const useCasesForm = useForm<DeploymentFormSchema>({
    defaultValues: {
      deployment_params: {
        rf_use_case: '',
        ssb_arfcns: '',
        dh_fh_vlans: '',
      },
    },
    resolver: zodResolver(schema),
    mode: 'onBlur',
    reValidateMode: 'onChange',
    criteriaMode: 'all',
    shouldFocusError: true,
  });

  const {
    control,
    watch,
    reset,
    setValue,
    formState: { isValid, errors, touchedFields },
    trigger,
  } = useCasesForm;

  const { data: rfUseCasesData, isLoading: isRfUseCasesLoading, error: rfUseCasesError } = getRfUseCases();

  const rfUseCases: Option[] = rfUseCasesData
    ?.sort((a: UseCaseOption, b: UseCaseOption) => a.use_case - b.use_case)
    .map((item: UseCaseOption) => ({
      value: item.use_case,
      label: `Use Case ${item.use_case} | ${item.band0} (${item.bw0}MHz, ${item.arfcn0}, ${item.freq0}MHz) | ${item.band1} (${item.bw1}MHz, ${item.arfcn1}, ${item.freq1}MHz) | ${item.band2} (${item.bw2}MHz, ${item.arfcn2}, ${item.freq2}MHz)`,
      raw: item,
    }));

  const generatedCellRef = data?.generatedCellRef;

  const ssbArfcnValue = watch('deployment_params.ssb_arfcns');

  useEffect(() => {
    if (ssbArfcnValue) {
      trigger('deployment_params.ssb_arfcns');
    }
  }, [isMoranDeployment, trigger, ssbArfcnValue]);

  const onUseCaseSelection = (selectedUseCase: string) => {
    const matched = rfUseCasesData.find((item: { use_case: number }) => String(item.use_case) === selectedUseCase);
    setSelectedUseCaseObj(matched || null);
  };

  const onSubmit = (formValues: DeploymentFormSchema) => {
    const ssb_arfcns_array = formValues.deployment_params.ssb_arfcns
      .split(',')
      .map((v: string) => parseInt(v.trim(), 10));

    const dh_fh_vlans_array = formValues.deployment_params.dh_fh_vlans
      .split(',')
      .map((v: string) => parseInt(v.trim(), 10));

    navigate('/oran-du-cu-manager/simple-view/deployment', {
      state: {
        selectedUseCaseObj,
        generatedCellRef,
        deployment_type: 'new',
        ssbArfcn: ssb_arfcns_array,
        duFhVlans: dh_fh_vlans_array,
        isMoranDeployment,
        formValues: {
          ...formValues,
          deployment_params: {
            ...formValues.deployment_params,
            ssb_arfcns: ssb_arfcns_array,
            dh_fh_vlans: dh_fh_vlans_array,
            max_cells: isMoranDeployment ? 2 : 1,
          },
        },
      },
    });
  };

  return (
    <FormProvider {...useCasesForm}>
      <Box px={{ base: 4, md: 12 }} width="100%">
        <Stack spacing="10" direction="column" width="100%">
          {/* use cases */}
          <Box mb="4">
            <SelectFormField
              isDisabled={isRfUseCasesLoading}
              name="deployment_params.rf_use_case"
              label="Use cases"
              options={rfUseCases}
              onChangeHandler={onUseCaseSelection}
              tooltip="Use cases"
            />

            {selectedUseCaseObj && (
              <Table variant="simple" mt={2}>
                <Thead>
                  <Tr>
                    <Th>Band</Th>
                    <Th>BW (MHz)</Th>
                    <Th>ARFCN</Th>
                    <Th>Freq (MHz)</Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {[0, 1, 2, 3].map((i) => {
                    const band = selectedUseCaseObj[`band${i}` as keyof UseCaseOption];
                    const bw = selectedUseCaseObj[`bw${i}` as keyof UseCaseOption];
                    const arfcn = selectedUseCaseObj[`arfcn${i}` as keyof UseCaseOption];
                    const freq = selectedUseCaseObj[`freq${i}` as keyof UseCaseOption];

                    if (!band) return null;

                    return (
                      <Tr key={i}>
                        <Td>{band}</Td>
                        <Td>{bw ?? '-'}</Td>
                        <Td>{arfcn ?? '-'}</Td>
                        <Td>{freq ?? '-'}</Td>
                      </Tr>
                    );
                  })}
                </Tbody>
              </Table>
            )}
          </Box>

          {/* MORAN Switch */}
          <Flex align="center" mb="4">
            <Switch
              isChecked={isMoranDeployment}
              onChange={() => setIsMoranDeployment(!isMoranDeployment)}
              mr={2}
              id="moran-switch"
            />
            <Tooltip label="Enable this for 2-cell MORAN deployment with two SSB ARFCNs">
              <Text as="label" htmlFor="moran-switch" cursor="pointer" mb="0">
                MORAN deployment
              </Text>
            </Tooltip>
          </Flex>

          {/* ARFCN */}
          <InputFormField
            name="deployment_params.ssb_arfcns"
            label="SSB ARFCN"
            tooltip={
              isMoranDeployment
                ? 'MORAN deployment requires exactly 2 comma-separated values (e.g., 12345, 67890)'
                : 'Enter either a single number (e.g., 12345) or comma-separated numbers (e.g., 12345, 67890)'
            }
            onChangeHandler={(value) => {
              const cleanedInput = value.replace(/[^0-9,\s]/g, '');
              useCasesForm.setValue('deployment_params.ssb_arfcns', cleanedInput, {
                shouldValidate: true,
                shouldTouch: true,
              });
              trigger('deployment_params.ssb_arfcns');
            }}
            placeholder={
              isMoranDeployment
                ? 'Enter exactly 2 comma-separated numbers'
                : 'Enter a number or comma-separated numbers'
            }
          />

          {/* Vlan */}
          <InputFormField
            name="deployment_params.dh_fh_vlans"
            label="DU FH Vlan"
            tooltip="Enter either a single number (e.g., 12345) or comma-separated numbers (e.g., 12345, 67890)."
            onChangeHandler={(value) => {
              const cleanedInput = value.replace(/[^0-9,\s]/g, '');
              useCasesForm.setValue('deployment_params.dh_fh_vlans', cleanedInput, {
                shouldValidate: true,
                shouldTouch: true,
              });
              trigger('deployment_params.dh_fh_vlans');
            }}
            placeholder="Enter a number or comma-separated numbers"
          />
        </Stack>

        <Divider my={8} />

        {/* Buttons */}
        <Flex justifyContent="flex-end" width="100%">
          <Button
            variant="primary"
            isDisabled={!isValid || !generatedCellRef}
            onClick={useCasesForm.handleSubmit(onSubmit)}
          >
            Deployment
            <ChevronRightIcon boxSize={6} />
          </Button>
        </Flex>
      </Box>
    </FormProvider>
  );
};

export default DeploymentUseCasesForm;
