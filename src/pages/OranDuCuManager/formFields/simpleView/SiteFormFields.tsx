import { Box, Heading, Input } from '@chakra-ui/react';

type SiteFormFieldsProps = {
  value: any;
  handleChange: (field: string, selectedValue: string | number | null) => void;
  isDisabled: boolean | undefined;
};

const SiteFormFields: React.FC<SiteFormFieldsProps> = ({ value, handleChange, isDisabled }) => {
  return (
    <Box mx="15%" mt="10">
      <Heading textAlign="center" as="h2" fontSize="20px" mb="4" mt="12">
        Enter a site label
      </Heading>
      <Input
        type="text"
        id={'payload.site'}
        value={value || ''}
        onChange={(e) => handleChange('deployment_name', e.target.value)}
        mr="4"
        disabled={isDisabled}
      />
    </Box>
  );
};

export default SiteFormFields;
