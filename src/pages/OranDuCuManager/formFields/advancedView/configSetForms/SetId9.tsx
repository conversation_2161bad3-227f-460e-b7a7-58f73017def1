import { InfoOutlineIcon } from '@chakra-ui/icons';
import { Box, Button, Flex, FormControl, FormErrorMessage, FormLabel, Input, Stack, Tooltip } from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import _ from 'lodash';
import moment from 'moment';
import { FieldErrors, useForm } from 'react-hook-form';
import { z } from 'zod';
import { FormObject, UnknownObject } from '../../../../../types/duCuManager.type';
import { ipv4Regex, ipv6Regex, validateIP } from '../../../../../utils/helpers';
import useConfigSets from '../../../hooks/services/useConfigSets';

type FormData = {
  'Physical Cell ID'?: number;
  's-NSSAI'?: number;
  'Administrative State'?: string;
  'RAN Area Code'?: number;
  'Local Cell ID'?: string;
  'Tracking Area Code (TAC)'?: string;
  'gNodeB ID'?: number;
  'gNodeB Index'?: number;
  configSetName?: string;
  PLMN: {
    MCC: string;
    MNC: string;
    nCI: string;
    id: string;
  };
};

type FormErrors = FieldErrors<FormData>;

const SetId9: React.FC<{ data: any }> = ({ data }) => {
  const validData = Array.isArray(data) ? data : [];

  const plmnSchema = z.object({
    MCC: z
      .string()
      .optional()
      .refine(
        (val) => {
          if (!val) return true;
          return val.length === 3 && !isNaN(Number(val));
        },
        {
          message: 'MC must be a 3-digit number',
        }
      ),
    MNC: z
      .string()
      .optional()
      .refine(
        (val) => {
          if (!val) return true;
          return val.length === 2 && !isNaN(Number(val));
        },
        {
          message: 'MC must be a 2-digit number',
        }
      ),
    nCI: z
      .string()
      .optional()
      .refine(
        (val: string | undefined) => {
          if (!val) return true;
          return /^[a-fA-F0-9]{9}$/.test(val.trim());
        },
        {
          message: 'Must be an 9-character Hex(a-f,0-9) string',
        }
      ),
    id: z
      .string()
      .optional()
      .refine((val) => _.isUndefined(val) || /^\d+$/.test(val), {
        message: 'Id must be a number with a minimum of 1 digit',
      }),
  });

  const schemaFields = validData.reduce((acc, field) => {
    if (
      //Fields we are hiding
      !field.name.includes('gNodeB ID Length') &&
      !field.name.includes('s-NSSAI') &&
      !field.name.includes('XnU localIpAddress') &&
      !field.name.includes('XnU remoteAddress') &&
      !field.name.includes('XnU gatewayAddress') &&
      !field.name.includes('userLabel') &&
      !field.name.includes('E1 userLabel') &&
      !field.name.includes('F1-U userLabel') &&
      !field.name.includes('NgU userLabel')
    ) {
      if (
        field.name.includes('E1 remoteAddress') ||
        field.name.includes('F1-U remoteAddress') ||
        field.name.includes('NgU remoteAddress') ||
        field.name.includes('E1 gatewayAddress') ||
        field.name.includes('F1-U gatewayAddress') ||
        field.name.includes('NgU gatewayAddress')
      ) {
        acc[field.name] = z
          .string()
          .optional()
          .refine((val) => !val || validateIP(val), {
            message: 'Invalid IP address format',
          });
      }
      if (
        field.name.includes('E1 localIpAddress') ||
        field.name.includes('F1-U localIpAddress') ||
        field.name.includes('NgU localIpAddress')
      ) {
        acc[field.name] = z
          .string()
          .optional()
          .refine((val) => !val || validateIP(val), {
            message: 'Invalid IP address format',
          });
      }
      if (field.name.includes('gNodeB ID') || field.name.includes('gNodeB Index')) {
        acc[field.name] = z
          .number()
          .optional()
          .refine((val) => _.isUndefined(val) || !isNaN(val), {
            message: 'Must be a valid number',
          });
      }
    }
    return acc;
  }, {});

  schemaFields['configSetName'] = z.string().min(1, 'Config set name is required');
  schemaFields['PLMN'] = plmnSchema;
  const dynamicSchema = z.object(schemaFields);

  const {
    handleSubmit,
    watch,
    register,
    formState: { errors, isDirty, isValid },
  } = useForm<FormData>({
    resolver: zodResolver(dynamicSchema),
    mode: 'all',
  });

  // API
  const { createConfigSet } = useConfigSets();
  const mutation = createConfigSet();

  const allValues = watch();
  const isConfigSetNameAndOneOtherFieldFilled =
    Object.values(allValues).filter((value) => value !== '' && value !== undefined && value !== 'ConfigSetName')
      .length >= 2;

  //Form submit handler
  const onSubmit = async (values: any) => {
    const { ...rest } = values;

    const updatedPLMN = {
      MCC: values.PLMN.MCC,
      MNC: values.PLMN.MNC,
      nCI: values.PLMN.nCI,
      id: values.PLMN.id,
    };

    rest.PLMN = updatedPLMN;

    let newObject;

    const matchedObjects = data.filter((obj: FormObject) => {
      const key = obj.name;

      if (Object.hasOwn(rest, key)) {
        return true;
      } else {
        return false;
      }
    });

    if (matchedObjects.length > 0) {
      newObject = {
        created: moment().format(),
        updated: moment().format(),
        updated_by: 1, //NOTE: should this be dynamic, its the user unique id.
        name: rest.configSetName,
        data: {
          params: matchedObjects.map((obj: FormObject) => {
            return {
              created: moment().format(),
              name: obj.name,
              parameter_set_id: 9, // Consider dynamic assignment if needed
              function_type: obj.function_type,
              template_section: obj.template_section,
              software_version: obj.software_version,
              oran_fn_type: obj.oran_fn_type,
              path: obj.path,
              read_write: obj.read_write,
              type: obj.type,
              default_value: obj.default_value,
              constraints: obj.constraints,
              value: rest[obj.name],
            };
          }),
        },
      };
    } else {
      console.log('No matching objects found.');
    }
    mutation.mutate(newObject);
  };
  return (
    <Box p="10">
      <Flex justify="center" m="8">
        <Stack spacing={3} width="50%">
          <form onSubmit={handleSubmit(onSubmit)}>
            {data
              ?.filter(
                (field: UnknownObject) =>
                  !field.name.includes('gNodeB ID Length') &&
                  !field.name.includes('s-NSSAI') &&
                  !field.name.includes('XnU localIpAddress') &&
                  !field.name.includes('XnU remoteAddress') &&
                  !field.name.includes('XnU gatewayAddress') &&
                  !field.name.includes('userLabel') &&
                  !field.name.includes('E1 userLabel') &&
                  !field.name.includes('F1U userLabel') &&
                  !field.name.includes('NgU userLabel')
              )
              .map((field: UnknownObject, index: number) => (
                <Box key={`${field.name}-${field.id}`} mb="14">
                  {field.name === 'gNodeB ID' ? (
                    <FormControl isInvalid={!!errors[field.name as keyof FormData]} mb="4">
                      <FormLabel>{field.name}</FormLabel>
                      <Flex alignItems="center">
                        <Input
                          type="text"
                          id="gNodeB ID"
                          placeholder={field.constraints.type}
                          {...register('gNodeB ID', {
                            setValueAs: (value) =>
                              _.isEmpty(value) ? undefined : !isNaN(value) ? Number(value) : value,
                          })}
                          mr="4"
                        />
                      </Flex>
                      <FormErrorMessage>{String(errors[field.name as keyof FormData]?.message)}</FormErrorMessage>
                    </FormControl>
                  ) : field.name === 'gNodeB Index' ? (
                    <FormControl isInvalid={!!errors[field.name as keyof FormData]} mb="4">
                      <FormLabel>{field.name}</FormLabel>
                      <Flex alignItems="center">
                        <Input
                          type="text"
                          id="gNodeB Index"
                          placeholder={field.constraints.type}
                          {...register('gNodeB Index', {
                            setValueAs: (value) =>
                              _.isEmpty(value) ? undefined : !isNaN(value) ? Number(value) : value,
                          })}
                          mr="4"
                        />
                      </Flex>
                      <FormErrorMessage>{String(errors[field.name as keyof FormData]?.message)}</FormErrorMessage>
                    </FormControl>
                  ) : field.name.includes('PLMN') ? (
                    <Box>
                      <FormControl isInvalid={!!errors[field.name as keyof FormData]} mb="4">
                        <FormLabel>{field.name} </FormLabel>
                        <Box ml="4">
                          {/* MCC */}
                          <Box mb="4">
                            <FormLabel>MCC</FormLabel>
                            <Input
                              id="PLMN.MCC"
                              placeholder={field.constraints.type}
                              {...register('PLMN.MCC', {
                                setValueAs: (value) => (value === '' ? '' : value),
                              })}
                              mr="4"
                            />
                          </Box>
                          {errors?.PLMN?.MCC && <FormErrorMessage>{errors?.PLMN?.MCC?.message}</FormErrorMessage>}
                          {/* MNC */}
                          <Box mb="4">
                            <FormLabel>MNC</FormLabel>
                            <Input
                              id="PLMN.MNC"
                              placeholder={field.constraints.type}
                              {...register('PLMN.MNC', {
                                setValueAs: (value) => (value === '' ? '' : value),
                              })}
                              mr="4"
                            />
                          </Box>
                          {errors?.PLMN?.MNC && <FormErrorMessage>{errors?.PLMN?.MNC?.message}</FormErrorMessage>}
                          {/* NrCGI - nCI */}
                          <Box mb="4">
                            <FormLabel>NrCGI - nCI - NR Cell Id</FormLabel>
                            <Input
                              id="PLMN.nCI"
                              placeholder={field.constraints.type}
                              {...register('PLMN.nCI', {
                                setValueAs: (value) => (value === '' ? '' : value),
                              })}
                              mr="4"
                            />
                          </Box>
                          {errors?.PLMN?.nCI && <FormErrorMessage>{errors?.PLMN?.nCI?.message}</FormErrorMessage>}
                          {/* id */}

                          <Box>
                            <FormLabel>id</FormLabel>
                            <Input
                              id="PLMN.id"
                              placeholder={field.constraints.type}
                              {...register('PLMN.id', {
                                setValueAs: (value) => (value === '' ? '' : value),
                              })}
                              mr="4"
                            />
                          </Box>
                          {errors?.PLMN?.id && <FormErrorMessage>{errors?.PLMN?.id?.message}</FormErrorMessage>}
                        </Box>
                      </FormControl>
                    </Box>
                  ) : (
                    <FormControl isInvalid={!!errors[field.name as keyof FormData]} mb="4">
                      <FormLabel>{field.name}</FormLabel>
                      <Flex alignItems="center">
                        <Input id={field.name} placeholder={field.constraints.type} {...register(field.name)} />
                      </Flex>
                      <FormErrorMessage>{String(errors[field.name as keyof FormData]?.message)}</FormErrorMessage>
                    </FormControl>
                  )}
                </Box>
              ))}
            <FormControl isInvalid={!!errors.configSetName} mb="4">
              <FormLabel htmlFor="configSetName">Config set name</FormLabel>
              <Flex alignItems="center">
                <Input id={'configSetName'} {...register('configSetName')} mr="4" />
                <Tooltip label="value be a string">
                  <InfoOutlineIcon boxSize={6} />
                </Tooltip>
              </Flex>
              <FormErrorMessage>{String(errors.configSetName?.message)}</FormErrorMessage>
            </FormControl>
            <Button
              type="submit"
              width="100%"
              marginY={4}
              colorScheme="blue"
              mt="7"
              isDisabled={!isConfigSetNameAndOneOtherFieldFilled}
            >
              Create config set
            </Button>
          </form>
        </Stack>
      </Flex>
    </Box>
  );
};

export default SetId9;
