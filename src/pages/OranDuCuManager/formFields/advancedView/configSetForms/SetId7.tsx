import { InfoOutlineIcon } from '@chakra-ui/icons';
import { Box, Button, Flex, FormControl, FormErrorMessage, FormLabel, Input, Stack, Tooltip } from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import moment from 'moment';
import { FieldErrors, useForm } from 'react-hook-form';
import { z } from 'zod';
import { FormObject, UnknownObject } from '../../../../../types/duCuManager.type';
import useConfigSets from '../../../hooks/services/useConfigSets';

type FormData = {
  'Local Cell ID'?: number;
  'Cell Identity (nCI)'?: number;
  configSetName?: string;
  PLMN: {
    MCC: string;
    MNC: string;
  };
};

type FormErrors = FieldErrors<FormData>;

const SetId7: React.FC<{ data: any }> = ({ data }) => {
  const validData = Array.isArray(data) ? data : [];

  const plmnSchema = z.object({
    MCC: z
      .string()
      .optional()
      .refine(
        (val) => {
          if (!val) return true;
          return val.length === 3 && !isNaN(Number(val));
        },
        {
          message: 'MC must be a 3-digit number',
        }
      ),
    MNC: z
      .string()
      .optional()
      .refine(
        (val) => {
          if (!val) return true;
          return val.length === 2 && !isNaN(Number(val));
        },
        {
          message: 'MC must be a 2-digit number',
        }
      ),
  });

  const schemaFields = validData.reduce((acc, field) => {
    if (!field.name.includes('userLabel')) {
      if (field.name.includes('Local Cell ID')) {
        acc[field.name] = z.string().optional();
      }
      if (field.name.includes('Cell Identity (nCI)')) {
        acc[field.name] = z.string().optional();
      }
    }

    return acc;
  }, {});
  schemaFields['configSetName'] = z.string().min(1, 'Config set name is required');
  schemaFields['PLMN'] = plmnSchema;
  const dynamicSchema = z.object(schemaFields);

  const {
    handleSubmit,
    watch,
    register,
    formState: { errors, isDirty, isValid },
  } = useForm<FormData>({
    resolver: zodResolver(dynamicSchema),
    mode: 'all',
  });

  // API
  const { createConfigSet } = useConfigSets();
  const mutation = createConfigSet();

  const allValues = watch();
  const isConfigSetNameAndOneOtherFieldFilled =
    Object.values(allValues).filter((value) => value !== '' && value !== undefined && value !== 'ConfigSetName')
      .length >= 2;

  //Form submit handler
  const onSubmit = async (values: any) => {
    const { ...rest } = values;

    const updatedPLMN = {
      MCC: values.PLMN.MCC,
      MNC: values.PLMN.MNC,
    };

    rest.PLMN = updatedPLMN;

    let newObject;
    const matchedObjects = data.filter((obj: FormObject) => {
      const key = obj.name;

      if (Object.hasOwn(rest, key)) {
        return true;
      } else {
        return false;
      }
    });

    if (matchedObjects.length > 0) {
      newObject = {
        created: moment().format(),
        updated: moment().format(),
        updated_by: 1, //NOTE: should this be dynamic, its the user unique id.
        name: rest.configSetName,
        data: {
          params: matchedObjects.map((obj: FormObject) => {
            return {
              created: moment().format(),
              name: obj.name,
              parameter_set_id: 7, // Consider dynamic assignment if needed
              function_type: obj.function_type,
              template_section: obj.template_section,
              software_version: obj.software_version,
              oran_fn_type: obj.oran_fn_type,
              path: obj.path,
              read_write: obj.read_write,
              type: obj.type,
              default_value: obj.default_value,
              constraints: obj.constraints,
              value: rest[obj.name],
            };
          }),
        },
      };
    } else {
      console.log('No matching objects found.');
    }
    mutation.mutate(newObject);
  };

  return (
    <Box p="10">
      <Flex justify="center" m="8">
        <Stack spacing={3} width="50%">
          <form onSubmit={handleSubmit(onSubmit)}>
            {data
              ?.filter(
                (field: UnknownObject) =>
                  !field.name.includes('userLabel') &&
                  !field.name.includes('s-NSSAI') &&
                  !field.name.includes('gNodeB ID')
              )
              .map((field: UnknownObject, index: number) => (
                <Box key={`${field.name}-${field.id}`} mb="14">
                  <FormControl isInvalid={!!errors[field.name as keyof FormData]} mb="4">
                    <FormLabel>{field.name}</FormLabel>
                    <Flex alignItems="center">
                      {field.name.includes('Local Cell ID') ? (
                        <Box>
                          <Input
                            id={field.name}
                            placeholder={field.constraints.type}
                            {...register(field.name)}
                            mr="4"
                          />
                          <FormErrorMessage>{String(errors[field.name as keyof FormData]?.message)}</FormErrorMessage>
                        </Box>
                      ) : null}
                      {field.name.includes('Cell Identity (nCI)') ? (
                        <Box>
                          <Input
                            id={field.name}
                            placeholder={field.constraints.type}
                            {...register(field.name)}
                            mr="4"
                          />
                          <FormErrorMessage>{String(errors[field.name as keyof FormData]?.message)}</FormErrorMessage>
                        </Box>
                      ) : null}
                      {field.name.includes('PLMN') ? (
                        <Box ml="4">
                          <Box>
                            <FormLabel>MCC</FormLabel>
                            <Flex alignItems="center">
                              <Input
                                mb="4"
                                id="PLMN.MCC"
                                placeholder={field.constraints.type}
                                {...register('PLMN.MCC', {
                                  setValueAs: (value) => (value === '' ? '' : value),
                                })}
                                mr="4"
                              />
                              <Tooltip label="3-digit number">
                                <InfoOutlineIcon boxSize={6} />
                              </Tooltip>
                            </Flex>
                          </Box>
                          {errors?.PLMN?.MCC && <FormErrorMessage>{errors?.PLMN?.MCC?.message}</FormErrorMessage>}
                          <Box>
                            <FormLabel>MNC</FormLabel>
                            <Flex alignItems="center">
                              <Input
                                id="PLMN.MNC"
                                placeholder={field.constraints.type}
                                {...register('PLMN.MNC', {
                                  setValueAs: (value) => (value === '' ? '' : value),
                                })}
                                mr="4"
                              />
                              <Tooltip label="2-digit number">
                                <InfoOutlineIcon boxSize={6} />
                              </Tooltip>
                            </Flex>
                          </Box>
                          {errors?.PLMN?.MNC && <FormErrorMessage>{errors?.PLMN?.MNC?.message}</FormErrorMessage>}
                        </Box>
                      ) : null}
                    </Flex>
                  </FormControl>
                </Box>
              ))}
            <FormControl isInvalid={!!errors.configSetName} mb="4">
              <FormLabel htmlFor="configSetName">Config set name</FormLabel>
              <Flex alignItems="center">
                <Input type={'text'} id={'configSetName'} {...register('configSetName')} mr="4" />
                <Tooltip label="value must be a string">
                  <InfoOutlineIcon boxSize={6} />
                </Tooltip>
              </Flex>
              <FormErrorMessage>{String(errors.configSetName?.message)}</FormErrorMessage>
            </FormControl>
            <Button
              type="submit"
              width="100%"
              marginY={4}
              colorScheme="blue"
              mt="7"
              isDisabled={!isConfigSetNameAndOneOtherFieldFilled}
            >
              Create config set
            </Button>
          </form>
        </Stack>
      </Flex>
    </Box>
  );
};

export default SetId7;
