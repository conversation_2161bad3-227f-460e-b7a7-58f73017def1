import { InfoOutlineIcon } from '@chakra-ui/icons';
import {
  Alert,
  AlertIcon,
  Box,
  Button,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Input,
  ListItem,
  Select,
  Stack,
  Text,
  Tooltip,
  UnorderedList,
} from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import moment from 'moment';
import { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { z } from 'zod';
import { FormObject, UnknownObject } from '../../../../../types/duCuManager.type';
import useConfigSets from '../../../hooks/services/useConfigSets';
import useParameterSets from '../../../hooks/services/useParameterSets';

type FormValues = {
  configSetName: string;
  Band: string;
  DL_Bandwidth: string;
  DL_ARFCN: string;
  SSB_Arfcn: string;
  SSB_Duration_ms: string;
  SSB_Periodicity_ms: string;
};

export type ValidArfcnsData = {
  dlArfcn_min: number;
  dlArfcn_max: number;
  ssbArfcn_min: number;
  ssbArfcn_max: number;
};

const SetId2: React.FC<{ data: any }> = ({ data }) => {
  const [bandMatch, setBandMatch] = useState<any>(null);
  const [params, setParams] = useState<{ band: string | null; bandwidth: string | null }>({
    band: '',
    bandwidth: null,
  });
  const [bandwidthOptions, setBandwidthOptions] = useState<string[]>([]);
  const [arfcnLimits, setArfcnLimits] = useState<ValidArfcnsData | null>(null);

  const processData = (data: any) => {
    const processedData: any[] = [];
    if (data) {
      for (const item of data) {
        const processedName = item.name.replace(/\s+/g, '_').replace(/[()]/g, '');
        const processedItem = { ...item, name: processedName };
        processedData.push(processedItem);
      }
    }
    return processedData;
  };

  const reverseProcessData = (processedData: Record<string, any>): Record<string, any> => {
    const originalData: Record<string, any> = {};
    Object.entries(processedData).forEach(([key, value]) => {
      const originalKey = key.replace(/_/g, ' ').replace(/ms/g, '(ms)');
      originalData[originalKey] = value;
    });
    return originalData;
  };

  const formProcessedData = processData(data);

  const isFormProcessedDataArray = Array.isArray(formProcessedData) ? formProcessedData : [];

  const formFieldOrder = [
    'Band',
    'DL_Bandwidth',
    'DL_ARFCN',
    'SSB_Arfcn',
    'SSB_Duration_ms',
    'SSB_Periodicity_ms',
    'configSetName',
  ];

  const generateBandwidthOptions = (band: string, data: any) => {
    const options: string[] = [];
    if (data[band]) {
      const { min, max, step } = data[band];
      for (let value = min; value <= max; value += step) {
        options.push(`${value}MHZ`);
      }
    }
    return options;
  };

  const sortedFormFields = formProcessedData
    .filter(
      (field: UnknownObject) =>
        !field.name.includes('userLabel') &&
        !field.name.includes('Deployment_Mode') &&
        !field.name.includes('Subcarrier_Spacing_Common') &&
        !field.name.includes('SSB_Subcarrier_Spacing') &&
        !field.name.includes('Duplex_Mode') &&
        !field.name.includes('Search_space_zero') &&
        !field.name.includes('SSB_Offset_ms')
    )
    .sort((a, b) => formFieldOrder.indexOf(a.name) - formFieldOrder.indexOf(b.name));

  const schemaFields = isFormProcessedDataArray.reduce((acc, field) => {
    if (
      !field.name.includes('userLabel') &&
      !field.name.includes('Deployment_Mode') &&
      !field.name.includes('Subcarrier_Spacing_Common') &&
      !field.name.includes('SSB_Subcarrier_Spacing') &&
      !field.name.includes('Duplex_Mode') &&
      !field.name.includes('SSB_Offset_ms')
    ) {
      if (field.name.includes('DL_ARFCN') && arfcnLimits) {
        acc[field.name] = z.preprocess(
          (value) => {
            if (typeof value === 'string') {
              const parsed = Number(value);
              return isNaN(parsed) ? undefined : parsed;
            }
            return typeof value === 'number' ? value : undefined;
          },
          z
            .number()
            .refine((value) => value !== undefined, { message: 'Value must be a number' })
            .refine((value) => value <= arfcnLimits.dlArfcn_max, {
              message: `Value must be at most ${arfcnLimits.dlArfcn_max}`,
            })
            .refine((value) => value >= arfcnLimits.dlArfcn_min, {
              message: `Value must be at least ${arfcnLimits.dlArfcn_min}`,
            })
        );
      }
      if (field.name.includes('SSB_Arfcn') && arfcnLimits) {
        acc[field.name] = z.preprocess(
          (value) => {
            if (typeof value === 'string') {
              const parsed = Number(value);
              return isNaN(parsed) ? undefined : parsed;
            }
            return typeof value === 'number' ? value : undefined;
          },
          z
            .number()
            .refine((value) => value !== undefined, { message: 'Value must be a number' })
            .refine((value) => value <= arfcnLimits.ssbArfcn_max, {
              message: `Value must be at most ${arfcnLimits.ssbArfcn_max}`,
            })
            .refine((value) => value >= arfcnLimits.ssbArfcn_min, {
              message: `Value must be at least ${arfcnLimits.ssbArfcn_min}`,
            })
        );
      }
      if (field.name.includes('Band')) {
        acc[field.name] = z.string();
      }
      if (field.name.includes('DL_Bandwidth')) {
        acc[field.name] = z.string();
      }
      if (field.name.includes('SSB_Duration_ms')) {
        acc[field.name] = z.string();
      }
      if (field.name.includes('SSB_Periodicity_ms')) {
        acc[field.name] = z.string();
      }
    }
    return acc;
  }, {});

  schemaFields['configSetName'] = z.string().min(1, 'Config set name is required');
  const dynamicSchema = z.object(schemaFields);

  const {
    handleSubmit,
    watch,
    control,
    reset,
    register,
    formState: { errors, isDirty, isValid },
  } = useForm({
    resolver: zodResolver(dynamicSchema),
    mode: 'all',
    defaultValues: {
      configSetName: '',
      Band: '',
      DL_Bandwidth: '',
      DL_ARFCN: '',
      SSB_Arfcn: '',
      SSB_Duration_ms: '',
      SSB_Periodicity_ms: '',
    },
  });

  const allValues = watch();

  const isConfigSetNameAndOneOtherFieldFilled =
    Object.values(allValues).filter((value) => value !== '' && value !== undefined && value !== 'ConfigSetName')
      .length >= 2;

  // API calls
  const parameterSets = useParameterSets();
  const { createConfigSet } = useConfigSets();
  const mutation = createConfigSet();

  const { data: bandData, error: bandError, isLoading: bandlsLoading } = parameterSets.getBands();

  const {
    data: validArfcnsData,
    error: validArfcnsError,
    isLoading: validArfcnslsLoading,
  } = parameterSets.getValidArfcns(params);

  const bandNames = bandData ? Object.keys(bandData) : [];

  const onBandSelection = (selectedBand: string) => {
    const bandDataAsObject = bandData as { [key: string]: any };

    if (bandData && selectedBand) {
      const bandMatchObj = {
        band: selectedBand,
        validation: bandDataAsObject[selectedBand],
      };

      setBandMatch(bandMatchObj);
    }
  };

  // Form submit handler
  const onSubmit = async (values: any) => {
    let newObject;

    const reverseProcessOfName = reverseProcessData(values);

    const matchedObjects = data.filter((obj: FormObject) => {
      return Object.hasOwn(reverseProcessOfName, obj.name) && reverseProcessOfName[obj.name] !== '';
    });

    if (matchedObjects.length > 0) {
      newObject = {
        created: moment().format(),
        updated: moment().format(),
        updated_by: 1, // NOTE: should this be dynamic, its the user unique id.
        name: reverseProcessOfName.configSetName,
        data: {
          params: matchedObjects.map((obj: FormObject) => ({
            created: moment().format(),
            name: obj.name,
            parameter_set_id: 2, // NOTE: should this be dynamic?
            function_type: obj.function_type,
            template_section: obj.template_section,
            software_version: obj.software_version,
            oran_fn_type: obj.oran_fn_type,
            path: obj.path,
            read_write: obj.read_write,
            type: obj.type,
            default_value: obj.default_value,
            value: obj.name === 'Band' ? [reverseProcessOfName[obj.name]] : reverseProcessOfName[obj.name],
          })),
        },
      };
    } else {
      console.log('No matching objects found.');
    }
    mutation.mutate(newObject);
  };

  useEffect(() => {
    if (bandMatch) {
      const SSB_Duration_ms = formProcessedData.find((item) => item.name === 'SSB_Duration_ms');
      const SSB_Periodicity_ms = formProcessedData.find((item) => item.name === 'SSB_Periodicity_ms');

      reset({
        configSetName: '',
        Band: bandMatch.band || '',
        DL_Bandwidth: `${bandMatch.validation.min}MHZ` || '',
        DL_ARFCN: String(validArfcnsData?.dlArfcn_min) || '',
        SSB_Arfcn: String(validArfcnsData?.ssbArfcn_min) || '',
        SSB_Duration_ms: SSB_Duration_ms?.default_value || '',
        SSB_Periodicity_ms: SSB_Periodicity_ms?.default_value || '',
      });
    }
  }, [bandMatch, reset, validArfcnsData]);

  useEffect(() => {
    if (bandMatch && bandData) {
      const options = generateBandwidthOptions(bandMatch.band, bandData);
      setBandwidthOptions(options);
    }
  }, [bandMatch, bandData]);

  useEffect(() => {
    if (bandMatch && bandMatch.band && bandMatch.validation) {
      setParams({ band: bandMatch.band, bandwidth: `${bandMatch.validation.min}MHZ` });
    }
  }, [bandMatch]);

  useEffect(() => {
    if (validArfcnsData) {
      setArfcnLimits(validArfcnsData);
      reset((prev) => ({
        ...prev,
        DL_ARFCN: String(validArfcnsData.dlArfcn_min),
        SSB_Arfcn: String(validArfcnsData.ssbArfcn_min),
      }));
    }
  }, [validArfcnsData, reset]);

  return (
    <Box p="10">
      <Flex justify="center" m="8">
        <Stack spacing={3} width="50%">
          <form onSubmit={handleSubmit(onSubmit)}>
            <Alert status="info" mb="14" display="flex" flexDirection="column" alignItems="baseline" pl="0">
              <Text display="flex">
                <AlertIcon alignSelf="center" />
                Choose a band from the list. This will automatically populate the form with some default values. These
                are just suggestions, and you can:
              </Text>
              <UnorderedList mt="4" pl="12">
                <ListItem>Submit the form as is if the defaults work for you.</ListItem>
                <ListItem>Edit the values to match your specific needs.</ListItem>
              </UnorderedList>
            </Alert>
            {sortedFormFields
              ?.filter(
                (field: UnknownObject) =>
                  !field.name.includes('userLabel') &&
                  !field.name.includes('Deployment_Mode') &&
                  !field.name.includes('Subcarrier_Spacing_Common') &&
                  !field.name.includes('SSB_Subcarrier_Spacing') &&
                  !field.name.includes('Duplex_Mode') &&
                  !field.name.includes('Search_space_zero') &&
                  !field.name.includes('SSB_Offset_ms')
              )
              .map((field: UnknownObject, index: number) => (
                <Box key={`${field.name}-${field.id}`} mb="14">
                  <FormControl isInvalid={!!errors[field.name as keyof FormValues]} mb="4">
                    <FormLabel>{field.name}</FormLabel>
                    <Controller
                      name={field.name}
                      control={control}
                      defaultValue=""
                      render={({ field: { onChange, onBlur, value, name, ref } }) => {
                        if (field.name === 'Band') {
                          return (
                            <Flex alignItems="center">
                              <Select
                                ref={ref}
                                onBlur={onBlur}
                                value={value}
                                name={name}
                                id={field.name}
                                placeholder="Select"
                                mr="10"
                                onChange={(e) => {
                                  const value = e.target.value;
                                  onBandSelection(value);
                                  onChange(value);
                                }}
                              >
                                {bandNames?.map((option: any) => (
                                  <option key={option} value={option}>
                                    {option}
                                  </option>
                                ))}
                              </Select>
                            </Flex>
                          );
                        }
                        if (field.name === 'DL_Bandwidth') {
                          return (
                            <Flex alignItems="center">
                              <Select
                                id={field.name}
                                placeholder="Select"
                                mr="10"
                                name={name}
                                ref={ref}
                                onBlur={onBlur}
                                onChange={(e) => {
                                  const value = e.target.value;
                                  onChange(value);
                                }}
                                value={value}
                              >
                                {bandwidthOptions?.map((option: any) => (
                                  <option key={option} value={option}>
                                    {option}
                                  </option>
                                ))}
                              </Select>
                            </Flex>
                          );
                        }
                        if (field.name === 'DL_ARFCN') {
                          return (
                            <Flex alignItems="center">
                              <Input
                                id={field.name}
                                type="number"
                                name={name}
                                ref={ref}
                                onBlur={onBlur}
                                onChange={(e) => {
                                  const value = e.target.value;
                                  onChange(value);
                                }}
                                value={value}
                                mr="4"
                              />
                              {arfcnLimits && (
                                <Tooltip
                                  label={`value must be min ${arfcnLimits.dlArfcn_min} to max ${arfcnLimits.dlArfcn_max}`}
                                >
                                  <InfoOutlineIcon boxSize={6} />
                                </Tooltip>
                              )}
                            </Flex>
                          );
                        }
                        if (field.name === 'SSB_Arfcn') {
                          return (
                            <Flex alignItems="center">
                              <Input
                                id={field.name}
                                type="number"
                                name={name}
                                ref={ref}
                                onBlur={onBlur}
                                onChange={(e) => {
                                  const value = e.target.value;
                                  onChange(value);
                                }}
                                value={value}
                                mr="4"
                              />
                              {arfcnLimits && (
                                <Tooltip
                                  label={`value must be min ${arfcnLimits.ssbArfcn_min} to max ${arfcnLimits.ssbArfcn_max}`}
                                >
                                  <InfoOutlineIcon boxSize={6} />
                                </Tooltip>
                              )}
                            </Flex>
                          );
                        }
                        if (field.name === 'SSB_Duration_ms') {
                          return (
                            <Flex alignItems="center">
                              <Select
                                id={field.name}
                                placeholder="Select"
                                mr="10"
                                name={name}
                                ref={ref}
                                onBlur={onBlur}
                                onChange={(e) => {
                                  const value = e.target.value;
                                  onChange(value);
                                }}
                                value={value}
                              >
                                {field.constraints?.enum?.map((option: any) => (
                                  <option key={option} value={option}>
                                    {option}
                                  </option>
                                ))}
                              </Select>
                            </Flex>
                          );
                        }
                        if (field.name === 'SSB_Periodicity_ms') {
                          return (
                            <Flex alignItems="center">
                              <Select
                                id={field.name}
                                placeholder="Select"
                                mr="10"
                                name={name}
                                ref={ref}
                                onBlur={onBlur}
                                onChange={(e) => {
                                  const value = e.target.value;
                                  onChange(value);
                                }}
                                value={value}
                              >
                                {field.constraints?.enum?.map((option: any) => (
                                  <option key={option} value={option}>
                                    {option}
                                  </option>
                                ))}
                              </Select>
                            </Flex>
                          );
                        }
                        return <div>Unsupported field type</div>;
                      }}
                    />
                    <FormErrorMessage>{String(errors[field.name as keyof FormValues]?.message)}</FormErrorMessage>
                  </FormControl>
                </Box>
              ))}
            <FormControl isInvalid={!!errors.configSetName} mb="4">
              <FormLabel htmlFor="configSetName">Config set name</FormLabel>
              <Flex alignItems="center">
                <Input type={'text'} id={'configSetName'} {...register('configSetName')} mr="4" />
                <Tooltip label="value must be a string">
                  <InfoOutlineIcon boxSize={6} />
                </Tooltip>
              </Flex>
              <FormErrorMessage>{String(errors.configSetName?.message)}</FormErrorMessage>
            </FormControl>
            <Button
              type="submit"
              width="100%"
              marginY={4}
              colorScheme="blue"
              mt="7"
              isDisabled={!isConfigSetNameAndOneOtherFieldFilled}
            >
              Create config set
            </Button>
          </form>
        </Stack>
      </Flex>
    </Box>
  );
};

export default SetId2;
