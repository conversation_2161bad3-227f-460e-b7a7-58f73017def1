import { InfoOutlineIcon } from '@chakra-ui/icons';
import { Box, Button, Flex, FormControl, FormErrorMessage, FormLabel, Input, Stack, Tooltip } from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import moment from 'moment';
import { useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { FormObject, UnknownObject } from '../../../../../types/duCuManager.type';
import useConfigSets from '../../../hooks/services/useConfigSets';

type TransformedData = {
  [key: string]: number;
};

const SetId3: React.FC<{ data: any }> = ({ data }) => {
  const validData = Array.isArray(data) ? data : [];

  const schemaFields = validData.reduce((acc, field) => {
    if (!field.name.includes('userLabel')) {
      if (field.name.includes('P-Max (dBm)')) {
        acc[field.name] = z
          .string()
          .transform((value) => {
            const parsed = Number(value);
            return isNaN(parsed) ? undefined : parsed;
          })
          .refine((value) => value !== undefined, { message: 'Value must be a number' })
          .refine((value) => value !== undefined && value >= -30, { message: 'Value must be at least -30' })
          .refine((value) => value !== undefined && value <= 33, { message: 'Value must not exceed 33' });
      }
      if (field.name.includes('Q Min Quality (dB)')) {
        acc[field.name] = z
          .string()
          .transform((value) => {
            const parsed = Number(value);
            return isNaN(parsed) ? undefined : parsed;
          })
          .refine((value) => value !== undefined, { message: 'Value must be a number' })
          .refine((value) => value !== undefined && value >= -43 && value <= -12, {
            message: 'Value must be between -43 and -12',
          });
      }
      if (field.name.includes('Q Rx Min Level (dBm)')) {
        acc[field.name] = z
          .string()
          .transform((value) => {
            const parsed = Number(value);
            return isNaN(parsed) ? undefined : parsed;
          })
          .refine((value) => value !== undefined, { message: 'Value must be a number' })
          .refine((value) => value !== undefined && value >= -70 && value <= -22, {
            message: 'Value must be between -70 and -22',
          });
      }
    }
    return acc;
  }, {});
  schemaFields['configSetName'] = z.string().min(1, 'Config set name is required');
  const dynamicSchema = useMemo(() => z.object(schemaFields), [validData]);

  const {
    handleSubmit,
    watch,
    register,
    formState: { errors, isDirty, isValid },
  } = useForm({
    resolver: zodResolver(dynamicSchema),
    mode: 'all',
  });

  // API
  const { createConfigSet } = useConfigSets();
  const mutation = createConfigSet();

  const allValues = watch();
  const isConfigSetNameAndOneOtherFieldFilled =
    Object.values(allValues).filter((value) => value !== '' && value !== undefined && value !== 'ConfigSetName')
      .length >= 2;

  //Form submit
  const onSubmit = async (values: any) => {
    const transformedData: TransformedData = Object.entries(values).reduce((acc: Record<string, any>, [key, value]) => {
      acc[key] = value === 0 ? NaN : value; // NOTE: Check if the user can enter 0 if it is allowed then make sure the user has entered that. Just make sure the default value from the input isn't 0 when the user doesn't enter anything
      return acc;
    }, {});

    let newObject;

    const matchedObjects = data.filter((obj: FormObject) => {
      const value = transformedData[obj.name];
      if (Object.hasOwn(transformedData, obj.name) && !isNaN(value)) {
        return true;
      } else {
        console.log(`${obj.name} is NaN or undefined.`);
        return false;
      }
    });

    if (matchedObjects.length > 0) {
      newObject = {
        created: moment().format(),
        updated: moment().format(),
        updated_by: 1, //NOTE: should this be dynamic, its the user unique id.
        name: values.configSetName,
        data: {
          params: matchedObjects.map((obj: FormObject) => ({
            created: moment().format(),
            name: obj.name,
            parameter_set_id: 3, //NOTE: should this be dynamic?
            function_type: obj.function_type,
            template_section: obj.template_section,
            software_version: obj.software_version,
            oran_fn_type: obj.oran_fn_type,
            path: obj.path,
            read_write: obj.read_write,
            type: obj.type,
            default_value: obj.default_value,
            value:
              obj.name === 'Q Min Quality (dB)' || obj.name === 'Q Rx Min Level (dBm)'
                ? { rsrpLvl: values[obj.name] }
                : values[obj.name],
          })),
        },
      };
    } else {
      console.log('No matching objects found.');
    }
    mutation.mutate(newObject);
  };

  return (
    <Box p="10">
      <Flex justify="center" m="8">
        <Stack spacing={3} width="50%">
          <form onSubmit={handleSubmit(onSubmit)}>
            {data
              ?.filter((field: UnknownObject) => !field.name.includes('userLabel'))
              .map((field: UnknownObject, index: number) => (
                <Box key={`${field.name}-${field.id}`} mb="14">
                  <FormControl isInvalid={!!errors[field.name]} mb="4">
                    <FormLabel>{field.name}</FormLabel>
                    <Flex alignItems="center">
                      <Input
                        type={'number'}
                        id={field.name}
                        placeholder={field.constraints.type}
                        defaultValue="NaN"
                        {...register(field.name)}
                        mr="4"
                      />
                      {field.name.includes('P-Max (dBm)') ? (
                        <Tooltip label="value must be -30 to +33">
                          <InfoOutlineIcon boxSize={6} />
                        </Tooltip>
                      ) : null}
                      {field.name.includes('Q Min Quality (dB)') ? (
                        <Tooltip label="Must be a -43 and 12">
                          <InfoOutlineIcon boxSize={6} />
                        </Tooltip>
                      ) : null}
                      {field.name.includes('Q Rx Min Level (dBm)') ? (
                        <Tooltip label="Must be a -70 and -22">
                          <InfoOutlineIcon boxSize={6} />
                        </Tooltip>
                      ) : null}
                    </Flex>
                    <FormErrorMessage>{String(errors[field.name]?.message)}</FormErrorMessage>
                  </FormControl>
                </Box>
              ))}
            <FormControl isInvalid={!!errors.configSetName} mb="4">
              <FormLabel htmlFor="configSetName">Config set name</FormLabel>
              <Flex alignItems="center">
                <Input type={'text'} id={'configSetName'} {...register('configSetName')} mr="4" />
                <Tooltip label="value be a string">
                  <InfoOutlineIcon boxSize={6} />
                </Tooltip>
              </Flex>
              <FormErrorMessage>{String(errors.configSetName?.message)}</FormErrorMessage>
            </FormControl>
            <Button
              type="submit"
              width="100%"
              marginY={4}
              colorScheme="blue"
              mt="7"
              isDisabled={!isConfigSetNameAndOneOtherFieldFilled}
            >
              Create config set
            </Button>
          </form>
        </Stack>
      </Flex>
    </Box>
  );
};

export default SetId3;
