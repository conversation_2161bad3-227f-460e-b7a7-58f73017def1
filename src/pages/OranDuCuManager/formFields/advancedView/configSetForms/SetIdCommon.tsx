import { Box, FormControl, FormLabel } from '@chakra-ui/react';

type SetIdCommonFieldProps = {
  name: string;
  id: number;
  constraints: string;
  default_value: string;
  type: string;
  read_write: boolean;
  field?: { name: string; id: number; constraints: string; default_value: string; type: string; read_write: boolean };
};

const SetIdCommon: React.FC<{ data: any }> = ({ data }) => {
  //const SetIdCommon = ({ data = [] }) => {
  // Destructure data and provide a default value
  return (
    <>
      {data?.map((field: SetIdCommonFieldProps, index: number) => (
        <Box m="10" key={`${field.name}-${field?.id}`}>
          {/* Prop Name */}
          <FormControl m="2">
            <FormLabel htmlFor={field.name}>Property Name: {field.name}</FormLabel>
          </FormControl>
          {/* Constraints */}
          <FormControl m="2">
            <FormLabel htmlFor={`constraints-${index}`}>
              Field constraints - {JSON.stringify(field.constraints, null, 2)}
            </FormLabel>
          </FormControl>
          {/* Default value */}
          <FormControl m="2">
            <FormLabel htmlFor={`default_value-${index}`}>
              Default value - {JSON.stringify(field.default_value, null, 2)}
            </FormLabel>
          </FormControl>
          {/* Type */}
          <FormControl m="2">
            <FormLabel htmlFor={`type-${index}`}>Type - {JSON.stringify(field.type, null, 2)}</FormLabel>
          </FormControl>
          {/* Read write */}
          <FormControl m="2">
            <FormLabel htmlFor={`read_write-${index}`}>Read write - {String(field.read_write)}</FormLabel>
          </FormControl>
        </Box>
      ))}
    </>
  );
};

export default SetIdCommon;
