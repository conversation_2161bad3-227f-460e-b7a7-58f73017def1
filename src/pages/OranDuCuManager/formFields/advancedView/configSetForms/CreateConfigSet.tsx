import { Box, Flex, Heading, Select, Stack, Text, useColorModeValue } from '@chakra-ui/react';
import { useState } from 'react';
import QueryError from '../../../../../components/errorComponents/QueryError';
import Loader from '../../../../../components/loader/Loader';
import AppBar from '../../../AppBar';
import useParameterSets from '../../../hooks/services/useParameterSets';
import SetId1 from './SetId1';
import SetId2 from './SetId2';
import SetId3 from './SetId3';
import SetId4 from './SetId4';
import SetId5 from './SetId5';
import SetId6 from './SetId6';
import SetId7 from './SetId7';
import SetId8 from './SetId8';
import SetId9 from './SetId9';
import SetIdCommon from './SetIdCommon';

const CreateConfigSet = () => {
  const [lastSelected, setLastSelected] = useState<number | null>(null);
  const colorModeValue = useColorModeValue('sm', 'sm-dark');

  // API calls
  const parameterSets = useParameterSets();
  // Api call
  const {
    data: parameterSetByIdData,
    error: parameterSetByIdError,
    isLoading: isParameterSetByIdLoading,
  } = parameterSets.getParameterSetById(lastSelected);

  const configSetMap: { [key: string]: React.ElementType | undefined } = {
    '9': SetId9,
    '8': SetId8,
    '7': SetId7,
    '6': SetId6,
    '5': SetId5,
    '4': SetId4,
    '3': SetId3,
    '2': SetId2,
    '1': SetId1,
  };

  const SelectedConfigSet = configSetMap[lastSelected ?? ''] ?? SetIdCommon;

  return (
    <>
      <Stack
        spacing="1"
        direction={{
          base: 'column',
          lg: 'row',
        }}
        justify="space-between"
      >
        <Stack spacing="1">
          <Heading fontWeight="bold">Config sets</Heading>
        </Stack>
      </Stack>
      <Stack
        data-testid="ORAN-DU-CU-CreateConfigSet-Manager"
        spacing={{
          base: '5',
          lg: '6',
        }}
      >
        <Box
          bg="bg-surface"
          boxShadow={{
            base: 'none',
            md: colorModeValue,
          }}
        >
          <Flex justify="center" m="8">
            <AppBar caller="advancedView" />
          </Flex>
          {/* Dropdown selectors */}
          <Box>
            <Flex justify="center" m="8">
              <Stack spacing={3} width="50%">
                <>
                  <Text>Config set kinds</Text>
                  <Select placeholder="Select kind" size="lg" onChange={(e) => setLastSelected(Number(e.target.value))}>
                    <option value="9">CU_UP App Config - Identity and Connectivity</option>
                    <option value="8">CU_CP App Config - Identity and Connectivity</option>
                    <option value="7">CU_CP Cell Config - Identity</option>
                    <option value="0">DU</option>
                  </Select>
                </>
                {/* DU Kind */}
                {lastSelected !== null && lastSelected >= 0 && lastSelected < 7 && (
                  <>
                    <Text>DU sub category</Text>
                    <Select
                      placeholder="Select DU sub category"
                      size="lg"
                      onChange={(e) => setLastSelected(Number(e.target.value))}
                    >
                      <option value="1">DU Cell Config - Cell Identity</option>
                      <option value="2">DU Cell Config - Frequency and SSB</option>
                      <option value="3">DU Cell Config - SIB1 / Power</option>
                      <option value="4">DU Cell Config - Timers</option>
                      <option value="5">DU Cell Config - MAC</option>
                      <option value="6">DU App Config - Identity and Connectivity</option>
                    </Select>
                  </>
                )}
                {/* DU Band */}
              </Stack>
            </Flex>
          </Box>
          {/* Config set create form */}
          <Box>
            {lastSelected && lastSelected !== null && isParameterSetByIdLoading ? <Loader /> : null}
            {parameterSetByIdError ? <QueryError error={parameterSetByIdError} /> : null}
            <SelectedConfigSet data={parameterSetByIdData} />
          </Box>
        </Box>
      </Stack>
    </>
  );
};

export default CreateConfigSet;
