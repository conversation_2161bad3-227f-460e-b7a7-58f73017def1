import { InfoOutlineIcon } from '@chakra-ui/icons';
import { Box, Button, Flex, FormControl, FormErrorMessage, FormLabel, Input, Stack, Tooltip } from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import moment from 'moment';
import { useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { FormObject, UnknownObject } from '../../../../../types/duCuManager.type';
import { ipv4Regex, ipv6Regex, validateIP } from '../../../../../utils/helpers';
import useConfigSets from '../../../hooks/services/useConfigSets';

const SetId6: React.FC<{ data: any }> = ({ data }) => {
  const validData = Array.isArray(data) ? data : [];

  const schemaFields = validData.reduce((acc, field) => {
    if (
      !field.name.includes('userLabel') &&
      !field.name.includes('gNodeB DU Name') &&
      !field.name.includes('gNodeB DU ID') &&
      !field.name.includes('gNodeB ID') &&
      !field.name.includes('gNodeB ID Length')
    ) {
      if (field.name.includes('FH-C subNetMask')) {
        acc[field.name] = z
          .string()
          .optional()
          .transform((value) => (value === undefined ? undefined : Number(value)))
          .refine((value) => value === undefined || (value >= 1 && value <= 255), {
            message: 'Value must be between 1 and 255',
          })
          .refine((value) => value === undefined || value.toString().length <= 3, {
            message: 'Value must be a maximum of 3 digits',
          });
      }
      if (field.constraints.type === 'ipv4 / ipv6 string') {
        acc[field.name] = z
          .string()
          .optional()
          .refine((val) => !val || validateIP(val), {
            message: 'Invalid IP address format',
          });
      }
      if (field.constraints.type === 'RU CONFIG ruUniqueId') {
        acc[field.name] = z
          .string()
          .optional()
          .refine((val) => !val || validateIP(val), {
            message: 'Invalid IP address format',
          });
      }
    }
    return acc;
  }, {});
  schemaFields['configSetName'] = z.string().min(1, 'Config set name is required');
  const dynamicSchema = useMemo(() => z.object(schemaFields), [validData]);

  const {
    handleSubmit,
    watch,
    register,
    formState: { errors, isDirty, isValid },
  } = useForm({
    resolver: zodResolver(dynamicSchema),
    mode: 'all',
  });

  const allValues = watch();
  const isConfigSetNameAndOneOtherFieldFilled =
    Object.values(allValues).filter((value) => value !== '' && value !== undefined && value !== 'ConfigSetName')
      .length >= 2;

  // API
  const { createConfigSet } = useConfigSets();
  const mutation = createConfigSet();

  //Form submit handler
  const onSubmit = async (values: any) => {
    let newObject;

    const matchedObjects = data.filter((obj: FormObject) => {
      return Object.hasOwn(values, obj.name) && values[obj.name] !== '';
    });

    if (matchedObjects.length > 0) {
      newObject = {
        created: moment().format(),
        updated: moment().format(),
        updated_by: 1, //NOTE: should this be dynamic, its the user unique id.
        name: values.configSetName,
        data: {
          params: matchedObjects.map((obj: FormObject) => ({
            created: moment().format(),
            name: obj.name,
            parameter_set_id: 6, //NOTE: should this be dynamic?
            function_type: obj.function_type,
            template_section: obj.template_section,
            software_version: obj.software_version,
            oran_fn_type: obj.oran_fn_type,
            path: obj.path,
            read_write: obj.read_write,
            type: obj.type,
            default_value: obj.default_value,
            value: values[obj.name],
          })),
        },
      };
    } else {
      console.log('No matching objects found.');
    }
    mutation.mutate(newObject);
  };
  return (
    <Box p="10">
      <Flex justify="center" m="8">
        <Stack spacing={3} width="50%">
          <form onSubmit={handleSubmit(onSubmit)}>
            {data
              ?.filter(
                (field: UnknownObject) =>
                  !field.name.includes('userLabel') &&
                  !field.name.includes('gNodeB DU Name') &&
                  !field.name.includes('gNodeB DU ID') &&
                  !field.name.includes('gNodeB ID') &&
                  !field.name.includes('gNodeB ID Length')
              )
              .map((field: UnknownObject, index: number) => (
                <Box key={`${field.name}-${field.id}`} mb="14">
                  <FormControl isInvalid={!!errors[field.name]} mb="4">
                    <FormLabel>{field.name}</FormLabel>
                    {field.name === 'FH-C subNetMask' ? (
                      <Flex alignItems="center">
                        <Input id={field.name} placeholder="Enter value" {...register(field.name)} mr="4" />
                        <Tooltip label="Must be a value between 1 and 255 and 3 digits">
                          <InfoOutlineIcon boxSize={6} />
                        </Tooltip>
                      </Flex>
                    ) : (
                      <Flex alignItems="center">
                        <Input id={field.name} placeholder={field.constraints.type} {...register(field.name)} mr="4" />
                        <Tooltip label="Must be a ipv4 or ipv6 IP address">
                          <InfoOutlineIcon boxSize={6} />
                        </Tooltip>
                      </Flex>
                    )}
                    {/* <Flex alignItems="center">
                      <Input id={field.name} placeholder={field.constraints.type} {...register(field.name)} mr="4" />
                      {field.name.includes('FH-C subNetMask') ? (
                        <Tooltip label="Must be a value between 1 and 255 and 3 digits">
                          <InfoOutlineIcon boxSize={6} />
                        </Tooltip>
                      ) : (
                        <Tooltip label="Must be a ipv4 or ipv6 IP address">
                          <InfoOutlineIcon boxSize={6} />
                        </Tooltip>
                      )}
                    </Flex> */}
                    <FormErrorMessage>{String(errors[field.name]?.message)}</FormErrorMessage>
                  </FormControl>
                </Box>
              ))}
            <FormControl isInvalid={!!errors.configSetName} mb="4">
              <FormLabel htmlFor="configSetName">Config set name</FormLabel>
              <Flex alignItems="center">
                <Input type={'text'} id={'configSetName'} {...register('configSetName')} mr="4" />
                <Tooltip label="value be a string">
                  <InfoOutlineIcon boxSize={6} />
                </Tooltip>
              </Flex>
              <FormErrorMessage>{String(errors.configSetName?.message)}</FormErrorMessage>
            </FormControl>
            <Button
              type="submit"
              width="100%"
              marginY={4}
              colorScheme="blue"
              mt="7"
              isDisabled={!isConfigSetNameAndOneOtherFieldFilled}
            >
              Create config set
            </Button>
          </form>
        </Stack>
      </Flex>
    </Box>
  );
};

export default SetId6;
