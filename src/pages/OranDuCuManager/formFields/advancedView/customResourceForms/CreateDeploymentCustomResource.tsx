import { InfoOutlineIcon } from '@chakra-ui/icons';
import {
  Box,
  Button,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Heading,
  Input,
  Select,
  Stack,
  Table,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  Tooltip,
  Tr,
  useColorModeValue,
} from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { SetStateAction, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useLocation } from 'react-router-dom';
import { z } from 'zod';
import { validateIP } from '../../../../../utils/helpers';
import useCustomResource from '../../../hooks/services/useCustomResource';
import { PassedData } from './CreateConfigCustomResource';

export type Payload = {
  site?: string;
  externalIp?: string;
  f1_ip?: string;
  e1_ip?: string;
  ru_vendor?: string;
};

export type FormData = {
  clusterId?: number;
  crType?: string;
  payload?: Payload;
};

const CreateDeploymentCustomResource: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState('');
  const colorModeValue = useColorModeValue('sm', 'sm-dark');

  const location = useLocation();
  const passedData: PassedData = location.state;

  // API
  const { createDeploymentCustomResource } = useCustomResource();
  const mutation = createDeploymentCustomResource();

  // Form validation
  const basePayloadSchema = z.object({
    site: z.string().min(1, 'Site name is required'),
    f1_ip: z
      .string()
      .optional()
      .refine((val) => !val || validateIP(val), {
        message: 'Invalid IP address format',
      }),
    e1_ip: z
      .string()
      .optional()
      .refine((val) => !val || validateIP(val), {
        message: 'Invalid IP address format',
      }),
    ru_vendor: z.string(),
  });

  const payloadSchemaWithExternalIp = basePayloadSchema.extend({
    site: z.string().min(1, 'Site name is required'),
    f1_ip: z.string().refine((val) => validateIP(val), {
      message: 'Invalid IP address format',
    }),
    e1_ip: z.string().refine((val) => validateIP(val), {
      message: 'Invalid IP address format',
    }),
    ru_vendor: z.string().min(1, 'RU vendor is required'),
  });

  const schema = z.object({
    clusterId: z.number().optional(),
    crType: z.string(),
    payload: selectedCategory === 'cu_cp_deployment' ? payloadSchemaWithExternalIp : basePayloadSchema,
  });

  const {
    handleSubmit,
    watch,
    register,
    formState: { errors, isDirty, isValid },
  } = useForm<FormData>({
    resolver: zodResolver(schema),
    mode: 'all',
  });

  // Button disabled state
  const allValues = watch();
  const isConfigSetNameAndOneOtherFieldFilled =
    Object.values(allValues).filter((value) => value !== '' && value !== undefined && value !== 'site').length >= 1;

  const handleDropdownChange = (e: { target: { value: SetStateAction<string> } }) => {
    setSelectedCategory(e.target.value);
  };

  //Form submit handler
  const onSubmit = async (values: any) => {
    if (values.crType && passedData?.updatedFilteredClusterList.id) {
      const payloadData: FormData = {
        clusterId: passedData?.updatedFilteredClusterList.id,
        crType: values.crType,
        payload: {
          site: values.payload?.site ?? '',
          f1_ip: values.payload?.f1Ip ?? '',
          e1_ip: values.payload?.e1Ip ?? '',
          ru_vendor: values.payload?.ruVendor ?? '',
        },
      };
      mutation.mutate(payloadData);
    } else {
      console.error('crType is invalid or not provided.');
    }
  };

  return (
    <Box
      bg="bg-surface"
      boxShadow={{
        base: 'none',
        md: colorModeValue,
      }}
    >
      <Box p="10">
        <Flex justify="center" m="8">
          <Stack spacing="3" width="50%">
            <form onSubmit={handleSubmit(onSubmit)}>
              <Heading fontWeight="bold">Create deployment custom resource</Heading>
              {/* Selected cluster */}
              <Box my="4" mb="12">
                <Text>Selected cluster</Text>
                <Table variant="simple" size="sm">
                  <Thead>
                    <Tr bg="GrayText">
                      <Th background="gray.50">Name</Th>
                      <Th background="gray.50">Id</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    <Tr>
                      <Td>{passedData?.updatedFilteredClusterList.cluster_name}</Td>
                      <Td>{passedData?.updatedFilteredClusterList.id}</Td>
                    </Tr>
                  </Tbody>
                </Table>
              </Box>
              {/* Custom resource type */}
              <Box my="4" mb="12">
                <FormControl isInvalid={!!errors.crType} mb="4">
                  <FormLabel htmlFor="crType">Custom resource type</FormLabel>
                  <Select
                    id="crType"
                    placeholder="Select custom resource type"
                    {...register('crType')}
                    mr="4"
                    onChange={handleDropdownChange}
                  >
                    <option key="cu_cp_deployment" value="cu_cp_deployment">
                      cu_cp_deployment
                    </option>
                    <option key="cu_up_deployment" value="cu_up_deployment">
                      cu_up_deployment
                    </option>
                    <option key="du_deployment" value="du_deployment">
                      du_deployment
                    </option>
                  </Select>
                  <FormErrorMessage>{errors.crType?.message}</FormErrorMessage>
                </FormControl>
              </Box>
              {/* Site */}
              <Box my="4" mb="12">
                <Box mb="14">
                  <FormControl isInvalid={!!errors.payload?.site} mb="4">
                    <FormLabel htmlFor="payload.site">Site</FormLabel>
                    <Flex alignItems="center">
                      <Input
                        type="text"
                        id={'payload.site'}
                        {...register('payload.site')}
                        mr="4"
                        value={passedData?.updatedFilteredClusterList.cr_site}
                      />
                      {/* <Tooltip label="value must be a string and lowercase eg:abc123, valid.abc.def, a1.b2.c3"> */}
                      <Tooltip label="value must be a string and lowercase">
                        <InfoOutlineIcon boxSize="6" />
                      </Tooltip>
                    </Flex>
                    <FormErrorMessage>{String(errors.payload?.site?.message)}</FormErrorMessage>
                  </FormControl>
                </Box>
              </Box>
              {/* F1 ip */}
              <Box my="4" mb="12">
                <Box mb="14">
                  <FormControl isInvalid={!!errors.payload?.f1_ip} mb="4">
                    <FormLabel htmlFor="payload.f1_ip">F1 ip</FormLabel>
                    <Flex alignItems="center">
                      <Input type="text" id={'payload.f1_ip'} {...register('payload.f1_ip')} mr="4" />
                      <Tooltip label="value must be a ip address">
                        <InfoOutlineIcon boxSize="6" />
                      </Tooltip>
                    </Flex>
                    <FormErrorMessage>{String(errors.payload?.f1_ip?.message)}</FormErrorMessage>
                  </FormControl>
                </Box>
              </Box>
              {/* E1 ip */}
              <Box my="4" mb="12">
                <Box mb="14">
                  <FormControl isInvalid={!!errors.payload?.e1_ip} mb="4">
                    <FormLabel htmlFor="payload.e1_ip">E1 ip</FormLabel>
                    <Flex alignItems="center">
                      <Input type="text" id={'payload.e1_ip'} {...register('payload.e1_ip')} mr="4" />
                      <Tooltip label="value must be a ip address">
                        <InfoOutlineIcon boxSize="6" />
                      </Tooltip>
                    </Flex>
                    <FormErrorMessage>{String(errors.payload?.e1_ip?.message)}</FormErrorMessage>
                  </FormControl>
                </Box>
              </Box>
              {/* RU vendor */}
              <Box my="4" mb="12">
                <FormControl isInvalid={!!errors.payload?.ru_vendor} mb="4">
                  <FormLabel htmlFor="ru_vendor">RU vendor</FormLabel>
                  <Select
                    id="ru_vendor"
                    placeholder="Select RU vendor"
                    {...register('payload.ru_vendor')}
                    mr="4"
                    onChange={handleDropdownChange}
                  >
                    <option key="cu_cp_deployment" value="EdgeQ">
                      EdgeQ
                    </option>
                    <option key="cu_up_deployment" value="Airspan">
                      Airspan
                    </option>
                    <option key="du_deployment" value="Pal">
                      Pal
                    </option>
                  </Select>
                  <FormErrorMessage>{errors.payload?.ru_vendor?.message}</FormErrorMessage>
                </FormControl>
              </Box>

              <Button
                type="submit"
                width="100%"
                marginY="4"
                colorScheme="blue"
                mt="7"
                isDisabled={!isConfigSetNameAndOneOtherFieldFilled}
              >
                Create config custom resource
              </Button>
            </form>
          </Stack>
        </Flex>
      </Box>
    </Box>
  );
};

export default CreateDeploymentCustomResource;
