import { AddIcon, InfoOutlineIcon, MinusIcon } from '@chakra-ui/icons';
import {
  Accordion,
  AccordionButton,
  AccordionIcon,
  AccordionItem,
  AccordionPanel,
  Box,
  Button,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Heading,
  Input,
  Select,
  Stack,
  Table,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  Tooltip,
  Tr,
  useColorModeValue,
} from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { SetStateAction, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useLocation } from 'react-router-dom';
import { z } from 'zod';
import QueryError from '../../../../../components/errorComponents/QueryError';
import Loader from '../../../../../components/loader/Loader';
import { SelectedOption } from '../../../../../types/duCuManager.type';
import { Cluster } from '../../../advancedView/CustomResources';
import useConfigSets from '../../../hooks/services/useConfigSets';
import useCustomResource from '../../../hooks/services/useCustomResource';

export type ItemType = {
  cr_kind: string;
  data: object;
  file_part_id: number;
  id: number;
  meta: { changes: any[] };
  name: string;
};

export type Payload = {
  site?: string;
  config_set_ids?: number[];
};

export type FormData = {
  clusterId?: number;
  crType?: string;
  payload?: Payload;
};

export type PassedData = {
  updatedFilteredClusterList: Cluster;
  item: string;
  selectedOption?: SelectedOption;
};

export type ObjectDetailsProps = {
  obj: {
    name: string;
    category: string;
    value: string;
  };
};

const CreateConfigCustomResource: React.FC = () => {
  const [selectedIds, setSelectedIds] = useState<(number | null)[]>([]);
  const [selectedCategory, setSelectedCategory] = useState('');

  const config_set_ids: number[] = selectedIds.filter((id): id is number => id !== null);
  const colorModeValue = useColorModeValue('sm', 'sm-dark');

  const location = useLocation();
  const passedData: PassedData = location.state;
  const modifiedCrKind = `${passedData?.item.toLowerCase()}s`;

  // API
  const { createConfigCustomResource } = useCustomResource();
  const mutation = createConfigCustomResource();
  const configSets = useConfigSets();
  const { data: configSetList, isLoading: isConfigSetLoading, error: ConfigSetError } = configSets.getConfigSetsList();

  // Filtering data
  const configGroups = configSetList && Object.entries(configSetList);

  const filteredData = configGroups?.filter(([categoryName]: [string, unknown]) => categoryName === selectedCategory);

  const categories = configSetList && Object.keys(configSetList);

  const categoryMappings = {
    ducellconfigs: 'du_cell_config',
    split6duappconfigs: 'du_app_config',
    cucpappconfigs: 'cu_cp_app_config',
    cuupappconfigs: 'cu_up_app_config',
    cucellconfigs: 'cu_cell_config',
  } as const;

  type CategoryKey = keyof typeof categoryMappings;

  const passedDataCrType = categories?.filter((kind: string) => modifiedCrKind.includes(kind));

  // Event handlers
  const handleItemClick = (id: number | null) => {
    setSelectedIds((prevSelectedIds) => {
      const isSelected = prevSelectedIds.includes(id);
      if (isSelected) {
        return prevSelectedIds.filter((existingId) => existingId !== id);
      } else {
        return [...prevSelectedIds, id];
      }
    });
  };

  const handleDropdownChange = (e: { target: { value: SetStateAction<string> } }) => {
    setSelectedCategory(e.target.value);
  };

  // Form validation
  const schema = z.object({
    clusterId: z.number().optional(),
    crType: z.string(),
    payload: z.object({
      site: z.string().min(1, 'Site name is required'),
      config_set_ids: z.array(z.number()).optional().default([]),
    }),
  });

  const {
    handleSubmit,
    watch,
    register,
    formState: { errors, isDirty, isValid },
  } = useForm<FormData>({
    resolver: zodResolver(schema),
    mode: 'all',
  });

  // Button disabled state
  const allValues = watch();
  const isConfigSetNameAndOneOtherFieldFilled =
    Object.values(allValues).filter((value) => value !== '' && value !== undefined && value !== 'site').length >= 1;

  const onSubmit = async (values: FormData) => {
    if (values.crType && Object.keys(categoryMappings).includes(values.crType)) {
      const crType = values.crType as CategoryKey;

      const payloadData: FormData = {
        clusterId: passedData?.updatedFilteredClusterList.id,
        crType: categoryMappings[crType],
        payload: {
          config_set_ids,
          site: values.payload?.site ?? '',
        },
      };

      mutation.mutate(payloadData);
    } else {
      console.error('crType is invalid or not provided.');
    }
  };

  const ObjectDetails = ({ obj }: ObjectDetailsProps) => {
    return (
      <Table size="sm">
        <Thead>
          <Tr>
            {Object.keys(obj).map((key, index) => (
              <Th key={index}>{key.replace(/_/g, ' ').charAt(0).toUpperCase() + key.slice(1)}</Th>
            ))}
          </Tr>
        </Thead>
        <Tbody>
          <Tr>
            {Object.values(obj).map((value, idx) => (
              <Td key={idx}>{`${JSON.stringify(value)}`}</Td>
            ))}
          </Tr>
        </Tbody>
      </Table>
    );
  };

  if (ConfigSetError) return <QueryError error={ConfigSetError} />;
  if (isConfigSetLoading) return <Loader />;

  return (
    <Box
      bg="bg-surface"
      boxShadow={{
        base: 'none',
        md: colorModeValue,
      }}
    >
      <Box p="10">
        <Flex justify="center" m="8">
          <Stack spacing="3" width="50%">
            <form onSubmit={handleSubmit(onSubmit)}>
              <Heading fontWeight="bold">Create Config Custom Resource</Heading>
              {/* Selected cluster */}
              <Box my="4" mb="12">
                <Text>Selected cluster</Text>
                <Table variant="simple" size="sm">
                  <Thead>
                    <Tr bg="GrayText">
                      <Th background="gray.50">Name</Th>
                      <Th background="gray.50">Id</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    <Tr>
                      <Td>{passedData?.updatedFilteredClusterList.cluster_name}</Td>
                      <Td>{passedData?.updatedFilteredClusterList.id}</Td>
                    </Tr>
                  </Tbody>
                </Table>
              </Box>
              {/* Custom resource type */}
              <Box my="4" mb="12">
                <FormControl isInvalid={!!errors.crType} mb="4">
                  <FormLabel htmlFor="crType">Custom resource type</FormLabel>
                  <Select
                    id="crType"
                    placeholder="Select"
                    {...register('crType')}
                    mr="4"
                    onChange={handleDropdownChange}
                  >
                    {categories
                      .filter((category: string) => modifiedCrKind.includes(category))
                      .map((category: string) => (
                        <option key={category} value={category}>
                          {category.replace(/([a-z])([A-Z])/g, '$1 $2')}{' '}
                        </option>
                      ))}
                  </Select>
                  <FormErrorMessage>{errors.crType?.message}</FormErrorMessage>
                </FormControl>
              </Box>
              {/* Select config sets */}
              <Box my="4" mb="12">
                <Flex justifyContent="space-between" mb="2">
                  <Text>Select config sets</Text>
                  <Tooltip label="Leave empty for default values">
                    <InfoOutlineIcon boxSize="6" />
                  </Tooltip>
                </Flex>
                <Accordion allowMultiple>
                  {filteredData.map(([groupName, groupItems]: [string, ItemType[]]) => (
                    <AccordionItem key={groupName}>
                      <h2>
                        <AccordionButton>
                          <Box flex="1" textAlign="left">
                            {groupName}
                          </Box>
                          <AccordionIcon />
                        </AccordionButton>
                      </h2>
                      <AccordionPanel p="0">
                        <Accordion allowMultiple>
                          {groupItems.map((item) => (
                            <AccordionItem
                              key={item.id}
                              onClick={() => handleItemClick(item.id)}
                              style={{
                                cursor: 'pointer',
                                color: selectedIds.includes(item.id) ? 'grey' : 'black',
                                backgroundColor: selectedIds.includes(item.id) ? 'rgba(56, 161, 105, 0.6)' : 'white',
                              }}
                            >
                              <h2>
                                <AccordionButton>
                                  <Box as="span" flex="1" textAlign="left">
                                    {selectedIds.includes(item.id) ? <MinusIcon mr="4" /> : <AddIcon mr="4" />}
                                    {item.name} - {item.id}
                                  </Box>
                                  <AccordionIcon />
                                </AccordionButton>
                              </h2>
                              <AccordionPanel p="1">
                                {item.meta.changes.map((change, index) => (
                                  <ObjectDetails key={index} obj={change} />
                                ))}
                              </AccordionPanel>
                            </AccordionItem>
                          )) || 'No changes available'}
                        </Accordion>
                      </AccordionPanel>
                    </AccordionItem>
                  ))}
                </Accordion>
              </Box>
              {/* Site */}
              <Box my="4" mb="12">
                <Box mb="14">
                  <FormControl isInvalid={!!errors.payload?.site} mb="4">
                    <FormLabel htmlFor="payload.site">Site</FormLabel>
                    <Flex alignItems="center">
                      <Input
                        type="text"
                        id={'payload.site'}
                        {...register('payload.site')}
                        mr="4"
                        value={passedData?.updatedFilteredClusterList.cr_site}
                      />
                      <Tooltip label="value must be a string and lowercase">
                        <InfoOutlineIcon boxSize="6" />
                      </Tooltip>
                    </Flex>
                    <FormErrorMessage>{String(errors.payload?.site?.message)}</FormErrorMessage>
                  </FormControl>
                </Box>
              </Box>
              <Button
                type="submit"
                width="100%"
                marginY="4"
                colorScheme="blue"
                mt="7"
                isDisabled={!isConfigSetNameAndOneOtherFieldFilled}
              >
                Create config custom resource
              </Button>
            </form>
          </Stack>
        </Flex>
      </Box>
    </Box>
  );
};

export default CreateConfigCustomResource;
