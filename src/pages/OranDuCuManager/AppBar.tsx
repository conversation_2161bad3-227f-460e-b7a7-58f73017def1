import { Circle, Flex } from '@chakra-ui/react';
import { Link } from 'react-router-dom';

const AppBar: React.FC<{ caller: string }> = ({ caller }) => {
  return (
    <Flex data-testid="ORAN-DU-CU-Manager-App-Bar">
      {caller === 'advancedView' ? (
        <>
          <Link style={{ marginRight: '2rem' }} to="/oran-du-cu-manager">
            <Flex>
              <Circle bg="bg-muted" px="4" py="2">
                Landing page{/* <Cell width="50px" height="50px" /> */}
              </Circle>
            </Flex>
          </Link>

          <Link style={{ marginRight: '2rem' }} to="/oran-du-cu-manager/advanced-view">
            <Flex>
              <Circle bg="bg-muted" px="4" py="2">
                Advanced view{/* <Cell width="50px" height="50px" /> */}
              </Circle>
            </Flex>
          </Link>

          <Link style={{ marginRight: '2rem' }} to="/oran-du-cu-manager/advanced-view/config-sets">
            <Flex>
              <Circle bg="bg-muted" px="4" py="2">
                Config sets{/* <Cell width="50px" height="50px" /> */}
              </Circle>
            </Flex>
          </Link>

          <Link style={{ marginRight: '2rem' }} to="/oran-du-cu-manager/advanced-view/custom-resources">
            <Flex>
              <Circle bg="bg-muted" px="4" py="2">
                Custom resources{/* <Research width="50px" height="50px" /> */}
              </Circle>
            </Flex>
          </Link>

          <Link style={{ marginRight: '2rem' }} to="/oran-du-cu-manager/advanced-view/pods">
            <Flex>
              <Circle bg="bg-muted" px="4" py="2">
                Pods{/* <Alarm width="50px" height="50px" /> */}
              </Circle>
            </Flex>
          </Link>

          <Link style={{ marginRight: '2rem' }} to="/oran-du-cu-manager/advanced-view/clusters">
            <Flex>
              <Circle bg="bg-muted" px="4" py="2">
                Clusters{/* <Alarm width="50px" height="50px" /> */}
              </Circle>
            </Flex>
          </Link>
        </>
      ) : null}
      {caller === 'simpleView' ? (
        <>
          <Link style={{ marginRight: '2rem' }} to="/oran-du-cu-manager">
            <Flex>
              <Circle bg="bg-muted" px="4" py="2">
                Home
              </Circle>
            </Flex>
          </Link>

          <Link style={{ marginRight: '2rem' }} to="/oran-du-cu-manager/simple-view">
            <Flex>
              <Circle bg="bg-muted" px="4" py="2">
                Simple view{/* <Cell width="50px" height="50px" /> */}
              </Circle>
            </Flex>
          </Link>
          <Link style={{ marginRight: '2rem' }} to="/oran-du-cu-manager/advanced-view">
            <Flex>
              <Circle bg="bg-muted" px="4" py="2">
                Advanced view
              </Circle>
            </Flex>
          </Link>
        </>
      ) : null}
    </Flex>
  );
};

export default AppBar;
