import {
  Alert,
  AlertIcon,
  Box,
  Button,
  Flex,
  Heading,
  ListItem,
  OrderedList,
  Stack,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
  Text,
  useColorModeValue,
} from '@chakra-ui/react';
import { ErrorBoundary } from 'react-error-boundary';
import { useNavigate } from 'react-router-dom';
import {
  ErrorBoundaryFallback,
  ErrorBoundaryLogError,
} from '../../../components/errorComponents/ErrorBoundaryFallback';
import QueryError from '../../../components/errorComponents/QueryError';
import Loader from '../../../components/loader/Loader';
import { AUTH_TOKEN_KEY, READ_WRITE_ACCESS_ROLES } from '../../../data/constants';
import useLogin from '../../../hooks/useLogin';
import { DataTable } from '../../MetricsCollector/components/DataTable';
import AppBar from '../AppBar';
import useConfigSets from '../hooks/services/useConfigSets';
import useCuCellConfigsColumns from '../hooks/tableColumns/useCuCellConfigsColumns';
import useCuCpAppConfigsColumns from '../hooks/tableColumns/useCuCpAppConfigsColumns';
import useCuUpAppConfigsColumns from '../hooks/tableColumns/useCuUpAppConfigsColumns';
import useDuCellConfigsColumns from '../hooks/tableColumns/useDuCellConfigsColumns';
import useSplit6DuAppConfigsColumns from '../hooks/tableColumns/useSplit6DuAppConfigsColumns';

const ConfigSets = () => {
  const navigate = useNavigate();
  const colorModeValue = useColorModeValue('sm', 'sm-dark');
  const configSets = useConfigSets();

  const { checkNmsDevAccess } = useLogin(AUTH_TOKEN_KEY);
  const checkRoleAccess = checkNmsDevAccess(READ_WRITE_ACCESS_ROLES);

  // API calls
  const { data: configSetList, isLoading: isConfigSetLoading, error: ConfigSetError } = configSets.getConfigSetsList();

  // Columns for the table config
  const duCellConfigsColumns = useDuCellConfigsColumns();
  const split6DuAppConfigsColumns = useSplit6DuAppConfigsColumns();
  const cuCpAppConfigsColumns = useCuCpAppConfigsColumns();
  const cuUpAppConfigsColumns = useCuUpAppConfigsColumns();
  const cuCellConfigsColumns = useCuCellConfigsColumns();

  if (isConfigSetLoading) return <Loader />;
  if (ConfigSetError) return <QueryError error={ConfigSetError} />;
  return (
    <>
      <Stack
        spacing="1"
        direction={{
          base: 'column',
          lg: 'row',
        }}
        justify="space-between"
      >
        <Stack spacing="1">
          <Heading fontWeight="bold">O-RAN DU/CU Manager</Heading>
        </Stack>
      </Stack>
      <Stack
        data-testid="ORAN-DU-CU-ConfigSet-Manager"
        spacing={{
          base: '5',
          lg: '6',
        }}
      >
        <Box
          bg="bg-surface"
          boxShadow={{
            base: 'none',
            md: colorModeValue,
          }}
        >
          <Flex justify="center" m="8">
            <AppBar caller="advancedView" />
          </Flex>
          <Heading as="h2" size="lg" textAlign="center" pb="4">
            Config set manager
          </Heading>
          {/* Alert */}
          <Box mx="25%" mb="18">
            <Alert status="info" mb="14" display="flex" flexDirection="column" alignItems="baseline" pl="0">
              <Text display="flex">
                <AlertIcon alignSelf="center" />
                Here you can view, create and delete config sets. To create a new config set:
              </Text>
              <OrderedList mt="4" pl="12">
                <ListItem>Click the Create a new config set button</ListItem>
                <ListItem>Select the config set kind.</ListItem>
                <ListItem>Fill out the form with the desired parameters.</ListItem>
                <ListItem>Give the config set a name</ListItem>
                <ListItem>
                  Click the Create config set button. The new config set will be created and you will be redirected to
                  the config set main page where you can see the newly created config set in the list.
                </ListItem>
              </OrderedList>
            </Alert>
          </Box>

          {/* Tables */}
          <Tabs mx="15%" variant="enclosed" isFitted size="md">
            <TabList>
              <Tab>DU cell configs</Tab>
              <Tab>Split-6 DU app configs</Tab>
              <Tab>CU_CP app configs</Tab>
              <Tab>CU_UP app configs</Tab>
              <Tab>CU cell configs</Tab>
            </TabList>

            <TabPanels>
              {/* ducellconfigs Table */}
              <TabPanel>
                <Box p="10">
                  <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
                    {configSetList?.ducellconfigs && configSetList?.ducellconfigs.length > 0 ? (
                      <DataTable
                        isExpandable={false}
                        columns={duCellConfigsColumns ?? []}
                        data={configSetList?.ducellconfigs}
                        isLoading={isConfigSetLoading}
                        defaultPageSize={50}
                      />
                    ) : (
                      <Text>No data for DU cell configs to display.</Text>
                    )}
                  </ErrorBoundary>
                </Box>
              </TabPanel>
              {/* split6duappconfigs Table */}
              <TabPanel>
                <Box p="10">
                  <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
                    {configSetList?.split6duappconfigs && configSetList?.split6duappconfigs.length > 0 ? (
                      <DataTable
                        isExpandable={false}
                        columns={split6DuAppConfigsColumns ?? []}
                        data={configSetList?.split6duappconfigs}
                        isLoading={isConfigSetLoading}
                        defaultPageSize={50}
                      />
                    ) : (
                      <Text>No data for split-6 DU app configs to display.</Text>
                    )}
                  </ErrorBoundary>
                </Box>
              </TabPanel>
              {/* cucpappconfigs Table */}
              <TabPanel>
                <Box p="10">
                  <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
                    {configSetList?.cucpappconfigs && configSetList?.cucpappconfigs.length > 0 ? (
                      <DataTable
                        isExpandable={false}
                        columns={cuCpAppConfigsColumns ?? []}
                        data={configSetList?.cucpappconfigs}
                        isLoading={isConfigSetLoading}
                        defaultPageSize={50}
                      />
                    ) : (
                      <Text>No data for CUCP app configs to display.</Text>
                    )}
                  </ErrorBoundary>
                </Box>
              </TabPanel>
              {/* cuupappconfigs Table */}
              <TabPanel>
                <Box p="10">
                  <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
                    {configSetList?.cuupappconfigs && configSetList?.cuupappconfigs.length > 0 ? (
                      <DataTable
                        isExpandable={false}
                        columns={cuUpAppConfigsColumns ?? []}
                        data={configSetList?.cuupappconfigs}
                        isLoading={isConfigSetLoading}
                        defaultPageSize={50}
                      />
                    ) : (
                      <Text>No data for CUUP app configs to display.</Text>
                    )}
                  </ErrorBoundary>
                </Box>
              </TabPanel>
              {/* cucellconfigs Table */}
              <TabPanel>
                <Box p="10">
                  <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
                    {configSetList?.cucellconfigs && configSetList?.cucellconfigs.length > 0 ? (
                      <DataTable
                        isExpandable={false}
                        columns={cuCellConfigsColumns ?? []}
                        data={configSetList?.cucellconfigs}
                        isLoading={isConfigSetLoading}
                        defaultPageSize={50}
                      />
                    ) : (
                      <Text>No data for CU cell configs to display.</Text>
                    )}
                  </ErrorBoundary>
                </Box>
              </TabPanel>
            </TabPanels>
          </Tabs>

          {/* Button */}
          {checkRoleAccess && (
            <Box display="flex" justifyContent="center" alignItems="center" p="10">
              <Button
                variant="primary"
                onClick={() => navigate('/oran-du-cu-manager/advanced-view/config-sets/create-config-set')}
              >
                Create a new config set
              </Button>
            </Box>
          )}
        </Box>
      </Stack>
    </>
  );
};

export default ConfigSets;
