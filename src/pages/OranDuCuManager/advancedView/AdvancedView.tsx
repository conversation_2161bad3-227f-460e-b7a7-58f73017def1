import {
  Alert,
  AlertIcon,
  Box,
  Button,
  Circle,
  Flex,
  Heading,
  HStack,
  ListItem,
  OrderedList,
  SimpleGrid,
  Stack,
  Text,
  useColorModeValue,
  VStack,
} from '@chakra-ui/react';
import { useNavigate } from 'react-router-dom';
import AppBar from '../AppBar';

const AdvancedView = () => {
  const colorModeValue = useColorModeValue('sm', 'sm-dark');
  const navigate = useNavigate();
  return (
    <>
      <Stack
        spacing="1"
        direction={{
          base: 'column',
          lg: 'row',
        }}
        justify="space-between"
      >
        <Stack spacing="1">
          <Heading fontWeight="bold">Advanced O-RAN DU/CU Manager</Heading>
        </Stack>
      </Stack>
      <Stack
        spacing={{
          base: '5',
          lg: '6',
        }}
      >
        <Box
          bg="bg-surface"
          boxShadow={{
            base: 'none',
            md: colorModeValue,
          }}
        >
          <Flex justify="center" m="8">
            <AppBar caller="advancedView" />
          </Flex>

          {/* Alert */}
          <Box mx="25%" mb="18">
            <Alert status="info" data-testid="how-to-use" p="10" mb="10" flexDirection="column">
              {/* <Alert status="info" mb="10"> */}
              <Box display="flex" alignItems="center" marginRight="auto">
                {/* <Box> */}
                <AlertIcon />
                <Text as="span">How to use:</Text>
              </Box>
              <OrderedList mt="4">
                <ListItem>Launch config sets to list, create and manage config set.</ListItem>
                <ListItem>
                  Launch custom resources to list, create(config, deployment) and manage custom resources.
                </ListItem>
                <ListItem>Launch pods to list pods, create pods, activate and deactivate custom resources.</ListItem>
                <ListItem>Launch clusters to list, create and manage clusters.</ListItem>
              </OrderedList>
            </Alert>
          </Box>

          {/* Cards */}
          <Box mx="15%" mb="18">
            <SimpleGrid
              columns={{
                base: 1,
                md: 4,
              }}
              spacing="6"
            >
              {/* Config sets */}
              <Flex
                data-testid="config-sets"
                direction="column"
                alignItems="center"
                rounded="md"
                padding="8"
                position="relative"
                bg={useColorModeValue('white', 'gray.700')}
                shadow={{
                  md: 'base',
                }}
              >
                <Circle size="6rem" bg="bg-muted">
                  <Text fontSize="4xl" color="blackAlpha.700" as="b">
                    1
                  </Text>
                </Circle>
                <VStack spacing="1" flex="1" mt="3" mb="3">
                  <HStack>
                    <Text marginBottom="1.5rem" fontWeight="bold">
                      Config sets
                    </Text>
                  </HStack>
                </VStack>
                <Button
                  variant="outline"
                  colorScheme="brand.600"
                  rounded="full"
                  size="sm"
                  width="full"
                  onClick={() => navigate('/oran-du-cu-manager/advanced-view/config-sets')}
                >
                  Launch
                </Button>
              </Flex>
              {/* Custom resources */}
              <Flex
                data-testid="custom-resources"
                direction="column"
                alignItems="center"
                rounded="md"
                padding="8"
                position="relative"
                bg={useColorModeValue('white', 'gray.700')}
                shadow={{
                  md: 'base',
                }}
              >
                <Circle size="6rem" bg="bg-muted">
                  <Text fontSize="4xl" color="blackAlpha.700" as="b">
                    2
                  </Text>
                </Circle>
                <VStack spacing="1" flex="1" mt="3" mb="3">
                  <HStack>
                    <Text marginBottom="1.5rem" fontWeight="bold">
                      Custom resources
                    </Text>
                  </HStack>
                </VStack>
                <Button
                  variant="outline"
                  colorScheme="brand.600"
                  rounded="full"
                  size="sm"
                  width="full"
                  onClick={() => navigate('/oran-du-cu-manager/advanced-view/custom-resources')}
                >
                  Launch
                </Button>
              </Flex>
              {/* Pods */}
              <Flex
                data-testid="pods"
                direction="column"
                alignItems="center"
                rounded="md"
                padding="8"
                position="relative"
                bg={useColorModeValue('white', 'gray.700')}
                shadow={{
                  md: 'base',
                }}
              >
                <Circle size="6rem" bg="bg-muted">
                  <Text fontSize="4xl" color="blackAlpha.700" as="b">
                    3
                  </Text>
                </Circle>
                <VStack spacing="1" flex="1" mt="3" mb="3">
                  <HStack>
                    <Text marginBottom="1.5rem" fontWeight="bold">
                      Pods
                    </Text>
                  </HStack>
                </VStack>
                <Button
                  variant="outline"
                  colorScheme="brand.600"
                  rounded="full"
                  size="sm"
                  width="full"
                  onClick={() => navigate('/oran-du-cu-manager/advanced-view/pods')}
                >
                  Launch
                </Button>
              </Flex>
              {/* Clusters */}
              <Flex
                data-testid="clusters"
                direction="column"
                alignItems="center"
                rounded="md"
                padding="8"
                position="relative"
                bg={useColorModeValue('white', 'gray.700')}
                shadow={{
                  md: 'base',
                }}
              >
                <Circle size="6rem" bg="bg-muted">
                  <Text fontSize="4xl" color="blackAlpha.700" as="b">
                    4
                  </Text>
                </Circle>
                <VStack spacing="1" flex="1" mt="3" mb="3">
                  <HStack>
                    <Text marginBottom="1.5rem" fontWeight="bold">
                      Clusters
                    </Text>
                  </HStack>
                </VStack>
                <Button
                  variant="outline"
                  colorScheme="brand.600"
                  rounded="full"
                  size="sm"
                  width="full"
                  onClick={() => navigate('/oran-du-cu-manager/advanced-view/clusters')}
                >
                  Launch
                </Button>
              </Flex>
            </SimpleGrid>
          </Box>
        </Box>
        {/* </Container> */}
      </Stack>
    </>
  );
};

export default AdvancedView;
