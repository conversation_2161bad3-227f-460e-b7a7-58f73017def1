import {
  Box,
  Flex,
  Heading,
  Stack,
  Table,
  TableContainer,
  Tbody,
  Td,
  Th,
  Thead,
  Tr,
  useColorModeValue,
} from '@chakra-ui/react';
import QueryError from '../../../components/errorComponents/QueryError';
import Loader from '../../../components/loader/Loader';
import AppBar from '../AppBar';
import useClusterList from '../hooks/services/useClusters';

const Clusters = () => {
  const { data: clusterList = [], isLoading: isClusterLoading, error: clusterError } = useClusterList();
  const colorModeValue = useColorModeValue('sm', 'sm-dark');

  if (isClusterLoading) return <Loader />;
  if (clusterError) return <QueryError error={clusterError} />;

  return (
    <>
      <Stack
        spacing="1"
        direction={{
          base: 'column',
          lg: 'row',
        }}
        justify="space-between"
      >
        <Stack spacing="1">
          <Heading fontWeight="bold">O-RAN DU/CU Manager</Heading>
        </Stack>
      </Stack>
      <Stack
        spacing={{
          base: '5',
          lg: '6',
        }}
      >
        <Box
          bg="bg-surface"
          boxShadow={{
            base: 'none',
            md: colorModeValue,
          }}
        >
          <Flex justify="center" m="8">
            <AppBar caller="advancedView" />
          </Flex>
          <Heading as="h2" size="lg" textAlign="center" pb="4">
            Clusters
          </Heading>

          {/* Table */}
          <Box mx="15%" mb="18">
            <TableContainer mb={4}>
              <Table variant="simple">
                <Thead>
                  <Tr>
                    <Th>Name</Th>
                    <Th>Type</Th>
                    <Th>Gcp project id</Th>
                    <Th>Gcp region</Th>
                    <Th>Cluster id</Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {clusterList && clusterList?.length > 0 ? (
                    clusterList.map((cluster, index) => (
                      <Tr key={cluster.id}>
                        <Td>{cluster.cluster_name}</Td>
                        <Td>{cluster.cluster_type}</Td>
                        <Td>{cluster.gcp_project_id}</Td>
                        <Td>{cluster.gcp_region}</Td>
                        <Td>{cluster.id}</Td>
                      </Tr>
                    ))
                  ) : (
                    <option value="option1">No data to display.</option>
                  )}
                </Tbody>
              </Table>
            </TableContainer>
          </Box>

          {/* Button */}
          <Box display="flex" justifyContent="center" alignItems="center" p="10">
            {/* <Button variant="primary">TODO: Create new cluster</Button> */}
          </Box>
        </Box>
      </Stack>
    </>
  );
};

export default Clusters;
