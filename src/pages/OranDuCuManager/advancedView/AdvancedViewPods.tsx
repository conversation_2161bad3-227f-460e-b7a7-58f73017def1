import { Box, Stack, useColorModeValue } from '@chakra-ui/react';
import { useLocation } from 'react-router';
import { SelectedOption } from '../../../types/duCuManager.type';
import { useEffect, useState } from 'react';
import SimpleViewPods from '../simpleView/SimpleViewPods';

export type passedData = {
  selectedOption: SelectedOption;
};

export type Pod = {
  ip: string;
  name: string;
  namespace: string;
  site: string;
};

const AdvancedViewPods = () => {
  const colorModeValue = useColorModeValue('sm', 'sm-dark');

  const location = useLocation();
  const passedData: passedData = location.state;

  const [selectedOption, setSelectedOption] = useState<any>({
    deployment_name: passedData?.selectedOption?.deployment_name ? passedData?.selectedOption?.deployment_name : '',
    du_site_name: passedData?.selectedOption?.du_site_name ? passedData?.selectedOption?.du_site_name : '',
    cucp_site_name: passedData?.selectedOption?.cucp_site_name ? passedData?.selectedOption?.cucp_site_name : '',
    cuup_site_name: passedData?.selectedOption?.cuup_site_name ? passedData?.selectedOption?.cuup_site_name : '',
    version: 'v1beta1',
    du_cluster: passedData?.selectedOption?.du_cluster ? passedData?.selectedOption?.du_cluster : null,
    cucp_cluster: passedData?.selectedOption?.cucp_cluster ? passedData?.selectedOption?.cucp_cluster : null,
    cuup_cluster: passedData?.selectedOption?.cuup_cluster ? passedData?.selectedOption?.cuup_cluster : null,
    bands: [],
    bandwidths: [],
    arfcns: [],
    du_cell_config_set_ids: [],
    du_app_config_set_ids: [],
    cu_cell_config_set_ids: [],
    cu_cp_app_config_set_ids: [],
    cu_up_app_config_set_ids: [],
    ru_vendor: null,
    f1_ip: passedData?.selectedOption?.f1_ip ? passedData?.selectedOption?.f1_ip : '',
    e1_ip: passedData?.selectedOption?.e1_ip ? passedData?.selectedOption?.e1_ip : '',
  });

  const [deploymentType, setDeploymentType] = useState('edit');

  return (
    <>
      <Stack
        data-testid="ORAN-DU-CU-pods-Manager"
        spacing={{
          base: '5',
          lg: '6',
        }}
      >
        <Box
          bg="bg-surface"
          boxShadow={{
            base: 'none',
            md: colorModeValue,
          }}
        >
          <SimpleViewPods selectedOption={selectedOption} deployment_type={deploymentType} view="advanced" />
        </Box>
      </Stack>
    </>
  );
};

export default AdvancedViewPods;
