import { AddIcon, DeleteIcon, InfoOutlineIcon, MinusIcon } from '@chakra-ui/icons';
import {
  Accordion,
  AccordionButton,
  AccordionItem,
  AccordionPanel,
  Alert,
  AlertIcon,
  Box,
  Button,
  Card,
  CardBody,
  CardFooter,
  CardHeader,
  Flex,
  Heading,
  ListItem,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalHeader,
  ModalOverlay,
  OrderedList,
  Select,
  SimpleGrid,
  Stack,
  Table,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  Tooltip,
  Tr,
  UnorderedList,
  useColorModeValue,
  useDisclosure,
  useColorModeValue as mode,
} from '@chakra-ui/react';
import _ from 'lodash';
import { useEffect, useRef, useState } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  ErrorBoundaryFallback,
  ErrorBoundaryLogError,
} from '../../../components/errorComponents/ErrorBoundaryFallback';
import QueryError from '../../../components/errorComponents/QueryError';
import { StaticStatusCircleIcon } from '../../../components/icons/StatusIcon';
import Loader from '../../../components/loader/Loader';
import {
  AUTH_TOKEN_KEY,
  CuCp_CR_KIND,
  CUSTOM_RESOURCE_KINDS,
  CuUp_CR_KIND,
  Du_CR_KIND,
  FALSE,
  NODE_KIND,
  READ_WRITE_ACCESS_ROLES,
  StatusToColor,
  TRAFFIC_OK_GREEN,
  TRAFFIC_UNKNOWN_GRAY,
  TRAFFIC_WARNING_ORANGE,
} from '../../../data/constants';
import useLogin from '../../../hooks/useLogin';
import { DebouncedInput } from '../../MetricsCollector/components/DebounceInput';
import CreateSiteForm from '../../SiteManager/components/CreateSiteForm';
import AppBar from '../AppBar';
import ModalSubComponentRenderer from '../hooks/ModalSubComponentRenderer';
import useCustomResource from '../hooks/services/useCustomResource';
import useCustomResourceColumns from '../hooks/tableColumns/useCustomResourceColumns';
import TableConfigMenu from '../TableConfigMenu';

export type Cluster = {
  cluster_ca_secret: string;
  cluster_name: string;
  cluster_type: string;
  created: string;
  cr_site?: string;
  cr_type?: string;
  gcp_project: string;
  gcp_region: string;
  id: number;
  ip_address: string;
  software_version: string;
  updated: string;
  updated_by: number;
};

export type CrCard = {
  created: string;
  enabled: string;
  kind: string;
  name: string;
  site: string;
};

export type AllCrKindTypes = CuCp_CR_KIND | CuUp_CR_KIND | Du_CR_KIND;

export type SelectedOption = {
  site: string;
  cluster?: number | null;
  type?: string;
  cucp?: number | null;
  cuup?: number | null;
  du?: number | null;
} | null;

export const mapCrTypeToStateValue = (crType: string) => {
  const typeMapping: Record<string, string> = {
    cu_cp_deployment: 'CuCp',
    CuCpDeployment: 'CuCp',
    cu_cp_app_config: 'CuCp',
    CuCpAppConfig: 'CuCp',
    cu_cell_config: 'CuCp',
    CuCellConfig: 'CuCp',
    cu_up_app_config: 'CuUp',
    CuUpAppConfig: 'CuUp',
    cu_up_deployment: 'CuUp',
    CuUpDeployment: 'CuUp',
    du_deployment: 'Du',
    Split6DuDeployment: 'Du',
    du_app_config: 'Du',
    Split6DuAppConfig: 'Du',
    du_cell_config: 'Du',
    DuCellConfig: 'Du',
  };
  return typeMapping[crType] || crType;
};

const CustomResources = () => {
  const colorModeValue = useColorModeValue('sm', 'sm-dark');
  const hoverBackgroundColor = mode('gray.200', 'gray.700');
  const bottomRef = useRef<HTMLDivElement>(null);
  const { getClusterList, getSingleCustomResource, getCustomResourceList, getCustomResourceBySite } =
    useCustomResource();

  const navigate = useNavigate();
  const location = useLocation();
  const passedData: any = location.state;

  const { checkNmsDevAccess } = useLogin(AUTH_TOKEN_KEY);
  const checkRoleAccess = checkNmsDevAccess(READ_WRITE_ACCESS_ROLES);

  const { isOpen, onOpen, onClose } = useDisclosure();
  const { isOpen: isSingleCrOpen, onOpen: onSingleCrOpen, onClose: onSingleCrClose } = useDisclosure();

  const [expandedIndex, setExpandedIndex] = useState<boolean>(false);
  const [selectedOption, setSelectedOption] = useState({
    cluster: passedData?.payloadData?.clusterId ? passedData?.payloadData?.clusterId : null,
    type: mapCrTypeToStateValue(passedData?.payloadData?.crType),
    site: passedData?.payloadData?.payload?.site ? passedData?.payloadData?.payload?.site : '',
  });
  const [selectedItem, setSelectedItem] = useState<CrCard | null>(null);
  const [isQueryEnabled, setIsQueryEnabled] = useState<boolean>(false);
  const [loadingStates, setLoadingStates] = useState<{ [key: string]: boolean }>({});

  // API calls
  //Drives the dropdown
  const { data: clusterList, isLoading: isClusterLoading, error: clusterError } = getClusterList();
  //Drives the table
  const {
    data: customResourceList,
    isLoading: isCustomResourceLoading,
    error: customResourceError,
  } = getCustomResourceList(selectedOption && selectedOption.cluster);
  //Drives the CR cards
  const selectedCriteria = selectedOption?.cluster && selectedOption?.site ? selectedOption : null;
  const {
    data: crCardList,
    isLoading: isCrCardLoading,
    error: crCardError,
  } = getCustomResourceBySite(selectedCriteria);
  // Get single cr
  const {
    data: singleCustomResource,
    isLoading: isSingleCustomResourceLoading,
    error: singleCustomResourceError,
  } = getSingleCustomResource(selectedOption?.cluster || '', selectedItem?.name || '', isQueryEnabled);
  //Delete CR
  const { deleteCustomResource } = useCustomResource();
  const customResourceMutation = deleteCustomResource();

  const customResourceSiteColumns = useCustomResourceColumns(selectedOption && selectedOption.cluster);

  const handleGetSingleCrData = (item: CrCard) => {
    setLoadingStates((prev) => ({ ...prev, [item.name]: true }));
    setSelectedItem(item);
    setIsQueryEnabled(true);
  };

  useEffect(() => {
    if (isQueryEnabled && singleCustomResource) {
      setIsQueryEnabled(false);
    }
  }, [isQueryEnabled, singleCustomResource, isSingleCustomResourceLoading, singleCustomResourceError]);

  useEffect(() => {
    if (!isSingleCustomResourceLoading && singleCustomResource) {
      onSingleCrOpen();
      if (selectedItem?.name) {
        setLoadingStates((prev) => ({ ...prev, [selectedItem.name]: false }));
      }
      setIsQueryEnabled(false);
    }
  }, [isSingleCustomResourceLoading, singleCustomResource, selectedItem?.name]);

  const handleUserInputChange = (field: string, selectedValue: string | null) => {
    if (selectedValue) {
      setSelectedOption((prev) => ({
        ...prev,
        [field]: selectedValue,
      }));
      setExpandedIndex(false);
    }
  };

  const handleRowClick = (item: CrCard) => {
    handleUserInputChange('type', mapCrTypeToStateValue(item.kind));
    handleUserInputChange('site', item.site);
    if (bottomRef.current) {
      bottomRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handelDeleteCustomResource = async (item: CrCard) => {
    const payloadData = {
      cluster_id: selectedOption?.cluster,
      name: item.name,
      cr_name: item.name,
      version: 'v1beta1',
    };
    onOpen();
    customResourceMutation.mutate(payloadData);
  };

  const filteredClusterList = clusterList?.find((item: Cluster) => item.id === _.toNumber(selectedOption?.cluster));

  const updatedFilteredClusterList = {
    ...filteredClusterList,
    cr_site: selectedOption?.site,
    cr_type: selectedOption?.type,
  };

  const filteredCrCardList = crCardList?.crs.filter((item: CrCard) => {
    return Object.values(CUSTOM_RESOURCE_KINDS).includes(item.kind as CUSTOM_RESOURCE_KINDS);
  });

  const filteredItemsByKeyword = filteredCrCardList?.filter((item: CrCard) => {
    const itemKindLower = item.kind.toLowerCase();
    const typeLower = selectedOption?.type?.toLowerCase();

    if (typeLower === 'cucp') {
      return itemKindLower.includes('cucp');
    } else if (typeLower === 'cuup') {
      return itemKindLower.includes('cuup');
    } else if (typeLower === 'du') {
      return itemKindLower.includes('du');
    }
    return itemKindLower.includes(typeLower);
  });

  const ExtraCrs = filteredCrCardList?.filter(
    (crCardItem: CrCard) => !filteredItemsByKeyword.some((keywordItem: CrCard) => keywordItem.kind === crCardItem.kind)
  );

  const completeCrs = filteredCrCardList?.filter((crCardItem: CrCard) =>
    filteredItemsByKeyword.some((keywordItem: CrCard) => keywordItem.kind === crCardItem.kind)
  );

  const getEnumValues = (kind: string) => {
    switch (kind) {
      case NODE_KIND.DU:
        return Object.values(Du_CR_KIND);
      case NODE_KIND.CUUP:
        return Object.values(CuUp_CR_KIND);
      case NODE_KIND.CUCP:
        return Object.values(CuCp_CR_KIND);
      default:
        return [];
    }
  };
  const allCrKinds: AllCrKindTypes[] = getEnumValues(selectedOption?.type);

  const missingCrKinds = allCrKinds?.filter(
    (kind: string) => completeCrs && !completeCrs.some((crCardItem: CrCard) => crCardItem.kind === kind)
  );

  if (clusterError) return <QueryError error={clusterError} />;

  return (
    <>
      <Stack
        spacing="1"
        direction={{
          base: 'column',
          lg: 'row',
        }}
        justify="space-between"
      >
        <Stack spacing="1">
          <Heading fontWeight="bold">O-RAN DU/CU Manager</Heading>
        </Stack>
      </Stack>
      <Stack
        data-testid="ORAN-DU-CU-CustomResource-Manager"
        spacing={{
          base: '5',
          lg: '6',
        }}
      >
        <Box
          bg="bg-surface"
          boxShadow={{
            base: 'none',
            md: colorModeValue,
          }}
        >
          <Flex justify="center" m="8">
            <AppBar caller="advancedView" />
          </Flex>
          {/* input boxes */}
          <Box>
            {isClusterLoading ? (
              <Loader />
            ) : (
              <>
                <Heading as="h2" size="lg" textAlign="center" pb="4">
                  Custom resources
                </Heading>
                {/* Alert */}
                <Box mx="25%" mb="18">
                  <Alert status="info" mb="14" display="flex" flexDirection="column" alignItems="baseline" pl="0">
                    <Text display="flex">
                      <AlertIcon alignSelf="center" />
                      How to create custom resources:
                    </Text>
                    <OrderedList mt="4" pl="12">
                      <ListItem>Select a cluster.</ListItem>
                      <ListItem>Select the custom resource type you want to create.</ListItem>
                      <ListItem>Enter a site name.</ListItem>
                      <ListItem>You will be shown the custom resources required to create a pod.</ListItem>
                      <ListItem>
                        Create the custom resource kind by clicking a card and select the appropriate config set for the
                        type and kind of custom resource.
                      </ListItem>
                    </OrderedList>
                  </Alert>
                </Box>
                {/* Select a cluster */}
                <Box mx="15%" mb="18">
                  <Heading textAlign="center" as="h2" fontSize="20px" mb="4" mt="12">
                    Select a cluster
                  </Heading>
                  <Select
                    placeholder="Select option"
                    onChange={(e) => handleUserInputChange('cluster', e.target.value)}
                    mb="2"
                  >
                    {clusterList?.map((item: Cluster) => (
                      <option key={item.id} value={item.id}>
                        {`${item.id}: ${item.cluster_name}`}
                      </option>
                    ))}
                  </Select>
                  {selectedOption.cluster && (
                    <Box>
                      {selectedOption.cluster && isCustomResourceLoading ? (
                        <Loader />
                      ) : (
                        <Accordion allowToggle onChange={(index) => setExpandedIndex(true as boolean)}>
                          {[1].map((section, index) => (
                            <AccordionItem key={`crTable-${index}`}>
                              <h2>
                                <AccordionButton>
                                  <Text flex="1" textAlign="center">
                                    <Text as={'b'}>Custom resource</Text> table for selected cluster
                                  </Text>
                                  {expandedIndex ? <MinusIcon /> : <AddIcon />}
                                </AccordionButton>
                              </h2>
                              <AccordionPanel p="0">
                                {customResourceList?.crs ? (
                                  <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
                                    <Table size="sm">
                                      <Thead>
                                        <Tr>
                                          <Th>Site</Th>
                                          <Th>Kind</Th>
                                          <Th>Name</Th>
                                          <Th>Enabled</Th>
                                          <Th>Menu</Th>
                                        </Tr>
                                      </Thead>
                                      <Tbody>
                                        {customResourceList?.crs.map((item: CrCard, index: number) => (
                                          <Tr
                                            key={`${item.name}-${index}`}
                                            onClick={() => handleRowClick(item)}
                                            _hover={{
                                              bg: hoverBackgroundColor,
                                              cursor: 'pointer',
                                              boxShadow: `0 0 12px ${hoverBackgroundColor}`,
                                            }}
                                          >
                                            <Td>{item.site}</Td>
                                            <Td>{item.kind}</Td>
                                            <Td>{item.name}</Td>
                                            <Td>
                                              <StaticStatusCircleIcon
                                                size={40}
                                                color={StatusToColor[item.enabled as keyof typeof StatusToColor]}
                                                dataTestId="du-cu-manager-cr-table-list-status-icon"
                                              />
                                            </Td>
                                            <Td>
                                              {item.enabled === FALSE ? (
                                                <TableConfigMenu
                                                  dataTestId="du-cu-manager-cr-table-list-config-icon"
                                                  type="customResource"
                                                  assetName={item.name}
                                                  id={selectedOption.cluster as number}
                                                />
                                              ) : null}
                                            </Td>
                                          </Tr>
                                        ))}
                                      </Tbody>
                                    </Table>
                                  </ErrorBoundary>
                                ) : null}
                              </AccordionPanel>
                            </AccordionItem>
                          ))}
                        </Accordion>
                      )}
                    </Box>
                  )}
                </Box>
                {/* Type and Site */}
                <Flex mx="15%" mb="18" justifyContent="space-between">
                  <Box width="50%" mr="4">
                    <Heading textAlign="center" as="h2" fontSize="20px" mb="4" mt="12">
                      Select a custom resource type
                    </Heading>
                    <Select
                      placeholder="Select option"
                      value={selectedOption.type || ''}
                      onChange={(e) => handleUserInputChange('type', e.target.value)}
                    >
                      <option value="CuUp">CuUp</option>
                      <option value="CuCp">CuCp</option>
                      <option value="Du">Du</option>
                    </Select>
                  </Box>
                  <Box width="50%" ml="4">
                    <Heading textAlign="center" as="h2" fontSize="20px" mb="4" mt="12">
                      Enter a site name
                    </Heading>
                    <Box>
                      <DebouncedInput
                        type="text"
                        id={'payload.site'}
                        value={selectedOption.site || ''}
                        onChange={(value) => {
                          handleUserInputChange('site', value as string);
                        }}
                        placeholder="Site name..."
                        testId="ORAN-DU-CU-CustomResource-Manager-siteName"
                        width="100%"
                        icon="null"
                      />
                    </Box>
                  </Box>
                </Flex>
              </>
            )}
          </Box>
          {/* Cards */}
          <Box mx="15%" mb="4">
            {selectedOption.cluster && selectedOption.type && selectedOption.site && isCrCardLoading ? (
              <Loader />
            ) : (
              <Box>
                <Heading as="h2" fontSize="20px" textAlign="center" mb="4" mt="12">
                  Create custom resources
                </Heading>
                {selectedOption.type === NODE_KIND.CUUP ? (
                  <Alert status="info" mb="14" display="flex" flexDirection="column" alignItems="baseline" pl="0">
                    <Text display="flex">
                      <AlertIcon alignSelf="center" />
                      CuUp requires the following custom resources:
                    </Text>
                    <UnorderedList mt="4" pl="12">
                      <ListItem>CuUpAppConfig</ListItem>
                      <ListItem>CuUpDeployment</ListItem>
                    </UnorderedList>
                  </Alert>
                ) : null}
                {selectedOption.type === NODE_KIND.CUCP || selectedOption.type === NODE_KIND.DU ? (
                  <Alert status="info" mb="14" display="flex" flexDirection="column" alignItems="baseline" pl="0">
                    <Text display="flex">
                      <AlertIcon alignSelf="center" />
                      {selectedOption.type} requires the following custom resources:
                    </Text>
                    <UnorderedList mt="4" pl="12">
                      <ListItem>{selectedOption.type}AppConfig</ListItem>
                      <ListItem>{selectedOption.type}CellConfig</ListItem>
                      <ListItem>{selectedOption.type}Deployment</ListItem>
                    </UnorderedList>
                  </Alert>
                ) : null}
                {selectedOption.cluster && selectedOption.type && selectedOption.site && crCardList?.crs ? (
                  <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
                    {/* complete */}
                    {completeCrs.length > 0 && (
                      <Box mb="4">
                        <Heading as="h3" size="md" textAlign="center" mb="4">
                          Complete CRs
                        </Heading>
                        <SimpleGrid spacing="3" templateColumns="repeat(auto-fill, minmax(300px, 1fr))" mb="4">
                          {completeCrs.map((item: CrCard, index: number) => (
                            <Card key={index} backgroundColor={TRAFFIC_OK_GREEN}>
                              <CardHeader>
                                <Flex justify="space-between">
                                  <Heading size="md">{item.kind}</Heading>
                                  {checkRoleAccess && item.enabled === 'False' ? (
                                    <Button
                                      data-testid={`ORAN-DUCU-Manager-advanced-view-CustomResource-Delete-${item.kind}`}
                                      onClick={(e) => {
                                        handelDeleteCustomResource(item);
                                      }}
                                    >
                                      <DeleteIcon />
                                    </Button>
                                  ) : (
                                    <Tooltip label="Disable custom resource to configure" aria-label="A tooltip">
                                      <InfoOutlineIcon />
                                    </Tooltip>
                                  )}
                                </Flex>
                              </CardHeader>
                              <CardBody>
                                <Text>{item.kind}</Text>
                              </CardBody>
                              <CardFooter>
                                <>
                                  {loadingStates[item.name] ? (
                                    <Loader />
                                  ) : (
                                    <Button onClick={() => handleGetSingleCrData(item)}>{item.site}</Button>
                                  )}
                                </>
                              </CardFooter>
                            </Card>
                          ))}
                        </SimpleGrid>
                      </Box>
                    )}
                    {/* missing */}
                    {missingCrKinds.length > 0 && (
                      <Box mb="4">
                        <Heading as="h3" size="md" textAlign="center" mb="4">
                          Missing CRs
                        </Heading>
                        <SimpleGrid spacing="3" templateColumns="repeat(auto-fill, minmax(300px, 1fr))" mb="4">
                          {missingCrKinds.map((item: string, index: number) => (
                            <Card key={index} backgroundColor={TRAFFIC_UNKNOWN_GRAY}>
                              <CardHeader>
                                <Heading size="md">{item}</Heading>
                              </CardHeader>
                              <CardBody>
                                <Text>{item}</Text>
                              </CardBody>
                              <CardFooter>
                                {!checkRoleAccess ? null : (
                                  <>
                                    {item.includes('Deployment') ? (
                                      <Button
                                        variant="primary"
                                        isDisabled={!updatedFilteredClusterList}
                                        onClick={() =>
                                          navigate(
                                            '/oran-du-cu-manager/advanced-view/custom-resources/create-deployment-custom-resource',
                                            {
                                              state: { updatedFilteredClusterList, item },
                                            }
                                          )
                                        }
                                      >
                                        {`Create ${item}`}
                                      </Button>
                                    ) : (
                                      <Button
                                        variant="primary"
                                        isDisabled={!updatedFilteredClusterList}
                                        onClick={() =>
                                          navigate(
                                            '/oran-du-cu-manager/advanced-view/custom-resources/create-config-custom-resource',
                                            {
                                              state: { updatedFilteredClusterList, item },
                                            }
                                          )
                                        }
                                      >
                                        {`Create ${item}`}
                                      </Button>
                                    )}
                                  </>
                                )}
                              </CardFooter>
                            </Card>
                          ))}
                        </SimpleGrid>
                      </Box>
                    )}
                    {/* Extra */}
                    {ExtraCrs.length > 0 && (
                      <Box mb="4">
                        <Heading as="h3" size="md" textAlign="center" mb="4">
                          Extra CRs
                        </Heading>
                        <SimpleGrid spacing="3" templateColumns="repeat(auto-fill, minmax(300px, 1fr))">
                          {ExtraCrs.map((item: CrCard, index: number) => (
                            <Card key={index} backgroundColor={TRAFFIC_WARNING_ORANGE}>
                              <CardHeader>
                                <Flex justify="space-between">
                                  <Heading size="md">{item.kind}</Heading>
                                  {checkRoleAccess && item.enabled === 'False' ? (
                                    <Button
                                      onClick={(e) => {
                                        handelDeleteCustomResource(item);
                                      }}
                                    >
                                      <DeleteIcon />
                                    </Button>
                                  ) : (
                                    <Tooltip label="Disable custom resource to configure" aria-label="A tooltip">
                                      <InfoOutlineIcon />
                                    </Tooltip>
                                  )}
                                </Flex>
                              </CardHeader>
                              <CardBody>
                                <Text>{item.kind}</Text>
                              </CardBody>
                              <CardFooter>
                                <>
                                  {loadingStates[item.name] ? (
                                    <Loader />
                                  ) : (
                                    <Button onClick={() => handleGetSingleCrData(item)}>{item.site}</Button>
                                  )}
                                </>
                              </CardFooter>
                            </Card>
                          ))}
                        </SimpleGrid>
                      </Box>
                    )}
                    {/* Pods */}
                    <Box width="100%">
                      {(selectedOption.type === 'CuCp' && completeCrs?.length === 3) ||
                      (selectedOption.type === 'Du' && completeCrs?.length === 3) ? (
                        <Button
                          width="100%"
                          variant="primary"
                          onClick={() =>
                            navigate('/oran-du-cu-manager/advanced-view/pods', {
                              state: { completeCrs, selectedOption },
                            })
                          }
                        >
                          Pods
                        </Button>
                      ) : null}
                    </Box>
                    {/* Buttons */}
                    <Box width="100%">
                      {selectedOption.type === 'CuUp' && completeCrs?.length === 2 ? (
                        <Button
                          width="100%"
                          variant="primary"
                          onClick={() =>
                            navigate('/oran-du-cu-manager/advanced-view/pods', {
                              state: { completeCrs, selectedOption },
                            })
                          }
                        >
                          Pods
                        </Button>
                      ) : null}
                    </Box>
                  </ErrorBoundary>
                ) : null}
              </Box>
            )}
          </Box>
          {/* The element to scroll to */}
          <div ref={bottomRef}></div>
          {/* Modal */}
          <Modal isOpen={isSingleCrOpen} onClose={onSingleCrClose} size="5xl" isCentered>
            <ModalOverlay bg="blackAlpha.900" />
            <ModalContent>
              <ModalHeader>
                <Heading as="h2" textAlign="center" mb="8">
                  {selectedItem?.name} custom resource
                </Heading>
                <Text fontSize="sm" pb="4">{`press ctrl+f(win) / cmd+f(mac) to search`}</Text>
                <Text fontSize="sm">{`click on the menu icon(3 dot upper right) > find & edit > find`}</Text>
              </ModalHeader>
              <ModalCloseButton />
              <ModalBody>{<ModalSubComponentRenderer data={singleCustomResource} />}</ModalBody>
            </ModalContent>
          </Modal>
        </Box>
      </Stack>
    </>
  );
};

export default CustomResources;
