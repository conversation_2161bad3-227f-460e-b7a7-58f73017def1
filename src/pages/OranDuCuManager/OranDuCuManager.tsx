import {
  Button,
  Circle,
  Container,
  Flex,
  Heading,
  HStack,
  SimpleGrid,
  Stack,
  Text,
  useColorModeValue,
  VStack,
} from '@chakra-ui/react';
import { useNavigate } from 'react-router-dom';

const OranDuCuManager = () => {
  const navigate = useNavigate();
  return (
    <>
      <Stack spacing="1">
        <Heading fontWeight="bold">O-RAN DU/CU Manager</Heading>
      </Stack>
      <Stack
        spacing={{
          base: '5',
          lg: '6',
        }}
      >
        <Container centerContent bg={useColorModeValue('white', 'gray.800')} py="12">
          <SimpleGrid
            width="50%"
            columns={{
              base: 1,
              md: 3,
            }}
            spacing="6"
          >
            {/* Simple View */}
            <Flex
              data-testid="simple-view"
              direction="column"
              alignItems="center"
              rounded="md"
              padding="8"
              position="relative"
              bg={useColorModeValue('white', 'gray.700')}
              shadow={{
                md: 'base',
              }}
            >
              <Circle size="6rem" bg={'bg-muted'}>
                <Text fontSize="4xl" color="blackAlpha.700" as="b">
                  S
                </Text>
              </Circle>
              <VStack spacing="1" flex="1" mt="3" mb="3">
                <HStack>
                  <Text marginBottom={'1.5rem'} fontWeight="bold">
                    Simple view
                  </Text>
                </HStack>
              </VStack>
              <Button
                variant="outline"
                colorScheme="brand.600"
                rounded="full"
                size="sm"
                width="full"
                //onClick={() => navigate('/oran-du-cu-manager/config-sets')}
                onClick={() => navigate('/oran-du-cu-manager/simple-view')}
              >
                Launch
              </Button>
            </Flex>
            {/* Advanced view */}
            <Flex
              data-testid="advanced-view"
              direction="column"
              alignItems="center"
              rounded="md"
              padding="8"
              position="relative"
              bg={useColorModeValue('white', 'gray.700')}
              shadow={{
                md: 'base',
              }}
            >
              <Circle size="6rem" bg={'bg-muted'}>
                <Text fontSize="4xl" color="blackAlpha.700" as="b">
                  A
                </Text>
              </Circle>
              <VStack spacing="1" flex="1" mt="3" mb="3">
                <HStack>
                  <Text marginBottom={'1.5rem'} fontWeight="bold">
                    Advanced view
                  </Text>
                </HStack>
              </VStack>
              <Button
                variant="outline"
                colorScheme="brand.600"
                rounded="full"
                size="sm"
                width="full"
                // onClick={() => navigate('/oran-du-cu-manager/custom-resources')}
                onClick={() => navigate('/oran-du-cu-manager/advanced-view')}
              >
                Launch
              </Button>
            </Flex>

            {/* CBRS Mangement */}
            <Flex
              data-testid="cbrs-mangement"
              direction="column"
              alignItems="center"
              rounded="md"
              padding="8"
              position="relative"
              bg={useColorModeValue('white', 'gray.700')}
              shadow={{
                md: 'base',
              }}
            >
              <Circle size="6rem" bg="bg-muted">
                <Text fontSize="4xl" color="blackAlpha.700" as="b">
                  C
                </Text>
              </Circle>
              <VStack spacing="1" flex="1" mt="3" mb="3">
                <HStack>
                  <Text marginBottom="1.5rem" fontWeight="bold" whiteSpace="nowrap">
                    CBRS Management
                  </Text>
                </HStack>
              </VStack>
              <Button
                variant="outline"
                colorScheme="brand.600"
                rounded="full"
                size="sm"
                width="full"
                onClick={() => navigate('/oran-du-cu-manager/cbrs-management/cbsd')}
              >
                Launch
              </Button>
            </Flex>
          </SimpleGrid>
        </Container>
      </Stack>
    </>
  );
};

export default OranDuCuManager;
