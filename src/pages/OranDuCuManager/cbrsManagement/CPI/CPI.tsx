import React from 'react';
import { Box, Container, Stack, Text } from '@chakra-ui/react';
import { useLocation } from 'react-router';
import CreateCPIForm from './createCPI/CreateCPIForm';
import EditCPIForm from './EditCPI/EditCPIForm';

const CPI = () => {
  const { pathname } = useLocation();
  const isEdit = (pathname as string).includes('edit');
  return (
    <Container
      py={{
        base: '4',
        md: '8',
      }}
    >
      <Stack spacing="5">
        <Text fontSize="2xl" fontWeight="medium" textAlign="center">
          {isEdit ? 'Edit CPI' : 'Create CPI'}
        </Text>
        <Box>{isEdit ? <EditCPIForm /> : <CreateCPIForm />}</Box>
      </Stack>
    </Container>
  );
};

export default CPI;
