import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useLocation, useNavigate } from 'react-router-dom';
import { useToast } from '@chakra-ui/react';
import { useMutation } from '@tanstack/react-query';
import { updateCPI } from '../../../../../services/orchestrator';
import { CPIForm } from '../../../../../types/duCuManager.type';
import { formatData } from '../helper';
import CPIFormFields from '../createCPI/CPIFormFields';

const EditCPIForm = () => {
  const { state } = useLocation();
  const navigate = useNavigate();
  const toast = useToast();
  const { rowData: cpiData } = state || {};

  const formMethods = useForm<CPIForm>({
    defaultValues: {
      email: '',
      installer_id: '',
      first_name: '',
      last_name: '',
      cpi_key: '',
    },
    mode: 'onChange',
  });

  const { mutateAsync: CPIMutation, isLoading: isCPIUpdating } = useMutation({
    mutationFn: updateCPI,
  });

  useEffect(() => {
    if (cpiData) {
      formMethods.reset(cpiData);
    }
  }, [cpiData, formMethods]);

  const onSubmit = async (data: CPIForm) => {
    const updatedData = formatData(cpiData, data);
    await CPIMutation(
      { cpi: updatedData, user_id: cpiData.id },
      {
        onSuccess: () => {
          toast({
            title: 'CPI updated.',
            description: 'The CPI has been successfully updated.',
            status: 'success',
            duration: 5000,
            isClosable: true,
            position: 'top',
          });
          navigate('/oran-du-cu-manager/cbrs-management/cpi');
        },
        onError: () => {
          toast({
            title: 'Error.',
            description: 'There was an error updating the CPI.',
            status: 'error',
            duration: 5000,
            isClosable: true,
            position: 'top',
          });
        },
      }
    );
  };

  return (
    <CPIFormFields
      formMethods={formMethods}
      onSubmit={onSubmit}
      isSubmitting={isCPIUpdating}
      isEdit={true}
      cpiData={cpiData}
    />
  );
};

export default EditCPIForm;
