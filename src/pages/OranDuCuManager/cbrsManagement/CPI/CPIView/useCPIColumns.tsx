import React, { useMemo } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { CPIForm } from '../../../../../types/duCuManager.type';
import CPIConfigMenu from './CPIConfigMenu';
import useLogin from '../../../../../hooks/useLogin';
import { AUTH_TOKEN_KEY, READ_WRITE_ACCESS_ROLES } from '../../../../../data/constants';

export default function useCPIColumns() {
  const { checkNmsDevAccess } = useLogin(AUTH_TOKEN_KEY);
  const checkRoleAccess = checkNmsDevAccess(READ_WRITE_ACCESS_ROLES);

  return useMemo<ColumnDef<CPIForm>[]>(
    () => [
      {
        header: 'Email',
        accessorKey: 'email',
        id: 'email',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        header: 'Installer Id',
        accessorKey: 'installer_id',
        id: 'installer_id',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        header: 'First Name',
        accessorKey: 'first_name',
        id: 'first_name',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        header: 'Last Name',
        accessorKey: 'last_name',
        id: 'last_name',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        header: '',
        accessorKey: 'config',
        id: 'config',
        enableSorting: false,
        cell: ({ row }) => {
          return checkRoleAccess && <CPIConfigMenu rowData={row?.original} />;
        },
      },
    ],
    []
  );
}
