import React from 'react';
import { Box, Flex, Heading, Stack, useColorModeValue } from '@chakra-ui/react';
import { DataTable } from '../../../../MetricsCollector/components/DataTable';
import { ErrorBoundary } from 'react-error-boundary';
import {
  ErrorBoundaryFallback,
  ErrorBoundaryLogError,
} from '../../../../../components/errorComponents/ErrorBoundaryFallback';
import useCPIColumns from './useCPIColumns';
import useGetCPI from '../../../hooks/services/use_GetCPI';
import Loader from '../../../../../components/loader/Loader';
import QueryError from '../../../../../components/errorComponents/QueryError';

const CPIView = () => {
  const colorModeValue = useColorModeValue('sm', 'sm-dark');
  const columns = useCPIColumns();

  const { isLoading, error, data } = useGetCPI();
  if (isLoading) return <Loader />;
  if (error) return <QueryError error={error} />;

  return (
    <Flex
      px={{
        base: '4',
        md: '8',
      }}
      flexDirection="column"
      mt="2"
    >
      <Stack
        spacing="1"
        direction={{
          base: 'column',
          lg: 'row',
        }}
        justify="center"
      >
        <Stack spacing="1" data-testid="cpi-heading">
          <Heading fontWeight="medium">Certified Professional Installer (CPI)</Heading>
        </Stack>
      </Stack>
      <Stack
        spacing={{
          base: '5',
          lg: '6',
        }}
      >
        <Box bg="bg-surface" padding="8">
          <Flex
            direction="column"
            mt="4"
            gap="2"
            boxShadow={{
              base: 'none',
              md: colorModeValue,
            }}
            data-testid="cpi-data-container"
          >
            <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError as any}>
              <DataTable
                isExpandable={false}
                columns={columns}
                data={data.users}
                pageSizeOptions={[100, 200, 300, 400, 500, 600, 700, 800, 900, 1000]}
                defaultPageSize={100}
                count={data?.count}
                hasEmptyResult={data?.count === 0}
                version={'v2'}
              />
            </ErrorBoundary>
          </Flex>
        </Box>
      </Stack>
    </Flex>
  );
};

export default CPIView;
