import React, { useState } from 'react';
import { Icon<PERSON>utton, <PERSON>u, Menu<PERSON>utton, MenuItem, MenuList } from '@chakra-ui/react';
import { BsThreeDots } from 'react-icons/bs';
import { DeleteIcon, EditIcon } from '@chakra-ui/icons';
import { useNavigate } from 'react-router-dom';
import { CPIForm } from '../../../../../types/duCuManager.type';
import GenericAlertDialog from '../../../../../components/AlertDialog';
import { useDeleteCPI } from '../../../hooks/services/useDeleteCPI';

const CPIConfigMenu = ({ rowData }: { rowData: CPIForm }) => {
  const navigate = useNavigate();
  const { id: userId } = rowData;
  const { deleteCPIDevicesMutation } = useDeleteCPI(userId);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleDeleteConfirm = async (rowData: CPIForm) => {
    await deleteCPIDevicesMutation(rowData);
    setIsDialogOpen(false);
  };
  return (
    <>
      <Menu data-testid="cpi-menu-items">
        <MenuButton
          data-testid="cpi-menu-items-button"
          onClick={(e) => {
            e.stopPropagation();
          }}
          as={IconButton}
          aria-label="Options"
          icon={<BsThreeDots />}
          variant="outline"
          border="none"
        />
        <MenuList minWidth="110px">
          <MenuItem
            data-testid="edit-cpi"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              localStorage.removeItem('cellRefId');
              navigate('/oran-du-cu-manager/cbrs-management/cpi/edit', {
                state: { rowData },
              });
            }}
          >
            <EditIcon mr="1rem" />
            Edit CPI
          </MenuItem>
          <MenuItem
            data-testid="delete-cbsd"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              setIsDialogOpen(true);
            }}
          >
            <DeleteIcon mr="1rem" />
            Delete
          </MenuItem>
        </MenuList>
      </Menu>
      {isDialogOpen && (
        <GenericAlertDialog
          title="Delete CPI Device"
          message={`Are you sure you want to delete the device with serial ${userId}? This action cannot be undone.`}
          confirmButtonText="Delete"
          cancelButtonText="Cancel"
          onConfirm={() => handleDeleteConfirm(rowData)}
          confirmButtonColorScheme="teal"
          onCancel={() => setIsDialogOpen(false)}
          width="650px"
        />
      )}
    </>
  );
};

export default CPIConfigMenu;
