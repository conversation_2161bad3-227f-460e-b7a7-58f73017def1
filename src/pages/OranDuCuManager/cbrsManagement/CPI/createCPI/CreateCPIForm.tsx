import React from 'react';
import { useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@chakra-ui/react';
import { useMutation } from '@tanstack/react-query';
import { createCPI } from '../../../../../services/orchestrator';
import { CPIForm } from '../../../../../types/duCuManager.type';
import CPIFormFields from './CPIFormFields';

const CreateCPIForm = () => {
  const navigate = useNavigate();
  const toast = useToast();

  const formMethods = useForm<CPIForm>({
    defaultValues: {
      email: '',
      installer_id: '',
      first_name: '',
      last_name: '',
      cpi_key: '',
    },
    mode: 'onChange',
  });

  const { mutateAsync: CPIMutation, isLoading: isCPIPosting } = useMutation({
    mutationFn: createCPI,
  });

  const onSubmit = async (data: CPIForm) => {
    await CPIMutation(
      { cpi: data },
      {
        onSuccess: () => {
          toast({
            title: 'CPI created.',
            description: 'The CPI has been successfully created.',
            status: 'success',
            duration: 5000,
            isClosable: true,
            position: 'top',
          });
          navigate('/oran-du-cu-manager/cbrs-management/cpi');
        },
        onError: () => {
          toast({
            title: 'Error.',
            description: 'There was an error creating the CPI.',
            status: 'error',
            duration: 5000,
            isClosable: true,
            position: 'top',
          });
        },
      }
    );
  };

  return <CPIFormFields formMethods={formMethods} onSubmit={onSubmit} isSubmitting={isCPIPosting} isEdit={false} />;
};

export default CreateCPIForm;
