import {
  Box,
  FormControl,
  FormLabel,
  Stack,
  Button,
  useColorModeValue,
  Input,
  FormErrorMessage,
  Textarea,
  Flex,
  Divider,
  SimpleGrid,
  Text,
} from '@chakra-ui/react';
import { SubmitHandler, UseFormReturn } from 'react-hook-form';
import { CPIForm } from '../../../../../types/duCuManager.type';
import { useNavigate } from 'react-router-dom';
import { useState } from 'react';
import { formatData } from '../helper';

interface CPIFormFieldsProps {
  formMethods: UseFormReturn<CPIForm>;
  onSubmit: SubmitHandler<CPIForm>;
  isSubmitting: boolean;
  isEdit?: boolean;
  cpiData?: CPIForm;
}

const CPIFormFields: React.FC<CPIFormFieldsProps> = ({ formMethods, onSubmit, isSubmitting, isEdit, cpiData }) => {
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, isValid },
  } = formMethods;
  const [isCpiKeyVisible, setIsCpiKeyVisible] = useState(!isEdit);

  const navigate = useNavigate();
  const watchAllFields = watch();

  return (
    <Stack
      spacing={{
        base: '5',
        lg: '6',
      }}
      w={{
        lg: '7xl',
      }}
      margin="auto"
    >
      <Box
        boxShadow={{
          base: 'none',
          md: useColorModeValue('sm', 'sm-dark'),
        }}
        borderRadius="lg"
        borderWidth="1px"
        padding="8"
        as="form"
        onSubmit={handleSubmit(onSubmit)}
      >
        <SimpleGrid
          columns={{ base: 1, md: 2 }}
          spacing="12"
          p={{
            base: '7',
            md: '8',
          }}
          mb="6"
        >
          <FormControl isInvalid={!!errors.email} isRequired mt="4">
            <FormLabel htmlFor="email">Email address</FormLabel>
            <Input
              type="email"
              placeholder="Please enter an email"
              {...register('email', { required: 'Email is required' })}
            />
            <FormErrorMessage>{errors.email?.message}</FormErrorMessage>
          </FormControl>

          <FormControl isInvalid={!!errors.installer_id} isRequired mt="4">
            <FormLabel>Installer ID</FormLabel>
            <Input
              placeholder="Please enter an installer id"
              {...register('installer_id', { required: 'Installer ID is required' })}
            />
            <FormErrorMessage>{errors.installer_id?.message}</FormErrorMessage>
          </FormControl>

          <FormControl mt="4">
            <FormLabel>First name</FormLabel>
            <Input placeholder="Please enter a first name" {...register('first_name')} />
          </FormControl>

          <FormControl mt="4">
            <FormLabel>Last name</FormLabel>
            <Input placeholder="Please enter a last name" {...register('last_name')} />
          </FormControl>
          <FormControl isInvalid={!!errors.cpi_key} isRequired={!isEdit} mt="4">
            <FormLabel>CPI Key</FormLabel>
            {isCpiKeyVisible ? (
              <Textarea
                placeholder="Please enter a cpi key"
                {...register('cpi_key', isEdit ? {} : { required: 'CPI Key is required' })}
              />
            ) : (
              <Text
                style={{ color: 'cornflowerblue', textDecoration: 'underline' }}
                onClick={() => setIsCpiKeyVisible(true)}
              >
                change CPI key
              </Text>
            )}
            <FormErrorMessage>{errors.cpi_key?.message}</FormErrorMessage>
          </FormControl>
        </SimpleGrid>
        <Divider />
        <Flex
          direction="row-reverse"
          justifyContent="space-between"
          py="4"
          px={{
            base: '4',
            md: '6',
          }}
        >
          <Button
            data-testid="submit-cpi"
            colorScheme="teal"
            type="submit"
            isLoading={isSubmitting}
            loadingText={isEdit ? 'Updating...' : 'Creating...'}
            isDisabled={!isValid || !Object.keys(formatData(cpiData, watchAllFields)).length}
          >
            {isEdit ? 'Update' : 'Create'}
          </Button>
          <Button
            colorScheme="teal"
            variant="outline"
            onClick={() => navigate('/oran-du-cu-manager/cbrs-management/cpi')}
          >
            Cancel
          </Button>
        </Flex>
      </Box>
    </Stack>
  );
};

export default CPIFormFields;
