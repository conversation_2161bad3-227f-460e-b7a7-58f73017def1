import React from 'react';
import { Box, Flex, Heading, Stack, useColorModeValue } from '@chakra-ui/react';
import { DataTable } from '../../../MetricsCollector/components/DataTable';
import { CBSDtype, CBSDHeader } from '../../../../types/duCuManager.type';
import {
  ErrorBoundaryFallback,
  ErrorBoundaryLogError,
} from '../../../../components/errorComponents/ErrorBoundaryFallback';
import { ErrorBoundary } from 'react-error-boundary';
import useGetCBSDDevices from '../../hooks/services/use_GetCBSDDevices';
import Loader from '../../../../components/loader/Loader';
import QueryError from '../../../../components/errorComponents/QueryError';
import useCBSDColumns from './useCBSDColumns';
import DisplayObject from '../../tables/DisplayObject';

const CBSD = () => {
  const colorModeValue = useColorModeValue('sm', 'sm-dark');
  const columns = useCBSDColumns();
  const { isLoading, error, data } = useGetCBSDDevices();
  if (isLoading) return <Loader />;
  if (error) return <QueryError error={error} />;

  const filterCbsdsData = (cbsds: CBSDtype[]) => {
    return cbsds.map((item) => ({
      displayName: item.displayName,
      cbrsId: item.id,
      fccId: item.fccId,
      serial: item.serialNumber,
      registered: (item.state === 'REGISTERED')?.toString(),
      grant: item.requestGrant?.toString(),
      heartbeat: Boolean(item.activeHeartbeats)?.toString(),
      state: item.state,
    }));
  };

  const cbsdData: CBSDHeader[] = filterCbsdsData(data.cbsds);

  return (
    <Flex
      px={{
        base: '4',
        md: '8',
      }}
      flexDirection="column"
      mt="2"
    >
      <Stack
        spacing="1"
        direction={{
          base: 'column',
          lg: 'row',
        }}
        justify="center"
      >
        <Stack spacing="1" data-testid="cbsd-heading">
          <Heading fontWeight="bold">Citizens Broadband Radio Service Devices (CBSD)</Heading>
        </Stack>
      </Stack>
      <Stack
        spacing={{
          base: '5',
          lg: '6',
        }}
      >
        <Box bg="bg-surface" padding="8">
          <Flex
            direction="column"
            mt="4"
            gap="2"
            boxShadow={{
              base: 'none',
              md: colorModeValue,
            }}
            data-testid="cbsd-data-container"
          >
            <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError as any}>
              <DataTable
                columns={columns}
                data={cbsdData}
                renderSubComponent={({ row }) => {
                  const displayNameToFilter = row.original.displayName;

                  return (
                    <Box p={4}>
                      <DisplayObject
                        data={data?.cbsds || {}}
                        filterCriteria={(value) => value.displayName === displayNameToFilter}
                      />
                    </Box>
                  );
                }}
                isExpandable={true}
                pageSizeOptions={[100, 200, 300, 400, 500, 600, 700, 800, 900, 1000]}
                defaultPageSize={100}
                count={data?.count}
                hasEmptyResult={data?.count === 0}
                version={'v2'}
              />
            </ErrorBoundary>
          </Flex>
        </Box>
      </Stack>
    </Flex>
  );
};

export default CBSD;
