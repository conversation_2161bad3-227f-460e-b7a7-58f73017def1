import React, { useEffect } from 'react';
import { Stack, Input, Select, Button, Box, Flex, FormControl, FormLabel, Icon, Heading, Text } from '@chakra-ui/react';
import { useFormContext } from 'react-hook-form';
import { RepeatIcon } from '@chakra-ui/icons';
import { FormValues } from '../../../../../types/duCuManager.type';

const ConfigForm: React.FC<{ isEdit?: boolean }> = ({ isEdit = false }) => {
  const {
    register,
    reset,
    setValue,
    getValues,
    formState: { errors },
  } = useFormContext<FormValues>();

  useEffect(() => {
    const formData = getValues('configFormData');
    Object.entries(formData).forEach(([key, value]) => {
      setValue(`configFormData.${key as keyof typeof formData}`, value);
    });
  }, [getValues, setValue]);

  return (
    <Stack>
      <Box marginX="auto" width="full" bg="bg-surface">
        <Flex display="flex" padding="4" justifyContent="space-between">
          <Text fontSize="3xl" fontWeight="bold" textAlign="center">
            Config
          </Text>
          {!isEdit && (
            <Button
              variant="primary"
              leftIcon={<Icon as={RepeatIcon} marginStart="-1" />}
              onClick={() =>
                reset({
                  deviceFormData: getValues('deviceFormData'),
                  installationFormData: getValues('installationFormData'),
                  configFormData: {
                    vendor: '',
                    model: '',
                    softwareVersion: '',
                    hardwareVersion: '',
                    firmwareVersion: '',
                    radioTechnology: 'NR',
                    supportedSpec: '',
                  },
                })
              }
              data-testid="config-reset-form"
            >
              Reset Config
            </Button>
          )}
        </Flex>
        <Stack
          spacing="5"
          p={{
            base: '7',
            md: '8',
          }}
        >
          <Heading fontSize="x-large" fontWeight="semi-bold">
            Model
          </Heading>
          <Stack
            spacing="16"
            direction={{
              base: 'column',
              md: 'row',
            }}
          >
            <FormControl isInvalid={Boolean(errors?.configFormData?.vendor)}>
              <FormLabel htmlFor="vendor">Vendor</FormLabel>
              <Input id="vendor" placeholder="Please add a vendor" {...register('configFormData.vendor')} />
            </FormControl>

            <FormControl isInvalid={Boolean(errors?.configFormData?.model)}>
              <FormLabel htmlFor="model">Name</FormLabel>
              <Input id="model" placeholder="Please add a name" {...register('configFormData.model')} />
            </FormControl>
          </Stack>

          <Stack
            spacing="16"
            direction={{
              base: 'column',
              md: 'row',
            }}
          >
            <FormControl isInvalid={Boolean(errors?.configFormData?.softwareVersion)}>
              <FormLabel htmlFor="softwareVersion">Software Version</FormLabel>
              <Input
                id="softwareVersion"
                placeholder="Please add software version"
                {...register('configFormData.softwareVersion')}
              />
            </FormControl>

            <FormControl isInvalid={Boolean(errors?.configFormData?.hardwareVersion)}>
              <FormLabel htmlFor="hardwareVersion">Hardware Version</FormLabel>
              <Input
                id="hardwareVersion"
                placeholder="Please add hardware version"
                {...register('configFormData.hardwareVersion')}
              />
            </FormControl>

            <FormControl isInvalid={Boolean(errors?.configFormData?.firmwareVersion)}>
              <FormLabel htmlFor="firmwareVersion">Firmware Version</FormLabel>
              <Input
                id="firmwareVersion"
                placeholder="Please add firmware version"
                {...register('configFormData.firmwareVersion')}
              />
            </FormControl>
          </Stack>
          <Heading mt="4" fontSize="x-large" fontWeight="semi-bold">
            Air Interface
          </Heading>
          <Stack
            spacing="16"
            direction={{
              base: 'column',
              md: 'row',
            }}
          >
            <FormControl isInvalid={Boolean(errors?.configFormData?.radioTechnology)}>
              <FormLabel htmlFor="radioTechnology">Radio Technology</FormLabel>
              <Select id="radioTechnology" {...register('configFormData.radioTechnology')}>
                <option value="E_UTRA">E_UTRA</option>
                <option value="NR">NR</option>
              </Select>
            </FormControl>

            <FormControl isInvalid={Boolean(errors?.configFormData?.supportedSpec)}>
              <FormLabel htmlFor="supportedSpec">Supported Spec</FormLabel>
              <Input
                id="supportedSpec"
                placeholder="Please add supported spec"
                {...register('configFormData.supportedSpec')}
              />
            </FormControl>
          </Stack>
        </Stack>
      </Box>
    </Stack>
  );
};

export default ConfigForm;
