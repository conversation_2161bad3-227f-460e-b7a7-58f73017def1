import { z } from 'zod';

export const installationFormSchema = z.object({
  latitude: z
    .string()
    .optional()
    .nullable()
    .refine((val) => !isNaN(Number(val)), { message: 'Latitude must be a number' }),
  longitude: z
    .string()
    .nullable()
    .optional()
    .refine((val) => !isNaN(Number(val)), { message: 'Longitude must be a number' }),
  height: z
    .string()
    .optional()
    .nullable()
    .refine((val) => !isNaN(Number(val)), { message: 'Height must be a number' }),
  horizontalAccuracy: z
    .string()
    .optional()
    .nullable()
    .refine((val) => !isNaN(Number(val)), { message: 'Horizontal accuracy must be a number' }),
  verticalAccuracy: z
    .string()
    .nullable()
    .optional()
    .refine((val) => !isNaN(Number(val)), { message: 'Vertical accuracy must be a number' }),
  indoorDeployment: z.boolean().optional(),
  antennaModel: z.string().optional().nullable(),
  eirpCapability: z
    .string()
    .optional()
    .nullable()
    .refine((val) => !isNaN(Number(val)), { message: 'EIRP must be a number' }),
  antennaBeamwidth: z
    .string()
    .optional()
    .nullable()
    .refine((val) => !isNaN(Number(val)), { message: 'Beamwidth must be a number' }),
  antennaAzimuth: z
    .string()
    .optional()
    .nullable()
    .refine((val) => !isNaN(Number(val)), { message: 'Azimuth must be a number' }),
  antennaDowntilt: z
    .string()
    .optional()
    .nullable()
    .refine((val) => !isNaN(Number(val)), { message: 'Downtilt must be a number' }),
  antennaGain: z
    .string()
    .nullable()
    .optional()
    .refine((val) => !isNaN(Number(val)), { message: 'Gain must be a number' }),
});
