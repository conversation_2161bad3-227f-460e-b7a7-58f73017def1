import React, { useEffect } from 'react';
import {
  Stack,
  Input,
  Button,
  Box,
  Flex,
  FormControl,
  FormLabel,
  Icon,
  Heading,
  Text,
  Checkbox,
  FormErrorMessage,
  Select,
} from '@chakra-ui/react';
import { useFormContext, Controller } from 'react-hook-form';
import { RepeatIcon } from '@chakra-ui/icons';
import { FormValues } from '../../../../../types/duCuManager.type';

const InstallationForm: React.FC<{ isEdit?: boolean }> = ({ isEdit = false }) => {
  const {
    control,
    reset,
    setValue,
    getValues,
    register,
    formState: { errors },
  } = useFormContext<FormValues['installationFormData']>();

  useEffect(() => {
    const formData = getValues();
    Object.entries(formData).forEach(([key, value]) => {
      setValue(key as keyof typeof formData, value);
    });
  }, [getValues, setValue]);

  return (
    <Stack>
      <Box marginX="auto" width="full" bg="bg-surface">
        <Flex display="flex" padding="4" justifyContent="space-between">
          <Text fontSize="3xl" fontWeight="bold" textAlign="center">
            Installation
          </Text>
          {!isEdit && (
            <Button
              variant="primary"
              leftIcon={<Icon as={RepeatIcon} marginStart="-1" />}
              onClick={() =>
                reset({
                  latitude: null,
                  longitude: null,
                  height: null,
                  heightType: 'HEIGHT_TYPE_AGL',
                  horizontalAccuracy: null,
                  verticalAccuracy: null,
                  indoorDeployment: false,
                  antennaModel: '',
                  eirpCapability: null,
                  antennaBeamwidth: null,
                  antennaAzimuth: null,
                  antennaDowntilt: null,
                  antennaGain: null,
                })
              }
              data-testid="installation-reset-form"
            >
              Reset Installation
            </Button>
          )}
        </Flex>
        <Stack
          spacing="5"
          p={{
            base: '7',
            md: '8',
          }}
        >
          <Stack
            spacing="16"
            direction={{
              base: 'column',
              md: 'row',
            }}
          >
            <FormControl isInvalid={!!errors.latitude}>
              <FormLabel htmlFor="latitude">Latitude</FormLabel>
              <Controller
                name="latitude"
                control={control}
                render={({ field }) => (
                  <Input
                    id="latitude"
                    data-testid="latitude"
                    placeholder="Please add latitude"
                    {...field}
                    value={field.value ?? ''}
                  />
                )}
              />
              <FormErrorMessage>{errors.latitude?.message}</FormErrorMessage>
            </FormControl>

            <FormControl isInvalid={!!errors.longitude}>
              <FormLabel htmlFor="longitude">Longitude</FormLabel>
              <Controller
                name="longitude"
                control={control}
                render={({ field }) => (
                  <Input
                    id="longitude"
                    data-testid="longitude"
                    placeholder="Please add longitude"
                    {...field}
                    value={field.value ?? ''}
                  />
                )}
              />
              <FormErrorMessage>{errors.longitude?.message}</FormErrorMessage>
            </FormControl>
          </Stack>

          <Stack
            spacing="16"
            direction={{
              base: 'column',
              md: 'row',
            }}
          >
            <FormControl isInvalid={!!errors.height}>
              <FormLabel htmlFor="height">Height</FormLabel>
              <Controller
                name="height"
                control={control}
                render={({ field }) => (
                  <Input id="height" placeholder="Please add height" {...field} value={field.value ?? ''} />
                )}
              />
              <FormErrorMessage>{errors.height?.message}</FormErrorMessage>
            </FormControl>
            <FormControl isInvalid={Boolean(errors?.heightType)}>
              <FormLabel htmlFor="heightType">Height Type</FormLabel>
              <Select id="heightType" {...register('heightType')}>
                <option value="HEIGHT_TYPE_AGL">AGL</option>
                <option value="HEIGHT_TYPE_AMSL">AMSL</option>
                <option value="HEIGHT_TYPE_UNSPECIFIED">UNSPECIFIED</option>
              </Select>
            </FormControl>

            <FormControl isInvalid={!!errors.horizontalAccuracy}>
              <FormLabel htmlFor="horizontalAccuracy">Horizontal Accuracy</FormLabel>
              <Controller
                name="horizontalAccuracy"
                control={control}
                render={({ field }) => (
                  <Input
                    id="horizontalAccuracy"
                    placeholder="Please add horizontal accuracy"
                    {...field}
                    value={field.value ?? ''}
                  />
                )}
              />
              <FormErrorMessage>{errors.horizontalAccuracy?.message}</FormErrorMessage>
            </FormControl>

            <FormControl isInvalid={!!errors.verticalAccuracy}>
              <FormLabel htmlFor="verticalAccuracy">Vertical Accuracy</FormLabel>
              <Controller
                name="verticalAccuracy"
                control={control}
                render={({ field }) => (
                  <Input
                    id="verticalAccuracy"
                    placeholder="Please add vertical accuracy"
                    {...field}
                    value={field.value ?? ''}
                  />
                )}
              />
              <FormErrorMessage>{errors.verticalAccuracy?.message}</FormErrorMessage>
            </FormControl>
          </Stack>

          <FormControl>
            <Checkbox {...register('indoorDeployment')} colorScheme="teal">
              Indoor deployment
            </Checkbox>
          </FormControl>

          <Heading mt="2" fontSize="x-large" fontWeight="semi-bold">
            Antenna
          </Heading>
          <Stack
            spacing="16"
            direction={{
              base: 'column',
              md: 'row',
            }}
          >
            <FormControl isInvalid={!!errors.antennaModel}>
              <FormLabel htmlFor="antennaModel">Model</FormLabel>
              <Controller
                name="antennaModel"
                control={control}
                render={({ field }) => (
                  <Input id="antennaModel" placeholder="Please add model" {...field} value={field.value ?? ''} />
                )}
              />
              <FormErrorMessage>{errors.antennaModel?.message}</FormErrorMessage>
            </FormControl>

            <FormControl isInvalid={!!errors.eirpCapability}>
              <FormLabel htmlFor="eirpCapability">EIRP</FormLabel>
              <Controller
                name="eirpCapability"
                control={control}
                render={({ field }) => (
                  <Input id="eirpCapability" placeholder="Please add EIRP" {...field} value={field.value ?? ''} />
                )}
              />
              <FormErrorMessage>{errors.eirpCapability?.message}</FormErrorMessage>
            </FormControl>

            <FormControl isInvalid={!!errors.antennaBeamwidth}>
              <FormLabel htmlFor="antennaBeamwidth">Beamwidth</FormLabel>
              <Controller
                name="antennaBeamwidth"
                control={control}
                render={({ field }) => (
                  <Input
                    id="antennaBeamwidth"
                    placeholder="Please add beamwidth"
                    {...field}
                    value={field.value ?? ''}
                  />
                )}
              />
              <FormErrorMessage>{errors.antennaBeamwidth?.message}</FormErrorMessage>
            </FormControl>
          </Stack>

          <Stack
            spacing="16"
            direction={{
              base: 'column',
              md: 'row',
            }}
          >
            <FormControl isInvalid={!!errors.antennaAzimuth}>
              <FormLabel htmlFor="antennaAzimuth">Azimuth</FormLabel>
              <Controller
                name="antennaAzimuth"
                control={control}
                render={({ field }) => (
                  <Input id="antennaAzimuth" placeholder="Please add azimuth" {...field} value={field.value ?? ''} />
                )}
              />
              <FormErrorMessage>{errors.antennaAzimuth?.message}</FormErrorMessage>
            </FormControl>

            <FormControl isInvalid={!!errors.antennaDowntilt}>
              <FormLabel htmlFor="antennaDowntilt">Downtilt</FormLabel>
              <Controller
                name="antennaDowntilt"
                control={control}
                render={({ field }) => (
                  <Input
                    id="antennaDowntilt"
                    placeholder="Please add a downtilt"
                    {...field}
                    value={field.value ?? ''}
                  />
                )}
              />
              <FormErrorMessage>{errors.antennaDowntilt?.message}</FormErrorMessage>
            </FormControl>

            <FormControl isInvalid={!!errors.antennaGain}>
              <FormLabel htmlFor="antennaGain">Gain</FormLabel>
              <Controller
                name="antennaGain"
                control={control}
                render={({ field }) => (
                  <Input id="antennaGain" placeholder="Please add gain" {...field} value={field.value ?? ''} />
                )}
              />
              <FormErrorMessage>{errors.antennaGain?.message}</FormErrorMessage>
            </FormControl>
          </Stack>
        </Stack>
      </Box>
    </Stack>
  );
};

export default InstallationForm;
