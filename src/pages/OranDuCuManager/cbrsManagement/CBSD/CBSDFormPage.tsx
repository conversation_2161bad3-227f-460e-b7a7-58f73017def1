import { Box, Container, Stack, Text, useColorModeValue } from '@chakra-ui/react';
import { useLocation } from 'react-router';
import CreateCBSDForm from './createCBSD/CreateCBSDForm';
import EditCBSDForm from './editCBSD/EditCBSDForm';

const CBSDFormPage = () => {
  const { pathname } = useLocation();
  const boxShadow = useColorModeValue('sm', 'sm-dark');
  const isEdit = (pathname as string).includes('edit');
  return (
    <Container
      py={{
        base: '4',
        md: '8',
      }}
    >
      <Stack spacing="5">
        <Text fontSize="2xl" fontWeight="medium" textAlign="center">
          {isEdit ? 'Edit CBSD' : 'Create CBSD'}
        </Text>
        <Box
          marginX="auto"
          width="full"
          maxW={{
            lg: '8xl',
          }}
          bg="bg-surface"
          boxShadow={boxShadow}
          borderRadius="lg"
        >
          {isEdit ? <EditCBSDForm /> : <CreateCBSDForm />}
        </Box>
      </Stack>
    </Container>
  );
};

export default CBSDFormPage;
