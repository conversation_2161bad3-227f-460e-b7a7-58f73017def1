export const getChangedFields = (
  original: Record<string, any>,
  current: Record<string, any>
): Record<string, { original: any; current: any }> => {
  return Object.keys(current).reduce((acc, key) => {
    if (
      (current[key] || original[key]) &&
      current[key] !== original[key] &&
      !(original[key] === 0 && current[key] === '0')
    ) {
      acc[key] = { original: original[key], current: current[key] };
    }
    return acc;
  }, {} as Record<string, { original: any; current: any }>);
};
