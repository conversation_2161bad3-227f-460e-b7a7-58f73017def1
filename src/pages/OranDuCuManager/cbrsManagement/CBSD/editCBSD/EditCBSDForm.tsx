import React, { useEffect, useState } from 'react';
import {
  Box,
  Button,
  Divider,
  Flex,
  Stack,
  useColorModeValue,
  useToast,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
  Text,
} from '@chakra-ui/react';
import { useLocation, useNavigate } from 'react-router-dom';
import { FormValues } from '../../../../../types/duCuManager.type';
import { useForm, FormProvider, useWatch } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { editCBSD } from '../../../../../services/orchestrator';
import { useMutation } from '@tanstack/react-query';
import { useGetCluster } from '../../../hooks/services/use_GetCBSD';
import Loader from '../../../../../components/loader/Loader';
import QueryError from '../../../../../components/errorComponents/QueryError';
import useGetCBSDDevicesBySerialNumber from '../../../hooks/services/use_GetCBSDDevicesBySerialNumber';
import { formatData } from '../../helper';
import InstallationForm from '../forms/InstallationForm';
import ConfigForm from '../forms/ConfigForm';
import DeviceForm from '../forms/DeviceForm';
import { installationFormSchema } from '../forms/schema';
import { getChangedFields } from './helper';

export type CBSDDevice = Record<string, any>;

const EditCBSDForm = () => {
  const { state = {} } = useLocation();
  const { data: cbsdDevices = {} } = useGetCBSDDevicesBySerialNumber(state?.serial, true);
  const navigate = useNavigate();
  const boxShadow = useColorModeValue('sm', 'sm-dark');
  const [isDeviceFormValid, setIsDeviceFormValid] = useState(false);
  const [isPreview, setIsPreview] = useState(false);
  const toast = useToast();
  const { isOpen, onOpen, onClose } = useDisclosure();

  const methods = useForm<FormValues>({
    defaultValues: {
      deviceFormData: {
        displayName: '',
        serialNumber: '',
        fccId: '',
        clusterId: '',
        userId: 'DenseAir',
        category: 'A',
      },
      configFormData: {
        vendor: '',
        model: '',
        softwareVersion: '',
        hardwareVersion: '',
        firmwareVersion: '',
        radioTechnology: 'NR',
        supportedSpec: '',
      },
    },
  });

  const installationMethods = useForm<FormValues['installationFormData']>({
    resolver: zodResolver(installationFormSchema),
    mode: 'onChange',
    defaultValues: {
      latitude: null,
      longitude: null,
      height: null,
      heightType: 'HEIGHT_TYPE_AGL',
      horizontalAccuracy: null,
      verticalAccuracy: null,
      indoorDeployment: false,
      antennaModel: '',
      eirpCapability: null,
      antennaBeamwidth: null,
      antennaAzimuth: null,
      antennaDowntilt: null,
      antennaGain: null,
    },
  });

  useEffect(() => {
    if (cbsdDevices) {
      methods.reset({
        deviceFormData: {
          displayName: cbsdDevices?.displayName || '',
          serialNumber: cbsdDevices?.serialNumber || '',
          fccId: cbsdDevices?.fccId || '',
          clusterId: cbsdDevices?.clusterId || '',
          userId: 'DenseAir',
          category: cbsdDevices?.category,
        },
        configFormData: {
          vendor: cbsdDevices?.cbsdInfo?.vendor || '',
          model: cbsdDevices?.cbsdInfo?.model || '',
          softwareVersion: cbsdDevices?.cbsdInfo?.softwareVersion || '',
          hardwareVersion: cbsdDevices?.cbsdInfo?.hardwareVersion || '',
          firmwareVersion: cbsdDevices?.cbsdInfo?.firmwareVersion || '',
          radioTechnology: cbsdDevices?.airInterface?.radioTechnology || 'NR',
          supportedSpec: cbsdDevices?.airInterface?.supportedSpec || '',
        },
      });

      installationMethods.reset({
        latitude: cbsdDevices?.installationParam?.latitude?.toString() || null,
        longitude: cbsdDevices?.installationParam?.longitude?.toString() || null,
        height: cbsdDevices?.installationParam?.height?.toString() || null,
        heightType: cbsdDevices?.installationParam?.heightType || 'HEIGHT_TYPE_AGL',
        horizontalAccuracy: cbsdDevices?.installationParam?.horizontalAccuracy?.toString() || null,
        verticalAccuracy: cbsdDevices?.installationParam?.verticalAccuracy?.toString() || null,
        indoorDeployment: cbsdDevices?.installationParam?.indoorDeployment || false,
        antennaModel: cbsdDevices?.installationParam?.antennaModel || '',
        eirpCapability: cbsdDevices?.installationParam?.eirpCapability?.toString() || null,
        antennaBeamwidth: cbsdDevices?.installationParam?.antennaBeamwidth?.toString() || null,
        antennaAzimuth: cbsdDevices?.installationParam?.antennaAzimuth?.toString() || null,
        antennaDowntilt: cbsdDevices?.installationParam?.antennaDowntilt?.toString() || null,
        antennaGain: cbsdDevices?.installationParam?.antennaGain?.toString() || null,
      });
    }
  }, [cbsdDevices, methods, installationMethods]);

  const deviceFormData = useWatch({ control: methods.control, name: 'deviceFormData' });
  const configFormData = useWatch({ control: methods.control, name: 'configFormData' });
  const installationFormData = useWatch({ control: installationMethods.control });

  const triggerInitialValidation = async () => {
    await installationMethods.trigger();
  };

  useEffect(() => {
    const isDeviceFormDataValid: boolean = Object.values(deviceFormData).every((field) => field !== '');
    triggerInitialValidation();
    setIsDeviceFormValid(isDeviceFormDataValid);
  }, [deviceFormData]);

  const { mutateAsync: CBSDMutation, isLoading: isCBSDUpdating } = useMutation({
    mutationFn: editCBSD,
  });

  const { isLoading: isClusterLoading, error: clusterError, data: clustersData } = useGetCluster(true);

  if (isClusterLoading) return <Loader />;
  if (clusterError) return <QueryError error={clusterError} />;

  const handleFormSubmit = async () => {
    const isMethodsValid = await methods.trigger();
    if (isMethodsValid) {
      const mainFormData = methods.getValues();
      const installationFormData = installationMethods.getValues();
      const combinedData = { ...mainFormData, installationFormData };
      const formattedData = formatData(combinedData);
      await CBSDMutation(
        { cbsd: formattedData, serialNumber: cbsdDevices?.serialNumber },
        {
          onSuccess: () => {
            toast({
              title: 'CBSD Updated.',
              description: 'The CBSD has been successfully Updated.',
              status: 'success',
              duration: 5000,
              isClosable: true,
              position: 'top',
            });
            navigate('/oran-du-cu-manager/cbrs-management/cbsd');
          },
          onError: () => {
            toast({
              title: 'Error.',
              description: 'There was an error while updating the CBSD.',
              status: 'error',
              duration: 5000,
              isClosable: true,
              position: 'top',
            });
          },
        }
      );
    }
  };

  const deviceChanges = getChangedFields(
    {
      displayName: cbsdDevices?.displayName,
      serialNumber: cbsdDevices?.serialNumber,
      fccId: cbsdDevices?.fccId,
      clusterId: cbsdDevices?.clusterId,
      userId: 'DenseAir',
      category: cbsdDevices?.category,
    },
    deviceFormData
  );
  const configChanges = getChangedFields(
    {
      vendor: cbsdDevices?.cbsdInfo?.vendor || '',
      model: cbsdDevices?.cbsdInfo?.model || '',
      softwareVersion: cbsdDevices?.cbsdInfo?.softwareVersion || '',
      hardwareVersion: cbsdDevices?.cbsdInfo?.hardwareVersion || '',
      firmwareVersion: cbsdDevices?.cbsdInfo?.firmwareVersion || '',
      radioTechnology: cbsdDevices?.airInterface?.radioTechnology || '',
      supportedSpec: cbsdDevices?.airInterface?.supportedSpec || '',
    },
    configFormData
  );
  const installationChanges = getChangedFields(
    {
      latitude: cbsdDevices?.installationParam?.latitude?.toString() || '' || '',
      longitude: cbsdDevices?.installationParam?.longitude?.toString() || '',
      height: cbsdDevices?.installationParam?.height?.toString() || '',
      heightType: cbsdDevices?.installationParam?.heightType,
      horizontalAccuracy: cbsdDevices?.installationParam?.horizontalAccuracy?.toString() || '',
      verticalAccuracy: cbsdDevices?.installationParam?.verticalAccuracy?.toString() || '',
      indoorDeployment: cbsdDevices?.installationParam?.indoorDeployment,
      antennaModel: cbsdDevices?.installationParam?.antennaModel?.toString() || '',
      eirpCapability: cbsdDevices?.installationParam?.eirpCapability?.toString() || '',
      antennaBeamwidth: cbsdDevices?.installationParam?.antennaBeamwidth?.toString() || '',
      antennaAzimuth: cbsdDevices?.installationParam?.antennaAzimuth?.toString() || '',
      antennaDowntilt: cbsdDevices?.installationParam?.antennaDowntilt?.toString() || '',
      antennaGain: cbsdDevices?.installationParam?.antennaGain?.toString() || '',
    },
    installationFormData
  );

  const hasChanges =
    Object.keys(deviceChanges).length > 0 ||
    Object.keys(configChanges).length > 0 ||
    Object.keys(installationChanges).length > 0;

  const renderChangesTable = (changes: Record<string, { original: any; current: any }>, title: string) => (
    <Box>
      <Text fontWeight="bold" mb="2">
        {title}
      </Text>
      <TableContainer>
        <Table p="10" variant="simple" colorScheme="gray">
          <Thead>
            <Tr>
              <Th background="gray.50" width="25%">
                Field
              </Th>
              <Th background="gray.50" width="25%">
                Existed Value
              </Th>
              <Th background="gray.50" width="25%">
                Updated Value
              </Th>
            </Tr>
          </Thead>
          <Tbody>
            {Object.keys(changes).map((key) => (
              <Tr key={key}>
                <Td>{key}</Td>
                <Td>{changes[key].original?.toString() || ''}</Td>
                <Td>{changes[key].current?.toString() || ''}</Td>
              </Tr>
            ))}
          </Tbody>
        </Table>
      </TableContainer>
    </Box>
  );

  return (
    <Box
      marginX="auto"
      as="form"
      width="full"
      maxW={{
        lg: '8xl',
      }}
      onSubmit={(e) => {
        e.preventDefault();
        handleFormSubmit();
      }}
      p="12"
    >
      <FormProvider {...methods}>
        <Stack
          spacing={{
            base: '5',
            lg: '6',
          }}
          padding="8"
        >
          <Box
            boxShadow={{
              base: 'none',
              md: boxShadow,
            }}
            borderRadius="lg"
            borderWidth="1px"
            padding="6"
          >
            <DeviceForm isEdit={true} clustersData={clustersData} />
            <Divider />
            <ConfigForm isEdit={true} />
            <Divider />
            <FormProvider {...installationMethods}>
              <InstallationForm isEdit={true} />
            </FormProvider>
          </Box>
        </Stack>
        <Flex
          direction="row-reverse"
          justifyContent="space-between"
          py="4"
          px={{
            base: '4',
            md: '6',
          }}
        >
          <Flex gap="2">
            <Button
              data-testid="preview-cbsd"
              onClick={() => {
                setIsPreview(true);
                onOpen();
              }}
              colorScheme="teal"
              variant="outline"
              isDisabled={!hasChanges}
            >
              Preview
            </Button>
            <Button
              data-testid="edit-cbsd"
              colorScheme="teal"
              type="submit"
              isLoading={isCBSDUpdating}
              loadingText="Updating..."
              isDisabled={!isDeviceFormValid || !installationMethods?.formState?.isValid}
            >
              Update
            </Button>
          </Flex>
          <Button
            colorScheme="teal"
            variant="outline"
            onClick={() => navigate('/oran-du-cu-manager/cbrs-management/cbsd')}
          >
            Cancel
          </Button>
        </Flex>
      </FormProvider>
      {isPreview && (
        <Modal isOpen={isOpen} onClose={onClose} size="6xl">
          <ModalOverlay />
          <ModalContent p="4">
            <ModalHeader textAlign="center" fontSize="x-large" fontWeight="bold">
              Preview Changes
            </ModalHeader>
            <ModalCloseButton />
            <ModalBody>
              <Stack spacing={4}>
                {Object.keys(deviceChanges).length > 0 && renderChangesTable(deviceChanges, 'Device Form Changes')}
                {Object.keys(configChanges).length > 0 && renderChangesTable(configChanges, 'Config Form Changes')}
                {Object.keys(installationChanges).length > 0 &&
                  renderChangesTable(installationChanges, 'Installation Form Changes')}
              </Stack>
            </ModalBody>
            <ModalFooter>
              <Button colorScheme="teal" mr="3" onClick={onClose}>
                Close
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      )}
    </Box>
  );
};

export default EditCBSDForm;
