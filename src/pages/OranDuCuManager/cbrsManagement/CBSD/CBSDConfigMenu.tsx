import React, { useState } from 'react';
import {
  Heading,
  IconButton,
  Text,
  Menu,
  MenuButton,
  MenuItem,
  MenuList,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalHeader,
  ModalOverlay,
  useDisclosure,
} from '@chakra-ui/react';
import { BsThreeDots } from 'react-icons/bs';
import { useActivateCBSDDevices } from '../../hooks/services/useActivateCBSDDevices';
import { useDeactivateCBSDDevices } from '../../hooks/services/useDeactivateCBSDDevices';
import { useRegisterCBSDDevices } from '../../hooks/services/useRegisterCBSDDevices';
import { useDeregisterCBSDDevices } from '../../hooks/services/useDeregisterCBSDDevices';
import { useDeleteCBSD } from '../../hooks/services/useDeleteCBSD';
import { useNavigate } from 'react-router-dom';
import { AddIcon, CheckIcon, DeleteIcon, EditIcon, InfoOutlineIcon, NotAllowedIcon } from '@chakra-ui/icons';
import GenericAlertDialog from '../../../../components/AlertDialog';
import ModalSubComponentRenderer from '../../hooks/ModalSubComponentRenderer';
import useGetCBSDDevicesBySerialNumber from '../../hooks/services/use_GetCBSDDevicesBySerialNumber';

type CBSDConfigMenuProps = {
  data: any;
  state: string;
  serial: string;
  grant: string;
};

const CBSDConfigMenu = ({ data, state, serial, grant }: CBSDConfigMenuProps) => {
  const navigate = useNavigate();
  const isRegistered = state?.toLowerCase() === 'registered';
  const isActiveEnabled = grant === 'true' && isRegistered ? false : (grant && isRegistered) || isRegistered;
  const isDeactivateEnabled = isActiveEnabled || !isRegistered;
  const isDeregistered = state?.toLowerCase() !== 'registered' && isDeactivateEnabled;
  const { activateCBSDDevicesMutation } = useActivateCBSDDevices();
  const { deactivateCBSDDevicesMutation } = useDeactivateCBSDDevices();
  const { registerCBSDDevicesMutation } = useRegisterCBSDDevices();
  const { deRegisterCBSDDevicesMutation } = useDeregisterCBSDDevices();
  const { deleteCBSDDevicesMutation } = useDeleteCBSD(serial);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const [isQueryEnabled, setIsQueryEnabled] = useState(false);
  const { isOpen: isOpenSingleCr, onOpen: onOpenSingleCr, onClose: onCloseSingleCr } = useDisclosure();

  const { data: cbsdDevices = {} } = useGetCBSDDevicesBySerialNumber(serial, isQueryEnabled);

  const handleDeleteConfirm = async (serial: string) => {
    await deleteCBSDDevicesMutation(serial);
    setIsDialogOpen(false);
  };

  return (
    <>
      <Menu data-testid="cbsd-menu-items">
        <MenuButton
          data-testid="cbsd-menu-items-button"
          onClick={(e) => {
            e.stopPropagation();
          }}
          as={IconButton}
          aria-label="Options"
          icon={<BsThreeDots />}
          variant="outline"
          border="none"
        />
        <MenuList minWidth="110px">
          <MenuItem
            data-testid="display-cr"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              setIsQueryEnabled(true);
              onOpenSingleCr();
            }}
          >
            <InfoOutlineIcon mr="1rem" />
            Display and search {data?.original?.displayName}
          </MenuItem>
          <MenuItem
            data-testid="edit-cbsd"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              navigate(`edit`, {
                state: { serial },
              });
            }}
            isDisabled={isRegistered}
          >
            <EditIcon mr="1rem" />
            Edit CBSD
          </MenuItem>
          <MenuItem
            data-testid="activate-cbsd"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              activateCBSDDevicesMutation(serial);
            }}
            isDisabled={!isActiveEnabled}
          >
            <CheckIcon mr="1rem" />
            Activate
          </MenuItem>
          <MenuItem
            data-testid="deactivate-cbsd"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              deactivateCBSDDevicesMutation(serial);
            }}
            isDisabled={isDeactivateEnabled}
          >
            <NotAllowedIcon mr="1rem" />
            Deactivate
          </MenuItem>
          <MenuItem
            data-testid="register-cbsd"
            isDisabled={isRegistered}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              registerCBSDDevicesMutation(serial);
            }}
          >
            <AddIcon mr="1rem" />
            Register
          </MenuItem>
          <MenuItem
            data-testid="register-cbsd"
            isDisabled={isDeregistered}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              deRegisterCBSDDevicesMutation(serial);
            }}
          >
            <NotAllowedIcon mr="1rem" />
            Deregister
          </MenuItem>
          <MenuItem
            data-testid="delete-cbsd"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              setIsDialogOpen(true);
            }}
          >
            <DeleteIcon mr="1rem" />
            Delete
          </MenuItem>
        </MenuList>
      </Menu>
      {isDialogOpen && (
        <GenericAlertDialog
          title="Delete CBSD Device"
          message={`Are you sure you want to delete the device with serial ${serial}? This action cannot be undone.`}
          confirmButtonText="Delete"
          cancelButtonText="Cancel"
          onConfirm={() => handleDeleteConfirm(serial)}
          confirmButtonColorScheme="teal"
          onCancel={() => setIsDialogOpen(false)}
          width="650px"
        />
      )}

      {cbsdDevices && (
        <Modal isOpen={isOpenSingleCr} onClose={onCloseSingleCr} size="5xl" isCentered>
          <ModalOverlay bg="blackAlpha.900" />
          <ModalContent>
            <ModalHeader>
              <Heading as="h2" textAlign="center" mb="8">
                {data?.original?.displayName} custom resource
              </Heading>
              <Text fontSize="sm" pb="4">{`press ctrl+f(win) / cmd+f(mac) to search`}</Text>
              <Text fontSize="sm">{`click on the menu icon(3 dot upper right) > find & edit > find`}</Text>
            </ModalHeader>
            <ModalCloseButton />
            <ModalBody>{<ModalSubComponentRenderer data={cbsdDevices} />}</ModalBody>
          </ModalContent>
        </Modal>
      )}
    </>
  );
};

export default CBSDConfigMenu;
