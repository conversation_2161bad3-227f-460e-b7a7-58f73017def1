import {
  Box,
  Button,
  Flex,
  Heading,
  Icon,
  Stack,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
  useColorModeValue,
} from '@chakra-ui/react';
import { useNavigate, useParams } from 'react-router-dom';
import { AddIcon, ArrowBackIcon } from '@chakra-ui/icons';
import { useEffect, useState } from 'react';
import CPIView from './CPI/CPIView/CPIView';
import CBSD from './CBSD/CBSD';
import useLogin from '../../../hooks/useLogin';
import { AUTH_TOKEN_KEY, READ_WRITE_ACCESS_ROLES } from '../../../data/constants';

const CBRSOverview = () => {
  const colorModeValue = useColorModeValue('sm', 'sm-dark');
  const navigate = useNavigate();
  const { tab } = useParams();
  const [tabIndex, setTabIndex] = useState(0);

  const { checkNmsDevAccess } = useLogin(AUTH_TOKEN_KEY);
  const checkRoleAccess = checkNmsDevAccess(READ_WRITE_ACCESS_ROLES);

  useEffect(() => {
    if (tab === 'cbsd') {
      setTabIndex(0);
    } else if (tab === 'cpi') {
      setTabIndex(1);
    }
  }, [tab]);

  const handleTabs = (index: number) => {
    if (index === 0) {
      navigate('/oran-du-cu-manager/cbrs-management/cbsd');
    } else {
      navigate('/oran-du-cu-manager/cbrs-management/cpi');
    }
  };

  return (
    <Stack
      px={{
        base: '4',
        md: '8',
      }}
      spacing="12"
    >
      <Stack
        spacing="1"
        direction={{
          base: 'column',
          lg: 'row',
        }}
        justify="space-between"
      >
        <Flex
          gap="1"
          alignItems="center"
          cursor="pointer"
          onClick={() => {
            navigate('/oran-du-cu-manager');
          }}
        >
          <Icon as={ArrowBackIcon} w="5" h="5" mr="4" />
          <Heading fontWeight="bold" data-testid="cbrs-heading">
            CBRS Overview
          </Heading>
        </Flex>
        {checkRoleAccess && (
          <Button
            data-testid="create-cbrs-mangement"
            variant="primary"
            leftIcon={<Icon as={AddIcon} marginStart="-1" />}
            onClick={() =>
              tabIndex === 0
                ? navigate('/oran-du-cu-manager/cbrs-management/cbsd/create')
                : navigate('/oran-du-cu-manager/cbrs-management/cpi/create')
            }
          >
            {tabIndex === 0 ? 'Create CBSD' : 'Create CPI'}
          </Button>
        )}
      </Stack>
      <Stack
        spacing={{
          base: '5',
          lg: '6',
        }}
      >
        <Box
          bg="bg-surface"
          boxShadow={{
            base: 'none',
            md: colorModeValue,
          }}
        >
          <Stack spacing="5">
            <Tabs
              isManual
              isFitted
              variant="enclosed-colored"
              orientation="horizontal"
              isLazy
              onChange={(index: number) => handleTabs(index)}
              index={tabIndex}
            >
              <TabList mb="1em">
                <Tab>CBSD</Tab>
                <Tab>CPI</Tab>
              </TabList>
              <TabPanels>
                <TabPanel data-testid="cbsd">
                  <CBSD />
                </TabPanel>
                <TabPanel data-testid="cpi">
                  <CPIView />
                </TabPanel>
              </TabPanels>
            </Tabs>
          </Stack>
        </Box>
      </Stack>
    </Stack>
  );
};

export default CBRSOverview;
