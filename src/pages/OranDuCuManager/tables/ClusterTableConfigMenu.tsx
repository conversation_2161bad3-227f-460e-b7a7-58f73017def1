import { DeleteIcon, InfoOutlineIcon } from '@chakra-ui/icons';
import {
  Box,
  Code,
  IconButton,
  Menu,
  MenuButton,
  Menu<PERSON>tem,
  MenuList,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalHeader,
  ModalOverlay,
  useDisclosure,
  Text,
  Heading,
} from '@chakra-ui/react';
import React, { useState } from 'react';
import { BsThreeDots } from 'react-icons/bs';
import { AUTH_TOKEN_KEY, READ_WRITE_ACCESS_ROLES } from '../../../data/constants';
import useLogin from '../../../hooks/useLogin';
import ModalSubComponentRenderer from '../hooks/ModalSubComponentRenderer';
import useConfigSets from '../hooks/services/useConfigSets';
import useCustomResource from '../hooks/services/useCustomResource';

type ClusterTableConfigMenuProps = {
  dataTestId: string;
  assetName: string;
  type?: string;
  category?: string;
  kind?: string;
  id: number;
  menuType?: string;
};

const ClusterTableConfigMenu = (props: ClusterTableConfigMenuProps) => {
  const { dataTestId, assetName, type, category, kind, id, menuType } = props;
  const [isQueryEnabled, setIsQueryEnabled] = useState(false);

  const { isOpen: isOpenSingleCr, onOpen: onOpenSingleCr, onClose: onCloseSingleCr } = useDisclosure();

  const { checkApplicationAccess } = useLogin(AUTH_TOKEN_KEY);
  const checkRoleAccess = checkApplicationAccess(READ_WRITE_ACCESS_ROLES);

  const { deleteConfigSet } = useConfigSets();
  const { deleteCustomResource, getSingleCustomResource } = useCustomResource();
  const configSetMutation = deleteConfigSet();
  const customResourceMutation = deleteCustomResource();

  // API
  const {
    data: singleCustomResource,
    isLoading: isSingleCustomResourceLoading,
    error: singleCustomResourceError,
  } = getSingleCustomResource(id, assetName, isQueryEnabled);

  const handleDeleteCr = async () => {
    switch (type) {
      case 'configSet': {
        configSetMutation.mutate(id);
        break;
      }
      case 'customResource': {
        const payloadData = { cluster_id: id, name: assetName };
        customResourceMutation.mutate(payloadData);
        break;
      }
      case 'pod': {
        break;
      }
      case 'cluster': {
        break;
      }
      default: {
        console.error('Unknown type');
        break;
      }
    }
  };

  return (
    <>
      <Menu data-testid={dataTestId}>
        <MenuButton
          data-testid={props.dataTestId}
          onClick={(e) => {
            e.stopPropagation();
          }}
          as={IconButton}
          aria-label="Options"
          icon={<BsThreeDots />}
          variant="outline"
        />
        <MenuList>
          {menuType === 'displayCR' ? (
            <MenuItem
              data-testid="display-cr"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                setIsQueryEnabled(true);
                onOpenSingleCr();
              }}
            >
              <InfoOutlineIcon mr="1rem" />
              Display and search {assetName}
            </MenuItem>
          ) : (
            <MenuItem
              data-testid="delete-cr"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleDeleteCr();
              }}
            >
              <DeleteIcon mr="1rem" />
              Delete {type} - {assetName}
            </MenuItem>
          )}
        </MenuList>
      </Menu>

      {singleCustomResource && (
        <Modal isOpen={isOpenSingleCr} onClose={onCloseSingleCr} size="5xl" isCentered>
          <ModalOverlay bg="blackAlpha.900" />
          <ModalContent>
            <ModalHeader>
              <Heading as="h2" textAlign="center" mb="8">
                {assetName} custom resource
              </Heading>
              <Text fontSize="sm" pb="4">{`press ctrl+f(win) / cmd+f(mac) to search`}</Text>
              <Text fontSize="sm">{`click on the menu icon(3 dot upper right) > find & edit > find`}</Text>
            </ModalHeader>
            <ModalCloseButton />
            <ModalBody>{<ModalSubComponentRenderer data={singleCustomResource} />}</ModalBody>
          </ModalContent>
        </Modal>
      )}
    </>
  );
};

export default ClusterTableConfigMenu;
