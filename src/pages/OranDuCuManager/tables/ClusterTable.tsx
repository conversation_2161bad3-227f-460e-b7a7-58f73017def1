import { AddIcon, MinusIcon } from '@chakra-ui/icons';
import {
  Accordion,
  AccordionButton,
  AccordionItem,
  AccordionPanel,
  Box,
  Heading,
  Select,
  Table,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  Tr,
} from '@chakra-ui/react';
import { useState } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import {
  ErrorBoundaryFallback,
  ErrorBoundaryLogError,
} from '../../../components/errorComponents/ErrorBoundaryFallback';
import CircleGreenTick from '../../../components/icons/CircleGreenTick';
import CircleRedCross from '../../../components/icons/CircleRedCross';
import Loader from '../../../components/loader/Loader';
import { TRUE } from '../../../data/constants';
import { Cluster, CrCard, SelectedOption } from '../../../types/duCuManager.type';
import TableConfigMenu from '../TableConfigMenu';

type ClusterTableProps = {
  handleChange: (field: string, selectedValue: string | number | null) => void;
  changeExpandedIndex: React.Dispatch<React.SetStateAction<number | null>>;
  colorModeValue: string;
  selectedOption: SelectedOption;
  setSelectedOption: any;
  clusterList: Cluster[];
  isTableClusterListLoading: boolean;
  expandedIndex: number | null;
  tableClusterList: any;
  showRowMenu: boolean;
};

type ClusterTablePayload = {
  site?: string;
  cluster?: number | null;
};

const ClusterTable: React.FC<ClusterTableProps> = ({
  handleChange,
  changeExpandedIndex,
  colorModeValue,
  selectedOption,
  setSelectedOption,
  clusterList,
  isTableClusterListLoading,
  expandedIndex,
  tableClusterList,
  showRowMenu,
}) => {
  const [siteInClusterListPayload, setSiteInClusterListPayload] = useState<ClusterTablePayload>({
    site: '',
    cluster: null,
  });

  const handleRowClick = (item: any) => {
    setSelectedOption({
      ...selectedOption,
      site: item.site,
      cluster: siteInClusterListPayload.cluster,
      cuup: siteInClusterListPayload.cluster,
      cucp: siteInClusterListPayload.cluster,
      du: siteInClusterListPayload.cluster,
    });
    window.scrollTo({ top: 0, left: 0, behavior: 'smooth' });
  };

  return (
    <Box mx="5%" my="24" p="8" boxShadow={colorModeValue} borderRadius="xl">
      <Heading textAlign="center" as="h2" fontSize="20px" mb="4">
        View custom resources for a cluster
      </Heading>
      <Select
        mb="2"
        placeholder="Select option"
        value={selectedOption?.cluster ?? undefined}
        onChange={(e) => {
          setSiteInClusterListPayload({
            ...siteInClusterListPayload,
            cluster: Number(e.target.value),
          });
          handleChange('cluster', e.target.value);
        }}
      >
        {clusterList?.map((item: Cluster) => (
          <option key={item.id} value={item.id}>
            {`${item.id}: ${item.cluster_name}`}
          </option>
        ))}
      </Select>
      {/* CR table */}
      {selectedOption?.cluster && isTableClusterListLoading ? (
        <Loader />
      ) : (
        <Box>
          <Accordion allowToggle onChange={(index) => changeExpandedIndex(index as number)}>
            {[1].map((section, index) => (
              <AccordionItem key={`crTable-${index}`}>
                <h2>
                  <AccordionButton>
                    <Text flex="1" textAlign="center">
                      <Text as={'b'}>Custom resource</Text> table for selected cluster
                    </Text>
                    {expandedIndex === index ? <MinusIcon /> : <AddIcon />}
                  </AccordionButton>
                </h2>
                <AccordionPanel p="0">
                  {tableClusterList?.crs ? (
                    <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
                      <Table size="sm">
                        <Thead>
                          <Tr>
                            <Th>Site</Th>
                            <Th>Kind</Th>
                            <Th>Name</Th>
                            <Th>Enabled</Th>
                            {showRowMenu ? <Th>Menu</Th> : null}
                          </Tr>
                        </Thead>
                        <Tbody>
                          {tableClusterList?.crs.map((item: CrCard, index: number) => (
                            <Tr key={`${item.name}-${index}`} onClick={() => handleRowClick(item)}>
                              <Td>{item.site}</Td>
                              <Td>{item.kind}</Td>
                              <Td>{item.name}</Td>
                              <Td>
                                {item.enabled === TRUE ? (
                                  <>
                                    <CircleGreenTick />
                                  </>
                                ) : (
                                  <>
                                    <CircleRedCross />
                                  </>
                                )}
                              </Td>
                              {showRowMenu ? (
                                <Td>
                                  <TableConfigMenu
                                    dataTestId="du-cu-manager-cr-table-list-config-icon"
                                    type="customResource"
                                    assetName={item.name}
                                    id={selectedOption?.cluster ?? 0}
                                    menuType="displayCR"
                                  />
                                </Td>
                              ) : null}
                            </Tr>
                          ))}
                        </Tbody>
                      </Table>
                    </ErrorBoundary>
                  ) : null}
                </AccordionPanel>
              </AccordionItem>
            ))}
          </Accordion>
        </Box>
      )}
    </Box>
  );
};

export default ClusterTable;
