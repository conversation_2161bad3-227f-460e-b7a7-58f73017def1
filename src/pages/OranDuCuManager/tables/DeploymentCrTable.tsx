import { Box, Heading, Text, useColorModeValue } from '@chakra-ui/react';
import { ColumnDef } from '@tanstack/react-table';
import { every, isEmpty } from 'lodash';
import React from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import EmptyDataErrorBoundary from '../../../components/errorComponents/EmptyDataErrorBoundary';
import {
  ErrorBoundaryFallback,
  ErrorBoundaryLogError,
} from '../../../components/errorComponents/ErrorBoundaryFallback';
import QueryError from '../../../components/errorComponents/QueryError';
import StatusComponent from '../../../components/icons/StatusIcon';
import Loader from '../../../components/loader/Loader';
import { CUSTOM_RESOURCE_KINDS } from '../../../data/constants';
import { getStatusColor } from '../../CellOverview/hooks/useStatus';
import { DataTable } from '../../MetricsCollector/components/DataTable';
import useCustomResource from '../hooks/services/useCustomResource';
import ClusterTableConfigMenu from './ClusterTableConfigMenu';
import CustomResourcesTable from './CustomResourcesTable';

type DeploymentCrTableConfigMenuProps = {
  deploymentName: string;
  du_cluster: number | null | undefined;
  cucp_cluster: number | null | undefined;
  cuup_cluster: number | null | undefined;
  colorModeValue: string;
  showTable?: boolean;
  showRowMenu?: boolean;
  isReady: boolean;
};

const DeploymentCrTable = ({
  deploymentName,
  du_cluster,
  cucp_cluster,
  cuup_cluster,
  colorModeValue,
  showTable = false,
  showRowMenu = true,
  isReady,
}: DeploymentCrTableConfigMenuProps) => {
  const getClusterId = (kind: CUSTOM_RESOURCE_KINDS | undefined) => {
    if (!kind) return null;
    switch (kind) {
      case CUSTOM_RESOURCE_KINDS.CuCpDeployment:
      case CUSTOM_RESOURCE_KINDS.CuCpAppConfig:
        return cucp_cluster;
      case CUSTOM_RESOURCE_KINDS.CuUpDeployment:
      case CUSTOM_RESOURCE_KINDS.CuUpAppConfig:
      case CUSTOM_RESOURCE_KINDS.CuCellConfig:
        return cuup_cluster;
      case CUSTOM_RESOURCE_KINDS.Split6DuDeployment:
      case CUSTOM_RESOURCE_KINDS.DuCellConfig:
      case CUSTOM_RESOURCE_KINDS.Split6DuAppConfig:
        return du_cluster;
      default:
        return null;
    }
  };

  const borderColor = useColorModeValue('gray.200', 'gray.700');

  // Table rendering
  const columns = React.useMemo<ColumnDef<any>[]>(
    () => [
      {
        header: 'Name',
        accessorKey: 'name',
        id: 'tableClusterList',
      },
      {
        header: 'Kind',
        accessorKey: 'kind',
        id: 'kind',
      },
      {
        header: 'Site',
        accessorKey: 'site',
        id: 'site',
      },
      {
        header: 'Created',
        accessorKey: 'created',
        id: 'created',
        cell: (props) => {
          const created = new Intl.DateTimeFormat('en-GB', {
            year: '2-digit',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
          }).format(new Date(props.row.original.created as string));
          return <Text>{created}</Text>;
        },
      },
      {
        header: 'Enabled',
        accessorKey: 'enabled',
        id: 'enabled',
        cell: (props) => {
          const cellStatus = props.row.original.enabled as string;

          return (
            <>
              <StatusComponent
                dataTestId="cell-main-table-status-icon"
                boxSize="sm"
                color={getStatusColor(cellStatus)}
                status={cellStatus}
              />
            </>
          );
        },
      },
      {
        header: 'Menu',
        accessorKey: 'row_menu',
        id: 'row_menu',
        cell: ({ row }) => {
          const rowData = row.original;
          const clusterSelected = getClusterId(rowData.kind as CUSTOM_RESOURCE_KINDS);
          return (
            <>
              {showRowMenu ? (
                <ClusterTableConfigMenu
                  dataTestId="du-cu-manager-simple-view-cr-table-list-config-icon"
                  menuType="displayCR"
                  assetName={rowData.name}
                  id={clusterSelected as number}
                />
              ) : null}
            </>
          );
        },
      },
    ],
    [showRowMenu, du_cluster, cucp_cluster, cuup_cluster]
  );
  const RenderConfigInfo = ({ row }: any) => {
    const duClusterId = du_cluster ?? 0;
    const cucpClusterId = cucp_cluster ?? 0;
    const cuupClusterId = cuup_cluster ?? 0;

    return (
      <CustomResourcesTable
        row={row}
        du_cluster={duClusterId}
        cucp_cluster={cucpClusterId}
        cuup_cluster={cuupClusterId}
      />
    );
  };

  // API Calls
  const { getDeploymentCustomResources } = useCustomResource();

  // Drives cluster table
  const {
    data: DeploymentCustomResourcesData,
    isLoading: isDeploymentCustomResourcesDataLoading,
    error: deploymentCustomResourcesDataError,
    isFetching: isDeploymentCustomResourcesDataFetching,
  } = getDeploymentCustomResources(deploymentName, isReady);

  const flattenedData = React.useMemo(() => {
    if (DeploymentCustomResourcesData) {
      return [
        ...DeploymentCustomResourcesData.du,
        ...DeploymentCustomResourcesData.cuup,
        ...DeploymentCustomResourcesData.cucp,
      ];
    }
    return [];
  }, [DeploymentCustomResourcesData]);

  if (isReady && isDeploymentCustomResourcesDataLoading) return <Loader />;
  if (deploymentCustomResourcesDataError) return <QueryError error={deploymentCustomResourcesDataError} />;

  if (
    DeploymentCustomResourcesData &&
    every(
      ['du', 'cucp', 'cuup'],
      (key) => Array.isArray(DeploymentCustomResourcesData[key]) && isEmpty(DeploymentCustomResourcesData[key])
    )
  ) {
    return (
      <EmptyDataErrorBoundary
        data={DeploymentCustomResourcesData}
        message={
          new Error(
            `All keys (du, cucp, cuup) are empty arrays. Expected at least one key to contain data. Received: ${JSON.stringify(
              DeploymentCustomResourcesData,
              null,
              2
            )}`
          )
        }
      />
    );
  }

  return (
    <Box my="20" boxShadow={colorModeValue} borderRadius="xl" data-testid="custom-resource-table">
      <Heading textAlign="center" as="h2" fontSize="20px" pb="4" pt="8">
        Custom resources for deployment {deploymentName}
      </Heading>

      <Box>
        {DeploymentCustomResourcesData ? (
          <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
            <DataTable
              columns={columns ?? []}
              data={flattenedData}
              isExpandable={true}
              renderSubComponent={(row) => <RenderConfigInfo row={row} />}
              defaultPageSize={50}
              hasEmptyResult={false}
              size="sm"
              showSearch={false}
            />
          </ErrorBoundary>
        ) : null}
      </Box>
    </Box>
  );
};

export default DeploymentCrTable;
