import { AddIcon, MinusIcon } from '@chakra-ui/icons';
import {
  Accordion,
  AccordionButton,
  AccordionItem,
  AccordionPanel,
  Box,
  Heading,
  Select,
  Text,
} from '@chakra-ui/react';
import { ColumnDef } from '@tanstack/react-table';
import React, { useEffect, useState } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import {
  ErrorBoundaryFallback,
  ErrorBoundaryLogError,
} from '../../../components/errorComponents/ErrorBoundaryFallback';
import QueryError from '../../../components/errorComponents/QueryError';
import Loader from '../../../components/loader/Loader';
import { Cluster } from '../../../types/duCuManager.type';
import { DataTable } from '../../MetricsCollector/components/DataTable';
import useCustomResource from '../hooks/services/useCustomResource';
import ClusterTableConfigMenu from './ClusterTableConfigMenu';

type ClusterTableConfigMenuProps = {
  clusterList: Cluster[];
  isClusterListLoading: boolean;
  clusterListError: any;
  selectedOption: any;
  setSelectedOption: any;
  colorModeValue: string;
  changeExpandedIndex: any;
  expandedIndex: number | null;
  showRowMenu: boolean;
};

const ClusterViewTable = ({
  clusterList,
  isClusterListLoading,
  clusterListError,
  selectedOption,
  setSelectedOption,
  colorModeValue,
  changeExpandedIndex,
  expandedIndex,
  showRowMenu,
}: ClusterTableConfigMenuProps) => {
  const [clusterSelected, setClusterSelected] = useState<number | null>(null);

  const handleChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedValue = Number(event.target.value);
    setClusterSelected(selectedValue);
  };

  const columns = React.useMemo<ColumnDef<any>[]>(
    () => [
      {
        header: 'Name',
        accessorKey: 'name',
        id: 'tableClusterList',
      },
      {
        header: 'Kind',
        accessorKey: 'kind',
        id: 'kind',
      },
      {
        header: 'Site',
        accessorKey: 'site',
        id: 'site',
      },
      {
        header: 'Enabled',
        accessorKey: 'enabled',
        id: 'enabled',
      },
      {
        header: 'Menu',
        accessorKey: 'row_menu',
        id: 'row_menu',
        cell: ({ row }) => {
          const rowData = row.original;
          return (
            <>
              {showRowMenu ? (
                <ClusterTableConfigMenu
                  dataTestId="du-cu-manager-cr-table-list-config-icon"
                  menuType="displayCR"
                  assetName={rowData.name}
                  id={clusterSelected as number}
                />
              ) : null}
            </>
          );
        },
      },
    ],
    [showRowMenu, clusterSelected]
  );

  // API Calls
  const { getCustomResourceList } = useCustomResource();

  // Drives cluster table
  const {
    data: tableClusterList,
    isLoading: isTableClusterListLoading,
    error: tableClusterListError,
  } = getCustomResourceList(clusterSelected);

  if (clusterListError) return <QueryError error={clusterListError} />;

  return (
    <Box mx="5%" my="24" p="8" boxShadow={colorModeValue} borderRadius="xl">
      <Heading textAlign="center" as="h2" fontSize="20px" mb="4">
        View custom resources for a cluster
      </Heading>

      {isClusterListLoading ? (
        <Loader />
      ) : (
        <Box>
          <Accordion allowToggle onChange={(index) => changeExpandedIndex(index as number)}>
            {[1].map((section, index) => (
              <AccordionItem key={`crTable-${index}`}>
                <h2>
                  <AccordionButton>
                    <Text flex="1" textAlign="center">
                      <Text as={'b'}>View cluster</Text>
                    </Text>
                    {expandedIndex === index ? <MinusIcon /> : <AddIcon />}
                  </AccordionButton>
                </h2>
                <AccordionPanel p="0">
                  <Select
                    mb="2"
                    placeholder="Select option"
                    value={clusterSelected ?? undefined}
                    onChange={handleChange}
                  >
                    {clusterList?.map((item: Cluster) => (
                      <option key={item.id} value={item.id}>
                        {`${item.id}: ${item.cluster_name}`}
                      </option>
                    ))}
                  </Select>
                  {tableClusterList?.crs ? (
                    <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
                      <DataTable
                        columns={columns ?? []}
                        data={tableClusterList?.crs}
                        isExpandable={false}
                        defaultPageSize={50}
                        hasEmptyResult={false}
                        size="sm"
                      />
                    </ErrorBoundary>
                  ) : null}
                </AccordionPanel>
              </AccordionItem>
            ))}
          </Accordion>
        </Box>
      )}
    </Box>
  );
};

export default ClusterViewTable;
