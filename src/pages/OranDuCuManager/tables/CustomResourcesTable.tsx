import { Box } from '@chakra-ui/react';
import QueryError from '../../../components/errorComponents/QueryError';
import Loader from '../../../components/loader/Loader';
import { CUSTOM_RESOURCE_KINDS } from '../../../data/constants';
import useCustomResource from '../hooks/services/useCustomResource';
import DisplayObject from './DisplayObject';

export type CustomResourcesTableProps = {
  row?: {
    row: {
      getIsExpanded: () => boolean;
      original: {
        site: string;
        name: string;
        kind: CUSTOM_RESOURCE_KINDS;
      };
    };
  };
  du_cluster: number;
  cucp_cluster: number;
  cuup_cluster: number;
};

const CustomResourcesTable = ({ row, du_cluster, cucp_cluster, cuup_cluster }: CustomResourcesTableProps) => {
  const refetchData = row?.row?.getIsExpanded();

  const site = row?.row?.original?.site;
  const name = row?.row?.original?.name;
  const kind = row?.row?.original?.kind as CUSTOM_RESOURCE_KINDS | undefined;

  const getClusterId = (kind: CUSTOM_RESOURCE_KINDS | undefined) => {
    if (!kind) return null;
    switch (kind) {
      case CUSTOM_RESOURCE_KINDS.CuCpDeployment:
      case CUSTOM_RESOURCE_KINDS.CuCpAppConfig:
        return cucp_cluster;
      case CUSTOM_RESOURCE_KINDS.CuUpDeployment:
      case CUSTOM_RESOURCE_KINDS.CuUpAppConfig:
      case CUSTOM_RESOURCE_KINDS.CuCellConfig:
        return cuup_cluster;
      case CUSTOM_RESOURCE_KINDS.Split6DuDeployment:
      case CUSTOM_RESOURCE_KINDS.DuCellConfig:
      case CUSTOM_RESOURCE_KINDS.Split6DuAppConfig:
        return du_cluster;
      default:
        return null;
    }
  };

  const clusterId = getClusterId(kind);

  const { getSingleCustomResource } = useCustomResource();

  if (!clusterId || !name) {
    return null;
  }

  // API
  const {
    data: singleCustomResource,
    isLoading: isSingleCustomResourceLoading,
    error: singleCustomResourceError,
  } = getSingleCustomResource(clusterId, name, true);

  if (isSingleCustomResourceLoading) return <Loader />;
  if (singleCustomResourceError) return <QueryError error={singleCustomResourceError} />;

  return (
    <>
      {singleCustomResource ? (
        <Box p={4}>
          <DisplayObject data={singleCustomResource} />
        </Box>
      ) : null}
    </>
  );
};

export default CustomResourcesTable;
