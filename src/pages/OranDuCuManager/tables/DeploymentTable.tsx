import { AddIcon, MinusIcon } from '@chakra-ui/icons';
import { Accordion, AccordionButton, AccordionItem, AccordionPanel, Box, Heading, Text } from '@chakra-ui/react';
import { ColumnDef } from '@tanstack/react-table';
import React from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import {
  ErrorBoundaryFallback,
  ErrorBoundaryLogError,
} from '../../../components/errorComponents/ErrorBoundaryFallback';
import QueryError from '../../../components/errorComponents/QueryError';
import Loader from '../../../components/loader/Loader';
import { DataTable } from '../../MetricsCollector/components/DataTable';
import DeploymentTableConfigMenu from './DeploymentTableConfigMenu';

type DeploymentTableProps = {
  allDeployments: any;
  isAllDeploymentsLoading: boolean;
  allDeploymentsError: any;
  selectedOption: any;
  setSelectedOption: any;
  colorModeValue: string;
  changeExpandedIndex: any;
  expandedIndex: number | null;
  showRowMenu: boolean;
};

const DeploymentTable = ({
  allDeployments,
  isAllDeploymentsLoading,
  allDeploymentsError,
  selectedOption,
  setSelectedOption,
  colorModeValue,
  changeExpandedIndex,
  expandedIndex,
  showRowMenu,
}: DeploymentTableProps) => {
  const columns = React.useMemo<ColumnDef<any>[]>(
    () => [
      {
        header: 'Deployment',
        accessorKey: 'deployment_name',
        id: 'deployment_name',
      },
      {
        header: 'Du site',
        accessorKey: 'du_site_name',
        id: 'du_site_name',
      },
      {
        header: 'CuCp site',
        accessorKey: 'cucp_site_name',
        id: 'cucp_site_name',
      },
      {
        header: 'CuUp site',
        accessorKey: 'cuup_site_name',
        id: 'cuup_site_name',
      },
      {
        header: 'Du cluster',
        accessorKey: 'du_cluster',
        id: 'du_cluster',
      },
      {
        header: 'CuUp cluster',
        accessorKey: 'cuup_cluster',
        id: 'cuup_cluster',
      },
      {
        header: 'CuCp cluster',
        accessorKey: 'cucp_cluster',
        id: 'cucp_cluster',
      },
      {
        header: 'Menu',
        accessorKey: 'row_menu',
        id: 'row_menu',
        cell: ({ row }) => {
          const rowData = row.original;
          return (
            <>
              {showRowMenu ? (
                <DeploymentTableConfigMenu
                  dataTestId="du-cu-manager-cr-table-list-config-icon"
                  type="customResource"
                  rowData={rowData}
                  id={selectedOption?.cluster ?? 0}
                  menuType="preFillForm"
                  selectedOption={selectedOption}
                  setSelectedOption={setSelectedOption}
                />
              ) : null}
            </>
          );
        },
      },
    ],
    []
  );

  const handleRowClick = (rowData: any) => {
    setSelectedOption({ ...rowData, rowClicked: true });
    // Perform any additional logic with the row data here
  };

  if (allDeploymentsError) return <QueryError error={allDeploymentsError} />;

  return (
    <Box mx="5%" my="24" p="8" boxShadow={colorModeValue} borderRadius="xl">
      <Heading textAlign="center" as="h2" fontSize="20px" mb="4">
        View all deployments
      </Heading>
      {isAllDeploymentsLoading ? (
        <Loader />
      ) : (
        <Box>
          <Accordion allowToggle onChange={(index) => changeExpandedIndex(index as number)}>
            {[1].map((section, index) => (
              <AccordionItem key={`crTable-${index}`}>
                <h2>
                  <AccordionButton>
                    <Text flex="1" textAlign="center">
                      <Text as={'b'}>View deployments</Text>
                    </Text>
                    {expandedIndex === index ? <MinusIcon /> : <AddIcon />}
                  </AccordionButton>
                </h2>
                <AccordionPanel p="0">
                  {allDeployments && Array.isArray(allDeployments) && allDeployments.length > 0 ? (
                    <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
                      <DataTable
                        onRowClick={handleRowClick}
                        columns={columns ?? []}
                        data={allDeployments}
                        isExpandable={false}
                        defaultPageSize={50}
                        hasEmptyResult={false}
                        size="sm"
                      />
                    </ErrorBoundary>
                  ) : null}
                </AccordionPanel>
              </AccordionItem>
            ))}
          </Accordion>
        </Box>
      )}
    </Box>
  );
};

export default DeploymentTable;
