import { DeleteIcon, InfoOutlineIcon } from '@chakra-ui/icons';
import { IconButton, Menu, MenuButton, MenuI<PERSON>, MenuList } from '@chakra-ui/react';
import React, { useState } from 'react';
import { BsThreeDots } from 'react-icons/bs';
import { AUTH_TOKEN_KEY, READ_WRITE_ACCESS_ROLES } from '../../../data/constants';
import useLogin from '../../../hooks/useLogin';

type CellConfigMenuProps = {
  dataTestId: string;
  rowData: any;
  type: string;
  category?: string;
  kind?: string;
  id: number;
  menuType?: string;
  selectedOption?: any;
  setSelectedOption?: any;
};

const DeploymentTableConfigMenu = (props: CellConfigMenuProps) => {
  const { dataTestId, rowData, type, category, kind, id, menuType, selectedOption, setSelectedOption } = props;
  const [isQueryEnabled, setIsQueryEnabled] = useState(false);

  const { checkApplicationAccess } = useLogin(AUTH_TOKEN_KEY);
  const checkRoleAccess = checkApplicationAccess(READ_WRITE_ACCESS_ROLES);

  const handleRowClick = (item: any) => {
    setSelectedOption({
      ...selectedOption,
      deployment_name: item.deployment_name,
      du_site_name: item.du_site_name,
      cucp_site_name: item.cucp_site_name,
      cuup_site_name: item.cuup_site_name,
      version: 'v1beta1',
      du_cluster: item.du_cluster,
      cucp_cluster: item.cucp_cluster,
      cuup_cluster: item.cuup_cluster,
      bands: item.bands,
      bandwidths: item.bandwidths,
      arfcns: item.arfcns,
      du_cell_config_set_ids: [],
      du_app_config_set_ids: [],
      cu_cell_config_set_ids: [],
      cu_cp_app_config_set_ids: [],
      cu_up_app_config_set_ids: [],
      ru_vendor: 'EdgeQ',
      f1_ip: item.f1_ip,
      e1_ip: item.e1_ip,
    });
    window.scrollTo({ top: 0, left: 0, behavior: 'smooth' });
  };

  return (
    <>
      {checkRoleAccess && (
        <Menu data-testid={dataTestId}>
          <MenuButton
            data-testid={props.dataTestId}
            onClick={(e) => {
              e.stopPropagation();
            }}
            as={IconButton}
            aria-label="Options"
            icon={<BsThreeDots />}
            variant="outline"
          />
          <MenuList>
            {menuType === 'preFillForm' ? (
              <MenuItem
                data-testid="display-cr"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setIsQueryEnabled(!isQueryEnabled);
                  handleRowClick(rowData);
                }}
              >
                <InfoOutlineIcon mr="1rem" />
                Pre fill form with row data
              </MenuItem>
            ) : (
              <MenuItem
                data-testid="delete-cr"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  console.log('delete');
                }}
              >
                <DeleteIcon mr="1rem" />
                Delete {type} - {rowData}
              </MenuItem>
            )}
          </MenuList>
        </Menu>
      )}
    </>
  );
};

export default DeploymentTableConfigMenu;
