import { Box, Text, Button } from '@chakra-ui/react';
import React, { useState } from 'react';

type DisplayObjectProps = {
  data: Record<string, any>;
  level?: number;
  onDoubleClick?: () => void;
  filterCriteria?: (value: any) => boolean;
};

type ExpandedState = {
  [key: string]: boolean;
};

const DisplayObject: React.FC<DisplayObjectProps> = ({ data, level = 0, onDoubleClick, filterCriteria }) => {
  const [isExpanded, setIsExpanded] = useState<ExpandedState>({});

  const toggleExpand = (key: string) => {
    setIsExpanded((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  const renderValue = (value: any, key: string, isLast: boolean) => {
    const isObject = typeof value === 'object' && value !== null;

    return (
      <Box position="relative" pl={level * 2} pt="2" pb="2">
        {/* Vertical Line */}
        <Box position="absolute" top="0" left="-8px" height={isLast ? '50%' : '100%'} width="1px" bg="gray.300" />
        {/* Horizontal Line */}
        <Box position="absolute" top="50%" left="-8px" width="12px" height="1px" bg="gray.300" />

        {/* Content */}
        <Box display="flex" alignItems="center">
          {isObject && (
            <Button onClick={() => toggleExpand(key)} size="xs" variant="ghost" mr="1">
              {isExpanded[key] ? '-' : '+'}
            </Button>
          )}
          <Text fontWeight="bold" fontSize="sm" color="teal.600" mr="4">
            {key}:
          </Text>
          {!isObject && (
            <Text fontSize="sm" color="gray.700">
              {String(value)}
            </Text>
          )}
        </Box>

        {/* Render Child Nodes */}
        {isObject && isExpanded[key] && <DisplayObject data={value} level={level + 2} />}
      </Box>
    );
  };

  const filteredData = filterCriteria
    ? Object.entries(data).reduce((acc, [key, value]) => {
        if (filterCriteria(value)) {
          acc[key] = value;
        }
        return acc;
      }, {} as Record<string, any>)
    : data;

  return (
    <Box onDoubleClick={onDoubleClick}>
      {Object.entries(filteredData).map(([key, value], index, arr) => (
        <Box key={key} position="relative">
          {renderValue(value, key, index === arr.length - 1)}
        </Box>
      ))}
    </Box>
  );
};

export default DisplayObject;
