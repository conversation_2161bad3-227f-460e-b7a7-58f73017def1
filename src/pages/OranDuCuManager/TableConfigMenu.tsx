import { DeleteIcon, InfoOutlineIcon } from '@chakra-ui/icons';
import {
  Box,
  Code,
  IconButton,
  Menu,
  MenuButton,
  Menu<PERSON>tem,
  <PERSON>uList,
  <PERSON>dal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalHeader,
  ModalOverlay,
  useDisclosure,
} from '@chakra-ui/react';
import React, { useState } from 'react';
import { BsThreeDots } from 'react-icons/bs';
import { AUTH_TOKEN_KEY, READ_WRITE_ACCESS_ROLES } from '../../data/constants';
import useLogin from '../../hooks/useLogin';
import useConfigSets from './hooks/services/useConfigSets';
import useCustomResource from './hooks/services/useCustomResource';

type CellConfigMenuProps = {
  dataTestId: string;
  assetName: string;
  type: string;
  category?: string;
  kind?: string;
  id: number;
  menuType?: string;
};

const TableConfigMenu = (props: CellConfigMenuProps) => {
  const { dataTestId, assetName, type, category, kind, id, menuType } = props;
  const [isQueryEnabled, setIsQueryEnabled] = useState(false);

  const { isOpen: isOpenSingleCr, onOpen: onOpenSingleCr, onClose: onCloseSingleCr } = useDisclosure();

  const { checkApplicationAccess } = useLogin(AUTH_TOKEN_KEY);
  const checkRoleAccess = checkApplicationAccess(READ_WRITE_ACCESS_ROLES);

  const { deleteConfigSet } = useConfigSets();
  const { deleteCustomResource, getSingleCustomResource } = useCustomResource();
  const configSetMutation = deleteConfigSet();
  const customResourceMutation = deleteCustomResource();

  //API
  const {
    data: singleCustomResource,
    isLoading: isSingleCustomResourceLoading,
    error: singleCustomResourceError,
  } = getSingleCustomResource(id, assetName, isQueryEnabled);

  const handelDeleteCr = async () => {
    //onOpen();
    switch (type) {
      case 'configSet': {
        configSetMutation.mutate(id);
        break;
      }
      case 'customResource': {
        const payloadData = { cluster_id: id, name: assetName };
        customResourceMutation.mutate(payloadData);
        break;
      }
      case 'pod': {
        break;
      }
      case 'cluster': {
        break;
      }
      default: {
        console.error('Unknown type');
        break;
      }
    }
  };

  const renderSubComponent = ({ data }: { data: any }) => {
    if (!data) {
      return null;
    }

    return (
      <Box>
        <pre style={{ fontSize: '10px' }}>
          {/* <Code size={'md'} variant={'outline'} style={{ padding: '1rem' }}> */}
          <Code width="100%" variant={'outline'} style={{ padding: '1rem' }}>
            <div style={{ whiteSpace: 'pre-wrap' }}>
              {Object.entries(data).map(([key, value]) => {
                let cellRefIds;
                if (key === 'cell_refs' && typeof value === 'string') {
                  cellRefIds = value.split(',');
                }
                return (
                  <div key={key}>
                    {key}:
                    {cellRefIds ? (
                      cellRefIds.map((id, index) => (
                        <React.Fragment key={id}>
                          <Box
                            as="span"
                            _activeLink={{ bg: 'gray.700', color: 'white' }}
                            color="cornflowerblue"
                            cursor="pointer"
                            role="button"
                            tabIndex={0}
                            p="1"
                          >
                            {id}
                          </Box>
                          {index < cellRefIds.length - 1 ? ',' : ''}
                        </React.Fragment>
                      ))
                    ) : (
                      <Box
                        as="span"
                        _activeLink={{ bg: 'gray.700', color: 'white' }}
                        color={key === 'nodeId' || key === 'cell_refs' ? 'cornflowerblue' : 'inherit'}
                        cursor={key === 'nodeId' || key === 'cell_refs' ? 'pointer' : 'default'}
                        role="button"
                        tabIndex={0}
                        p={1}
                      >
                        {JSON.stringify(value, null, 2)}
                      </Box>
                    )}
                  </div>
                );
              })}
            </div>
          </Code>
        </pre>
      </Box>
    );
  };

  return (
    <>
      {checkRoleAccess && (
        <Menu data-testid={dataTestId}>
          <MenuButton
            data-testid={props.dataTestId}
            onClick={(e) => {
              e.stopPropagation();
            }}
            as={IconButton}
            aria-label="Options"
            icon={<BsThreeDots />}
            variant="outline"
          />
          <MenuList>
            {menuType === 'displayCR' ? (
              <MenuItem
                data-testid="display-cr"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setIsQueryEnabled(!isQueryEnabled);
                  onOpenSingleCr();
                }}
              >
                <InfoOutlineIcon mr="1rem" />
                Display {type} - {assetName}
              </MenuItem>
            ) : (
              <MenuItem
                data-testid="delete-cr"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handelDeleteCr();
                }}
              >
                <DeleteIcon mr="1rem" />
                Delete {type} - {assetName}
              </MenuItem>
            )}
          </MenuList>
        </Menu>
      )}

      {checkRoleAccess && singleCustomResource && (
        <Modal isOpen={isOpenSingleCr} onClose={onCloseSingleCr} size="5xl" isCentered>
          <ModalOverlay bg="blackAlpha.900" />
          <ModalContent>
            <ModalHeader>{assetName} custom resource</ModalHeader>
            <ModalCloseButton />
            <ModalBody>{renderSubComponent({ data: singleCustomResource })}</ModalBody>
          </ModalContent>
        </Modal>
      )}
    </>
  );
};

export default TableConfigMenu;
