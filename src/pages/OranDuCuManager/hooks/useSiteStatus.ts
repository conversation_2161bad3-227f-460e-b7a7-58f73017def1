import { Resource } from '../../../types/duCuManager.type';

const useSiteStatus = () => {
  return {
    getMatchingClusters: useGetMatchingClusters,
    getMatchingSitesCustomResourceList: useGetMatchingSitesCustomResourceList,
    doDeploymentsExist: useDoDeploymentsExist,
    getMatchingCrSites: useGetMatchingCrSites,
    getMatchingCrEnabledSites: useGetMatchingCrEnabledSites,
    checkEnabledConsistency: useCheckEnabledConsistency,
  };
};

const useGetMatchingClusters = (du: number | null, cuup: number | null, cucp: number | null): number | null => {
  return du === cuup && cuup === cucp ? du : null;
};

const useGetMatchingSitesCustomResourceList = (siteToMatch: string, customResourceList?: any): any[] => {
  if (!customResourceList?.crs) {
    return [];
  }
  return customResourceList.crs.filter((resource: any) => resource.site === siteToMatch);
};

const useDoDeploymentsExist = (siteToMatch: string, customResourceList?: any): boolean => {
  const matchingSites = useGetMatchingSitesCustomResourceList(siteToMatch, customResourceList);
  const requiredKinds = ['CuCpDeployment', 'CuUpDeployment', 'Split6DuDeployment'];
  const kindsFound = new Set(matchingSites.map((resource) => resource.kind));
  return requiredKinds.every((kind) => kindsFound.has(kind));
};

const useGetMatchingCrSites = (siteToMatch: string, customResourceList?: any): any[] => {
  return customResourceList?.crs.filter((resource: any) => resource.site === siteToMatch && resource.enabled) || [];
};

const useGetMatchingCrEnabledSites = (siteToMatch: string, customResourceList?: any): any => {
  return customResourceList?.crs.filter((resource: any) => resource.site === siteToMatch && resource.enabled) || [];
};

const convertEnabledToBoolean = (enabled: string): boolean => {
  return enabled.toLowerCase() === 'true';
};

const useCheckEnabledConsistency = (
  resources: Resource[],
  selectedCr?: string
): {
  allEnabled: boolean;
  statuses: Record<string, boolean>;
  isSelectedEnabled?: boolean;
} => {
  const result: Record<string, boolean> = {};
  let allEnabled = true;
  let isSelectedEnabled: boolean | undefined;

  resources.forEach((resource) => {
    const isEnabled = convertEnabledToBoolean(resource.enabled);
    result[resource.name] = isEnabled;

    if (!isEnabled) {
      allEnabled = false;
    }

    if (selectedCr && resource.name === selectedCr) {
      isSelectedEnabled = isEnabled;
    }
  });

  return {
    allEnabled,
    statuses: result,
    ...(selectedCr && { isSelectedEnabled }),
  };
};

export default useSiteStatus;
