const usePodStatus = () => {
  return {
    getMatchingSitesPods: useGetMatchingSitesPods,
    doesSiteExistInPod: useDoesSiteExistInPod,
  };
};

const useGetMatchingSitesPods = (siteToMatch: string, podList?: any): any[] => {
  return podList?.sites.filter((sites: any) => sites === siteToMatch);
};
const useDoesSiteExistInPod = (siteToMatch: string, podList?: any[]): boolean => {
  const matchingSites = useGetMatchingSitesPods(siteToMatch, podList);
  const booleanMatchingSites = matchingSites?.length > 0;
  return booleanMatchingSites;
};

export default usePodStatus;
