const extractValue = (field: any): string => {
  if (typeof field === 'object' && field !== null) {
    return field.user_setting?.toString() || field.default?.toString() || '';
  }
  return field?.toString() || '';
};

const useTransformIdentitiesTableData = (
  settings: Record<string, any>,
  selectedOption: any,
  originalData: any,
  ruVendor: string[]
) => {
  const initialFinalFormData = {
    deployment_name: selectedOption?.deployment_name ?? '',
    shared: {
      gNodeB: {
        id: Number(settings['shared-user_setting-0']) || 0,
      },
      PLMN: {
        MCC: extractValue(settings['shared-user_setting-1']),
        MNC: extractValue(settings['shared-user_setting-2']),
      },
      node_type: 'shared',
    },
    du: {
      gNodeB: {
        du_id: Number(settings['du-user_setting-0']) || 0,
      },
      tracking_area_code: extractValue(settings['du-user_setting-1']),
      ran_area_code: parseInt(extractValue(settings['du-user_setting-2']), 10) || 0,
      node_type: 'du',
      nRTAC: extractValue(settings['du-user_setting-4']) || originalData?.du?.nRTAC || '',
    },
    cu_cp: {
      gNodeB: {
        cu_name: extractValue(settings['cu_cp-user_setting-0']),
        index: originalData?.cu_cp?.gNodeB?.index || 0,
      },
      node_type: 'cu_cp',
    },
    cu_up: {
      node_type: 'cu_up',
    },
  };

  return initialFinalFormData;
};

export default useTransformIdentitiesTableData;
