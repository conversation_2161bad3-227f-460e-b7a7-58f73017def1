import { useMemo } from 'react';
import { EdgeInterface, NodeInterface } from '../../../types/duCuManager.type';

export const useEdgeBackgroundColor = (interfaceName: EdgeInterface) => {
  const backgroundColor = useMemo(() => {
    switch (interfaceName) {
      case 'du':
        return 'rgba(0, 191, 255, 0.3)'; // Deep Sky Blue
      case 'ru':
        return 'rgba(50, 205, 50, 0.3)'; // Lime Green
      case 'cu_cp':
        return 'rgba(255, 140, 0, 0.3)'; // Dark Orange
      case 'cu_up':
        return 'rgba(186, 85, 211, 0.3)'; // Medium Orchid
      default:
        return 'rgba(0, 0, 0, 0.3)'; // Default to semi-transparent black
    }
  }, [interfaceName]);

  return backgroundColor;
};

export const useNodeBackgroundColor = (interfaceName: NodeInterface) => {
  const backgroundColor = useMemo(() => {
    switch (interfaceName) {
      case 'ng_u':
        return 'rgba(186, 85, 211, 0.5)'; // Medium Orchid
      case 'ng_u_gw':
        return 'rgba(186, 85, 211, 0.5)'; // Medium Orchid
      case 'ng_c':
        return 'rgba(255, 140, 0, 0.5)'; // Medium Orchid
      case 'f1_c_gw':
        return 'rgba(0, 191, 255, 0.5)'; // Medium Orchid
      default:
        return 'rgba(255, 255, 255, 0.5)'; // Default to semi-transparent black
    }
  }, [interfaceName]);

  return backgroundColor;
};
