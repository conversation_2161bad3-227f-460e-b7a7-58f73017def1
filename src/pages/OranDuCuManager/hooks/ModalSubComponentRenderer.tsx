import React from 'react';
import { Box, Code } from '@chakra-ui/react';

type SubComponentRendererProps = {
  data: Record<string, any> | null;
};

const ModalSubComponentRenderer: React.FC<SubComponentRendererProps> = ({ data }) => {
  if (!data) {
    return null;
  }

  return (
    <Box>
      <pre style={{ fontSize: '10px' }}>
        <Code width="100%" variant="outline" style={{ padding: '1rem' }}>
          <div style={{ whiteSpace: 'pre-wrap' }}>
            {Object.entries(data).map(([key, value]) => {
              let cellRefIds;
              if (key === 'cell_refs' && typeof value === 'string') {
                cellRefIds = value.split(',');
              }
              return (
                <div key={key}>
                  {key}:
                  {cellRefIds ? (
                    cellRefIds.map((id, index) => (
                      <React.Fragment key={id}>
                        <Box
                          as="span"
                          _activeLink={{ bg: 'gray.700', color: 'white' }}
                          color="cornflowerblue"
                          cursor="pointer"
                          role="button"
                          tabIndex={0}
                          p="1"
                        >
                          {id}
                        </Box>
                        {index < cellRefIds.length - 1 ? ',' : ''}
                      </React.Fragment>
                    ))
                  ) : (
                    <Box
                      as="span"
                      _activeLink={{ bg: 'gray.700', color: 'white' }}
                      color={key === 'nodeId' || key === 'cell_refs' ? 'cornflowerblue' : 'inherit'}
                      cursor={key === 'nodeId' || key === 'cell_refs' ? 'pointer' : 'default'}
                      role="button"
                      tabIndex={0}
                      p={1}
                    >
                      {JSON.stringify(value, null, 2)}
                    </Box>
                  )}
                </div>
              );
            })}
          </div>
        </Code>
      </pre>
    </Box>
  );
};

export default ModalSubComponentRenderer;
