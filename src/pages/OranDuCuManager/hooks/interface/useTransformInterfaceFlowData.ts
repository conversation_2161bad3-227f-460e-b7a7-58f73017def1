import { useEffect } from 'react';
import { Edge, Node } from 'reactflow';
import { transformData } from './TransformData';
//NOTE: for the flow diagram
export type NodeData = {
  fields?: { name: string; label: string; value: string }[];
  node?: string;
  edge?: string;
};

export type TransformedData = {
  nodes: Node<NodeData>[];
  edges: Edge[];
};

const useTransformData = (getInterfaceData: any, setNodes: any, setEdges: any) => {
  useEffect(() => {
    if (getInterfaceData) {
      const { nodes, edges } = transformData(getInterfaceData);
      setNodes(nodes);
      setEdges(edges);
    }
  }, [getInterfaceData, setNodes, setEdges]);
};

export default useTransformData;
