import { MarkerType } from 'reactflow';

const processFlowData = (data: any) => {
  const nodes: any = [];
  const edges: any = [];
  let nodeId = 0;

  const createNode = (label: any, group: any) => {
    const node = {
      id: `node-${nodeId++}`,
      data: { label },
      position: { x: 0, y: 0 },
      style: {
        backgroundColor:
          group === 'A'
            ? 'rgba(56, 161, 105, 0.5)'
            : group === 'B'
            ? 'rgba(113, 128, 150, 0.5)'
            : 'rgba(255, 165, 0, 0.5)',
      },
    };
    nodes.push(node);
    return node.id;
  };

  const createEdge = (source: any, target: any, label: any) => {
    edges.push({
      id: `edge-${source}-${target}`,
      source,
      target,
      label,
      markerEnd: { type: MarkerType.ArrowClosed },
      type: 'smoothstep',
    });
  };

  // Create nodes
  const duId = createNode('DU', 'A');
  const cuCpId = createNode('CU-CP', 'C');
  const cuUpId = createNode('CU-UP', 'B');

  // Helper function to get node ID by target name
  const getTargetId = (target: any) => {
    switch (target.toLowerCase()) {
      case 'ru':
        return duId;
      case 'cucp':
        return cuCpId;
      case 'cuup':
        return cuUpId;
      case 'core':
        return createNode('Core', 'C');
      case 'fronthaul gateway':
        return createNode('Fronthaul Gateway', 'A');
      case 'midhaul gateway':
        return createNode('Midhaul Gateway', 'B');
      case 'backhaul gateway':
        return createNode('Backhaul Gateway', 'C');
      default:
        return createNode(target, 'C'); // Default to group C for unknown targets
    }
  };

  // Process DU data
  data.du.forEach((item: any) => {
    const [edgeType, fieldType] = item.name.split(' ');
    const targetId = getTargetId(item.target);
    createEdge(duId, targetId, `${edgeType}: ${fieldType}`);
  });

  // Process CU-CP data
  data.cu_cp.forEach((item: any) => {
    const [edgeType, fieldType] = item.name.split(' ');
    const targetId = getTargetId(item.target);
    createEdge(cuCpId, targetId, `${edgeType}: ${fieldType}`);
  });

  // Process CU-UP data
  data.cu_up.forEach((item: any) => {
    const [edgeType, fieldType] = item.name.split(' ');
    const targetId = getTargetId(item.target);
    createEdge(cuUpId, targetId, `${edgeType}: ${fieldType}`);
  });

  return { nodes, edges };
};

export default processFlowData;
