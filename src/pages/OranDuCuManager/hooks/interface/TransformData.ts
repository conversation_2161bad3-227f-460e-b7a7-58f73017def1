import { Edge, Node } from 'reactflow';
import { NodeData } from './useTransformInterfaceFlowData';
import {
  addStaticGroupNodes,
  addStaticRuNode,
  createNode,
  createNodeId,
  createTargetNodeId,
  handleIds,
  mapEdgeType,
  mapNodeType,
} from './Helpers';
//NOTE: for the flow diagram
export const transformData = (data: any) => {
  const nodes: Node<NodeData>[] = [];
  const edges: Edge[] = [];
  const addedNodes = new Set<string>();
  const addedEdges = new Set<string>();

  // Add static group nodes
  nodes.push(...addStaticGroupNodes());

  // Add RU node
  const ruNodes = addStaticRuNode(data, addedNodes);
  nodes.push(...ruNodes);

  const processNodesAndEdges = (items: any[], type: string, parentNode: string) => {
    if (items.length > 0) {
      const nodeId = createNodeId(type.toLowerCase(), type.toLowerCase(), 0);
      if (!addedNodes.has(nodeId)) {
        nodes.push(
          createNode(
            nodeId,
            mapNodeType(type),
            { x: Math.random() * 450, y: Math.random() * 450 },
            { fields: [], node: type.toLowerCase() },
            parentNode
          )
        );
        addedNodes.add(nodeId);
      }

      items.forEach((item, subIndex) => {
        const targetId = createTargetNodeId(item.target, subIndex);
        if (!addedNodes.has(targetId)) {
          nodes.push(
            createNode(
              targetId,
              mapNodeType(item.target),
              { x: Math.random() * 450, y: Math.random() * 450 },
              { fields: [], node: item.target },
              parentNode
            )
          );
          addedNodes.add(targetId);
        }

        const edgeId = `e-${nodeId}-${targetId}-${subIndex}`;
        if (!addedEdges.has(edgeId)) {
          edges.push({
            id: edgeId,
            source: nodeId,
            sourceHandle: handleIds[mapNodeType(type)].source[subIndex % handleIds[mapNodeType(type)].source.length],
            target: targetId,
            targetHandle:
              handleIds[mapNodeType(item.target)].target[subIndex % handleIds[mapNodeType(item.target)].target.length],
            animated: false,
            type: mapEdgeType(type),
            //name: item.name,
            data: {
              fields: [
                {
                  name: item.name,
                  label: item.name,
                  value: item.user_setting || item.default,
                },
              ],
              interface: [type.toLowerCase()],
              name: item.name,
              position: 'translate(-100%, 50%)',
              edge: 'fh_c_gw',
            },
          });
          addedEdges.add(edgeId);
        }
      });
    }
  };

  // Process DU nodes in group A
  processNodesAndEdges(data.du, 'DU', 'A');

  // Process CU_CP nodes in group B
  processNodesAndEdges(data.cu_cp, 'CU_CP', 'B');

  // Process CU_UP nodes in group C
  processNodesAndEdges(data.cu_up, 'CU_UP', 'C');

  return { nodes, edges };
};
