import { NodeData } from './useTransformInterfaceFlowData';

export const NODE_TYPES = {
  DU: 'DuNode',
  CU_CP: 'CuCpNode',
  CU_UP: 'CuUpNode',
  FRONT_HAUL_GW: 'FronthaulGwNode',
  BACK_HAUL_GW: 'BackhaulGwNode',
  MID_HAUL_GW: 'MidhaulGwNode',
  OUTSIDE_WORLD: 'OutsideWorld',
  RU: 'RuNode',
} as const;

export type NodeType = (typeof NODE_TYPES)[keyof typeof NODE_TYPES];

export const EDGE_TYPES = {
  FH_C_EDGE: 'FhCEdge',
  FH_U_EDGE: 'FhUEdge',
  FH_C_GATEWAY: 'FhCGatewayEdge',
  FH_U_GATEWAY: 'FhUGatewayEdge',
  F1_U_GATEWAY: 'F1UGatewayEdge',
  F1_C_GATEWAY: 'F1CGatewayEdge',
  F1_C_EDGE: 'F1CEdge',
  E1_EDGE: 'E1Edge',
  NG_C: 'NgCEdge',
  NG_U: 'NgUEdge',
  F1_U_EDGE: 'F1UEdge',
  NG_U_GATEWAY: 'NgUGatewayEdge',
} as const;

export const handleIds: Record<NodeType, { source: string[]; target: string[] }> = {
  DuNode: {
    source: [
      'DuNode-source-left-bottom',
      'DuNode-source-bottom-left',
      'DuNode-source-right-bottom',
      'DuNode-source-bottom-right',
      'DuNode-source-right-top',
      'DuNode-source-right-mid',
    ],
    target: ['DuNode-target-top', 'DuNode-target-bottom'],
  },
  CuCpNode: {
    source: ['CuCpNode-source-right', 'CuCpNode-source-bottom'],
    target: ['CuCpNode-target-left-top'],
  },
  CuUpNode: {
    source: ['CuUpNode-source-right', 'CuUpNode-source-bottom-right', 'CuUpNode-source-bottom-left'],
    target: ['CuUpNode-target-top', 'CuUpNode-target-left'],
  },
  FronthaulGwNode: {
    source: [],
    target: ['FronthaulGwNode-target-top-1', 'FronthaulGwNode-target-top-2'],
  },
  BackhaulGwNode: {
    source: [],
    target: ['BackhaulGwNode-target-top-1', 'BackhaulGwNode-target-top-2'],
  },
  MidhaulGwNode: {
    source: [],
    target: ['MidhaulGwNode-target-top-1'],
  },
  OutsideWorld: {
    source: [],
    target: ['OutsideWorld-target-left', 'OutsideWorld-target-right'],
  },
  RuNode: {
    source: ['RuNode-source-top', 'RuNode-source-bottom'],
    target: ['RuNode-target-top', 'RuNode-target-bottom'],
  },
};

export const extractTypeAndField = (name: string) => {
  const [type, ...fieldParts] = name.split(' ');
  const field = fieldParts.join(' ');
  return { type, field };
};

export const mapNodeType = (target: string): NodeType => {
  switch (target) {
    case 'Du':
      return NODE_TYPES.DU;
    case 'DU':
      return NODE_TYPES.DU;
    case 'CuCp':
      return NODE_TYPES.CU_CP;
    case 'CU_CP':
      return NODE_TYPES.CU_CP;
    case 'CuUp':
      return NODE_TYPES.CU_UP;
    case 'CU_UP':
      return NODE_TYPES.CU_UP;
    case 'Ru':
      return NODE_TYPES.RU;
    case 'RU':
      return NODE_TYPES.RU;
    case 'Fronthaul Gateway':
      return NODE_TYPES.FRONT_HAUL_GW;
    case 'Backhaul Gateway':
      return NODE_TYPES.BACK_HAUL_GW;
    case 'Midhaul Gateway':
      return NODE_TYPES.MID_HAUL_GW;
    case 'Core':
      return NODE_TYPES.OUTSIDE_WORLD;
    default:
      throw new Error(`Unknown target type: ${target}`);
  }
};

export const mapEdgeType = (type: string): string => {
  switch (type) {
    case 'FH-C':
      return EDGE_TYPES.FH_C_EDGE;
    case 'FH-U':
      return EDGE_TYPES.FH_U_EDGE;
    case 'F1-C':
      return EDGE_TYPES.F1_C_EDGE;
    case 'F1-U':
      return EDGE_TYPES.F1_U_EDGE;
    case 'E1':
      return EDGE_TYPES.E1_EDGE;
    case 'NgC':
      return EDGE_TYPES.NG_C;
    case 'NgU':
      return EDGE_TYPES.NG_U;
    default:
      return 'UnknownEdge';
  }
};

export const createNodeId = (name: string, type: string, index: number): string => `${type}-${name}-${index}`;
export const createTargetNodeId = (target: string, index: number): string => `target-${target}-${index}`;

export const createNode = (
  id: string,
  type: NodeType,
  position: { x: number; y: number },
  data: NodeData,
  parentNode: string
) => ({
  id,
  type,
  position,
  data,
  parentNode,
  extent: 'parent' as const,
});

export const addStaticGroupNodes = () => {
  // const nodes: Node<NodeData>[] = [
  const nodes: any[] = [
    {
      id: 'A',
      type: 'group',
      data: { label: null },
      position: { x: 500, y: 70 },
      style: {
        width: 1000,
        height: 1240,
        zIndex: 0,
      },
    },
    {
      id: 'B',
      type: 'group',
      data: { label: null },
      position: { x: 1700, y: 70 },
      style: {
        width: 800,
        height: 440,
        zIndex: 0,
      },
    },
    {
      id: 'C',
      type: 'group',
      data: { label: null },
      position: { x: 1700, y: 600 },
      style: {
        width: 800,
        height: 840,
        zIndex: 0,
      },
    },
    {
      id: 'D',
      type: 'group',
      data: { label: null },
      position: { x: 100, y: 100 },
      style: {
        width: 500,
        height: 500,
        zIndex: 0,
      },
    },
  ];
  return nodes;
};

export const addStaticRuNode = (data: any, addedNodes: Set<string>) => {
  // const nodes: Node<NodeData>[] = [];
  const nodes: any[] = [];

  const nodeId = createNodeId('ru', 'ru', 0);
  if (data.ru && data.ru.length > 0) {
    const ruNode = data.ru[0];
    nodes.push(
      createNode(
        nodeId,
        NODE_TYPES.RU,
        { x: 140, y: 120 },
        { fields: [{ name: 'ip', label: 'IP address', value: ruNode.ip }] },
        'D'
      )
    );
  } else {
    // Add default RU node data
    nodes.push(
      createNode(
        nodeId,
        NODE_TYPES.RU,
        { x: 140, y: 120 },
        { fields: [{ name: 'ip', label: 'IP address', value: '0.0.0.0' }] },
        'D'
      )
    );
  }

  addedNodes.add(nodeId);
  return nodes;
};
