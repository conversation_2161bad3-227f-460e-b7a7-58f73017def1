import { Box } from '@chakra-ui/react';
import React from 'react';
import StatusComponent from '../../../../components/icons/StatusIcon';
import { getStatusColor } from '../../../CellOverview/hooks/useStatus';
import { default as TableConfigMenu } from '../../TableConfigMenu';
export default function useCustomResourceColumns(selectedOption: number | null) {
  return React.useMemo(
    () => [
      {
        header: 'Site',
        accessorKey: 'site',
        id: 'site',
        cell: (props: any) => {
          const site = props.row.original?.site;
          return site;
        },
      },
      {
        header: 'Kind',
        accessorKey: 'kind',
        id: 'kind',
        cell: (props: any) => {
          const kind = props.row.original.kind;
          return kind;
        },
      },
      {
        header: 'Name',
        accessorKey: 'name',
        id: 'name',
        cell: (props: any) => {
          const name = props.row.original?.name;
          return name;
        },
      },
      {
        header: 'Enabled',
        accessorKey: 'enabled',
        id: 'enabled',
        cell: (props: any) => {
          const enabled = props.row.original.enabled;
          //return enabled;
          return (
            <Box>
              <StatusComponent
                dataTestId="cell-main-table-status-icon"
                boxSize="sm"
                color={getStatusColor(enabled)}
                status={enabled}
              />{' '}
            </Box>
          );
        },
      },
      {
        header: 'Menu',
        accessorKey: 'menu',
        id: 'menu',
        cell: (props) => {
          const customResourceName = props.row.original?.name;
          const id = selectedOption as number;
          return (
            <TableConfigMenu
              dataTestId="du-cu-manager-table-list-config-icon"
              type="customResource"
              assetName={customResourceName}
              id={id}
            />
          );
        },
      },
    ],
    [selectedOption]
  );
}
