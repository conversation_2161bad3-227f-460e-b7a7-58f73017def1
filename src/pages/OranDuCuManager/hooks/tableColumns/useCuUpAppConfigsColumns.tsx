import React from 'react';
import TableConfigMenu from '../../TableConfigMenu';

export default function useCuUpAppConfigsColumns() {
  return React.useMemo(
    //return React.useMemo<ColumnDef<CellType, unknown>[]>(
    () => [
      {
        header: 'Config set name',
        accessorKey: 'config set name',
        id: 'config set name',
        cell: (props: any) => {
          const configSetName = props.row.original.name;
          return configSetName;
        },
      },
      {
        header: 'Name',
        accessorKey: 'name',
        id: 'name',
        cell: (props: any) => {
          const names = props.row.original.meta.changes.map((change: any) => change.name);
          const combinedName = names.join(', ');
          return combinedName || '';
        },
      },
      {
        header: 'Id',
        accessorKey: 'id',
        id: 'id',
        cell: (props) => {
          const id = props.row.original.id;
          return id;
        },
      },
      {
        header: 'Category',
        accessorKey: 'category',
        id: 'category',
        cell: (props) => {
          const categories = props.row.original.meta.changes.map((change: any) => change.category);
          const combinedCategories = categories.join(', ');
          return combinedCategories || '';
        },
      },
      {
        header: 'Value',
        accessorKey: 'value',
        id: 'value',
        cell: (props) => {
          const values = props.row.original.meta.changes.map((change: any) => change.value);
          const combinedValues = values.join(', ');
          return combinedValues || '';
        },
      },
      {
        header: `Menu`,
        accessorKey: 'menu',
        id: 'menu',
        cell: (props) => {
          const categories = props.row.original.meta.changes.map((change: any) => change.category);
          const configSetName = props.row.original.name;
          const combinedCategories = categories.join(', ');
          const kind = props.row.original.cr_kind as string;
          const id = props.row.original.id as number;
          return (
            <TableConfigMenu
              dataTestId="du-cu-manager-table-list-config-icon"
              type="configSet"
              assetName={configSetName}
              category={combinedCategories}
              kind={kind}
              id={id}
            />
          );
        },
      },
    ],
    []
  );
}
