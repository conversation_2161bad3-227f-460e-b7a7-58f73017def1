import { useToast } from '@chakra-ui/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { registerCBSDDevices } from '../../../../services/orchestrator';

export function useRegisterCBSDDevices() {
  const queryClient = useQueryClient();
  const toast = useToast();

  const { mutate: registerCBSDDevicesMutation } = useMutation({
    mutationFn: (serial: string) => registerCBSDDevices(serial),
    onSuccess: ({ detail }) => {
      queryClient.invalidateQueries({ queryKey: ['getCBSDDevices'] });
      toast({
        title: 'Success',
        description: `CBSD device ${detail.displayName} is registered.`,
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.detail?.detail;
      toast({
        title: 'Error',
        description: `${errorMessage}`,
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
  });
  return { registerCBSDDevicesMutation };
}
