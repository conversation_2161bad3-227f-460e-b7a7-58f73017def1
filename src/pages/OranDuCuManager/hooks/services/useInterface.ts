import { useToast } from '@chakra-ui/react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { getInterfacesList, PayloadData, postInterface } from '../../../../services/duCuManager';

export default function useInterface() {
  return {
    createInterface: useCreateInterface,
    getInterfaceList: useInterfaceList,
  };
}

const useInterfaceList = (payloadData: PayloadData, isClustersValid: boolean) => {
  const { data, error, isLoading, refetch, isFetching } = useQuery(
    ['interfacesList', payloadData],
    () => getInterfacesList(payloadData),
    {
      enabled: isClustersValid,
    }
  );
  return { data, error, isLoading, refetch, isFetching };
};

const useCreateInterface = () => {
  const queryClient = useQueryClient();
  const toast = useToast();
  return useMutation({
    mutationFn: (simpleViewInterfaceData: any) => postInterface(simpleViewInterfaceData),
    onSuccess: (data, variables) => {
      toast({
        title: 'Interface created.',
        description: 'Interfaces have been updated successfully.',
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: (error) => {
      console.error('--- API error --- :', error);
      toast({
        title: `Error ${error}`,
        description: 'Something went wrong with the Interfaces update.',
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
  });
};
