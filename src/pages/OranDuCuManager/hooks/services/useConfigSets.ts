import { useToast } from '@chakra-ui/react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { deleteConfigSet, getConfigSets, postConfigSet } from '../../../../services/duCuManager';

const useConfigSets = () => {
  return {
    getConfigSetsList: useConfigSetsList,
    createConfigSet: useCreateConfigSet,
    deleteConfigSet: useDeleteConfigSet,
  };
};

const useConfigSetsList = () => {
  const { data, error, isLoading } = useQuery(['configSetsList'], getConfigSets, {
    enabled: true,
  });

  return { data, error, isLoading };
};

const useCreateConfigSet = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const toast = useToast();
  return useMutation({
    mutationFn: (data: any) => postConfigSet(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['configSetsList'] });
      toast({
        title: 'Config set created.',
        description: 'Config set has been created successfully.',
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
      navigate('/oran-du-cu-manager/advanced-view/config-sets');
    },
    onError: (error) => {
      toast({
        title: `Error ${error}`,
        description: 'Something went wrong with the config set creation.',
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
  });
};

const useDeleteConfigSet = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: (data: number) => deleteConfigSet(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['configSetsList'] });
      toast({
        title: 'Config set deleted.',
        description: 'Config set has been deleted successfully.',
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
      navigate('/oran-du-cu-manager/advanced-view/config-sets');
    },
    onError: (error) => {
      toast({
        title: `Error ${error}`,
        description: 'Something went wrong with the config set deletion.',
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
  });
};

export default useConfigSets;
