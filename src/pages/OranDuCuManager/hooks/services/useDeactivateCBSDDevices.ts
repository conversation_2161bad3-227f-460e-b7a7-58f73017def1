import { useToast } from '@chakra-ui/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';

import { deactivateCBSDDevices } from '../../../../services/orchestrator';

export function useDeactivateCBSDDevices() {
  const queryClient = useQueryClient();
  const toast = useToast();

  const { mutate: deactivateCBSDDevicesMutation } = useMutation({
    mutationFn: (serial: string) => deactivateCBSDDevices(serial),
    onSuccess: ({ displayName }) => {
      queryClient.invalidateQueries({ queryKey: ['getCBSDDevices'] });
      toast({
        title: 'Success',
        description: `CBSD device ${displayName} is Deactivated.`,
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: () => {
      toast({
        title: 'Error',
        description: `Failed to deactivate a device`,
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
  });
  return { deactivateCBSDDevicesMutation };
}
