import { useToast } from '@chakra-ui/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { deleteCPIDevices } from '../../../../services/orchestrator';
import { CPIForm } from '../../../../types/duCuManager.type';

export function useDeleteCPI(userId: number) {
  const queryClient = useQueryClient();
  const toast = useToast();
  const { mutate: deleteCPIDevicesMutation } = useMutation({
    mutationFn: (rowData: CPIForm) => deleteCPIDevices(rowData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['getCBSDDevices'] });
      toast({
        title: 'Success',
        description: `CPI device ${userId} is deleted.`,
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: () => {
      toast({
        title: 'Error',
        description: `Failed to delete ${userId}.`,
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
  });
  return { deleteCPIDevicesMutation };
}
