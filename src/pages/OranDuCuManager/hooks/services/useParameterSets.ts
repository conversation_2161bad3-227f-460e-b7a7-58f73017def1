import { useQuery } from '@tanstack/react-query';
import {
  getBands,
  getDUCellConfigModels,
  getParameterSets,
  getUserParams,
  getValidArfcns,
} from '../../../../services/duCuManager';
import { ValidArfcnsData } from '../../formFields/advancedView/configSetForms/SetId2';

const useParameterSets = () => {
  return {
    getParameterSetsList: useGetParameterSetsList,
    getParameterSetById: useGetParameterSetById,
    getCellConfigModels: useGetCellConfigModels,
    getBands: useGetBands,
    getValidArfcns: useGetValidArfcns,
  };
};

const useGetParameterSetsList = () => {
  const { data, error, isLoading } = useQuery(['ParameterSetsList'], getParameterSets, {
    enabled: true,
  });

  return { data, error, isLoading };
};

const useGetParameterSetById = (set_id: number | null) => {
  const { data, error, isLoading } = useQuery(['parameterSetById', set_id], () => getUserParams(set_id), {
    enabled:
      typeof set_id === 'number' && set_id > 0 && set_id !== 100 && set_id !== 48 && set_id !== 77 && set_id !== 78,
  });

  return { data, error, isLoading };
};

const useGetCellConfigModels = () => {
  const { data, error, isLoading } = useQuery(['cellConfigModels'], getDUCellConfigModels, {
    enabled: true,
  });

  return { data, error, isLoading };
};

const useGetBands = () => {
  const { data, error, isLoading } = useQuery(['configBands'], getBands, {
    enabled: true,
  });

  return { data, error, isLoading };
};

const useGetValidArfcns = (params: { band: string | null; bandwidth: string | null }) => {
  const { data, error, isLoading } = useQuery<ValidArfcnsData, Error>(
    ['configValidArfcn', params],
    () => getValidArfcns(params),
    {
      enabled: !!params.band && params.bandwidth !== null,
    }
  );

  return { data, error, isLoading };
};

export default useParameterSets;
