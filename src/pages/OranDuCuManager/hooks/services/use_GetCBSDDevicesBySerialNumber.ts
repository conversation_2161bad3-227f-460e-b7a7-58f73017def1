import { useQuery } from '@tanstack/react-query';
import { getCBSDDevicesBySerialNumber } from '../../../../services/orchestrator';
import { CBSDDevice } from '../../cbrsManagement/CBSD/editCBSD/EditCBSDForm';

export default function useGetCBSDDevicesBySerialNumber(
  serialNumber: string,
  isQueryEnabled: boolean
): { data: CBSDDevice | null; isLoading: boolean; error: unknown } {
  const query = useQuery(
    ['getCBSDDevicesBySerialNumber', serialNumber],
    async () => {
      const data = await getCBSDDevicesBySerialNumber(serialNumber);
      return data as CBSDDevice;
    },
    {
      enabled: isQueryEnabled,
      refetchInterval: false,
      retry: true,
    }
  );

  return { data: query.data ?? null, isLoading: query.isLoading, error: query.error };
}
