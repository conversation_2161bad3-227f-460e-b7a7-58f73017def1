import { useToast } from '@chakra-ui/react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  activateAllCustomResourcesForSite,
  activateCustomResource,
  deActivateAllCustomResourcesForSite,
  deActivateCustomResource,
  getDeploymentPods,
  getPods,
  getPodStatus,
} from '../../../../services/duCuManager';

const usePods = () => {
  return {
    getPodList: usePodList,
    getPodStatus: useGetPodStatus,
    getDeploymentPods: useGetDeploymentPods,
    activateCustomResource: useActivateCustomResource,
    deActivateCustomResource: useDeActivateCustomResource,
    activateAllSiteCustomResources: useActivateAllCustomResourcesForSite,
    deActivateAllSiteCustomResources: useDeactivateAllCustomResourcesForSite,
  };
};

const usePodList = (payloadData: number | null) => {
  const { data, error, isLoading, isFetching } = useQuery(['podList', payloadData], () => getPods(payloadData), {
    enabled: !!payloadData,
  });
  return { data, error, isLoading, isFetching };
};

const useGetPodStatus = (clusterId: number | null, podName: string) => {
  const { data, error, isLoading, isFetching } = useQuery(
    ['getPodStatus', clusterId, podName],
    () => getPodStatus(clusterId, podName),
    {
      enabled: !!clusterId,
    }
  );
  return { data, error, isLoading, isFetching };
};

const useGetDeploymentPods = (deployment_name: string, showTable: boolean) => {
  const { data, error, isLoading, isFetching, refetch } = useQuery(
    ['GetDeploymentPods', deployment_name],
    () => getDeploymentPods(deployment_name),
    {
      enabled: showTable,
    }
  );

  return { data, error, isLoading, isFetching, refetch };
};

const useActivateCustomResource = () => {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: activateCustomResource,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['customResourceList'] });
      setTimeout(() => {
        queryClient.invalidateQueries(['podList']);
      }, 2000);
      toast({
        title: 'Custom resource activated',
        description: 'Custom resource has been activated successfully.',
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: (error) => {
      console.error('Activation error:', error);
      toast({
        title: `Error ${error}`,
        description: 'Something went wrong with the Custom resource activation.',
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
  });
};

const useDeActivateCustomResource = () => {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: deActivateCustomResource,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['customResourceList'] });
      setTimeout(() => {
        queryClient.invalidateQueries(['podList']);
      }, 2000);
      toast({
        title: 'Custom resource de-activated',
        description: 'Custom resource has been de-activated successfully.',
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: (error) => {
      toast({
        title: `Error ${error}`,
        description: 'Something went wrong with the Custom resource de-activated.',
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
  });
};

const useActivateAllCustomResourcesForSite = () => {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: activateAllCustomResourcesForSite,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['GetDeploymentPods'] });
      queryClient.invalidateQueries({ queryKey: ['GetDeploymentCustomResources'] });
      setTimeout(() => {
        queryClient.invalidateQueries(['podList']);
      }, 2000);
      toast({
        title: 'Custom resource activated',
        description: 'Custom resource has been activated successfully.',
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: (error) => {
      console.error('Activation error:', error);
      toast({
        title: `Error ${error}`,
        description: 'Something went wrong with the Custom resource activation.',
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
      throw error;
    },
  });
};

const useDeactivateAllCustomResourcesForSite = () => {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: deActivateAllCustomResourcesForSite,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['GetDeploymentPods'] });
      queryClient.invalidateQueries({ queryKey: ['GetDeploymentCustomResources'] });
      setTimeout(() => {
        queryClient.invalidateQueries(['podList']);
      }, 2000);
      toast({
        title: 'Custom resource de-activated',
        description: 'Custom resource has been de-activated successfully.',
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: (error) => {
      toast({
        title: `Error ${error}`,
        description: 'Something went wrong with the Custom resource de-activated.',
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
      throw error;
    },
  });
};

export default usePods;
