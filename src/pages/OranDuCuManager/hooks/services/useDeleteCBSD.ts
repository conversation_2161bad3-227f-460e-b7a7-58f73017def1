import { useToast } from '@chakra-ui/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { deleteCBSDDevices } from '../../../../services/orchestrator';

export function useDeleteCBSD(serial: string) {
  const queryClient = useQueryClient();
  const toast = useToast();
  const { mutate: deleteCBSDDevicesMutation } = useMutation({
    mutationFn: (serial: string) => deleteCBSDDevices(serial),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['getCBSDDevices'] });
      toast({
        title: 'Success',
        description: `CBSD device ${serial} is deleted.`,
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: () => {
      toast({
        title: 'Error',
        description: `Failed to delete ${serial}.`,
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
  });
  return { deleteCBSDDevicesMutation };
}
