import { useToast } from '@chakra-ui/react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { getRfUseCases } from '../../../../services/duCuManager';

const useRfUseCases = () => {
  return {
    getRfUseCases: useGetRfUseCases,
  };
};

export const useGetRfUseCases = () => {
  const { data, error, isLoading, refetch, isFetching } = useQuery(['getRfUseCases'], () => getRfUseCases(), {
    enabled: true,
  });
  return { data, error, isLoading, refetch, isFetching };
};

export default useRfUseCases;
