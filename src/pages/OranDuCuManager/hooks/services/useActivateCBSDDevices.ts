import { useToast } from '@chakra-ui/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';

import { activateCBSDDevices } from '../../../../services/orchestrator';

export function useActivateCBSDDevices() {
  const queryClient = useQueryClient();
  const toast = useToast();

  const { mutate: activateCBSDDevicesMutation } = useMutation({
    mutationFn: (serial: string) => activateCBSDDevices(serial),
    onSuccess: ({ displayName }) => {
      queryClient.invalidateQueries({ queryKey: ['getCBSDDevices'] });
      toast({
        title: 'Success',
        description: `CBSD device ${displayName} is activated.`,
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: () => {
      toast({
        title: 'Error',
        description: `Failed to activate a device`,
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
  });
  return { activateCBSDDevicesMutation };
}
