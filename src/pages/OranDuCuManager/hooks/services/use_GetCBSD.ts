import { useQuery } from '@tanstack/react-query';
import { getInvCluster, getRuManifest } from '../../../../services/inventoryManager';

export function useGetCluster(isEdit = false) {
  const { status, isLoading, error, data } = useQuery(['getInvCluster'], () => getInvCluster(), {
    refetchInterval: isEdit ? false : 30000,
    retry: 2,
  });
  return { status, isLoading, error, data };
}

export function useGetRuManifest() {
  const { status, isLoading, error, data } = useQuery(['getRuManifest'], () => getRuManifest(), {
    refetchInterval: 30000,
    retry: 2,
  });
  return { status, isLoading, error, data };
}
