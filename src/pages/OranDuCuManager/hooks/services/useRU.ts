import { useToast } from '@chakra-ui/react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { getAllRus, getAllRusManifest, getDeploymentRus, postSetDeploymentRus } from '../../../../services/duCuManager';

const useRus = () => {
  return {
    getDeploymentRus: useGetDeploymentRus,
    getAllRus: useGetAllRus,
    getAllRusManifest: useGetAllRusManifest,
    setRusForDeployment: useSetRusForDeployment,
  };
};

export const useGetDeploymentRus = (payloadData: string, deployment_type: string) => {
  const { data, error, isLoading, refetch, isFetching } = useQuery(
    ['deploymentRus', payloadData],
    () => getDeploymentRus(payloadData),
    {
      enabled: deployment_type === 'edit' ? true : false,
    }
  );
  return { data, error, isLoading, refetch, isFetching };
};

export const useGetAllRus = (deployment_type: string) => {
  const { data, error, isLoading, refetch, isFetching } = useQuery(['getAllRus'], () => getAllRus(), {
    enabled: deployment_type === 'edit' ? true : false,
  });
  return { data, error, isLoading, refetch, isFetching };
};

export const useGetAllRusManifest = (deployment_type: string) => {
  const { data, error, isLoading, refetch, isFetching } = useQuery(['getAllRusManifest'], () => getAllRusManifest(), {
    enabled: deployment_type === 'edit' ? true : false,
  });
  return { data, error, isLoading, refetch, isFetching };
};

export const useSetRusForDeployment = () => {
  const queryClient = useQueryClient();
  const toast = useToast();
  return useMutation({
    mutationFn: (payloadData: any) => postSetDeploymentRus(payloadData),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['deploymentRus'] });
      queryClient.invalidateQueries({ queryKey: ['getAllRus'] });
      toast({
        title: 'RU attached.',
        description: 'RU has been attached to the deployment successfully.',
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: (error) => {
      console.error('--- API error --- :', error);
      toast({
        title: `Error ${error}`,
        description: 'Something went wrong with the deployment custom resource creation.',
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
      throw error;
    },
  });
};

export default useRus;
