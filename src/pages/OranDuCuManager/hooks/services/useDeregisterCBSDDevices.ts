import { useToast } from '@chakra-ui/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { deRegisterCBSDDevices } from '../../../../services/orchestrator';

export function useDeregisterCBSDDevices() {
  const queryClient = useQueryClient();
  const toast = useToast();

  const { mutate: deRegisterCBSDDevicesMutation } = useMutation({
    mutationFn: (serial: string) => deRegisterCBSDDevices(serial),
    onSuccess: ({ id }) => {
      queryClient.invalidateQueries({ queryKey: ['getCBSDDevices'] });
      toast({
        title: 'Success',
        description: `CBSD device ${id} has been deregistered.`,
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.detail?.detail;
      toast({
        title: 'Error',
        description: `${errorMessage}`,
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
  });
  return { deRegisterCBSDDevicesMutation };
}
