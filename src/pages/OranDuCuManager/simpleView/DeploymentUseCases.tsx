import { Box, Flex, Heading, Stack, useColorModeValue, useDisclosure } from '@chakra-ui/react';
import { useState } from 'react';
import { useLocation } from 'react-router-dom';
import { SelectedOption } from '../../../types/duCuManager.type';
import AppBar from '../AppBar';
import SimpleViewAppBar from './SimpleViewAppBar';
import DeploymentUseCasesForm from '../formFields/simpleView/DeploymentUseCasesForm';

const DeploymentUseCases = () => {
  const location = useLocation();
  const passedData: any = location.state;

  const deployment_type = passedData?.deployment_type;
  const generatedCellRef = passedData?.generatedCellRef;
  const deployment_name = passedData?.selectedOption?.deployment_name;

  const colorModeValue = useColorModeValue('sm', 'sm-dark');
  const [selectedOption, setSelectedOption] = useState<SelectedOption>({
    deployment_name: generatedCellRef || deployment_name,
    du_site_name: '',
    cucp_site_name: '',
    cuup_site_name: '',
    du_cluster: null,
    cucp_cluster: null,
    cuup_cluster: null,
    du_cell_config_set_ids: [],
    du_app_config_set_ids: [],
    cu_cell_config_set_ids: [],
    cu_cp_app_config_set_ids: [],
    cu_up_app_config_set_ids: [],
    ru_vendor: '',
    f1_ip: '',
    e1_ip: '',
    deployment_params: {
      is_comp: false,
      comp_trp_count: 0,
      ssb_arfcns: [0],
      du_fh_vlans: [0],
    },
  });

  return (
    <>
      <Stack
        spacing="1"
        direction={{
          base: 'column',
          lg: 'row',
        }}
        justify="space-between"
      >
        <Stack spacing="1">
          <Heading fontWeight="bold">O-RAN DU/CU Manager</Heading>
        </Stack>
      </Stack>
      <Stack
        spacing={{
          base: '5',
          lg: '6',
        }}
      >
        <Box
          bg="bg-surface"
          boxShadow={{
            base: 'none',
            md: colorModeValue,
          }}
        >
          {/* App bar */}
          <Flex justify="center" m="8">
            {deployment_type === 'edit' && (
              <SimpleViewAppBar deployment_type={deployment_type} selectedOption={selectedOption} />
            )}
            {deployment_type === 'new' && <AppBar caller="simpleView" />}
          </Flex>
          <Heading as="h2" size="lg" textAlign="center" pb="4">
            RF use case
          </Heading>
          <Box p="12">
            <DeploymentUseCasesForm data={passedData} />
          </Box>
        </Box>
      </Stack>
    </>
  );
};

export default DeploymentUseCases;
