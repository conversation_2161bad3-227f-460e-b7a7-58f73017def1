import { <PERSON>, But<PERSON>, Divider, <PERSON>lex, <PERSON><PERSON>, Stack, useColorModeValue } from '@chakra-ui/react';
import { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import Loader from '../../../components/loader/Loader';
import { InterfaceFieldData } from '../../../types/duCuManager.type';
import AppBar from '../AppBar';
import useTransformIdentitiesTableData from '../hooks/identities/useTransformIdentitiesTableData';
import useClusterList from '../hooks/services/useClusters';
import useIdentity from '../hooks/services/useIdentity';
import InterfaceAndIdentitiesForm from './interfaces/summary/InterfaceAndIdentitiesForm';
import { isEqual, omit } from 'lodash';
import SimpleViewAppBar from './SimpleViewAppBar';
import { ChevronRightIcon } from '@chakra-ui/icons';
import useLogin from '../../../hooks/useLogin';
import { AUTH_TOKEN_KEY, READ_WRITE_ACCESS_ROLES } from '../../../data/constants';

export const initialFieldData: InterfaceFieldData = {
  default: null,
  user_setting: null,
  name: '',
  target: '',
};

const Identities = () => {
  const [isCreateIdentitiesSuccess, setIsCreateIdentitiesSuccess] = useState(false);
  const [createResponseData, setCreateResponseData] = useState(null);
  const [editedUserSettings, setEditedUserSettings] = useState<InterfaceFieldData>(initialFieldData);
  const [editing, setEditing] = useState<Record<string, boolean>>({});
  const [isButtonDisabled, setIsButtonDisabled] = useState(true);

  const { checkNmsDevAccess } = useLogin(AUTH_TOKEN_KEY);
  const checkRoleAccess = checkNmsDevAccess(READ_WRITE_ACCESS_ROLES);

  const navigate = useNavigate();
  const location = useLocation();
  const passedData: any = location.state;
  const deployment_type = passedData?.deployment_type;
  const selectedOption = passedData?.selectedOption;

  const colorModeValue = useColorModeValue('sm', 'sm-dark');

  //API
  const { createIdentities, getIdentitiesList } = useIdentity();
  const mutation = createIdentities();
  const { isLoading } = mutation;
  //GET Clusters
  const { data: clusterList = [], isLoading: isClusterLoading, error: clusterError } = useClusterList();
  //GET Identities
  const {
    data: getIdentitiesData,
    error: identitiesError,
    isLoading: isIdentitiesLoading,
    refetch: refetchIdentities,
    isFetching: isIdentitiesFetching,
  } = getIdentitiesList(selectedOption?.deployment_name, true);

  const updatedData = useTransformIdentitiesTableData(
    editedUserSettings,
    selectedOption,
    getIdentitiesData,
    selectedOption?.ru_vendor
  );

  // Submit form
  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    mutation.mutate(updatedData, {
      onSuccess: (data) => {
        setIsCreateIdentitiesSuccess(true);
        setCreateResponseData(data);
        refetchIdentities();

        setEditing({});
      },
      onError: (error) => {
        console.error('Error submitting data:', error);
      },
    });
  };

  if (selectedOption && isIdentitiesLoading) return <Loader />;

  return (
    <>
      <Stack
        spacing="1"
        direction={{
          base: 'column',
          lg: 'row',
        }}
        justify="space-between"
      >
        <Stack spacing="1">
          <Heading fontWeight="bold">O-RAN DU/CU Manager</Heading>
        </Stack>
      </Stack>
      <Stack
        spacing={{
          base: '5',
          lg: '6',
        }}
      >
        <Box
          bg="bg-surface"
          boxShadow={{
            base: 'none',
            md: colorModeValue,
          }}
        >
          {/* App bar */}
          <Flex justify="center" m="8">
            {deployment_type === 'edit' && (
              <SimpleViewAppBar deployment_type={deployment_type} selectedOption={selectedOption} />
            )}
            {deployment_type === 'new' && <AppBar caller="simpleView" />}
          </Flex>
          <Heading as="h2" size="lg" textAlign="center" pb="4">
            Identities
          </Heading>
          <Box p="12">
            <InterfaceAndIdentitiesForm
              caller="identities"
              isCreateIdentitiesSuccess={isCreateIdentitiesSuccess}
              createResponseData={createResponseData}
              getApiData={getIdentitiesData}
              clusterList={clusterList}
              editedUserSettings={editedUserSettings}
              setEditedUserSettings={setEditedUserSettings}
              isIdentitiesFetching={isIdentitiesFetching}
              selectedOption={selectedOption}
              setIsButtonDisabled={setIsButtonDisabled}
              setEditing={setEditing}
              editing={editing}
            />
            {isLoading ? (
              <Loader />
            ) : (
              <form onSubmit={handleSubmit}>
                <Divider />
                {deployment_type === 'edit' ? (
                  <Flex justifyContent="space-between" alignItems="center" py="4">
                    {false && <Button variant="primary">placeholder Button</Button>}

                    {checkRoleAccess && (
                      <Box flex="1" textAlign="center">
                        <Button
                          variant="primary"
                          type="submit"
                          isDisabled={isEqual(editedUserSettings, initialFieldData)}
                        >
                          Update
                        </Button>
                      </Box>
                    )}

                    {false && <Button variant="primary">Update Identity</Button>}
                  </Flex>
                ) : (
                  <Flex justifyContent="space-between" alignItems="center" py="4">
                    {false && <Button variant="primary">placeholder Button</Button>}

                    {checkRoleAccess && (
                      <Box flex="1" textAlign="center">
                        <Button variant="primary" type="submit" isDisabled={isButtonDisabled}>
                          Update
                        </Button>
                      </Box>
                    )}

                    {true && (
                      <Button
                        variant="primary"
                        onClick={() =>
                          navigate('/oran-du-cu-manager/simple-view/update-pods', {
                            state: { selectedOption, deployment_type },
                          })
                        }
                      >
                        Pods
                        <ChevronRightIcon boxSize={6} />
                      </Button>
                    )}
                  </Flex>
                )}
              </form>
            )}
          </Box>
        </Box>
      </Stack>
    </>
  );
};

export default Identities;
