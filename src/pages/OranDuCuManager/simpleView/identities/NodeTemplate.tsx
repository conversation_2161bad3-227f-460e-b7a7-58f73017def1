import { InfoOutlineIcon } from '@chakra-ui/icons';
import { Box, Flex, Input, Text, Tooltip, useTheme } from '@chakra-ui/react';
import React, { useCallback, useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router';
import { ReactFlowNodeData } from '../../../../types/duCuManager.type';
import { useEdgeBackgroundColor } from '../../hooks/useGetColours';
//import { getNodeStyles } from '../interfaceFlowDiagram/styles/getNodeStyles';

const NodeTemplate: React.FC<{ id: string; data: ReactFlowNodeData }> = ({ id, data }) => {
  const [fields, setFields] = useState(data?.fields || []);
  const theme = useTheme();
  //const customNodeStyles = getNodeStyles(theme);
  const backgroundColor = useEdgeBackgroundColor(data.node);

  const navigate = useNavigate();
  const location = useLocation();
  const passedData: any = location.state;

  useEffect(() => {
    setFields(data?.fields || []);
  }, [data?.fields]);

  const handleChange = useCallback(
    (name: string, value: string) => {
      setFields((prevFields: any) =>
        prevFields.map((field: any) => (field.name === name ? { ...field, value } : field))
      );
      data.onChange(id, name, value);
    },
    [data, id]
  );

  return (
    <Box
      textAlign="center"
      style={{
        width: '100%',
        height: '100%',
        border: `4px solid ${backgroundColor}`,
        backgroundColor: 'rgba(255, 255, 255, 0.2)',
        borderRadius: '0',
      }}
      //__css={customNodeStyles.content}
    >
      <Box
        pb="2"
        style={{
          backgroundColor: `${backgroundColor}`,
          borderRadius: '0',
        }}
        //__css={customNodeStyles.header}
      >
        <Flex align="center" justify="center">
          <Text mr={2}>{data?.name}</Text>
          <Tooltip label="Info" fontSize="md">
            <InfoOutlineIcon />
          </Tooltip>
        </Flex>
      </Box>
      <Flex m="8">
        {fields.map((field: any) => {
          const value =
            field.name === 'cluster'
              ? passedData?.selectedOption?.cluster
              : field.name === 'site'
              ? passedData?.selectedOption?.site
              : field.value;

          return (
            <Box key={`${field.name}`} mr="2">
              <Text textAlign="left">{field.label}:</Text>
              <Input
                width="100%"
                type="text"
                backgroundColor="white"
                style={{
                  pointerEvents: 'all',
                  width: '150px',
                }}
                value={value}
                onChange={(e) => handleChange(field.name, e.target.value)}
              />
            </Box>
          );
        })}
      </Flex>
    </Box>
  );
};

const styles = {
  handle: {
    background: '#555',
  },
};

export default NodeTemplate;
