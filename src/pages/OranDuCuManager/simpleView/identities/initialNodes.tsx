import { Node } from 'reactflow';

export type NodeData = {
  fields?: { name: string; label: string; value: string }[];
  node?: string;
  name?: string;
  edge?: string;
};

export type GroupNodeData = {
  label: null;
};

export type CustomNode = Node<NodeData>;
export type GroupNode = Node<GroupNodeData>;

type InitialNodeType = Node<NodeData | GroupNodeData>;

export const initialIdentitiesTableLayout: any[] = [
  {
    id: '1',
    type: 'SharedNode',
    position: { x: 1100, y: 10 },
    data: {
      fields: [
        { name: 'gnodeb_id_length', label: 'Id Length', value: '' },
        { name: 'gnodeb_id', label: 'Id', value: '' },
        { name: 'plmn_mcc', label: 'MCC', value: '' },
        { name: 'plmn_mnc', label: 'MNC', value: '' },
        { name: 'node_type', label: 'node type', value: 'shared' },
      ],
      node: 'shared',
      name: 'Shared',
    },
  },
  // Group A
  {
    id: '2',
    type: 'DuNode',
    position: { x: 900, y: 310 },
    data: {
      fields: [
        { name: 'gNodeB_du_id', label: 'gNodeB Du id', value: '' },
        { name: 'gNodeB_du_name', label: 'gNodeB Du name', value: '' },
        { name: 'local_cell_id', label: 'local cell id', value: '' },
        { name: 'physical_cell_id', label: 'physical cell id', value: '' },
        { name: 'tracking_area_code', label: 'tracking area code', value: '' },
        { name: 'ran_area_code', label: 'ran area code', value: '' },
        { name: 'node_type', label: 'node type', value: 'du' },
      ],
      node: 'du',
      name: 'DU',
    },
  },
  // Group B
  {
    id: '3',
    type: 'CuCpNode',
    position: { x: 850, y: 650 },
    data: {
      fields: [
        { name: 'gNodeB_cu_name', label: 'gNodeB CU name', value: '' },
        { name: 'gNodeB_index', label: 'gNodeB index', value: '' },
        { name: 'node_type', label: 'node type', value: 'cu_cp' },
      ],
      node: 'cu_cp',
      name: 'CU-CP',
    },
  },

  // Group C
  {
    id: '4',
    type: 'CuUpNode',
    position: { x: 1900, y: 650 },
    data: {
      fields: [{ name: 'node_type', label: 'node type', value: 'cu_up' }],
      node: 'cu_up',
      name: 'CU-UP',
    },
  },
];

export const initialNodes: InitialNodeType[] = [
  {
    id: '1',
    type: 'SharedNode',
    position: { x: 1100, y: 10 },
    data: {
      fields: [
        { name: 'gnodeb_id_length', label: 'Id Length', value: '' },
        { name: 'plmn_mcc', label: 'MCC', value: '' },
        { name: 'plmn_mnc', label: 'MNC', value: '' },
        { name: 'node', label: 'Node', value: '' },
        { name: 'ip', label: 'IP address', value: '' },
        { name: 'node_type', label: 'node type', value: 'shared' },
      ],
      node: 'shared',
      name: 'Shared',
    },
  },
  // Group A
  {
    id: '2',
    type: 'DuNode',
    position: { x: 900, y: 310 },
    data: {
      fields: [
        { name: 'cluster', label: 'Cluster', value: '' },
        { name: 'site', label: 'Site label', value: '' },
        { name: 'gNodeB_cu_name', label: 'gNodeB CU name', value: '' },
        { name: 'gNodeB_index', label: 'gNodeB index', value: '' },
        { name: 'local_cell_id', label: 'local cell id', value: '' },
        { name: 'physical_cell_id', label: 'physical cell id', value: '' },
        { name: 'tracking_area_code', label: 'tracking area code', value: '' },
        { name: 'ran_area_code', label: 'ran area code', value: '' },
        { name: 'node_type', label: 'node type', value: 'du' },
      ],
      node: 'du',
      name: 'DU',
    },
  },
  // Group B
  {
    id: '3',
    type: 'CuCpNode',
    position: { x: 850, y: 650 },
    data: {
      fields: [
        { name: 'cluster', label: 'Cluster', value: '' },
        { name: 'site', label: 'Site label', value: '' },
        { name: 'gNodeB_cu_name', label: 'gNodeB CU name', value: '' },
        { name: 'gNodeB_index', label: 'gNodeB index', value: '' },
        { name: 'node_type', label: 'node type', value: 'cu_cp' },
      ],
      node: 'cu_cp',
      name: 'CU-CP',
    },
  },

  // Group C
  {
    id: '4',
    type: 'CuUpNode',
    position: { x: 1900, y: 650 },
    data: {
      fields: [
        { name: 'cluster', label: 'Cluster', value: '' },
        { name: 'site', label: 'Site label', value: '' },
        { name: 'node_type', label: 'node type', value: 'cu_up' },
      ],
      node: 'cu_up',
      name: 'CU-UP',
    },
  },
];
