import { Box, Flex, Heading, Stack, useColorModeValue } from '@chakra-ui/react';
import { useLocation } from 'react-router';
import AppBar from '../AppBar';
import InterfaceTable from './interfaces/InterfaceTable';
import SimpleViewAppBar from './SimpleViewAppBar';

const Interfaces = () => {
  const colorModeValue = useColorModeValue('sm', 'sm-dark');

  const location = useLocation();
  const passedData: any = location.state;
  const deployment_type = passedData?.deployment_type;
  const selectedOption = passedData?.selectedOption;

  return (
    <>
      <Stack
        spacing="1"
        direction={{
          base: 'column',
          lg: 'row',
        }}
        justify="space-between"
      >
        <Stack spacing="1">
          <Heading fontWeight="bold">O-RAN DU/CU Manager</Heading>
        </Stack>
      </Stack>
      <Stack
        spacing={{
          base: '5',
          lg: '6',
        }}
      >
        <Box
          bg="bg-surface"
          boxShadow={{
            base: 'none',
            md: colorModeValue,
          }}
        >
          {/* App bar */}
          <Flex justify="center" m="8">
            {deployment_type === 'edit' && (
              <SimpleViewAppBar deployment_type={deployment_type} selectedOption={selectedOption} />
            )}
            {deployment_type === 'new' && <AppBar caller="simpleView" />}
          </Flex>
          <Heading as="h2" size="lg" textAlign="center" pb="4">
            Interfaces
          </Heading>

          {/* Interfaces Map */}
          <Box>
            <InterfaceTable />
          </Box>
        </Box>
      </Stack>
    </>
  );
};

export default Interfaces;
