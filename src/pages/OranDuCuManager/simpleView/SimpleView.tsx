import { AddIcon, EditIcon } from '@chakra-ui/icons';
import {
  Box,
  Button,
  Circle,
  Flex,
  Heading,
  HStack,
  SimpleGrid,
  Stack,
  Text,
  useColorModeValue,
  VStack,
} from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { FormProvider, useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { z } from 'zod';
import { useState } from 'react';
import SelectFormField from '../../../components/form/oranDuCuManager/select/SelectFormField';
import { SelectedOption } from '../../../types/duCuManager.type';
import AppBar from '../AppBar';
import useCustomResource from '../hooks/services/useCustomResource';
import Loader from '../../../components/loader/Loader';
import useLogin from '../../../hooks/useLogin';
import { AUTH_TOKEN_KEY, READ_WRITE_ACCESS_ROLES } from '../../../data/constants';

const SimpleView = () => {
  const colorModeValue = useColorModeValue('sm', 'sm-dark');
  const navigate = useNavigate();

  const { checkNmsDevAccess, getNMSRoles } = useLogin(AUTH_TOKEN_KEY);
  const checkRoleAccess = checkNmsDevAccess(READ_WRITE_ACCESS_ROLES);

  const [selectedOption, setSelectedOption] = useState<SelectedOption>({
    deployment_name: '',
    du_site_name: '',
    cucp_site_name: '',
    cuup_site_name: '',
    du_cluster: null,
    cucp_cluster: null,
    cuup_cluster: null,
    du_cell_config_set_ids: [],
    du_app_config_set_ids: [],
    cu_cell_config_set_ids: [],
    cu_cp_app_config_set_ids: [],
    cu_up_app_config_set_ids: [],
    ru_vendor: 'EdgeQ',
    f1_ip: '',
    e1_ip: '',
  });

  // API calls
  const { getAllDeployments } = useCustomResource();

  //Get all Deployments
  const {
    data: allDeployments,
    isLoading: isAllDeploymentsLoading,
    error: allDeploymentsError,
  } = getAllDeployments('edit');

  const nameRegex = /^[a-z0-9]([-/.a-z0-9]*?[a-z0-9])$/;

  const schema = z.object({
    deployment_name: z
      .string()
      .min(2, { message: 'Name must be at least 2 characters' })
      .regex(nameRegex, { message: 'Invalid format' }),
  });

  const methods = useForm<FormData>({
    resolver: zodResolver(schema),
  });

  const clusterSelectHandler = (value: string) => {
    const selectedDeployment = allDeployments.find((deployment: any) => deployment.id === parseInt(value, 10));
    if (selectedDeployment) {
      setSelectedOption(selectedDeployment);
    }
  };

  return (
    <>
      <Stack
        spacing="1"
        direction={{
          base: 'column',
          lg: 'row',
        }}
        justify="space-between"
      >
        <Stack spacing="1">
          <Heading fontWeight="bold">Simple O-RAN DU/CU Manager</Heading>
        </Stack>
      </Stack>
      <Stack
        spacing={{
          base: '5',
          lg: '6',
        }}
      >
        <Box
          bg="bg-surface"
          boxShadow={{
            base: 'none',
            md: colorModeValue,
          }}
        >
          <Flex justify="center" m="8">
            <AppBar caller="simpleView" />
          </Flex>

          {/* Cards */}
          <Box mx="15%" my="20">
            <SimpleGrid
              columns={{
                base: 1,
                md: 2,
              }}
              spacing="6"
            >
              {/* Create a new deployment */}
              <Flex
                data-testid="create-deployment"
                direction="column"
                alignItems="center"
                rounded="md"
                padding="8"
                position="relative"
                bg={useColorModeValue('white', 'gray.700')}
                shadow={{
                  md: 'base',
                }}
              >
                <Circle size="6rem" bg={'bg-muted'}>
                  <AddIcon boxSize={6} />
                </Circle>
                <VStack spacing="1" flex="1" mt="3" mb="3">
                  <HStack>
                    <Text marginBottom={'1.5rem'} fontWeight="bold">
                      Create a new deployment
                    </Text>
                  </HStack>
                </VStack>
                {checkRoleAccess ? (
                  <Button
                    variant="outline"
                    colorScheme="brand.600"
                    rounded="full"
                    size="sm"
                    width="full"
                    onClick={() =>
                      navigate('/oran-du-cu-manager/simple-view/deployment-cell', {
                        state: { deployment_type: 'new' },
                      })
                    }
                  >
                    Launch
                  </Button>
                ) : null}
              </Flex>
              {/* Edit an existing deployment */}
              <Flex
                data-testid="edit-view-deployment"
                direction="column"
                alignItems="center"
                rounded="md"
                padding="8"
                position="relative"
                bg={useColorModeValue('white', 'gray.700')}
                shadow={{
                  md: 'base',
                }}
              >
                <Circle size="6rem" bg={'bg-muted'}>
                  <EditIcon boxSize={6} />
                </Circle>
                <VStack spacing="1" flex="1" mt="3" mb="3">
                  <Text marginBottom={'1.5rem'} fontWeight="bold">
                    Edit / View existing deployment
                  </Text>
                  {isAllDeploymentsLoading ? <Loader /> : null}
                  <FormProvider {...methods}>
                    {allDeployments && allDeployments.length > 0 ? (
                      <SelectFormField
                        name="deployment_name"
                        label="Deployment name"
                        options={allDeployments.map((cluster: any) => ({
                          value: cluster.id,
                          label: `${cluster.id.toString()} : ${cluster.deployment_name}`,
                        }))}
                        onChangeHandler={(value: string) => clusterSelectHandler(value)}
                        tooltip="Enter the deployment name"
                      />
                    ) : (
                      <Text mb="4">No Deployments exist</Text>
                    )}
                  </FormProvider>
                </VStack>
                {allDeployments && allDeployments.length > 0 ? (
                  <Button
                    variant="outline"
                    colorScheme="brand.600"
                    rounded="full"
                    size="sm"
                    width="full"
                    onClick={() => {
                      navigate('/oran-du-cu-manager/simple-view/deployment', {
                        state: { deployment_type: 'edit', selectedOption },
                      });
                    }}
                  >
                    Launch
                  </Button>
                ) : (
                  <Text mb="4">Please create a deployment</Text>
                )}
              </Flex>
            </SimpleGrid>
          </Box>
        </Box>
      </Stack>
    </>
  );
};

export default SimpleView;
