import React, { useEffect, useState } from 'react';
import { EdgeProps, getBezierPath } from 'reactflow';

const CustomEdge: React.FC<EdgeProps> = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  data,
}) => {
  const [value, setValue] = useState(data?.value || '');

  useEffect(() => {
    setValue(data?.value || '');
  }, [data?.value]);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.value;
    setValue(newValue);
    data.onChange(id, newValue);
  };

  const [edgePath] = getBezierPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
  });

  return (
    <>
      <path id={id} style={style} className="react-flow__edge-path" d={edgePath} />
      <foreignObject width={220} height={40} x={(sourceX + targetX) / 2 - 120} y={(sourceY + targetY) / 2 - 20}>
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            background: 'white',
            border: '1px solid #777',
            borderRadius: '4px',
            padding: '5px',
          }}
        >
          <input type="text" value={value} onChange={handleChange} />
        </div>
      </foreignObject>
    </>
  );
};

export default CustomEdge;
