import { EditIcon, InfoIcon, SmallCloseIcon } from '@chakra-ui/icons';
import {
  Box,
  Button,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Heading,
  IconButton,
  Input,
  Popover,
  PopoverArrow,
  PopoverBody,
  PopoverCloseButton,
  PopoverContent,
  PopoverFooter,
  PopoverHeader,
  PopoverTrigger,
  Table,
  Tbody,
  Td,
  Th,
  Thead,
  Tooltip,
  Tr,
  useColorModeValue,
} from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { z } from 'zod';
import EmptyDataErrorBoundary from '../../../../../components/errorComponents/EmptyDataErrorBoundary';
import FileUpload from '../../../../../components/fileUpload/FileUpload';
import Loader from '../../../../../components/loader/Loader';
import { AUTH_TOKEN_KEY, FileUploadTypes, READ_WRITE_ACCESS_ROLES } from '../../../../../data/constants';
import useLogin from '../../../../../hooks/useLogin';
import { InterfaceFieldData } from '../../../../../types/duCuManager.type';
import { ipv4Regex, ipv6Regex } from '../../../../../utils/helpers';
import { excludedInterfaceFields } from '../../../hooks/interface/excludedInterfaceFields';
import { defaultInterfaceFields } from '../../../hooks/interface/useTransformInterfaceTableData';
import useCustomResource from '../../../hooks/services/useCustomResource';
import useSiteStatus from '../../../hooks/useSiteStatus';

export type InitialNodeType = {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: {
    fields: { name: string; label: string; value: string }[];
    node: string;
    name: string;
  };
};

export type DynamicTableProps = {
  caller: 'interfaces' | 'identities';
  createResponseData?: any;
  getInterfaceData?: any;
  getIdentitiesData?: any;
  editedUserSettings?: any;
  setEditedUserSettings?: any;
  isCreateInterfaceSuccess?: any;
  isCreateIdentitiesSuccess?: any;
  isInterfaceListQueryFetching?: any;
  isIdentitiesFetching?: any;
  selectedOption?: any;
  setIsButtonDisabled?: any;
  setEditing?: any;
  editing?: any;
};

const DynamicTable: React.FC<DynamicTableProps> = ({
  caller,
  createResponseData,
  getInterfaceData,
  getIdentitiesData,
  editedUserSettings,
  setEditedUserSettings,
  isCreateInterfaceSuccess,
  isCreateIdentitiesSuccess,
  isInterfaceListQueryFetching,
  isIdentitiesFetching,
  selectedOption,
  setIsButtonDisabled,
  setEditing,
  editing,
}) => {
  const [originalValues, setOriginalValues] = useState<Record<string, string>>({});
  const { checkEnabledConsistency } = useSiteStatus();

  const { checkNmsDevAccess } = useLogin(AUTH_TOKEN_KEY);
  const checkRoleAccess = checkNmsDevAccess(READ_WRITE_ACCESS_ROLES);

  const isReady =
    !!selectedOption?.deployment_name &&
    !!selectedOption?.du_site_name &&
    !!selectedOption?.cucp_site_name &&
    !!selectedOption?.cuup_site_name;

  // API Calls
  const { getDeploymentCustomResources } = useCustomResource();

  // Drives cluster table
  const {
    data: DeploymentCustomResourcesData,
    isLoading: isDeploymentCustomResourcesDataLoading,
    error: deploymentCustomResourcesDataError,
    isFetching: isDeploymentCustomResourcesDataFetching,
  } = getDeploymentCustomResources(selectedOption?.deployment_name ?? '', isReady);

  const flattenedData = React.useMemo(() => {
    if (DeploymentCustomResourcesData) {
      return [
        ...DeploymentCustomResourcesData.du,
        ...DeploymentCustomResourcesData.cuup,
        ...DeploymentCustomResourcesData.cucp,
        ...(DeploymentCustomResourcesData.cucp2 || []),
        ...(DeploymentCustomResourcesData.cuup2 || []),
      ];
    }
    return [];
  }, [DeploymentCustomResourcesData]);

  //NOTE: below i had to do a workaround for disabling the edit button if more than 2 crs are enabled, just for the time being until the backend is consistent.
  // In the future we will just use allEnabled.
  // allCrsEnabled.allEnabled = areMoreThanTwoEnabled; is a temp fix
  const { allEnabled, statuses, isSelectedEnabled } = checkEnabledConsistency(flattenedData);
  const allCrsEnabled = checkEnabledConsistency(flattenedData);
  const crEnabledCount = Object.values(statuses).filter((status) => status === true).length;
  const areMoreThanTwoEnabled = crEnabledCount > 2;
  allCrsEnabled.allEnabled = areMoreThanTwoEnabled;

  const borderColor = useColorModeValue('rgba(237, 242, 247, 0.9)', 'rgba(45, 55, 72, 0.9)');

  const getBgColor = (header: string, item: any, cellKey: string) => {
    if (editing[cellKey]) {
      return 'rgba(255, 165, 0, 0.5)';
    }
    if (header === 'user_setting') {
      if (item['user_setting'] !== null && item['user_setting'] !== undefined) {
        return 'rgba(56, 161, 105, 0.5)';
      } else if (item['default']) {
        return 'rgba(113, 128, 150, 0.5)';
      } else {
        return 'rgba(113, 128, 150, 0.5)';
      }
    } else if (header === 'default') {
      if (!item['default']) {
        return 'rgba(113, 128, 150, 0.5)';
      } else if (item['user_setting'] !== null && item['user_setting'] !== undefined) {
        return 'rgba(113, 128, 150, 0.5)';
      } else {
        return 'rgba(56, 161, 105, 0.5)';
      }
    }
    return '';
  };

  const handleEditClick = (group: string, header: string, index: number, field: any) => {
    const cellKey = `${group}-${header}-${index}`;
    setEditing((prev: any) => ({ ...prev, [cellKey]: true }));
    setOriginalValues((prev) => ({ ...prev, [cellKey]: field[header] }));
  };

  const handleCloseClick = (group: string, header: string, index: number) => {
    const cellKey = `${group}-${header}-${index}`;
    setEditing((prev: any) => ({ ...prev, [cellKey]: false }));
    setValue(cellKey, originalValues[cellKey] || '');
    setEditedUserSettings((prev: any) => {
      const { [cellKey]: _, ...rest } = prev;
      return rest;
    });
  };

  const handleInputChange = (group: string, header: string, index: number, value: string, item: any) => {
    const cellKey = `${group}-${header}-${index}`;
    const updatedItem = {
      ...item,
      [header]: value,
    };
    setEditedUserSettings((prev: any) => ({
      ...prev,
      [cellKey]: updatedItem,
    }));
  };

  const handleInputBlur = async (group: string, header: string, index: number, value: string, item: any) => {
    const cellKey = `${group}-${header}-${index}`;
    setEditedUserSettings((prev: any) => ({
      ...prev,
      [cellKey]: {
        ...item,
        [header]: value,
      },
    }));
    const isValid = await trigger(`${cellKey}`);
    if (!isValid) {
      console.log(`Validation failed for ${cellKey}`);
    }
  };

  const isEditable = (header: string) => {
    return header === 'user_setting';
  };

  const generateSchema = () => {
    const schemaShape: Record<string, any> = {};

    if (caller === 'interfaces' && getInterfaceData) {
      Object.keys(getInterfaceData).forEach((group) => {
        if (Array.isArray(getInterfaceData[group])) {
          getInterfaceData[group].forEach((field: InterfaceFieldData, index: number) => {
            const cellKey = `${group}-user_setting-${index}`;
            if (field?.name === 'FH-C subNetMask') {
              schemaShape[cellKey] = z.string().refine(
                (val) => {
                  const num = Number(val);
                  return !isNaN(num) && num >= 0 && num <= 32;
                },
                { message: 'SubNetMask must be a number between 0 and 32' }
              );
            } else {
              schemaShape[cellKey] = z
                .string()
                .refine((val) => ipv4Regex.test(val) || ipv6Regex.test(val), {
                  message: 'Invalid IP address format',
                })
                .optional();
            }
          });
        }
      });
    }

    if (caller === 'identities' && getIdentitiesData) {
      Object.keys(getIdentitiesData).forEach((group) => {
        if (typeof getIdentitiesData[group] === 'object' && !Array.isArray(getIdentitiesData[group])) {
          Object.keys(getIdentitiesData[group]).forEach((fieldKey, index) => {
            const field = getIdentitiesData[group][fieldKey];
            const cellKey = `${group}-user_setting-${index}`;

            if (field?.name === 'MCC') {
              schemaShape[cellKey] = z
                .string()
                .min(3, { message: 'MCC must be a 3-digit number' })
                .max(3, { message: 'MCC must be a 3-digit number' })
                .refine((val) => !isNaN(Number(val)), {
                  message: 'MCC must be a numeric string',
                });
            } else if (field?.name === 'MNC') {
              schemaShape[cellKey] = z
                .string()
                .min(2, { message: 'MNC must be a 2-digit number' })
                .max(2, { message: 'MNC must be a 2-digit number' })
                .refine((val) => !isNaN(Number(val)), {
                  message: 'MNC must be a numeric string',
                });
            } else if (field?.name === 'Tracking Area Code (TAC)') {
              schemaShape[cellKey] = z
                .string()
                .min(1, { message: 'Tracking Area Code (TAC) is required' })
                .refine((val) => /^[0-9]+$/.test(val.trim()), {
                  message: 'Tracking Area Code (TAC) must be numeric',
                });
            } else {
              schemaShape[cellKey] = z
                .string()
                .refine((val) => /^[a-zA-Z0-9]+$/.test(val), { message: 'Invalid value, must be alphanumeric' })
                .optional();
            }
          });
        }
      });
    }

    return z.object(schemaShape);
  };

  const defaultSchema = z.object({});
  const schema = generateSchema() || defaultSchema;

  const methods = useForm<InterfaceFieldData>({
    resolver: zodResolver(schema),
    mode: 'onBlur',
    reValidateMode: 'onChange',
  });

  const {
    handleSubmit,
    control,
    setValue,
    trigger,
    formState: { errors, isValid },
  } = methods;

  const getErrorMessage = (error: any): string => {
    if (typeof error?.message === 'string') {
      return error.message;
    }
    return 'Invalid value';
  };

  useEffect(() => {
    if (!editedUserSettings || Object.keys(editedUserSettings).length === 0) return;

    console.log('Processing editedUserSettings in DynamicTable:', editedUserSettings);

    const data = caller === 'interfaces' ? getInterfaceData : getIdentitiesData;
    if (!data) return;

    const updatedCellKeys: string[] = [];
    const newEditingState = { ...editing };

    Object.keys(data).forEach((group) => {
      if (Array.isArray(data[group])) {
        data[group].forEach((field: InterfaceFieldData, rowIndex: number) => {
          if (!field || !field.name) return;

          const importKeys = Object.keys(editedUserSettings).filter((k) => k.startsWith(`${group}-user_setting-`));

          for (const importKey of importKeys) {
            const [_group, _type, indexStr] = importKey.split('-');
            const index = parseInt(indexStr);

            const isCellMatch = matchesFieldPattern(field, group, index);

            if (isCellMatch) {
              const cellKey = `${group}-user_setting-${rowIndex}`;
              const importValue = editedUserSettings[importKey];

              field.user_setting = importValue.user_setting || importValue;
              newEditingState[cellKey] = true;
              updatedCellKeys.push(cellKey);
              break;
            }
          }
        });
      } else if (typeof data[group] === 'object') {
        // Handle object data (identities)
        // Similar logic for identities can be added here
      }
    });

    if (setEditing && updatedCellKeys.length > 0) {
      console.log('Marking fields as edited:', updatedCellKeys);
      setEditing(newEditingState);
    }
  }, [editedUserSettings, caller, getInterfaceData, getIdentitiesData]);

  const matchesFieldPattern = (field: InterfaceFieldData, group: string, index: number): boolean => {
    if (group !== 'du') return index === 0;

    const name = field.name || '';

    // Specific matching logic for DU fields (indices 5-10)
    if (index === 5 && name.includes('F1-C') && name.includes('(Cell 1)')) return true;
    if (index === 6 && name.includes('F1-U localIpAddress') && name.includes('(Cell 1)')) return true;
    if (index === 7 && name.includes('F1-U gatewayAddress') && name.includes('(Cell 1)')) return true;
    if (index === 8 && name.includes('F1-C') && name.includes('(Cell 2)')) return true;
    if (index === 9 && name.includes('F1-U localIpAddress') && name.includes('(Cell 2)')) return true;
    if (index === 10 && name.includes('F1-U gatewayAddress') && name.includes('(Cell 2)')) return true;

    return index < 5 && field.name.indexOf('(Cell') === -1;
  };

  useEffect(() => {
    if (isCreateInterfaceSuccess) {
      setEditing({});
      setEditedUserSettings({});
    }
  }, [isCreateInterfaceSuccess]);

  useEffect(() => {
    if (isCreateIdentitiesSuccess) {
      setEditing({});
      setEditedUserSettings({});
    }
  }, [isCreateIdentitiesSuccess]);

  useEffect(() => {
    setIsButtonDisabled(!isValid || _.isEmpty(editing));
  }, [isValid, editing]);

  if (isInterfaceListQueryFetching || isIdentitiesFetching) return <Loader />;

  if (
    DeploymentCustomResourcesData &&
    _.every(
      ['du', 'cucp', 'cuup'],
      (key) => Array.isArray(DeploymentCustomResourcesData[key]) && _.isEmpty(DeploymentCustomResourcesData[key])
    )
  ) {
    return (
      <EmptyDataErrorBoundary
        data={DeploymentCustomResourcesData}
        message={
          new Error(
            `All keys (du, cucp, cuup) are empty arrays. Expected at least one key to contain data. Received: ${JSON.stringify(
              DeploymentCustomResourcesData,
              null,
              2
            )}`
          )
        }
      />
    );
  }

  const renderInterfaceTable = (data: InterfaceFieldData[], title: string, group: string) => {
    const isMoranDeployment =
      selectedOption?.deployment_params?.max_cells === 2 ||
      selectedOption?.cu2?.cucp_site_name2 ||
      selectedOption?.cu2?.cuup_site_name2 ||
      title.includes('CUCP2') ||
      title.includes('CUUP2');

    if (defaultInterfaceFields[group as keyof typeof defaultInterfaceFields]) {
      const defaults = defaultInterfaceFields[group as keyof typeof defaultInterfaceFields];
      const existingFieldNames = new Set(data.map((field) => field.name));

      defaults.forEach((defaultField) => {
        if (!existingFieldNames.has(defaultField.name)) {
          data.push({
            ...defaultField,
            user_setting: null,
          });
        }
      });

      if (isMoranDeployment && group === 'du' && defaultInterfaceFields.du_moran) {
        const moranDefaults = defaultInterfaceFields.du_moran;
        const existingMoranFieldNames = new Set(data.map((field) => field.name));

        moranDefaults.forEach((defaultField) => {
          if (!existingMoranFieldNames.has(defaultField.name)) {
            data.push({
              ...defaultField,
              user_setting: null,
            });
          }
        });
      }
    }

    return (
      <Box mb="4">
        <Box as="h2" fontSize="lg" mb="2">
          {title}
        </Box>
        <Table>
          <Thead>
            <Tr style={{ border: `0.1em solid ${borderColor}` }}>
              {['Name', 'Default', 'User Setting', 'Target'].map((header) => (
                <Th key={header}>{header}</Th>
              ))}
            </Tr>
          </Thead>
          <Tbody>
            {data
              .filter((field) => {
                if (
                  !isMoranDeployment &&
                  field.target &&
                  (field.target === 'CuCp2' ||
                    field.target === 'CuUp2' ||
                    field.moran === 'c2' ||
                    field.name.includes('(Cell 2)'))
                ) {
                  return false;
                }

                const fieldKey = `${group}.${field.name}`;
                return !excludedInterfaceFields.includes(fieldKey);
              })
              .map((field: any, rowIndex: number) => (
                <Tr key={rowIndex}>
                  {['name', 'default', 'user_setting', 'target'].map((header: string) => {
                    const cellKey = `${group}-${header}-${rowIndex}`;
                    const editable = isEditable(header);
                    const bgColor = getBgColor(header, field, cellKey);

                    const fieldWidth = header === 'user_setting' ? '30%' : '20%';

                    return (
                      <Td
                        key={cellKey}
                        position="relative"
                        width={fieldWidth}
                        p="4"
                        style={{ backgroundColor: bgColor, border: `0.1em solid ${borderColor}` }}
                      >
                        {header === 'name' ? (
                          <Tooltip
                            label={`${field?.name} - ${group} > ${field?.target}`}
                            aria-label="Name Tooltip"
                            placement="top"
                          >
                            <span>{field[header] || ''}</span>
                          </Tooltip>
                        ) : editing[cellKey] ? (
                          <Controller
                            name={`${group}-${header}-${rowIndex}`}
                            control={control}
                            defaultValue={field[header]}
                            render={({ field: controllerField }) => {
                              const errorKey = `${group}-${header}-${rowIndex}` as keyof InterfaceFieldData;
                              return (
                                <FormControl isInvalid={!!errors[errorKey]}>
                                  <Box display="flex" alignItems="baseline">
                                    <FormLabel htmlFor={`${group}-${header}-${rowIndex}`} width="25%">
                                      {field?.user_setting ? `${field?.user_setting}` : `${field?.default}`}
                                    </FormLabel>
                                    <Input
                                      id={`${group}-${header}-${rowIndex}`}
                                      {...controllerField}
                                      style={{
                                        pointerEvents: 'all',
                                      }}
                                      type={field?.name === 'FH-C subNetMask' ? 'number' : 'text'}
                                      backgroundColor="white"
                                      color="black"
                                      width="60%"
                                      mr="10%"
                                      value={controllerField.value || ''}
                                      onChange={(e) => {
                                        controllerField.onChange(e.target.value);
                                        handleInputChange(group, header, rowIndex, e.target.value, field);
                                      }}
                                      onBlur={(e) => {
                                        controllerField.onBlur();
                                        handleInputBlur(group, header, rowIndex, e.target.value, field);
                                      }}
                                    />
                                  </Box>
                                  {errors[errorKey] && (
                                    <FormErrorMessage m="0">
                                      {(errors[errorKey]?.message as React.ReactNode) || 'Invalid value'}
                                    </FormErrorMessage>
                                  )}
                                </FormControl>
                              );
                            }}
                          />
                        ) : (
                          <span>{field[header] || ''}</span>
                        )}
                        {checkRoleAccess && editable && (
                          <Popover>
                            <PopoverTrigger>
                              <IconButton
                                icon={
                                  !allCrsEnabled.allEnabled ? (
                                    editing[cellKey] ? (
                                      <SmallCloseIcon />
                                    ) : (
                                      <EditIcon data-testid="edit-icon" />
                                    )
                                  ) : (
                                    <InfoIcon data-testid="info-icon" />
                                  )
                                }
                                size="md"
                                onClick={() => {
                                  if (!allCrsEnabled.allEnabled) {
                                    editing[cellKey]
                                      ? handleCloseClick(group, header, rowIndex)
                                      : handleEditClick(group, header, rowIndex, field);
                                  }
                                }}
                                aria-label={
                                  !allCrsEnabled.allEnabled ? (editing[cellKey] ? 'Close field' : 'Edit field') : 'Info'
                                }
                                position="absolute"
                                top="50%"
                                right="4px"
                                transform="translateY(-50%)"
                              />
                            </PopoverTrigger>
                            {allCrsEnabled.allEnabled && (
                              <PopoverContent>
                                <PopoverArrow />
                                <PopoverCloseButton />
                                <PopoverHeader>Edit blocked</PopoverHeader>
                                <PopoverBody>
                                  Please deactivate pod/custom resource <br />
                                  to edit this field
                                </PopoverBody>
                              </PopoverContent>
                            )}
                          </Popover>
                        )}
                      </Td>
                    );
                  })}
                </Tr>
              ))}
          </Tbody>
        </Table>
      </Box>
    );
  };

  const renderIdentitiesTable = (
    data: Record<string, { name: string; default: string; user_setting?: string; target?: string }>,
    title: string,
    group: string
  ) => {
    return (
      <Box mb="4">
        <Box as="h2" fontSize="lg" mb="2">
          {title}
        </Box>
        <Table>
          <Thead>
            <Tr style={{ border: `0.1em solid ${borderColor}` }}>
              <Th>#</Th>
              {['Name', 'Default', 'User Setting', 'Target'].map((header) => (
                <Th key={header}>{header}</Th>
              ))}
            </Tr>
          </Thead>
          <Tbody>
            {Object.entries(data)
              .filter(([key]) => key !== 'gnodeb_du_name')
              .filter(([key, value]) => value !== null)
              .map(([key, value], rowIndex) => (
                <Tr key={rowIndex}>
                  <Td style={{ border: `0.1em solid ${borderColor}` }}>{rowIndex + 1}</Td>
                  {['name', 'default', 'user_setting', 'target'].map((header: string) => {
                    const cellKey = `${group}-${header}-${rowIndex}`;
                    const editable = isEditable(header);
                    const bgColor = getBgColor(header, value, cellKey);

                    const fieldWidth = header === 'user_setting' ? '30%' : '20%';

                    let displayValue;
                    if (header === 'name') {
                      displayValue = key || '';
                    } else {
                      const fieldValue = value?.[header as keyof typeof value];
                      displayValue = fieldValue !== null && fieldValue !== undefined ? fieldValue : '';
                    }

                    return (
                      <Td
                        key={cellKey}
                        position="relative"
                        width={fieldWidth}
                        p="4"
                        style={{ backgroundColor: bgColor, border: `0.1em solid ${borderColor}` }}
                      >
                        {editing[cellKey] ? (
                          <Controller
                            name={`${group}-${header}-${rowIndex}`}
                            control={control}
                            defaultValue={value[header as keyof typeof value]}
                            render={({ field: controllerField }) => {
                              const errorKey = `${group}-${header}-${rowIndex}`;
                              return (
                                <FormControl isInvalid={!!errors[errorKey as keyof InterfaceFieldData]}>
                                  <Box display="flex" alignItems="baseline">
                                    <FormLabel htmlFor={`${group}-${header}-${rowIndex}`} width="25%">
                                      {value && value[header as keyof typeof value] !== null
                                        ? value[header as keyof typeof value]
                                        : 'null'}
                                    </FormLabel>
                                    <Input
                                      id={`${group}-${header}-${rowIndex}`}
                                      {...controllerField}
                                      style={{
                                        pointerEvents: 'all',
                                      }}
                                      type="text"
                                      backgroundColor="white"
                                      color="black"
                                      width="60%"
                                      mr="10%"
                                      value={controllerField.value || ''}
                                      onChange={(e) => {
                                        controllerField.onChange(e.target.value);
                                        handleInputChange(group, header, rowIndex, e.target.value, value);
                                      }}
                                      onBlur={(e) => {
                                        controllerField.onBlur();
                                        handleInputBlur(group, header, rowIndex, e.target.value, value);
                                      }}
                                    />
                                  </Box>
                                  {errors[errorKey as keyof InterfaceFieldData] && (
                                    <FormErrorMessage m="0">
                                      {(errors[errorKey as keyof InterfaceFieldData]?.message as React.ReactNode) ||
                                        'Invalid value'}
                                    </FormErrorMessage>
                                  )}
                                </FormControl>
                              );
                            }}
                          />
                        ) : (
                          <span>{displayValue}</span>
                        )}
                        {checkRoleAccess && editable && !editing[cellKey] && (
                          <IconButton
                            icon={<EditIcon />}
                            size="md"
                            onClick={() => handleEditClick(group, header, rowIndex, value)}
                            aria-label="Edit field"
                            position="absolute"
                            top="50%"
                            right="4px"
                            transform="translateY(-50%)"
                          />
                        )}
                        {checkRoleAccess && editable && editing[cellKey] && (
                          <IconButton
                            icon={<SmallCloseIcon />}
                            size="md"
                            onClick={() => handleCloseClick(group, header, rowIndex)}
                            aria-label="Close field"
                            position="absolute"
                            top="50%"
                            right="4px"
                            transform="translateY(-50%)"
                          />
                        )}
                        {checkRoleAccess && editable && (
                          <Popover>
                            <PopoverTrigger>
                              <IconButton
                                icon={
                                  !allCrsEnabled.allEnabled ? (
                                    editing[cellKey] ? (
                                      <SmallCloseIcon />
                                    ) : (
                                      <EditIcon />
                                    )
                                  ) : (
                                    <InfoIcon />
                                  )
                                }
                                size="md"
                                onClick={() => {
                                  if (!allCrsEnabled.allEnabled) {
                                    editing[cellKey]
                                      ? handleCloseClick(group, header, rowIndex)
                                      : handleEditClick(group, header, rowIndex, value);
                                  }
                                }}
                                aria-label={
                                  !allCrsEnabled.allEnabled ? (editing[cellKey] ? 'Close field' : 'Edit field') : 'Info'
                                }
                                position="absolute"
                                top="50%"
                                right="4px"
                                transform="translateY(-50%)"
                              />
                            </PopoverTrigger>
                            {allCrsEnabled.allEnabled && (
                              <PopoverContent>
                                <PopoverArrow />
                                <PopoverCloseButton />
                                <PopoverHeader>Edit blocked</PopoverHeader>
                                <PopoverBody>
                                  Please deactivate pod/custom resource <br />
                                  to edit this field
                                </PopoverBody>
                              </PopoverContent>
                            )}
                          </Popover>
                        )}
                      </Td>
                    );
                  })}
                </Tr>
              ))}
          </Tbody>
        </Table>
      </Box>
    );
  };

  return (
    <Box>
      {caller === 'interfaces' ? (
        <>
          {getInterfaceData?.du && renderInterfaceTable(getInterfaceData?.du, 'DU Interfaces', 'du')}
          {getInterfaceData?.cu_cp && renderInterfaceTable(getInterfaceData?.cu_cp, 'CUCP Interfaces', 'cu_cp')}
          {getInterfaceData?.cu_up && renderInterfaceTable(getInterfaceData?.cu_up, 'CUUP Interfaces', 'cu_up')}
          {getInterfaceData?.cu_cp2 && renderInterfaceTable(getInterfaceData?.cu_cp2, 'CUCP2 Interfaces', 'cu_cp2')}
          {getInterfaceData?.cu_up2 && renderInterfaceTable(getInterfaceData?.cu_up2, 'CUUP2 Interfaces', 'cu_up2')}
          <Flex justifyContent="space-between" alignItems="center" width="100%" mt="12" mb="8">
            <FileUpload
              formMethods={methods}
              caller="simpleViewInterface"
              type={FileUploadTypes.import}
              text="Import Interface"
              setEditing={setEditing}
              editing={editing}
              setEditedUserSettings={setEditedUserSettings}
            />
            <FileUpload
              formMethods={methods}
              caller="simpleViewInterface"
              type={FileUploadTypes.export}
              text="Export Interface"
              setEditing={setEditing}
              editing={editing}
              setEditedUserSettings={setEditedUserSettings}
            />
          </Flex>
        </>
      ) : (
        <>
          {getIdentitiesData?.du && renderIdentitiesTable(getIdentitiesData?.du, 'DU Identities', 'du')}
          {getIdentitiesData?.cu_cp && renderIdentitiesTable(getIdentitiesData?.cu_cp, 'CUCP Identities', 'cu_cp')}
          {getIdentitiesData?.cu_up && renderIdentitiesTable(getIdentitiesData?.cu_up, 'CUUP Identities', 'cu_up')}
          {getIdentitiesData?.cu_cp2 && renderIdentitiesTable(getIdentitiesData?.cu_cp2, 'CUCP2 Identities', 'cu_cp2')}
          {getIdentitiesData?.cu_up2 && renderIdentitiesTable(getIdentitiesData?.cu_up2, 'CUUP2 Identities', 'cu_up2')}
          {getIdentitiesData?.shared && renderIdentitiesTable(getIdentitiesData?.shared, 'shared Identities', 'shared')}
          <Flex justifyContent="space-between" alignItems="center" width="100%" mt="12" mb="8">
            <FileUpload
              formMethods={methods}
              caller="simpleViewIdentities"
              type={FileUploadTypes.import}
              text="Import Identities"
              setEditing={setEditing}
              editing={editing}
              setEditedUserSettings={setEditedUserSettings}
            />
            <FileUpload
              formMethods={methods}
              caller="simpleViewIdentities"
              type={FileUploadTypes.export}
              text="Export Identities"
              setEditing={setEditing}
              editing={editing}
              setEditedUserSettings={setEditedUserSettings}
            />
          </Flex>
        </>
      )}
      {isCreateInterfaceSuccess ? (
        <Heading as="h2" size="md" mt="12" mb="8" textAlign="center">
          {caller} have been updated successfully
        </Heading>
      ) : null}
    </Box>
  );
};

export default DynamicTable;
