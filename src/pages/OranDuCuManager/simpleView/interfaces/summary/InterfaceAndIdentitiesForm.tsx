import { Accordion, AccordionButton, AccordionIcon, AccordionItem, AccordionPanel, Text } from '@chakra-ui/react';
import DynamicTable from '../tables/DynamicTable';
import DeploymentSummary from './DeploymentSummary';

type InterfaceAndIdentitiesFormProps = {
  caller?: string;
  isCreateInterfaceSuccess?: boolean;
  isCreateIdentitiesSuccess?: boolean;
  createResponseData?: any;
  getApiData?: any;
  editedUserSettings?: any;
  setEditedUserSettings?: any;
  clusterList?: any;
  isInterfaceListQueryFetching?: any;
  isIdentitiesFetching?: any;
  selectedOption?: any;
  setIsButtonDisabled?: any;
  setEditing?: any;
  editing?: any;
};

const InterfaceAndIdentitiesForm = ({
  caller,
  isCreateInterfaceSuccess,
  isCreateIdentitiesSuccess,
  createResponseData,
  getApiData,
  editedUserSettings,
  setEditedUserSettings,
  clusterList,
  isInterfaceListQueryFetching,
  isIdentitiesFetching,
  selectedOption,
  setIsButtonDisabled,
  setEditing,
  editing,
}: InterfaceAndIdentitiesFormProps) => {
  if (caller === 'interfaces') {
    return (
      <>
        <DeploymentSummary getApiData={getApiData} clusterList={clusterList} selectedOption={selectedOption} />
        <Accordion defaultIndex={[0]} allowMultiple>
          <AccordionItem>
            <AccordionButton>
              <Text width="100%">Node Interfaces</Text>
              <AccordionIcon />
            </AccordionButton>
            <AccordionPanel pb="4">
              <DynamicTable
                caller="interfaces"
                createResponseData={createResponseData}
                getInterfaceData={getApiData}
                editedUserSettings={editedUserSettings}
                setEditedUserSettings={setEditedUserSettings}
                isCreateInterfaceSuccess={isCreateInterfaceSuccess}
                isInterfaceListQueryFetching={isInterfaceListQueryFetching}
                selectedOption={selectedOption}
                setIsButtonDisabled={setIsButtonDisabled}
                setEditing={setEditing}
                editing={editing}
              />
            </AccordionPanel>
          </AccordionItem>
        </Accordion>
      </>
    );
  } else {
    return (
      <>
        <DeploymentSummary getApiData={getApiData} clusterList={clusterList} selectedOption={selectedOption} />
        <Accordion defaultIndex={[0]} allowMultiple>
          <AccordionItem>
            <AccordionButton>
              <Text width="100%">Node Interfaces</Text>
              <AccordionIcon />
            </AccordionButton>
            <AccordionPanel pb="4">
              <DynamicTable
                caller="identities"
                createResponseData={createResponseData}
                getIdentitiesData={getApiData}
                editedUserSettings={editedUserSettings}
                setEditedUserSettings={setEditedUserSettings}
                isCreateIdentitiesSuccess={isCreateIdentitiesSuccess}
                isIdentitiesFetching={isIdentitiesFetching}
                selectedOption={selectedOption}
                setIsButtonDisabled={setIsButtonDisabled}
                setEditing={setEditing}
                editing={editing}
              />
            </AccordionPanel>
          </AccordionItem>
        </Accordion>
      </>
    );
  }
};

export default InterfaceAndIdentitiesForm;
