import { Box, Flex, Heading, Text } from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import { PayloadData } from '../../../../../services/duCuManager';

type Cluster = {
  id: number;
  description: string;
  ip_address: string;
  software_release: string;
  cluster_ca_secret: string;
  cluster_name: string;
};

type DeploymentSummaryProps = {
  getInterfaceData?: {
    deployment: PayloadData;
    du: any[];
    cu_cp: any[];
    cu_up: any[];
  };
  getApiData?: any;
  clusterList?: Cluster[];
  selectedOption?: any;
};

const DeploymentSummary: React.FC<DeploymentSummaryProps> = ({ getApiData, clusterList, selectedOption }) => {
  const [duClusterName, setDuClusterName] = useState<string | null>(null);
  const [cucpClusterName, setCucpClusterName] = useState<string | null>(null);
  const [cuupClusterName, setCuupClusterName] = useState<string | null>(null);
  const deploymentSummary = getApiData?.deployment;

  useEffect(() => {
    const findClusterNameById = (id: number | null) => {
      if (clusterList && Array.isArray(clusterList)) {
        const foundCluster = clusterList.find((cluster) => cluster.id === id);
        return foundCluster ? foundCluster.cluster_name : null;
      }
      return null;
    };

    if (selectedOption) {
      setDuClusterName(findClusterNameById(selectedOption.du_cluster));
      setCucpClusterName(findClusterNameById(selectedOption.cucp_cluster));
      setCuupClusterName(findClusterNameById(selectedOption.cuup_cluster));
    }
  }, [clusterList, selectedOption]);

  return (
    <Box width="100%">
      <Heading as="h2" size="lg" mb="8" textAlign="center">
        Deployment
      </Heading>
      <Flex justifyContent="space-evenly" mb="8">
        <Box>
          <Heading as="h3" size="md" mb="4" mr="4">
            Name:
          </Heading>
          <Text as="em">
            {deploymentSummary ? deploymentSummary?.deployment_name : selectedOption?.deployment_name}
          </Text>
        </Box>
        <Box>
          <Heading as="h3" size="md" mb="4" mr="4">
            DU cluster:
          </Heading>
          <Text as="em">{duClusterName}</Text>
        </Box>
        <Box>
          <Heading as="h3" size="md" mb="4" mr="4">
            CU CP cluster:
          </Heading>
          <Text as="em">{cucpClusterName}</Text>
        </Box>
        <Box>
          <Heading as="h3" size="md" mb="4" mr="4">
            CU UP cluster:
          </Heading>
          <Text as="em">{cuupClusterName}</Text>
        </Box>
      </Flex>
    </Box>
  );
};

export default DeploymentSummary;
