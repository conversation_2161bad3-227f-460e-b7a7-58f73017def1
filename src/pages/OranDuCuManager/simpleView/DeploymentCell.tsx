import { Box, Flex, Heading, Stack, useColorModeValue, useDisclosure } from '@chakra-ui/react';

import { useLocation } from 'react-router';

import AppBar from '../AppBar';
import CreateCellForDeployment from '../formFields/simpleView/CreateCellForDeployment';

import SimpleViewAppBar from './SimpleViewAppBar';

const DeploymentCell = () => {
  const boxShadow = useColorModeValue('sm', 'sm-dark');

  const location = useLocation();
  const passedData: any = location.state;
  const deployment_type = passedData?.deployment_type;

  return (
    <>
      <Stack
        spacing="1"
        direction={{
          base: 'column',
          lg: 'row',
        }}
        justify="space-between"
      >
        <Stack spacing="1">
          <Heading fontWeight="bold">O-RAN DU/CU Manager</Heading>
        </Stack>
      </Stack>
      <Stack
        spacing={{
          base: '5',
          lg: '6',
        }}
      >
        <Box
          bg="bg-surface"
          boxShadow={{
            base: 'none',
            md: boxShadow,
          }}
        >
          {/* App bar */}
          <Flex justify="center" m="8">
            {deployment_type === 'edit' && (
              <SimpleViewAppBar deployment_type={deployment_type} selectedOption={passedData.selectedOption} />
            )}
            {deployment_type === 'new' && <AppBar caller="simpleView" />}
          </Flex>
          {/* --- Header --- */}
          <Stack spacing="1">
            <Heading as="h2" size="lg" textAlign="center" pb="4">
              Create a cell for the deployment
            </Heading>
          </Stack>

          {/* --- FORM --- */}
          <Box mx="20" mt="10">
            <CreateCellForDeployment />
          </Box>
        </Box>
      </Stack>
    </>
  );
};

export default DeploymentCell;
