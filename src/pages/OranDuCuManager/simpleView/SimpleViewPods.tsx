import { Box, Flex, Heading, Select, Stack, useColorModeValue } from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import { useLocation } from 'react-router';
import { passedData, SelectedOption } from '../../../types/duCuManager.type';
import AppBar from '../AppBar';
import DeploymentForm from '../formFields/simpleView/DeploymentForm';
import useCustomResource from '../hooks/services/useCustomResource';
import SimpleViewAppBar from './SimpleViewAppBar';

type SimpleViewPodsProps = {
  selectedOption?: SelectedOption;
  deployment_type?: string;
  view?: string;
};

const SimpleViewPods: React.FC<SimpleViewPodsProps> = ({
  selectedOption: initialSelectedOptionProp,
  deployment_type: initialDeploymentTypeProp,
  view = 'simple',
}) => {
  const colorModeValue = useColorModeValue('sm', 'sm-dark');
  const location = useLocation();

  const passedData: passedData | undefined = location.state;
  const initialSelectedOption = initialSelectedOptionProp || passedData?.selectedOption;
  const initialDeploymentType = initialDeploymentTypeProp || passedData?.deployment_type;

  const [selectedOption, setSelectedOption] = useState<SelectedOption>({
    deployment_name: '',
    du_site_name: '',
    cucp_site_name: '',
    cuup_site_name: '',
    du_cluster: null,
    cucp_cluster: null,
    cuup_cluster: null,
    du_cell_config_set_ids: [],
    du_app_config_set_ids: [],
    cu_cell_config_set_ids: [],
    cu_cp_app_config_set_ids: [],
    cu_up_app_config_set_ids: [],
    ru_vendor: 'EdgeQ',
    f1_ip: '',
    e1_ip: '',
  });

  const isReady = !!selectedOption?.deployment_name;

  // API calls
  const { getAllDeployments, getDeploymentCustomResources } = useCustomResource();

  // CR's for deployment
  const {
    data: DeploymentCustomResourcesData,
    isLoading: isDeploymentCustomResourcesDataLoading,
    error: deploymentCustomResourcesDataError,
  } = getDeploymentCustomResources(selectedOption?.deployment_name ?? '', isReady);

  // Get all Deployments
  const {
    data: allDeployments,
    isLoading: isAllDeploymentsLoading,
    error: allDeploymentsError,
  } = getAllDeployments('edit');

  const handleUserInputChange = (selectedValue: string) => {
    if (selectedValue !== null) {
      const parsedValue = JSON.parse(selectedValue);
      setSelectedOption((prev: any) => ({
        ...prev,
        deployment_name: parsedValue.deployment_name,
        du_site_name: parsedValue.du_site_name,
        cucp_site_name: parsedValue.cucp_site_name,
        cuup_site_name: parsedValue.cuup_site_name,
        du_cluster: parsedValue.du_cluster,
        cucp_cluster: parsedValue.cucp_cluster,
        cuup_cluster: parsedValue.cuup_cluster,
        du_cell_config_set_ids: parsedValue.du_cell_config_set_ids,
        du_app_config_set_ids: parsedValue.du_app_config_set_ids,
        cu_cell_config_set_ids: parsedValue.cu_cell_config_set_ids,
        cu_cp_app_config_set_ids: parsedValue.cu_cp_app_config_set_ids,
        cu_up_app_config_set_ids: parsedValue.cu_up_app_config_set_ids,
        ru_vendor: parsedValue.ru_vendor,
        f1_ip: parsedValue.f1_ip,
        e1_ip: parsedValue.e1_ip,
      }));
    }
  };

  useEffect(() => {
    if (initialSelectedOption) {
      setSelectedOption((prev) => ({
        ...prev,
        ...initialSelectedOption,
        deployment_name: initialSelectedOption.deployment_name || '',
        du_site_name: initialSelectedOption.du_site_name || '',
        cucp_site_name: initialSelectedOption.cucp_site_name || '',
        cuup_site_name: initialSelectedOption.cuup_site_name || '',
        du_cluster: initialSelectedOption.du_cluster || null,
        cucp_cluster: initialSelectedOption.cucp_cluster || null,
        cuup_cluster: initialSelectedOption.cuup_cluster || null,
        f1_ip: initialSelectedOption.f1_ip || '',
        e1_ip: initialSelectedOption.e1_ip || '',
      }));
    }
  }, [initialSelectedOption]);

  return (
    <>
      <Stack
        spacing="1"
        direction={{
          base: 'column',
          lg: 'row',
        }}
        justify="space-between"
      >
        <Stack spacing="1">
          <Heading fontWeight="bold">O-RAN DU/CU Manager</Heading>
        </Stack>
      </Stack>
      <Stack
        spacing={{
          base: '5',
          lg: '6',
        }}
      >
        <Box
          bg="bg-surface"
          boxShadow={{
            base: 'none',
            md: colorModeValue,
          }}
        >
          {/* App bar */}
          <Flex justify="center" m="8">
            {initialDeploymentType === 'edit' && view === 'simple' && (
              <SimpleViewAppBar deployment_type={initialDeploymentType} selectedOption={selectedOption} />
            )}
            {initialDeploymentType === 'new' && <AppBar caller="simpleView" />}
            {view === 'advanced' && <AppBar caller="advancedView" />}
          </Flex>
          <Heading as="h2" size="lg" textAlign="center" pb="4">
            Pods
          </Heading>

          {view === 'advanced' && (
            <Box mx="15%" mb="18">
              <Heading textAlign="center" as="h2" fontSize="20px" mb="4" mt="12">
                Select a deployment
              </Heading>
              <Select
                mb="2"
                placeholder="Select option"
                value={selectedOption?.cluster ?? undefined}
                onChange={(e) => handleUserInputChange(e.target.value)}
              >
                {allDeployments?.map((item: any) => (
                  <option key={item.id} value={JSON.stringify(item)}>
                    {`${item.id}: ${item.deployment_name}`}
                  </option>
                ))}
              </Select>
            </Box>
          )}

          {/* FORM */}
          <Box mx="20" mt="10">
            <DeploymentForm
              isDisabled={true}
              DeploymentCustomResourcesData={DeploymentCustomResourcesData}
              selectedOption={selectedOption}
              setSelectedOption={setSelectedOption}
              caller="simpleViewPods"
            />
          </Box>
        </Box>
      </Stack>
    </>
  );
};

export default SimpleViewPods;
