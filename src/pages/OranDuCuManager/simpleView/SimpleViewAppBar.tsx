import { Box, Button } from '@chakra-ui/react';
import { useNavigate } from 'react-router-dom';

type SimpleViewAppBarProps = {
  deployment_type: string;
  selectedOption: any;
};

const SimpleViewAppBar = ({ deployment_type, selectedOption }: SimpleViewAppBarProps) => {
  const navigate = useNavigate();

  const navigateToPage = (path: any) => {
    navigate(path, {
      state: { deployment_type, selectedOption },
    });
  };

  return (
    <Box>
      <Button onClick={() => navigateToPage('/oran-du-cu-manager')} mr="8">
        Oran DU/CU manager
      </Button>
      <Button onClick={() => navigateToPage('/oran-du-cu-manager/simple-view/deployment')} mr="8">
        Deployment
      </Button>
      <Button onClick={() => navigateToPage('/oran-du-cu-manager/simple-view/interfaces')} mr="8">
        Interfaces
      </Button>
      <Button onClick={() => navigateToPage('/oran-du-cu-manager/simple-view/identities')} mr="8">
        Identities
      </Button>
      <Button onClick={() => navigateToPage('/oran-du-cu-manager/simple-view/custom-resources-parameters')} mr="8">
        Custom resource parameters
      </Button>
      <Button onClick={() => navigateToPage('/oran-du-cu-manager/simple-view/update-pods')} mr="8">
        Pods
      </Button>
    </Box>
  );
};

export default SimpleViewAppBar;
