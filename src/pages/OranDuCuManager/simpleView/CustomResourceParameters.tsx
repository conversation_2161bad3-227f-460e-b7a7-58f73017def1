import { BsPlusCircleFill } from 'react-icons/bs';
import {
  Box,
  Flex,
  Heading,
  Stack,
  useColorModeValue,
  Text,
  HStack,
  Button,
  Popover,
  PopoverTrigger,
  PopoverContent,
  PopoverArrow,
  PopoverCloseButton,
  PopoverBody,
  PopoverHeader,
} from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import React, { useEffect, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';

import { useLocation } from 'react-router-dom';
import { z } from 'zod';
import SelectFormField from '../../../components/form/oranDuCuManager/select/SelectFormField';
import Loader from '../../../components/loader/Loader';
import useCustomResource from '../hooks/services/useCustomResource';
import SimpleViewAppBar from './SimpleViewAppBar';
import { DataTable } from '../../MetricsCollector/components/DataTable';
import InputFormField from '../../../components/form/oranDuCuManager/input/InputFormField';
import { PatchCustomResourceUnnamedPayload } from '../../../services/duCuManager';
import useSiteStatus from '../hooks/useSiteStatus';
import { InfoIcon, InfoOutlineIcon } from '@chakra-ui/icons';

type CustomResourceUpdateParamValues = {
  path: string;
  value: string | number | boolean;
  type: 'str' | 'int' | 'bool';
};

const CustomResourceParameters = () => {
  const colorModeValue = useColorModeValue('sm', 'sm-dark');
  const location = useLocation();
  const passedData: any = location.state;
  const deployment_type = passedData?.deployment_type;
  const selectedOption = passedData?.selectedOption;
  const [searchParam, setSearchParam] = useState('');
  const [selectedCrParam, setSelectedCrParam] = useState<any>(null);
  const text = 'Update custom resource parameter';
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  const createSchema = (selectedType: 'str' | 'int' | 'bool') =>
    z.object({
      cluster_list: z
        .string()
        .transform((val) => parseInt(val, 10))
        .nullable(),
      cr_list: z
        .string()
        .transform((val) => parseInt(val, 10))
        .nullable(),
      cr_param_value: z
        .string()
        .nonempty({ message: 'New value is required' })
        .refine(
          (value) => {
            if (selectedType === 'int') {
              return !isNaN(Number(value)) && Number.isInteger(Number(value));
            } else if (selectedType === 'bool') {
              return value === 'true' || value === 'false';
            } else if (selectedType === 'str') {
              return isNaN(Number(value));
            }
            return false;
          },
          {
            message: `Value must match the expected type: ${selectedType}`,
          }
        ),
    });

  const [schema, setSchema] = React.useState(createSchema('str'));

  type FormData = z.infer<typeof schema>;

  const defaultValues: FormData = {
    cluster_list: null,
    cr_list: null,
    cr_param_value: '',
  };

  // Initialize the form
  const methods = useForm<FormData>({
    resolver: zodResolver(schema),
    defaultValues,
    mode: 'onChange',
  });

  useEffect(() => {
    const type = selectedCrParam?.type || 'str';
    setSchema(createSchema(type));
  }, [selectedCrParam?.type]);

  const { watch, handleSubmit } = methods;
  const selectedCluster = watch('cluster_list');
  const selectedCr = watch('cr_list');

  // API calls
  const { getClusterList, getCustomResourceList, getCustomResourcePath, patchCustomResourceUnnamed } =
    useCustomResource();
  const { checkEnabledConsistency } = useSiteStatus();

  const { data: clusterList, isLoading: isClusterLoading, error } = getClusterList();
  const { data: crCardList, isLoading: isCrCardLoading, error: crCardError } = getCustomResourceList(selectedCluster);

  const isQueryEnabled = !!selectedCluster && !!selectedCr && !!clusterList && !!crCardList && !!searchParam;

  const resources = crCardList?.crs || [];
  const selectedCrString = selectedCr !== null ? String(selectedCr) : undefined;
  const { allEnabled, statuses, isSelectedEnabled } = checkEnabledConsistency(resources, selectedCrString);

  const formattedSelectedCr = selectedCr !== null && selectedCr !== undefined ? String(selectedCr) : undefined;
  const {
    data: crPath,
    isLoading: isCrPathLoading,
    error: crPathError,
  } = getCustomResourcePath(selectedCluster, formattedSelectedCr, searchParam, isQueryEnabled);

  const mutation = patchCustomResourceUnnamed();

  const handleSearchParamChange = (newSearchParam: string) => {
    setSearchParam((prevSearchParam) => {
      if (prevSearchParam !== newSearchParam) {
        setSelectedCrParam(null);
      }
      return newSearchParam;
    });
  };

  const columns = [
    {
      header: 'path',
      accessorKey: 'path',
      id: 'path',
    },
    {
      header: 'value',
      accessorKey: 'value',
      id: 'value',
    },
    {
      header: 'type',
      accessorKey: 'type',
      id: 'type',
    },
    {
      header: 'add',
      accessorKey: 'add',
      id: 'add',
      cell: ({ row }: any) => {
        return isSelectedEnabled ? (
          <Popover>
            <PopoverTrigger>
              <Box as="span" cursor="pointer">
                <InfoIcon data-testid="info-icon" w={6} h={6} color="blue.500" />
              </Box>
            </PopoverTrigger>
            <PopoverContent>
              <PopoverArrow />
              <PopoverCloseButton />
              <PopoverHeader>Edit blocked</PopoverHeader>
              <PopoverBody>
                Please deactivate pod/custom resource <br />
                to edit this field
              </PopoverBody>
            </PopoverContent>
          </Popover>
        ) : (
          <BsPlusCircleFill
            size="30"
            color="green"
            onClick={() => handleAddClick(row.original)}
            style={{ cursor: 'pointer' }}
          />
        );
      },
    },
  ];

  const handleAddClick = (record: any) => {
    setSelectedCrParam(record);
  };

  const handleGoBack = () => {
    setSelectedCrParam(null);
  };

  const handleUpdate = (data: FormData) => {
    const payloadData: PatchCustomResourceUnnamedPayload = {
      cluster_id: data.cluster_list,
      cr_name: selectedCr?.toString() || '',
      path_str: selectedCrParam.path,
      new_value: data.cr_param_value,
    };

    mutation.mutate(payloadData);
    setSelectedCrParam((prev: CustomResourceUpdateParamValues) => ({
      ...prev,
      value: data.cr_param_value,
    }));
  };

  return (
    <>
      <Stack
        spacing="1"
        direction={{
          base: 'column',
          lg: 'row',
        }}
        justify="space-between"
      >
        <Stack spacing="1">
          <Heading fontWeight="bold">O-RAN DU/CU Manager</Heading>
        </Stack>
      </Stack>
      <Stack
        spacing={{
          base: '5',
          lg: '6',
        }}
      >
        <Box
          bg="bg-surface"
          boxShadow={{
            base: 'none',
            md: colorModeValue,
          }}
        >
          {/* App bar */}
          <Flex justify="center" m="8">
            <SimpleViewAppBar deployment_type={deployment_type} selectedOption={selectedOption} />
          </Flex>
          <Heading as="h2" size="lg" textAlign="center" pb="4">
            Custom Resource Parameters
          </Heading>
          <FormProvider {...methods}>
            <Box p="12">
              {/* Cluster list Dropdown with Loader */}
              {isClusterLoading ? (
                <Box textAlign="center" py="4">
                  <Loader />
                </Box>
              ) : (
                <SelectFormField
                  name="cluster_list"
                  label="Cluster list"
                  options={clusterList?.map((cluster: any) => ({
                    value: cluster.id,
                    label: `${cluster.id.toString()}:${cluster.cluster_name} || ${cluster.software_release}`,
                  }))}
                  tooltip="List of available clusters"
                  isDisabled={false}
                />
              )}

              {/* CR names Dropdown with Loader */}
              {selectedCluster ? (
                isCrCardLoading ? (
                  <Box textAlign="center" py="4">
                    <Loader />
                  </Box>
                ) : (
                  <SelectFormField
                    name="cr_list"
                    label="CR list"
                    options={crCardList?.crs?.map((cluster: any) => ({
                      value: cluster.name,
                      label: `${cluster.name.toString()} `,
                    }))}
                    tooltip="List of available CRs"
                    isDisabled={false}
                  />
                )
              ) : null}

              {/* Search Parameter Input */}
              {selectedCluster && crCardList && (
                <Box py="4">
                  <InputFormField
                    name="cr_search_param_value"
                    label="Search Parameter"
                    tooltip="Search for a custom resource parameter, be specific with your search parameter"
                    placeholder="Enter search parameter"
                    onChangeHandler={(value) => handleSearchParamChange(value)}
                  />
                </Box>
              )}

              {/* Custom Resource Path */}
              {isQueryEnabled && isCrPathLoading && selectedCluster && crCardList ? (
                <Loader />
              ) : selectedCrParam ? (
                // Edit form
                <>
                  <Stack borderRadius="4" boxShadow="lg" border={borderColor} p="8" marginTop="4" marginBottom="12">
                    <HStack justifyContent="center" mb="4">
                      <Text fontSize="lg" fontWeight="bold">
                        {text.toUpperCase()}
                      </Text>
                    </HStack>

                    {mutation.isLoading ? (
                      <Flex justifyContent="center" alignItems="center" minHeight="100px">
                        <Loader />
                      </Flex>
                    ) : (
                      <HStack key={'item.id'} justifyContent="space-around" mb="4">
                        {/* Path */}
                        <Box>
                          <Text as="b" fontSize="lg" mr="2">
                            Path:
                          </Text>
                          <Text fontSize="lg">{selectedCrParam.path}</Text>
                        </Box>

                        {/* Value */}
                        <Box>
                          <Text as="b" fontSize="lg" mr="2">
                            Value:
                          </Text>
                          <Text fontSize="lg">{selectedCrParam.value}</Text>
                        </Box>

                        {/* Type */}
                        <Box>
                          <Text as="b" fontSize="lg" mr="2">
                            Type:
                          </Text>
                          <Text fontSize="lg">{selectedCrParam.type}</Text>
                        </Box>
                      </HStack>
                    )}

                    {/* Value Input */}
                    <Box mt="4">
                      <InputFormField
                        name="cr_param_value"
                        label="New value"
                        tooltip={`New value must be of type ${selectedCrParam?.type || 'type'}`}
                        isDisabled={isSelectedEnabled}
                        placeholder={selectedCrParam.value}
                      />
                    </Box>

                    {/* Buttons */}
                    <Flex mt="4" gap="4" justifyContent="space-between">
                      <Button variant="outline" colorScheme="teal" onClick={handleGoBack}>
                        Go Back
                      </Button>

                      <Button variant="primary" onClick={handleSubmit(handleUpdate)}>
                        Submit
                      </Button>
                    </Flex>

                    <Flex alignItems="center" mt="8">
                      <InfoIcon data-testid="info-icon" />
                      <Text ml="2">
                        In order for the new parameter value to take affect you need to deactivate and then activate the
                        custom resources.
                      </Text>
                    </Flex>
                  </Stack>
                </>
              ) : (
                crPath &&
                Array.isArray(crPath) && (
                  <>
                    <DataTable
                      columns={columns || []}
                      data={crPath || []}
                      isExpandable={false}
                      defaultPageSize={50}
                      hasEmptyResult={false}
                      size="sm"
                    />
                  </>
                )
              )}
            </Box>
          </FormProvider>
        </Box>
      </Stack>
    </>
  );
};

export default CustomResourceParameters;
