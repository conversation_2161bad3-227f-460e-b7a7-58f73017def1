import {
  DarkModeUpdateObject,
  FanModeUpdateObject,
  FirewallNode,
  NetConfResponse,
  NetworkUpdateRequestObject,
  PowerNode,
  SwitchNode,
  SystemModeUpdateObject,
  TempSensorUpdateObject,
} from '../types/orchestrator.types';
import { MONITOR_STATE } from './../data/constants';
import { apiClient } from './httpCommon';
import { ActionPhase, ServerActionType } from '../pages/CellOverview/hooks/services/use_Orc_ServerTasks';
import _ from 'lodash';
import { CBSDPostType, CPIForm } from '../types/duCuManager.type';
import { ApiResponse } from '../pages/CellOverview/hooks/services/use_Orc_OranRuTasks';
import { OranRuActionType } from '../components/nodeComponents/du-cuManager/OranRuActionTypes';

export const getNodeCompByNodeId = async (id: string) => {
  const { data } = await apiClient.get(`/orc/nodes/${id}`);
  return data;
};

export const get4gNodeCompByNodeId = async (id: string) => {
  const { data } = await apiClient.get(`/orc/nodes/4g/${id}`);
  return data;
};

export const getPodNodeDetails = async (node_id: string) => {
  const { data } = await apiClient.get(`/orc/pod/nodes/${node_id}`);
  return data;
};

export const getRadioNodeDetails = async (node_id: string) => {
  const { data } = await apiClient.get(`/orc/radio/nodes/${node_id}`);
  return data;
};

// PUT request to update antenna pattern
export const putAntennaPattern = async ({ nodeId, configId }: { nodeId: string; configId: number }) => {
  const { data } = await apiClient.put(`/orc/nodes/${nodeId}/antenna`, configId);
  return data;
};

// PUT request Network

export const putNetworkUpdate = async ({
  nodeId,
  networkConfig,
}: {
  nodeId: string;
  networkConfig: NetworkUpdateRequestObject;
}): Promise<NetConfResponse> => {
  const { data } = await apiClient.put(`/orc/nodes/${nodeId}/network`, networkConfig);
  return data;
};

export const putDarkMode = async ({
  nodeId,
  darkModeUpdateObject,
}: {
  nodeId: string;
  darkModeUpdateObject: DarkModeUpdateObject;
}) => {
  const { data } = await apiClient.put(`/orc/nodes/${nodeId}/mode/dark`, darkModeUpdateObject);
  return data;
};

export const NodeResetByNodeId = async (nodeId: string) => {
  const { data } = await apiClient.post(`/orc/nodes/${nodeId}/reset`, 'SYSTEM');
  return data;
};

// PUT request Fan mode
export const putFanMode = async ({
  nodeId,
  fanModeUpdateObject,
}: {
  nodeId: string;
  fanModeUpdateObject: FanModeUpdateObject;
}) => {
  const { data } = await apiClient.put(`/orc/nodes/${nodeId}/mode/fan`, fanModeUpdateObject);
  return data;
};

// PUT request System mode
export const putSystemMode = async ({
  nodeId,
  systemModeUpdateObject,
}: {
  nodeId: string;
  systemModeUpdateObject: SystemModeUpdateObject;
}) => {
  const { data } = await apiClient.put(`/orc/nodes/${nodeId}/mode/system`, systemModeUpdateObject);
  return data;
};

// PUT temp request mode
export const putTempSensor = async ({
  nodeId,
  tempSensorUpdateObject,
}: {
  nodeId: string;
  tempSensorUpdateObject: TempSensorUpdateObject;
}) => {
  const { data } = await apiClient.put(`/orc/nodes/${nodeId}/sensor/temp`, tempSensorUpdateObject);
  return data;
};

// Patch SNMP By Node Type
export const patchSNMPByNodeType = async ({
  nodeId,
  componentType,
  snmpConfiguration,
}: {
  nodeId: string;
  componentType: 'BLUWIRELESS' | 'FIBROLAN';
  snmpConfiguration: MONITOR_STATE;
}) => {
  const { data } = await apiClient.patch(`/orc/snmp/manage/${nodeId}/${componentType}`, {
    monitored: snmpConfiguration,
  });
  return data;
};

// SERVERS
export const getServerNodeDetailsByNodeId = async (node_id: string) => {
  const { data } = await apiClient.get(`/orc/server/nodes/${node_id}`);
  return data;
};

// FIREWALL
export const getFirewallNodeByNodeId = async (node_id: string): Promise<FirewallNode> => {
  const { data } = await apiClient.get<FirewallNode>(`/orc/firewall/nodes/${node_id}`);
  return data;
};

// SWITCH
export const getSwitchNodeDetailsByNodeId = async (node_id: string): Promise<SwitchNode> => {
  const { data } = await apiClient.get<SwitchNode>(`/orc/network/nodes/${node_id}`);
  return data;
};

// POWER
export const getPowerNodeByNodeId = async (node_id: string): Promise<PowerNode> => {
  const { data } = await apiClient.get<PowerNode>(`/orc/power/nodes/${node_id}`);
  return data;
};

export const createServerTask = async (node_id: string, task_type: string) => {
  const { data } = await apiClient.post(`/orc/actions/tasks/${task_type}/server/nodes/${node_id}`);
  return data;
};

export const getServerTasksByTaskStatus = async (node_id: string, task_status: string[]) => {
  // generate query filter
  const query = task_status.map((status) => `task_status=${status}`).join('&');
  const { data } = await apiClient.get(`/orc/actions/tasks/server/nodes/${node_id}?${query}`);
  return data;
};

export const getServerTaskByTaskId = async (node_id: string, task_id: string) => {
  const { data } = await apiClient.get(`/orc/actions/tasks/${task_id}/server/nodes/${node_id}`);
  return data;
};

export const getServerTasks = async (node_id: string) => {
  const { data }: { data: any } = await apiClient.get(`/orc/actions/tasks/server/nodes/${node_id}`);
  const sortedTasks = data.sort(
    (task_a: any, task_b: any) =>
      new Date(task_b.task_created_at).getTime() - new Date(task_a.task_created_at).getTime()
  );
  const firstFiveTasks = sortedTasks.slice(0, 5);
  return firstFiveTasks;
};

const postActionDruidRestart = async (payload: any) => {
  const { data } = await apiClient.post(`/orc/actions/action_druid_restart`, payload);
  return data;
};

const postActionRadioReset = async (payload: any) => {
  const { data } = await apiClient.post(`/orc/actions/action_radio_reset`, payload);
  return data;
};

const postActionRadioLock = async (payload: any) => {
  const { data } = await apiClient.post(`/orc/actions/action_radio_lock`, payload);
  return data;
};

const postActionRadioUnlock = async (payload: any) => {
  const { data } = await apiClient.post(`/orc/actions/action_radio_unlock`, payload);
  return data;
};

export const postServerRadioActions = async (
  local_node_id: string,
  phase: ActionPhase | '',
  actionType: ServerActionType | ''
) => {
  if (actionType === ServerActionType.RESET) {
    const payload = {
      node_id: local_node_id,
      reset_type: 'resetNode',
      phase: phase,
      priority: 0,
    };

    const data = await postActionRadioReset(payload);
    return data;
  }

  if (actionType === ServerActionType.LOCK) {
    const payload = {
      node_id: local_node_id,
      phase: phase,
      priority: 0,
    };

    const data = await postActionRadioLock(payload);
    return data;
  }

  if (actionType === ServerActionType.UNLOCK) {
    const payload = {
      node_id: local_node_id,
      phase: phase,
      priority: 0,
    };

    const data = await postActionRadioUnlock(payload);
    return data;
  }
};

export const postServerDruidActions = async (local_node_id: string, phase: ActionPhase | '') => {
  const payload = {
    node_id: local_node_id,
    phase: phase,
    priority: 0,
  };

  const data = await postActionDruidRestart(payload);
  return data;
};

export const getServerAction = async (actionData: any) => {
  if (_.isUndefined(actionData)) {
    return null;
  }

  if (_.isUndefined(actionData) || _.isNull(actionData) || actionData.length === 0) {
    return null;
  }

  const action_uid = actionData[0]?.uid;
  const { data } = await apiClient.get(`/orc/actions/operation/${action_uid}`);
  return data;
};

export const getClusterResourceByNodeId = async (node_id: string) => {
  const { data } = await apiClient.get(`/orc/anthos/cluster_status/${node_id}`);
  return data;
};

// Oran RU Actions
const postActionOranRuAll = async (action_type: string, payload: any) => {
  const { data } = await apiClient.post(`/orc/actions/ru/${action_type}`, payload);
  return data;
};
export const postOranRuActions = async (
  local_node_id: string,
  phase: ActionPhase | '',
  actionType: OranRuActionType | ''
): Promise<ApiResponse[]> => {
  const payload = {
    node_id: local_node_id,
    phase: phase,
    priority: 0,
  };

  let response;

  switch (actionType) {
    case OranRuActionType.REBOOT:
      response = await postActionOranRuAll(OranRuActionType.REBOOT, payload);
      break;

    case OranRuActionType.FACTORY_RECOVERY:
      response = await postActionOranRuAll(OranRuActionType.FACTORY_RECOVERY, payload);
      break;

    case OranRuActionType.TRIGGER_DIAGNOSTICS:
      response = await postActionOranRuAll(OranRuActionType.TRIGGER_DIAGNOSTICS, payload);
      break;

    case OranRuActionType.TRIGGER_RU_CRASH_LOGS_UPLOAD:
      response = await postActionOranRuAll(OranRuActionType.TRIGGER_RU_CRASH_LOGS_UPLOAD, payload);
      break;

    default:
      return [];
  }
  return response;
};

// Oran RU E500 Actions
export const getE500SoftwareVersions = async () => {
  const { data } = await apiClient.get(`/orc/software/e500`);
  return data;
};
export const postE500SoftwareVersion = async (payload: any) => {
  const { data } = await apiClient.post(`/orc/software/e500/update`, payload);
  return data;
};
export const getE500SoftwareUpdateStatus = async (deployment_id: string) => {
  const { data } = await apiClient.get(`/orc/software/deployment/${deployment_id}`);
  return data;
};

//Oran DU Actions
export const postOranDuRestartActions = async (deploymentName: string, nodeType: string): Promise<ApiResponse[]> => {
  const { data } = await apiClient.post(
    `/rad/config/integration/pods/restart?deployment_name=${deploymentName}&pod_type=${nodeType}`
  );
  return data;
};
export const postOranDuLockActions = async (deploymentName: string): Promise<ApiResponse[]> => {
  const { data } = await apiClient.post(`/rad/config/integration/cell/lock?deployment_name=${deploymentName}`);
  return data;
};
export const postOranDuUnLockActions = async (deploymentName: string): Promise<ApiResponse[]> => {
  const { data } = await apiClient.post(`/rad/config/integration/cell/unlock?deployment_name=${deploymentName}`);
  return data;
};
export const postOranDuActions = async (
  deploymentName: string,
  sanitisedNodeType: string,
  actionType: string
): Promise<ApiResponse[]> => {
  let response;

  switch (actionType) {
    case OranRuActionType.RESTART:
      response = await postOranDuRestartActions(deploymentName, sanitisedNodeType);
      break;

    case OranRuActionType.LOCK:
      response = await postOranDuLockActions(deploymentName);
      break;

    case OranRuActionType.UNLOCK:
      response = await postOranDuUnLockActions(deploymentName);
      break;

    default:
      return [];
  }
  return response;
};

export const getCBSDDevices = async () => {
  const { data } = await apiClient.get(`/orc/cbsd/search?limit=1000`);
  return data;
};
export const getCBSDDevicesBySerialNumber = async (serialNumber: string) => {
  const { data } = await apiClient.get(`/orc/cbsd/search?serial_number=${serialNumber}`);
  const { cbsds = [] } = data;
  return cbsds[0];
};

export const activateCBSDDevices = async (serial: string) => {
  const { data } = await apiClient.patch(`/orc/cbsd/${serial}/grant/activate`);
  return data;
};

export const deactivateCBSDDevices = async (serial: string) => {
  const { data } = await apiClient.patch(`/orc/cbsd/${serial}/grant/deactivate`);
  return data;
};

export const registerCBSDDevices = async (serial: string) => {
  const { data } = await apiClient.post(`/orc/cbsd/${serial}/register`);
  return data;
};

export const deRegisterCBSDDevices = async (serial: string) => {
  const { data } = await apiClient.post(`/orc/cbsd/${serial}/deregister`);
  return data;
};

export const createCBSD = async ({
  cbsd,
  cpiSigned,
}: {
  cbsd: CBSDPostType;
  cpiSigned: boolean;
}): Promise<CBSDPostType> => {
  const { data } = await apiClient.post(`/orc/cbsd?sign=${cpiSigned}`, cbsd);
  return data;
};

export const getCPI = async () => {
  const { data } = await apiClient.get(`/orc/cpi/search?limit=1000`);
  return data;
};

export const createCPI = async ({ cpi }: { cpi: CPIForm }): Promise<CPIForm> => {
  const { data } = await apiClient.post(`/orc/cpi`, cpi);
  return data;
};

export const updateCPI = async ({ cpi, user_id }: { cpi: CPIForm; user_id: number }): Promise<CPIForm> => {
  const { data } = await apiClient.patch(`/orc/cpi/${user_id}`, cpi);
  return data;
};

export const editCBSD = async ({
  cbsd,
  serialNumber,
}: {
  cbsd: CBSDPostType;
  serialNumber: string;
}): Promise<CBSDPostType> => {
  const { data } = await apiClient.patch(`/orc/cbsd/${serialNumber}`, cbsd);
  return data;
};

export const deleteCBSDDevices = async (serial: string) => {
  const { data } = await apiClient.delete(`/orc/cbsd/${serial}`);
  return data;
};

export const deleteCPIDevices = async (rowData: CPIForm) => {
  const { data } = await apiClient.delete(`/orc/cpi/${rowData.id}`, { data: rowData });
  return data;
};
