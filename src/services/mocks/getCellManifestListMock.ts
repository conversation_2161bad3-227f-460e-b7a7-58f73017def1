export const getManifestListMock = [
  {
    node_serial_no: 'F0486900D97C',
    node_type: 'StreetCell',
    lifecycle: 'COMMISSIONING',
    node_id: 'F0486900D97C',
    version: '1.1',
    orientation: 'UNK',
    roles: ['RU', 'DU', 'CU'],
    site_id: 12,
    country_code: 'GBR',
    country_name: 'United Kingdom',
    region_id: 5,
    region_code: 'BCH1',
    region_name: 'Beach Dev1',
    site_name: 'Beach Dev 1 Site',
    latitude: 51.573654,
    longitude: -0.76069,
    deployed_count: 1,
    manifest_type: 'streetcell',
  },
  {
    node_serial_no: '9XJKTN3-dauk-mrl-d-cav2-kvm-druid',
    node_type: 'Server-VM',
    lifecycle: 'STAGING',
    node_id: 'GB-DEV2-VM-0001',
    version: '1.1',
    roles: [],
    site_id: 11,
    country_code: 'GBR',
    country_name: 'United Kingdom',
    region_id: 4,
    region_code: 'DEV2',
    region_name: 'Marlow Development',
    site_name: 'Ansir Test',
    latitude: 51.573654,
    longitude: -0.76069,
    deployed_count: 6,
    manifest_type: 'server',
  },
  {
    node_serial_no: 'AAAA34407606',
    node_type: 'StreetCell',
    lifecycle: 'COMMISSIONING',
    node_id: 'AAAA34407606',
    version: '1.1',
    orientation: 'S',
    roles: ['RU'],
    site_id: 11,
    country_code: 'GBR',
    country_name: 'United Kingdom',
    region_id: 4,
    region_code: 'DEV2',
    region_name: 'Marlow Development',
    site_name: 'Ansir Test',
    latitude: 51.573654,
    longitude: -0.76069,
    deployed_count: 1,
    manifest_type: 'streetcell',
  },
  {
    node_serial_no: 'AAAA34408106',
    node_type: 'StreetCell',
    lifecycle: 'COMMISSIONING',
    node_id: 'AAAA34408106',
    version: '1.1',
    orientation: 'S',
    roles: ['RU', 'DU', 'CU'],
    site_id: 11,
    country_code: 'GBR',
    country_name: 'United Kingdom',
    region_id: 4,
    region_code: 'DEV2',
    region_name: 'Marlow Development',
    site_name: 'Ansir Test',
    latitude: 51.573654,
    longitude: -0.76069,
    deployed_count: 1,
    manifest_type: 'streetcell',
  },
];
