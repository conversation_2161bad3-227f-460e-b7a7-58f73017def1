export const getPowerNodeMock = {
  id: 'GB-DEV2-POWER-0001',
  ups: {
    system: {
      sysDescr: 'Linux SNMP-System',
      sysObjectID: '1.3.6.1.4.1.43943',
      sysUpTime: 7931708,
      upTime: '22:01:57',
      startTime: '2024-07-18T11:00:35.216404Z',
      sysContact: 'syscontact',
      sysName: 'SNMP-System',
      latitude: null,
      longitude: null,
      sysServices: 79,
      sysORLastChange: null,
    },
    info: {
      manufacturer: '',
      model: 'OLHV3K0',
      name: 'SNMP web pro',
    },
    battery: {
      battery_status: 'normal',
      time_on_battery: 0,
      estimated_time_left: 375,
      estimated_charge_left: 21,
      battery_voltage: 82.1,
      battery_current: 0,
      temperature: 25,
    },
    input: {
      inputs: [
        {
          index: 1,
          voltage: 239,
          current: 0,
          power: 0,
          frequency: 50.1,
        },
      ],
    },
    output: {
      source: 'normal',
      frequency: 50.1,
      outputs: [
        {
          index: 1,
          voltage: 239,
          current: 0,
          power: 0,
          percent_load: 0,
        },
      ],
    },
    firmware: 'VERFW:01955.04',
    ip_address: '*************',
    component_id: '83322401503852',
    node_type: 'Power',
    monitored: 'Y',
    status: 'OK',
    status_change_time: '2024-07-19T07:35:09.369517Z',
    last_success_time: '2024-07-19T09:02:32.216404Z',
    last_query_time: '2024-07-19T09:02:32.216404Z',
    last_query_duration: 1.7456450462341309,
    last_error: null,
    query_error_count: 0,
  },
};
