import { AlarmElement } from '../../types/metricCollector.type';

export const getAlarmListMock = {
  count: 16,
  alarms: [
    {
      id: 0,
      object_id: 'F04869016064',
      object_type: 'GnbXpu',
      severity: 'minor',
      type: 'vsr_status',
      created: '2023-10-05T12:02:59.359303+00:00',
      updated: '2023-10-05T12:02:59.359303+00:00',
      expires: null,
      status: 'new',
      acknowledged: null,
      acknowledger: null,
      resolved: null,
      resolver: null,
      resolution: null,

      //   status: 'acknowledged',
      //   acknowledged: 'TStamp',
      //   acknowledger: 'me',
      //   resolved: null,
      //   resolver: null,
      //   resolution: null,

      //   status: 'resolved',
      //   acknowledged: 'TStamp',
      //   acknowledger: 'me',
      //   resolved: "ts",
      //   resolver: 'me',
      //   resolution: 'I fixed the screw',

      // status: 'resolved',
      // acknowledged: null,
      // acknowledger: null,
      // resolved: "ts",
      //   resolver: 'me',
      //   resolution: 'I fixed the screw',

      details: 'kasdjflkajskld',
      events: [48246],
    },
    {
      id: 1,
      object_id: 'F04869016064',
      object_type: 'vsrplatform',
      severity: 'none',
      type: 'vsr_status',
      status: 'acknowledged',
      created: '2023-10-05T12:02:59.359303+00:00',
      updated: '2023-10-05T12:02:59.359303+00:00',
      expires: null,

      acknowledged: '2023-10-05T12:02:59.359303+00:00',
      acknowledger: 'Atif',

      resolved: null,
      resolver: null,
      resolution: null,
      details: null,
      events: [48246],
    },
    {
      id: 2,
      object_id: 'F04869016064',
      object_type: 'GnbXpu',
      severity: 'none',
      type: 'Node Lost Comms',
      status: 'resolved',
      created: '2023-10-05T12:02:59.359303+00:00',
      updated: '2023-10-05T12:02:59.359303+00:00',
      expires: null,
      acknowledged: '2023-10-05T12:02:59.359303+00:00',
      acknowledger: 'Atif',
      // new to resolved should not be allowed to acknowledge
      //update alarm with acknoledged status will be as previously acknowledged by and
      //  acknowledged button should be enabled.
      // - resolved alarm can't be updated

      resolved: '2023-10-05T12:02:59.359303+00:00',
      resolver: 'Atif',
      resolution: null,
      details: null,
      events: [48246],
    },
    {
      id: 3,
      object_id: 'F04869016064',
      object_type: 'GnbXpu',
      severity: 'none',
      type: 'Node Lost Comms',
      status: 'updated',
      created: '2023-10-05T12:02:59.359303+00:00',
      updated: '2023-10-05T12:02:59.359303+00:00',
      expires: null,
      acknowledged: '2023-10-05T12:02:59.359303+00:00',
      acknowledger: 'Atif',
      resolved: null,
      resolver: null,
      resolution: null,
      details: 'alarm no 3',
      events: [48246],
    },
    {
      id: 4,
      object_id: 'F04869016064',
      object_type: 'vsrplatform',
      severity: 'minor',
      type: 'Node Lost Comms',
      status: 'resolved',
      created: '2023-10-05T12:02:59.359303+00:00',
      updated: '2023-10-05T12:02:59.359303+00:00',
      expires: null,
      acknowledged: null,
      acknowledger: null,
      resolved: null,
      resolver: null,
      resolution: null,
      details: null,
      events: [48246],
    },
    {
      id: 5,
      object_id: 'F04869016064',
      object_type: 'GnbXpu',
      severity: 'major',
      type: 'Node Lost Comms',
      status: 'resolved',
      created: '2023-10-05T12:02:59.359303+00:00',
      updated: '2023-10-05T12:02:59.359303+00:00',
      expires: null,
      acknowledged: null,
      acknowledger: null,
      resolved: null,
      resolver: null,
      resolution: null,
      details: null,
      events: [48246],
    },
    {
      id: 6,
      object_id: 'F04869016064',
      object_type: 'GnbXpu',
      severity: 'none',
      type: 'Node Lost Comms',
      status: 'resolved',
      created: '2023-10-05T12:02:59.359303+00:00',
      updated: '2023-10-05T12:02:59.359303+00:00',
      expires: null,
      acknowledged: null,
      acknowledger: null,
      resolved: null,
      resolver: null,
      resolution: null,
      details: null,
      events: [48246],
    },
    {
      id: 7,
      object_id: 'F04869016064',
      object_type: 'GnbXpu',
      severity: 'minor',
      type: 'vsr_status',
      status: 'acknowledged',
      created: '2023-10-05T12:02:59.359303+00:00',
      updated: '2023-10-05T12:02:59.359303+00:00',
      expires: null,
      acknowledged: null,
      acknowledger: null,
      resolved: null,
      resolver: null,
      resolution: null,
      details: null,
      events: [48246],
    },
    {
      id: 8,
      object_id: 'F04869016064',
      object_type: 'GnbXpu',
      severity: 'none',
      type: 'Node Lost Comms',
      status: 'acknowledged',
      created: '2023-10-05T12:02:59.359303+00:00',
      updated: '2023-10-05T12:02:59.359303+00:00',
      expires: null,
      acknowledged: null,
      acknowledger: null,
      resolved: null,
      resolver: null,
      resolution: null,
      details: null,
      events: [48246],
    },
    {
      id: 9,
      object_id: 'F04869016064',
      object_type: 'vsrplatform',
      severity: 'minor',
      type: 'Node Lost Comms',
      status: 'updated',
      created: '2023-10-05T12:02:59.359303+00:00',
      updated: '2023-10-05T12:02:59.359303+00:00',
      expires: null,
      acknowledged: null,
      acknowledger: null,
      resolved: null,
      resolver: null,
      resolution: null,
      details: null,
      events: [48246],
    },
    {
      id: 10,
      object_id: 'F04869016064',
      object_type: 'GnbXpu',
      severity: 'major',
      type: 'vsr_status',
      status: 'acknowledged',
      created: '2023-10-05T12:02:59.359303+00:00',
      updated: '2023-10-05T12:02:59.359303+00:00',
      expires: null,
      acknowledged: null,
      acknowledger: null,
      resolved: null,
      resolver: null,
      resolution: null,
      details: null,
      events: [48246],
    },
    {
      id: 11,
      object_id: 'F04869016064',
      object_type: 'vsrplatform',
      severity: 'major',
      type: 'vsr_status',
      status: 'acknowledged',
      created: '2023-10-05T12:02:59.359303+00:00',
      updated: '2023-10-05T12:02:59.359303+00:00',
      expires: null,
      acknowledged: null,
      acknowledger: null,
      resolved: null,
      resolver: null,
      resolution: null,
      details: null,
      events: [48246],
    },
    {
      id: 12,
      object_id: 'F04869016064',
      object_type: 'vsrplatform',
      severity: 'minor',
      type: 'vsr_status',
      status: 'resolved',
      created: '2023-10-05T12:02:59.359303+00:00',
      updated: '2023-10-05T12:02:59.359303+00:00',
      expires: null,
      acknowledged: null,
      acknowledger: null,
      resolved: null,
      resolver: null,
      resolution: null,
      details: null,
      events: [48246],
    },
    {
      id: 13,
      object_id: 'F04869016064',
      object_type: 'vsrplatform',
      severity: 'major',
      type: 'Node Lost Comms',
      status: 'acknowledged',
      created: '2023-10-05T12:02:59.359303+00:00',
      updated: '2023-10-05T12:02:59.359303+00:00',
      expires: null,
      acknowledged: null,
      acknowledger: null,
      resolved: null,
      resolver: null,
      resolution: null,
      details: null,
      events: [48246],
    },
    {
      id: 14,
      object_id: 'F04869016064',
      object_type: 'vsrplatform',
      severity: 'none',
      type: 'Node Lost Comms',
      status: 'new',
      created: '2023-10-05T12:02:59.359303+00:00',
      updated: '2023-10-05T12:02:59.359303+00:00',
      expires: null,
      acknowledged: null,
      acknowledger: null,
      resolved: null,
      resolver: null,
      resolution: null,
      details: null,
      events: [48246],
    },
    {
      id: 15,
      object_id: 'F04869016064',
      object_type: 'GnbXpu',
      severity: 'minor',
      type: 'Node Lost Comms',
      status: 'acknowledged',
      created: '2023-10-05T12:02:59.359303+00:00',
      updated: '2023-10-05T12:02:59.359303+00:00',
      expires: null,
      acknowledged: null,
      acknowledger: null,
      resolved: null,
      resolver: null,
      resolution: null,
      details: null,
      events: [48246],
    },
  ],
};

export const getEventListMock = {
  count: 1,
  events: [
    {
      id: 48246,
      header: {
        domain: 'nms',
        eventId: '63559',
        eventName: 'Node Lost Comms',
        eventType: 'alarm',
        priority: 'high',
        reportingEntityName: 'airspan_acp_agent',
        sourceName: 'GnbXpu',
        sourceId: 'F04869016064',
        eventTime: '2023-09-13T15:08:07.477000+00:00',
        eventDuration: 0,
        systemDN: '',
      },
      data: {
        objectId: 'F04869016064',
        objectType: 'GnbXpu',
        uri: 'https://192.168.140.63:8181',
        type: 'Node Lost Comms',
        cause: 'The server is unable to communicate with the Node',
        perceivedSeverity: 'High',
        specificProblem:
          'Loss in communications is normally due to a temporary network condition and will typically clear automatically',
        trendIndication: 'no_change',
        monitoredAttributes: [],
        proposedRepairActions: [],
        additionalText: '',
        additionalInformation: '',
      },
      alarm_id: 2,
    },
  ],
  facets: null,
};

export const mockAlarms: AlarmElement[] = [
  {
    id: 12756,
    internal_id: '12656',
    severity: 'major',
    type: 'Cell Out Of Service',
    status: 'new',
    specific_problem: 'Cell Out Of Service',
    expires: null,
    subRows: [
      {
        id: 0, // Dummy ID for subRow
        internal_id: '0',
        object_id: 'F0486901674C',
        object_type: 'AIRSPAN',
        event_name: 'device_connectivity',
        severity: 'major',
        type: '',
        status: 'new',
        specific_problem: '',
        userFullName: 'Saqib Ishfaq',
      },
    ],
    inventory: {
      node: {
        node_id: 'AABA31700001',
        node_type: 'StreetCell',
        cell_refs: ['GBDEV2NS000001'],
        lifecycle: 'OPERATIONAL',
        version: '1.1',
        roles: ['RU'],
        site_id: 7,
        site_name: 'CoMP Lab',
        site_address: 'Atlas House, Third Avenue, Globe Business Park',
        latitude: 51.573654,
        longitude: -0.76069,
        orientation: 'UNK',
        location_error: 69.686,
        node_location: {
          latitude: 51.573755,
          longitude: -0.759698,
          altitude: 87,
        },
        node_serial_no: 'AABA31700001',
        status: 'ERROR',
        trouble_score: 0,
      },
      cells: [
        {
          cell_ref: 'GBDEV2NS000001',
          oran_split: 'Split 6',
          country_code: 'GBR',
          country_name: 'United Kingdom',
          region_id: 4,
          region_code: 'DEV2',
          region_name: 'Marlow Development',
          site_id: 7,
          site_name: 'CoMP Lab',
          latitude: 51.573654,
          longitude: -0.76069,
          orientation: 'NE',
          lifecycle: 'FACTORY',
          status: 'ERROR',
          trouble_score: 0,
        },
      ],
    },
  },
];
