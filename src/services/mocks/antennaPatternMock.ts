export const antennaPatternMock = {
  version: 536,
  modeName: 'NORMAL_MODE',
  darkMode: false,
  caseOpen: true,
  fanMode: 'LINEAR',
  overtemp: 45,
  hysteresis: 36,
  tempSensor1: 39.875,
  tempSensor2: 0,
  bwFanSpeed: 0,
  asFanSpeed: 0,
  antenna: {
    board1: {
      switch1: 'RF1',
      switch2: 'RF1',
      switch3: 'RF2',
      switch4: 'RF1',
      switch5: 'RF2',
      switch6: 'RF1',
    },
    board2: {
      switch1: 'RF1',
      switch2: 'RF1',
      switch3: 'RF2',
      switch4: 'RF1',
      switch5: 'RF2',
      switch6: 'RF1',
    },
  },
  antennaConfiguration: 6,
  networkConfiguration: {
    DHCP: false,
    IP: '*************',
    SUB: '***************',
    GW: '*************',
    DNS: '0.0.0.0',
  },
  alarmCodes: [],
};
