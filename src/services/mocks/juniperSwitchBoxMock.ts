export const JunpierSwitcBoxhMock = {
  box: {
    jnxBoxClass: '*******.4.1.2636.*******.132.0',
    jnxBoxDescr: 'Juniper EX2300-24T Switch',
    jnxBoxSerialNo: 'JZ0220460459',
    jnxBoxRevision: '',
    jnxBoxInstalled: '2022-11-25T11:41:09.834139Z',
    jnxLEDLastChange: 0,
    jnxBoxKernelMemoryUsedPercent: 0,
    jnxBoxSystemDomainType: 'notApplicable',
    jnxBoxPersonality: '0.0',
    containers: [
      {
        jnxContainersIndex: 1,
        jnxContainersView: ['front'],
        jnxContainersLevel: 0,
        jnxContainersWithin: 0,
        jnxContainersType: '*******.4.1.2636.*******.132.0',
        jnxContainersDescr: 'chassis frame',
        jnxContainersCount: 1,
        contents: [
          {
            detail: {
              jnxContentsL1Index: 1,
              jnxContentsL2Index: 0,
              jnxContentsL3Index: 0,
              jnxContentsType: '*******.4.1.2636.*******.132',
              jnxContentsDescr: '',
              jnxContentsSerialNo: 'JZ0220460459',
              jnxContentsRevision: '',
              jnxContentsInstalled: '1970-01-01T00:00:00Z',
              jnxContentsPartNo: '',
              jnxContentsChassisId: 'singleChassis',
              jnxContentsChassisDescr: 'Single Chassis',
              jnxContentsChassisCleiCode: '',
              jnxContentsModel: '',
            },
            operating: {
              jnxOperatingContentsIndex: 1,
              jnxOperatingL1Index: 1,
              jnxOperatingL2Index: 0,
              jnxOperatingL3Index: 0,
              jnxOperatingDescr: '',
              jnxOperatingState: 'running',
              jnxOperatingTemp: 0,
              jnxOperatingCPU: 0,
              jnxOperatingISR: 0,
              jnxOperatingDRAMSize: 0,
              jnxOperatingBuffer: 0,
              jnxOperatingHeap: 0,
              jnxOperatingUpTime: '37 days, 23:31:02',
              jnxOperatingLastRestart: '1970-01-01T00:00:00Z',
              jnxOperatingMemory: 0,
              jnxOperatingStateOrdered: 'running',
              jnxOperatingChassisId: 'singleChassis',
              jnxOperatingChassisDescr: 'Single Chassis',
              jnxOperatingRestartTime: '2024-02-27T14:32:45Z',
              jnxOperating1MinLoadAvg: 0,
              jnxOperating5MinLoadAvg: 0,
              jnxOperating15MinLoadAvg: 0,
              jnxOperating1MinAvgCPU: 0,
              jnxOperating5MinAvgCPU: 0,
              jnxOperating15MinAvgCPU: 0,
              jnxOperatingFRUPower: 0,
              jnxOperatingBufferCP: 0,
              jnxOperatingMemoryCP: 0,
              jnxOperatingBufferExt: 0,
            },
            fru: null,
            led: {
              jnxLEDOriginator: '*******.4.1.2636.*******.132',
              jnxLEDDescr: ' chassis alarm LED',
              jnxLEDState: 'other',
              jnxLEDStateOrdered: 'other',
            },
          },
          {
            detail: {
              jnxContentsL1Index: 1,
              jnxContentsL2Index: 0,
              jnxContentsL3Index: 0,
              jnxContentsType: '*******.4.1.2636.*******.132',
              jnxContentsDescr: 'testing',
              jnxContentsSerialNo: 'JZ0220460459',
              jnxContentsRevision: '',
              jnxContentsInstalled: '1970-01-01T00:00:00Z',
              jnxContentsPartNo: '',
              jnxContentsChassisId: 'singleChassis',
              jnxContentsChassisDescr: 'Single Chassis',
              jnxContentsChassisCleiCode: '',
              jnxContentsModel: '',
            },
            operating: {
              jnxOperatingContentsIndex: 1,
              jnxOperatingL1Index: 1,
              jnxOperatingL2Index: 0,
              jnxOperatingL3Index: 0,
              jnxOperatingDescr: '',
              jnxOperatingState: 'running',
              jnxOperatingTemp: 0,
              jnxOperatingCPU: 0,
              jnxOperatingISR: 0,
              jnxOperatingDRAMSize: 0,
              jnxOperatingBuffer: 0,
              jnxOperatingHeap: 0,
              jnxOperatingUpTime: '37 days, 23:31:02',
              jnxOperatingLastRestart: '1970-01-01T00:00:00Z',
              jnxOperatingMemory: 0,
              jnxOperatingStateOrdered: 'running',
              jnxOperatingChassisId: 'singleChassis',
              jnxOperatingChassisDescr: 'Single Chassis',
              jnxOperatingRestartTime: '2024-02-27T14:32:45Z',
              jnxOperating1MinLoadAvg: 0,
              jnxOperating5MinLoadAvg: 0,
              jnxOperating15MinLoadAvg: 0,
              jnxOperating1MinAvgCPU: 20,
              jnxOperating5MinAvgCPU: 0,
              jnxOperating15MinAvgCPU: 0,
              jnxOperatingFRUPower: 0,
              jnxOperatingBufferCP: 0,
              jnxOperatingMemoryCP: 0,
              jnxOperatingBufferExt: 0,
            },
            fru: null,
            led: {
              jnxLEDOriginator: '*******.4.1.2636.*******.132',
              jnxLEDDescr: ' chassis alarm LED',
              jnxLEDState: 'other',
              jnxLEDStateOrdered: 'other',
            },
          },
        ],
      },
      {
        jnxContainersIndex: 2,
        jnxContainersView: ['rear'],
        jnxContainersLevel: 2,
        jnxContainersWithin: 1,
        jnxContainersType: '*******.4.1.2636.1.1.3.2.132.1.1.0',
        jnxContainersDescr: 'Power Supply slot',
        jnxContainersCount: 1,
        contents: [
          {
            detail: {
              jnxContentsL1Index: 1,
              jnxContentsL2Index: 1,
              jnxContentsL3Index: 0,
              jnxContentsType: '*******.4.1.2636.1.1.3.2.12.4',
              jnxContentsDescr: 'Power Supply 0 @ 0/0/*',
              jnxContentsSerialNo: '',
              jnxContentsRevision: '',
              jnxContentsInstalled: '1970-01-01T00:00:00Z',
              jnxContentsPartNo: '',
              jnxContentsChassisId: 'singleChassis',
              jnxContentsChassisDescr: 'Single Chassis',
              jnxContentsChassisCleiCode: '',
              jnxContentsModel: 'JPSU-65W-AC-AFO',
            },
            operating: {
              jnxOperatingContentsIndex: 2,
              jnxOperatingL1Index: 1,
              jnxOperatingL2Index: 1,
              jnxOperatingL3Index: 0,
              jnxOperatingDescr: 'Power Supply 0 @ 0/0/*',
              jnxOperatingState: 'running',
              jnxOperatingTemp: 0,
              jnxOperatingCPU: 0,
              jnxOperatingISR: 0,
              jnxOperatingDRAMSize: 0,
              jnxOperatingBuffer: 0,
              jnxOperatingHeap: 0,
              jnxOperatingUpTime: '37 days, 23:31:03',
              jnxOperatingLastRestart: '1970-01-01T00:00:00Z',
              jnxOperatingMemory: 0,
              jnxOperatingStateOrdered: 'running',
              jnxOperatingChassisId: 'singleChassis',
              jnxOperatingChassisDescr: 'Single Chassis',
              jnxOperatingRestartTime: '2024-02-27T14:32:45Z',
              jnxOperating1MinLoadAvg: 0,
              jnxOperating5MinLoadAvg: 0,
              jnxOperating15MinLoadAvg: 0,
              jnxOperating1MinAvgCPU: 0,
              jnxOperating5MinAvgCPU: 0,
              jnxOperating15MinAvgCPU: 0,
              jnxOperatingFRUPower: 0,
              jnxOperatingBufferCP: 0,
              jnxOperatingMemoryCP: 0,
              jnxOperatingBufferExt: 0,
            },
            fru: null,
            led: {
              jnxLEDOriginator: '*******.4.1.2636.1.1.3.2.12.4',
              jnxLEDDescr: ' Power Supply 0 LED',
              jnxLEDState: 'green',
              jnxLEDStateOrdered: 'green',
            },
          },
        ],
      },
      {
        jnxContainersIndex: 4,
        jnxContainersView: ['rear'],
        jnxContainersLevel: 2,
        jnxContainersWithin: 1,
        jnxContainersType: '*******.4.1.2636.1.1.3.2.132.1.2.0',
        jnxContainersDescr: 'Fan Tray slot',
        jnxContainersCount: 1,
        contents: [
          {
            detail: {
              jnxContentsL1Index: 1,
              jnxContentsL2Index: 1,
              jnxContentsL3Index: 0,
              jnxContentsType: '*******.4.1.2636.1.1.3.2.12.5',
              jnxContentsDescr: 'Fan Tray 0 @ 0/0/*',
              jnxContentsSerialNo: '',
              jnxContentsRevision: '',
              jnxContentsInstalled: '1970-01-01T00:00:00Z',
              jnxContentsPartNo: '',
              jnxContentsChassisId: 'singleChassis',
              jnxContentsChassisDescr: 'Single Chassis',
              jnxContentsChassisCleiCode: '',
              jnxContentsModel: '',
            },
            operating: {
              jnxOperatingContentsIndex: 4,
              jnxOperatingL1Index: 1,
              jnxOperatingL2Index: 1,
              jnxOperatingL3Index: 0,
              jnxOperatingDescr: 'Fan Tray 0 @ 0/0/*',
              jnxOperatingState: 'running',
              jnxOperatingTemp: 0,
              jnxOperatingCPU: 0,
              jnxOperatingISR: 0,
              jnxOperatingDRAMSize: 0,
              jnxOperatingBuffer: 0,
              jnxOperatingHeap: 0,
              jnxOperatingUpTime: '37 days, 23:31:03',
              jnxOperatingLastRestart: '1970-01-01T00:00:00Z',
              jnxOperatingMemory: 0,
              jnxOperatingStateOrdered: 'running',
              jnxOperatingChassisId: 'singleChassis',
              jnxOperatingChassisDescr: 'Single Chassis',
              jnxOperatingRestartTime: '2024-02-27T14:32:45Z',
              jnxOperating1MinLoadAvg: 0,
              jnxOperating5MinLoadAvg: 0,
              jnxOperating15MinLoadAvg: 0,
              jnxOperating1MinAvgCPU: 0,
              jnxOperating5MinAvgCPU: 0,
              jnxOperating15MinAvgCPU: 0,
              jnxOperatingFRUPower: 0,
              jnxOperatingBufferCP: 0,
              jnxOperatingMemoryCP: 0,
              jnxOperatingBufferExt: 0,
            },
            fru: null,
            led: null,
          },
        ],
      },
      {
        jnxContainersIndex: 7,
        jnxContainersView: ['front'],
        jnxContainersLevel: 1,
        jnxContainersWithin: 1,
        jnxContainersType: '*******.4.1.2636.1.1.3.2.132.1.0',
        jnxContainersDescr: 'FPC slot',
        jnxContainersCount: 1,
        contents: [
          {
            detail: {
              jnxContentsL1Index: 1,
              jnxContentsL2Index: 0,
              jnxContentsL3Index: 0,
              jnxContentsType: '*******.4.1.2636.1.1.3.2.12.1',
              jnxContentsDescr: 'FPC: EX2300-24T @ 0/*/*',
              jnxContentsSerialNo: 'JZ0220460459',
              jnxContentsRevision: 'REV 19',
              jnxContentsInstalled: '1970-01-01T00:00:00Z',
              jnxContentsPartNo: '650-059979',
              jnxContentsChassisId: 'singleChassis',
              jnxContentsChassisDescr: 'Single Chassis',
              jnxContentsChassisCleiCode: 'CMMU700ARB',
              jnxContentsModel: 'EX2300-24T',
            },
            operating: {
              jnxOperatingContentsIndex: 7,
              jnxOperatingL1Index: 1,
              jnxOperatingL2Index: 0,
              jnxOperatingL3Index: 0,
              jnxOperatingDescr: 'FPC: EX2300-24T @ 0/*/*',
              jnxOperatingState: 'running',
              jnxOperatingTemp: 44,
              jnxOperatingCPU: 74,
              jnxOperatingISR: 1,
              jnxOperatingDRAMSize: 2035286016,
              jnxOperatingBuffer: 48,
              jnxOperatingHeap: 13,
              jnxOperatingUpTime: '37 days, 23:25:56',
              jnxOperatingLastRestart: '1970-01-01T00:00:00Z',
              jnxOperatingMemory: 1941,
              jnxOperatingStateOrdered: 'running',
              jnxOperatingChassisId: 'singleChassis',
              jnxOperatingChassisDescr: 'Single Chassis',
              jnxOperatingRestartTime: '2024-02-27T14:37:52Z',
              jnxOperating1MinLoadAvg: 0,
              jnxOperating5MinLoadAvg: 0,
              jnxOperating15MinLoadAvg: 0,
              jnxOperating1MinAvgCPU: 67,
              jnxOperating5MinAvgCPU: 67,
              jnxOperating15MinAvgCPU: 67,
              jnxOperatingFRUPower: 0,
              jnxOperatingBufferCP: 0,
              jnxOperatingMemoryCP: 0,
              jnxOperatingBufferExt: 0,
            },
            fru: null,
            led: null,
          },
        ],
      },
      {
        jnxContainersIndex: 8,
        jnxContainersView: ['front', 'rear'],
        jnxContainersLevel: 2,
        jnxContainersWithin: 7,
        jnxContainersType: '*******.4.1.2636.1.1.2.3.132.1.0',
        jnxContainersDescr: 'PIC slot',
        jnxContainersCount: 2,
        contents: [
          {
            detail: {
              jnxContentsL1Index: 1,
              jnxContentsL2Index: 1,
              jnxContentsL3Index: 0,
              jnxContentsType: '*******.4.1.2636.*******.132.1',
              jnxContentsDescr: 'PIC: 24x10/100/1000 Base-T @ 0/0/*',
              jnxContentsSerialNo: 'BUILTIN',
              jnxContentsRevision: 'REV 19',
              jnxContentsInstalled: '2024-02-27T14:39:06.834139Z',
              jnxContentsPartNo: 'BUILTIN',
              jnxContentsChassisId: 'singleChassis',
              jnxContentsChassisDescr: 'Single Chassis',
              jnxContentsChassisCleiCode: 'CMMU700ARB',
              jnxContentsModel: 'EX2300-24T',
            },
            operating: {
              jnxOperatingContentsIndex: 8,
              jnxOperatingL1Index: 1,
              jnxOperatingL2Index: 1,
              jnxOperatingL3Index: 0,
              jnxOperatingDescr: 'PIC: 24x10/100/1000 Base-T @ 0/0/*',
              jnxOperatingState: 'running',
              jnxOperatingTemp: 0,
              jnxOperatingCPU: 0,
              jnxOperatingISR: 0,
              jnxOperatingDRAMSize: 0,
              jnxOperatingBuffer: 0,
              jnxOperatingHeap: 0,
              jnxOperatingUpTime: '37 days, 23:24:48',
              jnxOperatingLastRestart: '2024-02-27T14:39:06.834139Z',
              jnxOperatingMemory: 0,
              jnxOperatingStateOrdered: 'running',
              jnxOperatingChassisId: 'singleChassis',
              jnxOperatingChassisDescr: 'Single Chassis',
              jnxOperatingRestartTime: '2024-02-27T14:39:00Z',
              jnxOperating1MinLoadAvg: 0,
              jnxOperating5MinLoadAvg: 0,
              jnxOperating15MinLoadAvg: 0,
              jnxOperating1MinAvgCPU: 0,
              jnxOperating5MinAvgCPU: 0,
              jnxOperating15MinAvgCPU: 0,
              jnxOperatingFRUPower: 0,
              jnxOperatingBufferCP: 0,
              jnxOperatingMemoryCP: 0,
              jnxOperatingBufferExt: 0,
            },
            fru: null,
            led: null,
          },
          {
            detail: {
              jnxContentsL1Index: 1,
              jnxContentsL2Index: 2,
              jnxContentsL3Index: 0,
              jnxContentsType: '*******.4.1.2636.1.1.3.3.12.1.443',
              jnxContentsDescr: 'PIC: 4x10G SFP/SFP+ @ 0/1/*',
              jnxContentsSerialNo: 'JZ0220460459',
              jnxContentsRevision: 'REV 19',
              jnxContentsInstalled: '2024-02-27T14:39:06.834139Z',
              jnxContentsPartNo: '650-059979',
              jnxContentsChassisId: 'singleChassis',
              jnxContentsChassisDescr: 'Single Chassis',
              jnxContentsChassisCleiCode: 'CMMU700ARB',
              jnxContentsModel: 'EX2300-24T',
            },
            operating: {
              jnxOperatingContentsIndex: 8,
              jnxOperatingL1Index: 1,
              jnxOperatingL2Index: 2,
              jnxOperatingL3Index: 0,
              jnxOperatingDescr: 'PIC: 4x10G SFP/SFP+ @ 0/1/*',
              jnxOperatingState: 'running',
              jnxOperatingTemp: 0,
              jnxOperatingCPU: 0,
              jnxOperatingISR: 0,
              jnxOperatingDRAMSize: 0,
              jnxOperatingBuffer: 0,
              jnxOperatingHeap: 0,
              jnxOperatingUpTime: '37 days, 23:24:47',
              jnxOperatingLastRestart: '2024-02-27T14:39:06.834139Z',
              jnxOperatingMemory: 0,
              jnxOperatingStateOrdered: 'running',
              jnxOperatingChassisId: 'singleChassis',
              jnxOperatingChassisDescr: 'Single Chassis',
              jnxOperatingRestartTime: '2024-02-27T14:39:01Z',
              jnxOperating1MinLoadAvg: 0,
              jnxOperating5MinLoadAvg: 0,
              jnxOperating15MinLoadAvg: 0,
              jnxOperating1MinAvgCPU: 0,
              jnxOperating5MinAvgCPU: 0,
              jnxOperating15MinAvgCPU: 0,
              jnxOperatingFRUPower: 0,
              jnxOperatingBufferCP: 0,
              jnxOperatingMemoryCP: 0,
              jnxOperatingBufferExt: 0,
            },
            fru: null,
            led: null,
          },
        ],
      },
      {
        jnxContainersIndex: 9,
        jnxContainersView: ['rear'],
        jnxContainersLevel: 1,
        jnxContainersWithin: 1,
        jnxContainersType: '*******.4.1.2636.*******.132.1.0',
        jnxContainersDescr: 'Routing Engine slot',
        jnxContainersCount: 2,
        contents: [
          {
            detail: {
              jnxContentsL1Index: 1,
              jnxContentsL2Index: 0,
              jnxContentsL3Index: 0,
              jnxContentsType: '*******.4.1.2636.1.1.3.2.12.3',
              jnxContentsDescr: 'Routing Engine 0',
              jnxContentsSerialNo: 'BUILTIN',
              jnxContentsRevision: '',
              jnxContentsInstalled: '1970-01-01T00:00:00Z',
              jnxContentsPartNo: 'BUILTIN',
              jnxContentsChassisId: 'singleChassis',
              jnxContentsChassisDescr: 'Single Chassis',
              jnxContentsChassisCleiCode: 'CMMU700ARB',
              jnxContentsModel: 'EX2300-24T',
            },
            operating: {
              jnxOperatingContentsIndex: 9,
              jnxOperatingL1Index: 1,
              jnxOperatingL2Index: 0,
              jnxOperatingL3Index: 0,
              jnxOperatingDescr: 'Routing Engine 0',
              jnxOperatingState: 'running',
              jnxOperatingTemp: 44,
              jnxOperatingCPU: 76,
              jnxOperatingISR: 1,
              jnxOperatingDRAMSize: **********,
              jnxOperatingBuffer: 13,
              jnxOperatingHeap: 0,
              jnxOperatingUpTime: '37 days, 23:31:02',
              jnxOperatingLastRestart: '1970-01-01T00:00:00Z',
              jnxOperatingMemory: 1942,
              jnxOperatingStateOrdered: 'running',
              jnxOperatingChassisId: 'singleChassis',
              jnxOperatingChassisDescr: 'Single Chassis',
              jnxOperatingRestartTime: '2024-02-27T14:32:45Z',
              jnxOperating1MinLoadAvg: 197,
              jnxOperating5MinLoadAvg: 163,
              jnxOperating15MinLoadAvg: 151,
              jnxOperating1MinAvgCPU: 67,
              jnxOperating5MinAvgCPU: 66,
              jnxOperating15MinAvgCPU: 66,
              jnxOperatingFRUPower: 0,
              jnxOperatingBufferCP: 13,
              jnxOperatingMemoryCP: 1942,
              jnxOperatingBufferExt: 0,
            },
            fru: {
              jnxFruContentsIndex: 9,
              jnxFruL1Index: 1,
              jnxFruL2Index: 0,
              jnxFruL3Index: 0,
              jnxFruName: 'Routing Engine 0',
              jnxFruType: 'routingEngine',
              jnxFruSlot: 0,
              jnxFruState: 'online',
              jnxFruTemp: 44,
              jnxFruOfflineReason: 'none',
              jnxFruLastPowerOff: 0,
              jnxFruLastPowerOn: 0,
              jnxFruPowerUpTime: '37 days, 23:26:00',
              jnxFruChassisId: 'singleChassis',
              jnxFruChassisDescr: 'Single Chassis',
              jnxFruPsdAssignment: 0,
            },
            led: null,
          },
          {
            detail: null,
            operating: null,
            fru: null,
            led: null,
          },
        ],
      },
    ],
  },
};
export const SwitchNodeMockData = {
  id: 'GB-MARL-SW-0002',
  fibrolan_switch: null,
  juniper_switch: {
    box: {
      jnxBoxClass: '*******.4.1.2636.*******.132.0',
      jnxBoxDescr: 'Juniper EX2300-24T Switch',
      jnxBoxSerialNo: 'JZ0220460459',
      jnxBoxRevision: '',
      jnxBoxInstalled: '2022-12-02T13:19:42.803139Z',
      jnxLEDLastChange: 0,
      jnxBoxKernelMemoryUsedPercent: 0,
      jnxBoxSystemDomainType: 'notApplicable',
      jnxBoxPersonality: '0.0',
      containers: [
        {
          jnxContainersIndex: 1,
          jnxContainersView: ['front'],
          jnxContainersLevel: 0,
          jnxContainersWithin: 0,
          jnxContainersType: '*******.4.1.2636.*******.132.0',
          jnxContainersDescr: 'chassis frame',
          jnxContainersCount: 1,
          contents: [
            {
              detail: {
                jnxContentsL1Index: 1,
                jnxContentsL2Index: 0,
                jnxContentsL3Index: 0,
                jnxContentsType: '*******.4.1.2636.*******.132',
                jnxContentsDescr: '',
                jnxContentsSerialNo: 'JZ0220460459',
                jnxContentsRevision: '',
                jnxContentsInstalled: '1970-01-01T00:00:00Z',
                jnxContentsPartNo: '',
                jnxContentsChassisId: 'singleChassis',
                jnxContentsChassisDescr: 'Single Chassis',
                jnxContentsChassisCleiCode: '',
                jnxContentsModel: '',
              },
              operating: {
                jnxOperatingContentsIndex: 1,
                jnxOperatingL1Index: 1,
                jnxOperatingL2Index: 0,
                jnxOperatingL3Index: 0,
                jnxOperatingDescr: '',
                jnxOperatingState: 'running',
                jnxOperatingTemp: 0,
                jnxOperatingCPU: 0,
                jnxOperatingISR: 0,
                jnxOperatingDRAMSize: 0,
                jnxOperatingBuffer: 0,
                jnxOperatingHeap: 0,
                jnxOperatingUpTime: '45 days, 1:09:30',
                jnxOperatingLastRestart: '1970-01-01T00:00:00Z',
                jnxOperatingMemory: 0,
                jnxOperatingStateOrdered: 'running',
                jnxOperatingChassisId: 'singleChassis',
                jnxOperatingChassisDescr: 'Single Chassis',
                jnxOperatingRestartTime: '2024-02-27T14:32:45Z',
                jnxOperating1MinLoadAvg: 0,
                jnxOperating5MinLoadAvg: 0,
                jnxOperating15MinLoadAvg: 0,
                jnxOperating1MinAvgCPU: 0,
                jnxOperating5MinAvgCPU: 0,
                jnxOperating15MinAvgCPU: 0,
                jnxOperatingFRUPower: 0,
                jnxOperatingBufferCP: 0,
                jnxOperatingMemoryCP: 0,
                jnxOperatingBufferExt: 0,
              },
              fru: null,
              led: {
                jnxLEDOriginator: '*******.4.1.2636.*******.132',
                jnxLEDDescr: ' chassis alarm LED',
                jnxLEDState: 'other',
                jnxLEDStateOrdered: 'other',
              },
            },
          ],
        },
        {
          jnxContainersIndex: 2,
          jnxContainersView: ['rear'],
          jnxContainersLevel: 2,
          jnxContainersWithin: 1,
          jnxContainersType: '*******.4.1.2636.1.1.3.2.132.1.1.0',
          jnxContainersDescr: 'Power Supply slot',
          jnxContainersCount: 1,
          contents: [
            {
              detail: {
                jnxContentsL1Index: 1,
                jnxContentsL2Index: 1,
                jnxContentsL3Index: 0,
                jnxContentsType: '*******.4.1.2636.1.1.3.2.12.4',
                jnxContentsDescr: 'Power Supply 0 @ 0/0/*',
                jnxContentsSerialNo: '',
                jnxContentsRevision: '',
                jnxContentsInstalled: '1970-01-01T00:00:00Z',
                jnxContentsPartNo: '',
                jnxContentsChassisId: 'singleChassis',
                jnxContentsChassisDescr: 'Single Chassis',
                jnxContentsChassisCleiCode: '',
                jnxContentsModel: 'JPSU-65W-AC-AFO',
              },
              operating: {
                jnxOperatingContentsIndex: 2,
                jnxOperatingL1Index: 1,
                jnxOperatingL2Index: 1,
                jnxOperatingL3Index: 0,
                jnxOperatingDescr: 'Power Supply 0 @ 0/0/*',
                jnxOperatingState: 'running',
                jnxOperatingTemp: 0,
                jnxOperatingCPU: 0,
                jnxOperatingISR: 0,
                jnxOperatingDRAMSize: 0,
                jnxOperatingBuffer: 0,
                jnxOperatingHeap: 0,
                jnxOperatingUpTime: '45 days, 1:09:35',
                jnxOperatingLastRestart: '1970-01-01T00:00:00Z',
                jnxOperatingMemory: 0,
                jnxOperatingStateOrdered: 'running',
                jnxOperatingChassisId: 'singleChassis',
                jnxOperatingChassisDescr: 'Single Chassis',
                jnxOperatingRestartTime: '2024-02-27T14:32:45Z',
                jnxOperating1MinLoadAvg: 0,
                jnxOperating5MinLoadAvg: 0,
                jnxOperating15MinLoadAvg: 0,
                jnxOperating1MinAvgCPU: 0,
                jnxOperating5MinAvgCPU: 0,
                jnxOperating15MinAvgCPU: 0,
                jnxOperatingFRUPower: 0,
                jnxOperatingBufferCP: 0,
                jnxOperatingMemoryCP: 0,
                jnxOperatingBufferExt: 0,
              },
              fru: null,
              led: {
                jnxLEDOriginator: '*******.4.1.2636.1.1.3.2.12.4',
                jnxLEDDescr: ' Power Supply 0 LED',
                jnxLEDState: 'green',
                jnxLEDStateOrdered: 'green',
              },
            },
          ],
        },
        {
          jnxContainersIndex: 4,
          jnxContainersView: ['rear'],
          jnxContainersLevel: 2,
          jnxContainersWithin: 1,
          jnxContainersType: '*******.4.1.2636.1.1.3.2.132.1.2.0',
          jnxContainersDescr: 'Fan Tray slot',
          jnxContainersCount: 1,
          contents: [
            {
              detail: {
                jnxContentsL1Index: 1,
                jnxContentsL2Index: 1,
                jnxContentsL3Index: 0,
                jnxContentsType: '*******.4.1.2636.1.1.3.2.12.5',
                jnxContentsDescr: 'Fan Tray 0 @ 0/0/*',
                jnxContentsSerialNo: '',
                jnxContentsRevision: '',
                jnxContentsInstalled: '1970-01-01T00:00:00Z',
                jnxContentsPartNo: '',
                jnxContentsChassisId: 'singleChassis',
                jnxContentsChassisDescr: 'Single Chassis',
                jnxContentsChassisCleiCode: '',
                jnxContentsModel: '',
              },
              operating: {
                jnxOperatingContentsIndex: 4,
                jnxOperatingL1Index: 1,
                jnxOperatingL2Index: 1,
                jnxOperatingL3Index: 0,
                jnxOperatingDescr: 'Fan Tray 0 @ 0/0/*',
                jnxOperatingState: 'running',
                jnxOperatingTemp: 0,
                jnxOperatingCPU: 0,
                jnxOperatingISR: 0,
                jnxOperatingDRAMSize: 0,
                jnxOperatingBuffer: 0,
                jnxOperatingHeap: 0,
                jnxOperatingUpTime: '45 days, 1:09:35',
                jnxOperatingLastRestart: '1970-01-01T00:00:00Z',
                jnxOperatingMemory: 0,
                jnxOperatingStateOrdered: 'running',
                jnxOperatingChassisId: 'singleChassis',
                jnxOperatingChassisDescr: 'Single Chassis',
                jnxOperatingRestartTime: '2024-02-27T14:32:45Z',
                jnxOperating1MinLoadAvg: 0,
                jnxOperating5MinLoadAvg: 0,
                jnxOperating15MinLoadAvg: 0,
                jnxOperating1MinAvgCPU: 0,
                jnxOperating5MinAvgCPU: 0,
                jnxOperating15MinAvgCPU: 0,
                jnxOperatingFRUPower: 0,
                jnxOperatingBufferCP: 0,
                jnxOperatingMemoryCP: 0,
                jnxOperatingBufferExt: 0,
              },
              fru: null,
              led: null,
            },
          ],
        },
        {
          jnxContainersIndex: 7,
          jnxContainersView: ['front'],
          jnxContainersLevel: 1,
          jnxContainersWithin: 1,
          jnxContainersType: '*******.4.1.2636.1.1.3.2.132.1.0',
          jnxContainersDescr: 'FPC slot',
          jnxContainersCount: 1,
          contents: [
            {
              detail: {
                jnxContentsL1Index: 1,
                jnxContentsL2Index: 0,
                jnxContentsL3Index: 0,
                jnxContentsType: '*******.4.1.2636.1.1.3.2.12.1',
                jnxContentsDescr: 'FPC: EX2300-24T @ 0/*/*',
                jnxContentsSerialNo: 'JZ0220460459',
                jnxContentsRevision: 'REV 19',
                jnxContentsInstalled: '1970-01-01T00:00:00Z',
                jnxContentsPartNo: '650-059979',
                jnxContentsChassisId: 'singleChassis',
                jnxContentsChassisDescr: 'Single Chassis',
                jnxContentsChassisCleiCode: 'CMMU700ARB',
                jnxContentsModel: 'EX2300-24T',
              },
              operating: {
                jnxOperatingContentsIndex: 7,
                jnxOperatingL1Index: 1,
                jnxOperatingL2Index: 0,
                jnxOperatingL3Index: 0,
                jnxOperatingDescr: 'FPC: EX2300-24T @ 0/*/*',
                jnxOperatingState: 'running',
                jnxOperatingTemp: 45,
                jnxOperatingCPU: 71,
                jnxOperatingISR: 1,
                jnxOperatingDRAMSize: 2035286016,
                jnxOperatingBuffer: 48,
                jnxOperatingHeap: 13,
                jnxOperatingUpTime: '45 days, 1:04:27',
                jnxOperatingLastRestart: '1970-01-01T00:00:00Z',
                jnxOperatingMemory: 1941,
                jnxOperatingStateOrdered: 'running',
                jnxOperatingChassisId: 'singleChassis',
                jnxOperatingChassisDescr: 'Single Chassis',
                jnxOperatingRestartTime: '2024-02-27T14:37:52Z',
                jnxOperating1MinLoadAvg: 0,
                jnxOperating5MinLoadAvg: 0,
                jnxOperating15MinLoadAvg: 0,
                jnxOperating1MinAvgCPU: 68,
                jnxOperating5MinAvgCPU: 67,
                jnxOperating15MinAvgCPU: 67,
                jnxOperatingFRUPower: 0,
                jnxOperatingBufferCP: 0,
                jnxOperatingMemoryCP: 0,
                jnxOperatingBufferExt: 0,
              },
              fru: null,
              led: null,
            },
          ],
        },
        {
          jnxContainersIndex: 8,
          jnxContainersView: ['front', 'rear'],
          jnxContainersLevel: 2,
          jnxContainersWithin: 7,
          jnxContainersType: '*******.4.1.2636.1.1.2.3.132.1.0',
          jnxContainersDescr: 'PIC slot',
          jnxContainersCount: 2,
          contents: [
            {
              detail: {
                jnxContentsL1Index: 1,
                jnxContentsL2Index: 1,
                jnxContentsL3Index: 0,
                jnxContentsType: '*******.4.1.2636.*******.132.1',
                jnxContentsDescr: 'PIC: 24x10/100/1000 Base-T @ 0/0/*',
                jnxContentsSerialNo: 'BUILTIN',
                jnxContentsRevision: 'REV 19',
                jnxContentsInstalled: '2024-02-27T14:39:06.803139Z',
                jnxContentsPartNo: 'BUILTIN',
                jnxContentsChassisId: 'singleChassis',
                jnxContentsChassisDescr: 'Single Chassis',
                jnxContentsChassisCleiCode: 'CMMU700ARB',
                jnxContentsModel: 'EX2300-24T',
              },
              operating: {
                jnxOperatingContentsIndex: 8,
                jnxOperatingL1Index: 1,
                jnxOperatingL2Index: 1,
                jnxOperatingL3Index: 0,
                jnxOperatingDescr: 'PIC: 24x10/100/1000 Base-T @ 0/0/*',
                jnxOperatingState: 'running',
                jnxOperatingTemp: 0,
                jnxOperatingCPU: 0,
                jnxOperatingISR: 0,
                jnxOperatingDRAMSize: 0,
                jnxOperatingBuffer: 0,
                jnxOperatingHeap: 0,
                jnxOperatingUpTime: '45 days, 1:03:19',
                jnxOperatingLastRestart: '2024-02-27T14:39:06.803139Z',
                jnxOperatingMemory: 0,
                jnxOperatingStateOrdered: 'running',
                jnxOperatingChassisId: 'singleChassis',
                jnxOperatingChassisDescr: 'Single Chassis',
                jnxOperatingRestartTime: '2024-02-27T14:39:00Z',
                jnxOperating1MinLoadAvg: 0,
                jnxOperating5MinLoadAvg: 0,
                jnxOperating15MinLoadAvg: 0,
                jnxOperating1MinAvgCPU: 0,
                jnxOperating5MinAvgCPU: 0,
                jnxOperating15MinAvgCPU: 0,
                jnxOperatingFRUPower: 0,
                jnxOperatingBufferCP: 0,
                jnxOperatingMemoryCP: 0,
                jnxOperatingBufferExt: 0,
              },
              fru: null,
              led: null,
            },
            {
              detail: {
                jnxContentsL1Index: 1,
                jnxContentsL2Index: 2,
                jnxContentsL3Index: 0,
                jnxContentsType: '*******.4.1.2636.1.1.3.3.12.1.443',
                jnxContentsDescr: 'PIC: 4x10G SFP/SFP+ @ 0/1/*',
                jnxContentsSerialNo: 'JZ0220460459',
                jnxContentsRevision: 'REV 19',
                jnxContentsInstalled: '2024-02-27T14:39:06.803139Z',
                jnxContentsPartNo: '650-059979',
                jnxContentsChassisId: 'singleChassis',
                jnxContentsChassisDescr: 'Single Chassis',
                jnxContentsChassisCleiCode: 'CMMU700ARB',
                jnxContentsModel: 'EX2300-24T',
              },
              operating: {
                jnxOperatingContentsIndex: 8,
                jnxOperatingL1Index: 1,
                jnxOperatingL2Index: 2,
                jnxOperatingL3Index: 0,
                jnxOperatingDescr: 'PIC: 4x10G SFP/SFP+ @ 0/1/*',
                jnxOperatingState: 'running',
                jnxOperatingTemp: 0,
                jnxOperatingCPU: 0,
                jnxOperatingISR: 0,
                jnxOperatingDRAMSize: 0,
                jnxOperatingBuffer: 0,
                jnxOperatingHeap: 0,
                jnxOperatingUpTime: '45 days, 1:03:19',
                jnxOperatingLastRestart: '2024-02-27T14:39:06.803139Z',
                jnxOperatingMemory: 0,
                jnxOperatingStateOrdered: 'running',
                jnxOperatingChassisId: 'singleChassis',
                jnxOperatingChassisDescr: 'Single Chassis',
                jnxOperatingRestartTime: '2024-02-27T14:39:01Z',
                jnxOperating1MinLoadAvg: 0,
                jnxOperating5MinLoadAvg: 0,
                jnxOperating15MinLoadAvg: 0,
                jnxOperating1MinAvgCPU: 0,
                jnxOperating5MinAvgCPU: 0,
                jnxOperating15MinAvgCPU: 0,
                jnxOperatingFRUPower: 0,
                jnxOperatingBufferCP: 0,
                jnxOperatingMemoryCP: 0,
                jnxOperatingBufferExt: 0,
              },
              fru: null,
              led: null,
            },
          ],
        },
        {
          jnxContainersIndex: 9,
          jnxContainersView: ['rear'],
          jnxContainersLevel: 1,
          jnxContainersWithin: 1,
          jnxContainersType: '*******.4.1.2636.*******.132.1.0',
          jnxContainersDescr: 'Routing Engine slot',
          jnxContainersCount: 2,
          contents: [
            {
              detail: {
                jnxContentsL1Index: 1,
                jnxContentsL2Index: 0,
                jnxContentsL3Index: 0,
                jnxContentsType: '*******.4.1.2636.1.1.3.2.12.3',
                jnxContentsDescr: 'Routing Engine 0',
                jnxContentsSerialNo: 'BUILTIN',
                jnxContentsRevision: '',
                jnxContentsInstalled: '1970-01-01T00:00:00Z',
                jnxContentsPartNo: 'BUILTIN',
                jnxContentsChassisId: 'singleChassis',
                jnxContentsChassisDescr: 'Single Chassis',
                jnxContentsChassisCleiCode: 'CMMU700ARB',
                jnxContentsModel: 'EX2300-24T',
              },
              operating: {
                jnxOperatingContentsIndex: 9,
                jnxOperatingL1Index: 1,
                jnxOperatingL2Index: 0,
                jnxOperatingL3Index: 0,
                jnxOperatingDescr: 'Routing Engine 0',
                jnxOperatingState: 'running',
                jnxOperatingTemp: 45,
                jnxOperatingCPU: 67,
                jnxOperatingISR: 1,
                jnxOperatingDRAMSize: **********,
                jnxOperatingBuffer: 13,
                jnxOperatingHeap: 0,
                jnxOperatingUpTime: '45 days, 1:09:30',
                jnxOperatingLastRestart: '1970-01-01T00:00:00Z',
                jnxOperatingMemory: 1942,
                jnxOperatingStateOrdered: 'running',
                jnxOperatingChassisId: 'singleChassis',
                jnxOperatingChassisDescr: 'Single Chassis',
                jnxOperatingRestartTime: '2024-02-27T14:32:45Z',
                jnxOperating1MinLoadAvg: 160,
                jnxOperating5MinLoadAvg: 161,
                jnxOperating15MinLoadAvg: 157,
                jnxOperating1MinAvgCPU: 68,
                jnxOperating5MinAvgCPU: 67,
                jnxOperating15MinAvgCPU: 67,
                jnxOperatingFRUPower: 0,
                jnxOperatingBufferCP: 13,
                jnxOperatingMemoryCP: 1942,
                jnxOperatingBufferExt: 0,
              },
              fru: {
                jnxFruContentsIndex: 9,
                jnxFruL1Index: 1,
                jnxFruL2Index: 0,
                jnxFruL3Index: 0,
                jnxFruName: 'Routing Engine 0',
                jnxFruType: 'routingEngine',
                jnxFruSlot: 0,
                jnxFruState: 'online',
                jnxFruTemp: 45,
                jnxFruOfflineReason: 'none',
                jnxFruLastPowerOff: 0,
                jnxFruLastPowerOn: 0,
                jnxFruPowerUpTime: '45 days, 1:04:32',
                jnxFruChassisId: 'singleChassis',
                jnxFruChassisDescr: 'Single Chassis',
                jnxFruPsdAssignment: 0,
              },
              led: null,
            },
            { detail: null, operating: null, fru: null, led: null },
          ],
        },
      ],
    },
    interfaces: [
      {
        ifIndex: 4,
        ifDescr: 'lsi',
        ifType: 'mplsTunnel',
        ifMtu: **********,
        ifSpeed: 0,
        ifPhysAddress: '',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 2022,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 5,
        ifDescr: 'dsc',
        ifType: 'other',
        ifMtu: **********,
        ifSpeed: 0,
        ifPhysAddress: '',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 2049,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 6,
        ifDescr: 'lo0',
        ifType: 'softwareLoopback',
        ifMtu: **********,
        ifSpeed: 0,
        ifPhysAddress: '',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 2050,
        ifInOctets: 304047762,
        ifInUcastPkts: 191178849,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 304250746,
        ifOutUcastPkts: 191179312,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.204,
        ifOut1SecRate: 0.205,
      },
      {
        ifIndex: 7,
        ifDescr: 'tap',
        ifType: 'other',
        ifMtu: **********,
        ifSpeed: 0,
        ifPhysAddress: '',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 2051,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 8,
        ifDescr: 'gre',
        ifType: 'tunnel',
        ifMtu: **********,
        ifSpeed: 0,
        ifPhysAddress: '',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 2050,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 9,
        ifDescr: 'ipip',
        ifType: 'tunnel',
        ifMtu: **********,
        ifSpeed: 0,
        ifPhysAddress: '',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 2050,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 10,
        ifDescr: 'pime',
        ifType: 'tunnel',
        ifMtu: **********,
        ifSpeed: 0,
        ifPhysAddress: '',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 2051,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 11,
        ifDescr: 'pimd',
        ifType: 'tunnel',
        ifMtu: **********,
        ifSpeed: 0,
        ifPhysAddress: '',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 2051,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 12,
        ifDescr: 'mtun',
        ifType: 'tunnel',
        ifMtu: **********,
        ifSpeed: 0,
        ifPhysAddress: '',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 2051,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 16,
        ifDescr: 'lo0.0',
        ifType: 'softwareLoopback',
        ifMtu: **********,
        ifSpeed: 0,
        ifPhysAddress: '',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 2050,
        ifInOctets: 21168,
        ifInUcastPkts: 122,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 21168,
        ifOutUcastPkts: 122,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 22,
        ifDescr: 'lo0.16385',
        ifType: 'softwareLoopback',
        ifMtu: **********,
        ifSpeed: 0,
        ifPhysAddress: '',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 2050,
        ifInOctets: 303687729,
        ifInUcastPkts: 191172348,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 303890897,
        ifOutUcastPkts: 191172815,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.205,
        ifOut1SecRate: 0.205,
      },
      {
        ifIndex: 33,
        ifDescr: 'me0',
        ifType: 'ethernetCsmacd',
        ifMtu: 1514,
        ifSpeed: 0,
        ifPhysAddress: '00:c5:2c:f6:b8:ab',
        ifAdminStatus: 'up',
        ifOperStatus: 'down',
        ifLastChange: 2051,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 34,
        ifDescr: 'me0.0',
        ifType: 'propVirtual',
        ifMtu: 1514,
        ifSpeed: 0,
        ifPhysAddress: '00:c5:2c:f6:b8:ab',
        ifAdminStatus: 'up',
        ifOperStatus: 'lowerLayerDown',
        ifLastChange: 2051,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 35,
        ifDescr: 'vme',
        ifType: 'other',
        ifMtu: 1514,
        ifSpeed: 0,
        ifPhysAddress: '00:c5:2c:f6:b8:ab',
        ifAdminStatus: 'up',
        ifOperStatus: 'down',
        ifLastChange: 2052,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 37,
        ifDescr: 'bme0',
        ifType: 'ethernetCsmacd',
        ifMtu: 9512,
        ifSpeed: 0,
        ifPhysAddress: '02:00:00:00:00:0a',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 2044,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 2231685073,
        ifOutUcastPkts: 10464947,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.045,
      },
      {
        ifIndex: 220,
        ifDescr: 'bme0.0',
        ifType: 'propVirtual',
        ifMtu: 9498,
        ifSpeed: 0,
        ifPhysAddress: '02:00:00:00:00:0a',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 2046,
        ifInOctets: 823250841,
        ifInUcastPkts: 215259544,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 168,
        ifOutUcastPkts: 4,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.077,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 501,
        ifDescr: 'cbp0',
        ifType: 'other',
        ifMtu: 9192,
        ifSpeed: 0,
        ifPhysAddress: '00:00:00:00:00:14',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 2048,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 502,
        ifDescr: 'esi',
        ifType: 'other',
        ifMtu: **********,
        ifSpeed: 0,
        ifPhysAddress: '',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 2050,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 503,
        ifDescr: 'irb',
        ifType: 'other',
        ifMtu: 1514,
        ifSpeed: 0,
        ifPhysAddress: '00:c5:2c:f6:b8:aa',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 2050,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 504,
        ifDescr: 'ge-0/0/2.0',
        ifType: 'propVirtual',
        ifMtu: 1514,
        ifSpeed: 1000000000,
        ifPhysAddress: '00:c5:2c:f6:b8:af',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 379918031,
        ifInOctets: 5830352,
        ifInUcastPkts: 71924,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 55772001,
        ifOutUcastPkts: 140130,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 505,
        ifDescr: 'jsrv',
        ifType: 'other',
        ifMtu: 1514,
        ifSpeed: 0,
        ifPhysAddress: '00:c5:2c:f6:b8:aa',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 2144,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 506,
        ifDescr: 'jsrv.1',
        ifType: 'propVirtual',
        ifMtu: 1514,
        ifSpeed: 1000000000,
        ifPhysAddress: '00:c5:2c:f6:b8:aa',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 2163,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 507,
        ifDescr: 'pip0',
        ifType: 'other',
        ifMtu: 9192,
        ifSpeed: 0,
        ifPhysAddress: '00:c5:2c:f6:b8:bb',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 2051,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 508,
        ifDescr: 'vtep',
        ifType: 'other',
        ifMtu: **********,
        ifSpeed: 0,
        ifPhysAddress: '',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 2052,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 509,
        ifDescr: 'pfh-0/0/0',
        ifType: 'other',
        ifMtu: 0,
        ifSpeed: 800000000,
        ifPhysAddress: '',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 5265,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 510,
        ifDescr: 'pfe-0/0/0',
        ifType: 'other',
        ifMtu: 0,
        ifSpeed: 800000000,
        ifPhysAddress: '',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 5265,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 511,
        ifDescr: 'pfe-0/0/0.16383',
        ifType: 'propVirtual',
        ifMtu: **********,
        ifSpeed: 0,
        ifPhysAddress: '',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 5283,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 512,
        ifDescr: 'pfh-0/0/0.16383',
        ifType: 'propVirtual',
        ifMtu: **********,
        ifSpeed: 0,
        ifPhysAddress: '',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 5277,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 513,
        ifDescr: 'pfh-0/0/0.16384',
        ifType: 'propVirtual',
        ifMtu: **********,
        ifSpeed: 0,
        ifPhysAddress: '',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 5277,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 514,
        ifDescr: 'ge-0/0/0',
        ifType: 'ethernetCsmacd',
        ifMtu: 1514,
        ifSpeed: 1000000000,
        ifPhysAddress: '00:c5:2c:f6:b8:ad',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 369457847,
        ifInOctets: 1214854239,
        ifInUcastPkts: 34240821,
        ifInNUcastPkts: 120438423,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 4106332307,
        ifOutUcastPkts: 74528681,
        ifOutNUcastPkts: 6798002,
        ifOutDiscards: 62,
        ifOutErrors: 0,
        ifIn1SecRate: 0.027,
        ifOut1SecRate: 0.007,
      },
      {
        ifIndex: 515,
        ifDescr: 'ge-0/0/1',
        ifType: 'ethernetCsmacd',
        ifMtu: 1514,
        ifSpeed: 1000000000,
        ifPhysAddress: '00:c5:2c:f6:b8:ae',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 234254982,
        ifInOctets: 4285413025,
        ifInUcastPkts: 76623481,
        ifInNUcastPkts: 2024479,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 3751831685,
        ifOutUcastPkts: 60907528,
        ifOutNUcastPkts: 20202563,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.018,
        ifOut1SecRate: 0.029,
      },
      {
        ifIndex: 516,
        ifDescr: 'ge-0/0/2',
        ifType: 'ethernetCsmacd',
        ifMtu: 1514,
        ifSpeed: 1000000000,
        ifPhysAddress: '00:c5:2c:f6:b8:af',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 379918031,
        ifInOctets: 1859468208,
        ifInUcastPkts: 25901670,
        ifInNUcastPkts: 134348,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 1527853002,
        ifOutUcastPkts: 39257607,
        ifOutNUcastPkts: 25937261,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.008,
        ifOut1SecRate: 0.01,
      },
      {
        ifIndex: 517,
        ifDescr: 'ge-0/0/3',
        ifType: 'ethernetCsmacd',
        ifMtu: 1514,
        ifSpeed: 0,
        ifPhysAddress: '00:c5:2c:f6:b8:b0',
        ifAdminStatus: 'up',
        ifOperStatus: 'down',
        ifLastChange: 6275,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 518,
        ifDescr: 'ge-0/0/4',
        ifType: 'ethernetCsmacd',
        ifMtu: 1514,
        ifSpeed: 1000000000,
        ifPhysAddress: '00:c5:2c:f6:b8:b1',
        ifAdminStatus: 'up',
        ifOperStatus: 'down',
        ifLastChange: 612006,
        ifInOctets: 88462708,
        ifInUcastPkts: 824546,
        ifInNUcastPkts: 2,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 893964340,
        ifOutUcastPkts: 24451731,
        ifOutNUcastPkts: 38559,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 519,
        ifDescr: 'ge-0/0/5',
        ifType: 'ethernetCsmacd',
        ifMtu: 1514,
        ifSpeed: 0,
        ifPhysAddress: '00:c5:2c:f6:b8:b2',
        ifAdminStatus: 'up',
        ifOperStatus: 'down',
        ifLastChange: 6288,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 520,
        ifDescr: 'ge-0/0/6',
        ifType: 'ethernetCsmacd',
        ifMtu: 1514,
        ifSpeed: 0,
        ifPhysAddress: '00:c5:2c:f6:b8:b3',
        ifAdminStatus: 'up',
        ifOperStatus: 'down',
        ifLastChange: 6292,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 521,
        ifDescr: 'ge-0/0/7',
        ifType: 'ethernetCsmacd',
        ifMtu: 1514,
        ifSpeed: 0,
        ifPhysAddress: '00:c5:2c:f6:b8:b4',
        ifAdminStatus: 'up',
        ifOperStatus: 'down',
        ifLastChange: 6312,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 522,
        ifDescr: 'ge-0/0/8',
        ifType: 'ethernetCsmacd',
        ifMtu: 1514,
        ifSpeed: 1000000000,
        ifPhysAddress: '00:c5:2c:f6:b8:b5',
        ifAdminStatus: 'up',
        ifOperStatus: 'down',
        ifLastChange: 862158,
        ifInOctets: 139782,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 2179,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 6573946,
        ifOutUcastPkts: 188,
        ifOutNUcastPkts: 86959,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 523,
        ifDescr: 'ge-0/0/9',
        ifType: 'ethernetCsmacd',
        ifMtu: 1514,
        ifSpeed: 0,
        ifPhysAddress: '00:c5:2c:f6:b8:b6',
        ifAdminStatus: 'up',
        ifOperStatus: 'down',
        ifLastChange: 6319,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 524,
        ifDescr: 'ge-0/0/10',
        ifType: 'ethernetCsmacd',
        ifMtu: 1514,
        ifSpeed: 1000000000,
        ifPhysAddress: '00:c5:2c:f6:b8:b7',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 379918373,
        ifInOctets: 266162016,
        ifInUcastPkts: 40340684,
        ifInNUcastPkts: 363,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 534843892,
        ifOutUcastPkts: 135768508,
        ifOutNUcastPkts: 25923598,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.008,
        ifOut1SecRate: 0.01,
      },
      {
        ifIndex: 525,
        ifDescr: 'ge-0/0/11',
        ifType: 'ethernetCsmacd',
        ifMtu: 1514,
        ifSpeed: 1000000000,
        ifPhysAddress: '00:c5:2c:f6:b8:b8',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 380862285,
        ifInOctets: 209802245,
        ifInUcastPkts: 1348912,
        ifInNUcastPkts: 291143,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 2431792709,
        ifOutUcastPkts: 1511072,
        ifOutNUcastPkts: 25681606,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.004,
      },
      {
        ifIndex: 526,
        ifDescr: 'ge-0/0/12',
        ifType: 'ethernetCsmacd',
        ifMtu: 1514,
        ifSpeed: 0,
        ifPhysAddress: '00:c5:2c:f6:b8:b9',
        ifAdminStatus: 'up',
        ifOperStatus: 'down',
        ifLastChange: 6335,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 527,
        ifDescr: 'ge-0/0/13',
        ifType: 'ethernetCsmacd',
        ifMtu: 1514,
        ifSpeed: 0,
        ifPhysAddress: '00:c5:2c:f6:b8:ba',
        ifAdminStatus: 'up',
        ifOperStatus: 'down',
        ifLastChange: 6339,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 528,
        ifDescr: 'ge-0/0/14',
        ifType: 'ethernetCsmacd',
        ifMtu: 1514,
        ifSpeed: 1000000000,
        ifPhysAddress: '00:c5:2c:f6:b8:bb',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 380066181,
        ifInOctets: 1274832542,
        ifInUcastPkts: 25190051,
        ifInNUcastPkts: 7280,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 3538678991,
        ifOutUcastPkts: 46872799,
        ifOutNUcastPkts: 22556945,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.009,
        ifOut1SecRate: 0.01,
      },
      {
        ifIndex: 529,
        ifDescr: 'ge-0/0/15',
        ifType: 'ethernetCsmacd',
        ifMtu: 1514,
        ifSpeed: 0,
        ifPhysAddress: '00:c5:2c:f6:b8:bc',
        ifAdminStatus: 'up',
        ifOperStatus: 'down',
        ifLastChange: 6800,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 530,
        ifDescr: 'ge-0/0/16',
        ifType: 'ethernetCsmacd',
        ifMtu: 1514,
        ifSpeed: 1000000000,
        ifPhysAddress: '00:c5:2c:f6:b8:bd',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 380066718,
        ifInOctets: 1059273360,
        ifInUcastPkts: 21903238,
        ifInNUcastPkts: 3230,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 495967637,
        ifOutUcastPkts: 39313328,
        ifOutNUcastPkts: 22549286,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.008,
        ifOut1SecRate: 0.008,
      },
      {
        ifIndex: 531,
        ifDescr: 'ge-0/0/17',
        ifType: 'ethernetCsmacd',
        ifMtu: 1514,
        ifSpeed: 0,
        ifPhysAddress: '00:c5:2c:f6:b8:be',
        ifAdminStatus: 'up',
        ifOperStatus: 'down',
        ifLastChange: 6811,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 532,
        ifDescr: 'ge-0/0/18',
        ifType: 'ethernetCsmacd',
        ifMtu: 1514,
        ifSpeed: 0,
        ifPhysAddress: '00:c5:2c:f6:b8:bf',
        ifAdminStatus: 'up',
        ifOperStatus: 'down',
        ifLastChange: 6813,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 533,
        ifDescr: 'ge-0/0/19',
        ifType: 'ethernetCsmacd',
        ifMtu: 1514,
        ifSpeed: 0,
        ifPhysAddress: '00:c5:2c:f6:b8:c0',
        ifAdminStatus: 'up',
        ifOperStatus: 'down',
        ifLastChange: 6815,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 534,
        ifDescr: 'ge-0/0/20',
        ifType: 'ethernetCsmacd',
        ifMtu: 1514,
        ifSpeed: 0,
        ifPhysAddress: '00:c5:2c:f6:b8:c1',
        ifAdminStatus: 'up',
        ifOperStatus: 'down',
        ifLastChange: 6819,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 535,
        ifDescr: 'ge-0/0/21',
        ifType: 'ethernetCsmacd',
        ifMtu: 1514,
        ifSpeed: 0,
        ifPhysAddress: '00:c5:2c:f6:b8:c2',
        ifAdminStatus: 'up',
        ifOperStatus: 'down',
        ifLastChange: 6821,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 536,
        ifDescr: 'ge-0/0/22',
        ifType: 'ethernetCsmacd',
        ifMtu: 1514,
        ifSpeed: 1000000000,
        ifPhysAddress: '00:c5:2c:f6:b8:c3',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 376195,
        ifInOctets: 4248023540,
        ifInUcastPkts: 250787422,
        ifInNUcastPkts: 22401937,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 769207797,
        ifOutUcastPkts: 103075774,
        ifOutNUcastPkts: 3729135,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.027,
        ifOut1SecRate: 0.072,
      },
      {
        ifIndex: 537,
        ifDescr: 'ge-0/0/23',
        ifType: 'ethernetCsmacd',
        ifMtu: 1514,
        ifSpeed: 1000000000,
        ifPhysAddress: '00:c5:2c:f6:b8:c4',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 369457858,
        ifInOctets: 2432978626,
        ifInUcastPkts: 84955392,
        ifInNUcastPkts: 88858905,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 4248344276,
        ifOutUcastPkts: 35949649,
        ifOutNUcastPkts: 20404229,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.027,
        ifOut1SecRate: 0.009,
      },
      {
        ifIndex: 538,
        ifDescr: 'ge-0/0/0.0',
        ifType: 'propVirtual',
        ifMtu: 1514,
        ifSpeed: 1000000000,
        ifPhysAddress: '00:c5:2c:f6:b8:ad',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 369457847,
        ifInOctets: 62592740,
        ifInUcastPkts: 99999897,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 59003931,
        ifOutUcastPkts: 142861,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.016,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 539,
        ifDescr: 'ge-0/0/4.0',
        ifType: 'propVirtual',
        ifMtu: 1514,
        ifSpeed: 1000000000,
        ifPhysAddress: '00:c5:2c:f6:b8:b1',
        ifAdminStatus: 'up',
        ifOperStatus: 'lowerLayerDown',
        ifLastChange: 612006,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 21600,
        ifOutUcastPkts: 54,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 540,
        ifDescr: 'ge-0/0/6.0',
        ifType: 'propVirtual',
        ifMtu: 1514,
        ifSpeed: 0,
        ifPhysAddress: '00:c5:2c:f6:b8:b3',
        ifAdminStatus: 'up',
        ifOperStatus: 'lowerLayerDown',
        ifLastChange: 306623,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 541,
        ifDescr: 'ge-0/0/8.0',
        ifType: 'propVirtual',
        ifMtu: 1514,
        ifSpeed: 1000000000,
        ifPhysAddress: '00:c5:2c:f6:b8:b5',
        ifAdminStatus: 'up',
        ifOperStatus: 'lowerLayerDown',
        ifLastChange: 862158,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 58800,
        ifOutUcastPkts: 147,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 542,
        ifDescr: 'ge-0/0/1.0',
        ifType: 'propVirtual',
        ifMtu: 1514,
        ifSpeed: 1000000000,
        ifPhysAddress: '00:c5:2c:f6:b8:ae',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 234254982,
        ifInOctets: 45496542,
        ifInUcastPkts: 121853,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 49593994,
        ifOutUcastPkts: 121944,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 543,
        ifDescr: 'ge-0/0/3.0',
        ifType: 'propVirtual',
        ifMtu: 1514,
        ifSpeed: 0,
        ifPhysAddress: '00:c5:2c:f6:b8:b0',
        ifAdminStatus: 'up',
        ifOperStatus: 'lowerLayerDown',
        ifLastChange: 438174,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 544,
        ifDescr: 'ge-0/0/22.0',
        ifType: 'propVirtual',
        ifMtu: 1514,
        ifSpeed: 1000000000,
        ifPhysAddress: '00:c5:2c:f6:b8:c3',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 376195,
        ifInOctets: 1384104344,
        ifInUcastPkts: 19876501,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 1215210479,
        ifOutUcastPkts: 9359121,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.002,
        ifOut1SecRate: 0.041,
      },
      {
        ifIndex: 545,
        ifDescr: 'ge-0/0/23.0',
        ifType: 'propVirtual',
        ifMtu: 1514,
        ifSpeed: 1000000000,
        ifPhysAddress: '00:c5:2c:f6:b8:c4',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 369457858,
        ifInOctets: 3201819642,
        ifInUcastPkts: 86069657,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 56289526,
        ifOutUcastPkts: 140024,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.016,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 546,
        ifDescr: 'ge-0/0/10.0',
        ifType: 'propVirtual',
        ifMtu: 1514,
        ifSpeed: 1000000000,
        ifPhysAddress: '00:c5:2c:f6:b8:b7',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 379918373,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 55871220,
        ifOutUcastPkts: 140028,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 547,
        ifDescr: 'ge-0/0/11.0',
        ifType: 'propVirtual',
        ifMtu: 1514,
        ifSpeed: 1000000000,
        ifPhysAddress: '00:c5:2c:f6:b8:b8',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 380862285,
        ifInOctets: 248280,
        ifInUcastPkts: 4138,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 55892473,
        ifOutUcastPkts: 140081,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 548,
        ifDescr: 'ge-0/0/5.0',
        ifType: 'propVirtual',
        ifMtu: 1514,
        ifSpeed: 0,
        ifPhysAddress: '00:c5:2c:f6:b8:b2',
        ifAdminStatus: 'up',
        ifOperStatus: 'lowerLayerDown',
        ifLastChange: 441625,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 549,
        ifDescr: 'ge-0/0/7.0',
        ifType: 'propVirtual',
        ifMtu: 1514,
        ifSpeed: 0,
        ifPhysAddress: '00:c5:2c:f6:b8:b4',
        ifAdminStatus: 'up',
        ifOperStatus: 'lowerLayerDown',
        ifLastChange: 441627,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 550,
        ifDescr: 'ge-0/0/9.0',
        ifType: 'propVirtual',
        ifMtu: 1514,
        ifSpeed: 0,
        ifPhysAddress: '00:c5:2c:f6:b8:b6',
        ifAdminStatus: 'up',
        ifOperStatus: 'lowerLayerDown',
        ifLastChange: 441629,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 551,
        ifDescr: 'ge-0/0/12.0',
        ifType: 'propVirtual',
        ifMtu: 1514,
        ifSpeed: 0,
        ifPhysAddress: '00:c5:2c:f6:b8:b9',
        ifAdminStatus: 'up',
        ifOperStatus: 'lowerLayerDown',
        ifLastChange: 570342,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 552,
        ifDescr: 'ge-0/0/13.0',
        ifType: 'propVirtual',
        ifMtu: 1514,
        ifSpeed: 0,
        ifPhysAddress: '00:c5:2c:f6:b8:ba',
        ifAdminStatus: 'up',
        ifOperStatus: 'lowerLayerDown',
        ifLastChange: 573643,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 553,
        ifDescr: 'ge-0/0/14.0',
        ifType: 'propVirtual',
        ifMtu: 1514,
        ifSpeed: 1000000000,
        ifPhysAddress: '00:c5:2c:f6:b8:bb',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 380066181,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 56131769,
        ifOutUcastPkts: 140328,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 554,
        ifDescr: 'ge-0/0/15.0',
        ifType: 'propVirtual',
        ifMtu: 1514,
        ifSpeed: 0,
        ifPhysAddress: '00:c5:2c:f6:b8:bc',
        ifAdminStatus: 'up',
        ifOperStatus: 'lowerLayerDown',
        ifLastChange: 573647,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 555,
        ifDescr: 'ge-0/0/16.0',
        ifType: 'propVirtual',
        ifMtu: 1514,
        ifSpeed: 1000000000,
        ifPhysAddress: '00:c5:2c:f6:b8:bd',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 380066718,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 56064831,
        ifOutUcastPkts: 140161,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 556,
        ifDescr: 'ge-0/0/17.0',
        ifType: 'propVirtual',
        ifMtu: 1514,
        ifSpeed: 0,
        ifPhysAddress: '00:c5:2c:f6:b8:be',
        ifAdminStatus: 'up',
        ifOperStatus: 'lowerLayerDown',
        ifLastChange: 573651,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 557,
        ifDescr: 'ge-0/0/18.0',
        ifType: 'propVirtual',
        ifMtu: 1514,
        ifSpeed: 0,
        ifPhysAddress: '00:c5:2c:f6:b8:bf',
        ifAdminStatus: 'up',
        ifOperStatus: 'lowerLayerDown',
        ifLastChange: 573653,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 558,
        ifDescr: 'ge-0/0/19.0',
        ifType: 'propVirtual',
        ifMtu: 1514,
        ifSpeed: 0,
        ifPhysAddress: '00:c5:2c:f6:b8:c0',
        ifAdminStatus: 'up',
        ifOperStatus: 'lowerLayerDown',
        ifLastChange: 573655,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 559,
        ifDescr: 'ge-0/0/20.0',
        ifType: 'propVirtual',
        ifMtu: 1514,
        ifSpeed: 0,
        ifPhysAddress: '00:c5:2c:f6:b8:c1',
        ifAdminStatus: 'up',
        ifOperStatus: 'lowerLayerDown',
        ifLastChange: 573657,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 560,
        ifDescr: 'ge-0/0/21.0',
        ifType: 'propVirtual',
        ifMtu: 1514,
        ifSpeed: 0,
        ifPhysAddress: '00:c5:2c:f6:b8:c2',
        ifAdminStatus: 'up',
        ifOperStatus: 'lowerLayerDown',
        ifLastChange: 573659,
        ifInOctets: 0,
        ifInUcastPkts: 0,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 0,
        ifOutUcastPkts: 0,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.0,
        ifOut1SecRate: 0.0,
      },
      {
        ifIndex: 603,
        ifDescr: 'irb.9',
        ifType: 'propVirtual',
        ifMtu: 1500,
        ifSpeed: 1000000000,
        ifPhysAddress: '00:c5:2c:f6:b8:aa',
        ifAdminStatus: 'up',
        ifOperStatus: 'up',
        ifLastChange: 50802565,
        ifInOctets: 1606781863,
        ifInUcastPkts: 9115243,
        ifInNUcastPkts: 0,
        ifInDiscards: 0,
        ifInErrors: 0,
        ifInUnknownProtos: 0,
        ifOutOctets: 1158612632,
        ifOutUcastPkts: 9221683,
        ifOutNUcastPkts: 0,
        ifOutDiscards: 0,
        ifOutErrors: 0,
        ifIn1SecRate: 0.006,
        ifOut1SecRate: 0.041,
      },
    ],
    system: {
      sysDescr:
        'Juniper Networks, Inc. ex2300-24t Ethernet Switch, kernel JUNOS 21.4R3.15, Build date: 2022-09-03 07:25:28 UTC Copyright (c) 1996-2022 Juniper Networks, Inc.',
      sysObjectID: '*******.4.1.2636.*******.132.1',
      sysUpTime: 389183759,
      upTime: '45 days, 1:03:57',
      startTime: '2024-02-27T14:38:05.803139Z',
      sysContact: '',
      sysName: 'DAUK-MRL-SYSV-SW1',
      latitude: null,
      longitude: null,
      sysServices: 6,
      sysORLastChange: null,
    },
    firmware: '21.4R3.15',
    ip_address: '**************',
    component_id: 'DAUK-MRL-SYSV-SW1',
    node_type: 'Switch',
    monitored: 'Y',
    status: 'OK',
    status_change_time: '2024-04-10T09:40:58.952413Z',
    last_success_time: '2024-04-12T15:42:02.803139Z',
    last_query_time: '2024-04-12T15:42:02.803139Z',
    last_query_duration: 19.95230484008789,
    last_error: 'error',
    query_error_count: 2,
  },
};
