export const FirewallNodeMockData = {
  id: 'string',
  juniper_firewall: {
    box: {
      jnxBoxClass: '*******.4.1.2636.*******.132.0',
      jnxBoxDescr: 'Juniper EX2300-24T Switch',
      jnxBoxSerialNo: 'JZ0220460459',
      jnxBoxRevision: '',
      jnxBoxInstalled: '2024-02-21T21:30:15Z',
      jnxLEDLastChange: '2024-02-21T21:30:15Z',
      jnxBoxKernelMemoryUsedPercent: 0,
      jnxBoxSystemDomainType: 'notApplicable',
      jnxBoxPersonality: '0.0',
      containers: [
        {
          jnxContainersIndex: 1,
          jnxContainersView: ['front', 'rear'],
          jnxContainersLevel: 0,
          jnxContainersWithin: 0,
          jnxContainersType: '*******.4.1.2636.*******.132.0',
          jnxContainersDescr: 'chassis frame',
          jnxContainersCount: 0,
          contents: [
            {
              detail: {
                jnxContentsL1Index: 0,
                jnxContentsL2Index: 0,
                jnxContentsL3Index: 0,
                jnxContentsType: '*******.4.1.2636.*******.132',
                jnxContentsDescr: 'Routing Engine 0',
                jnxContentsSerialNo: 'JZ0220460459',
                jnxContentsRevision: '',
                jnxContentsInstalled: '2024-02-21T21:30:15+00:00',
                jnxContentsPartNo: '',
                jnxContentsChassisId: 'singleChassis',
                jnxContentsChassisDescr: 'Single Chassis',
                jnxContentsChassisCleiCode: 'CMMU700ARB',
                jnxContentsModel: 'EX2300-24T',
              },
              // operating: {
              //   jnxOperatingContentsIndex: 7,
              //   jnxOperatingL1Index: 1,
              //   jnxOperatingL2Index: 0,
              //   jnxOperatingL3Index: 0,
              //   jnxOperatingDescr: 'FPC: EX2300-24T @ 0/*/*',
              //   jnxOperatingState: 'unknown',
              //   jnxOperatingTemp: 44,
              //   jnxOperatingCPU: 51,
              //   jnxOperatingISR: 1,
              //   jnxOperatingDRAMSize: **********,
              //   jnxOperatingBuffer: 48,
              //   jnxOperatingHeap: 13,
              //   jnxOperatingUpTime: '10 days, 2:42:30',
              //   jnxOperatingLastRestart: '2024-02-27T14:39:40.142124+00:00',
              //   jnxOperatingMemory: 1941,
              //   jnxOperatingStateOrdered: 'unknown',
              //   jnxOperatingChassisId: 'singleChassis',
              //   jnxOperatingChassisDescr: 'Single Chassis',
              //   jnxOperatingRestartTime: '2024-02-21T21:30:15+00:00',
              //   jnxOperating1MinLoadAvg: 0,
              //   jnxOperating5MinLoadAvg: 0,
              //   jnxOperating15MinLoadAvg: 0,
              //   jnxOperating1MinAvgCPU: 31,
              //   jnxOperating5MinAvgCPU: 32,
              //   jnxOperating15MinAvgCPU: 33,
              //   jnxOperatingFRUPower: 0,
              //   jnxOperatingBufferCP: 0,
              //   jnxOperatingMemoryCP: 0,
              //   jnxOperatingBufferExt: 0,
              // },
              operating: undefined,
              fru: {
                jnxFruContentsIndex: 7,
                jnxFruL1Index: 1,
                jnxFruL2Index: 0,
                jnxFruL3Index: 0,
                jnxFruName: 'FPC: EX2300-24T @ 0/*/*',
                jnxFruType: 'flexiblePicConcentrator',
                jnxFruSlot: 0,
                jnxFruState: 'online',
                jnxFruTemp: 45,
                jnxFruOfflineReason: 'none',
                jnxFruLastPowerOff: 0,
                jnxFruLastPowerOn: 5146,
                jnxFruPowerUpTime: '12 days, 18:55:24',
                jnxFruChassisId: 'singleChassis',
                jnxFruChassisDescr: 'Single Chassis',
                jnxFruPsdAssignment: 0,
              },
              led: {
                jnxLEDOriginator: 'string',
                jnxLEDDescr: 'string',
                jnxLEDState: 'green',
                jnxLEDStateOrdered: 'green',
              },
            },
          ],
        },
      ],
    },
    interfaces: [],
    system: {},
    vpninfo: {
      jnxVpnConfiguredVpns: 0,
      jnxVpnActiveVpns: null,
    },
    vpns: [],
    phase1: [],
    phase2: [],
    firewalls: [],
    firmware: '21.4R3.15',
    ip_address: '**************',
    component_id: 'DAUK-MRL-SYSV-SW1',
    node_type: 'Switch',
    monitored: 'Y',
    status: 'OK',
    status_change_time: null,
    last_success_time: null,
    last_query_time: null,
    last_query_duration: 1.5,
    last_error: null,
    query_error_count: 0,
  },
};
