import { LIFE_CYCLE, NODE_TYPE, ORIENTATION, ROLE_OF_NODE, STATUS } from '../../data/constants';

// export const getNodeByCellRefMock: Node[] = [
//   {
//     node_id: 'GBMARLNS000001',
//     node_type: NODE_TYPE.STREETCELL,
//     cell_refs: ['GBMARLNS000001'],
//     lifecycle: LIFE_CYCLE.STAGING,
//     version: '1.1',
//     roles: [ROLE_OF_NODE.RU],
//     //roles: ['RU', 'DU', 'CU'],
//     site_id: 2,
//     site_name: 'CC4',
//     site_address: '2FV7+PP6 Bedford',
//     latitude: 52.044288,
//     longitude: -0.535724,
//     orientation: ORIENTATION.North,
//     location_error: 3,
//     node_location: {
//       latitude: 51.57365374524476,
//       longitude: -0.760690388181796,
//       altitude: 0,
//     },
//     node_serial_no: 'AAAA31300103',
//     status: STATUS.UNKNOWN,
//     trouble_score: 0,
//   },
// ];

export const getNodeByCellRefMock: any = [
  {
    row: {
      getIsExpanded: () => true,
      original: {
        node_id: 'GBMARLNS000001',
        node_type: NODE_TYPE.STREETCELL,
        cell_refs: ['GBMARLNS000001'],
        lifecycle: LIFE_CYCLE.STAGING,
        version: '1.1',
        roles: [ROLE_OF_NODE.RU],
        //roles: ['RU', 'DU', 'CU'],
        site_id: 2,
        site_name: 'CC4',
        site_address: '2FV7+PP6 Bedford',
        latitude: 52.044288,
        longitude: -0.535724,
        orientation: ORIENTATION.North,
        location_error: 3,
        node_location: {
          latitude: 51.57365374524476,
          longitude: -0.760690388181796,
          altitude: 0,
        },
        node_serial_no: 'AAAA31300103',
        status: STATUS.UNKNOWN,
        trouble_score: 0,
      },
    },
  },
];
