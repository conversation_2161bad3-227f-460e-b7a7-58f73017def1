///nms/orc/server/nodes/{node_id}
export const getServerNodeDetails = {
  id: 'dauk-mrl-d-cav2',
  server: {
    server_id: 'dauk-mrl-d-cav2',
    name: 'dauk-mrl-d-cav2',
    ip_address: '*************',
    node_type: 'MeshRoot',
    access_method: 'ssh',
    last_attempted: '2023-10-23T12:41:00',
    last_contacted: '2023-10-23T12:41:00',
    subsystems: [],
    status: 'OK',
    reason: 'string',
  },
  vsr: {
    id: 'STRV1SERIALNO123',
    app: 'platform_vsr_agent',
    version: '1.0.0',
    sha: 'aa7850cb',
    started_at: '2023-11-23 14:59:16.180059+00:00',
    up_time: '0:01:06.545802',
    ip_address: '***************',
    poll: true,
    created: '2021-06-16T14:00:00Z',
    updated: '2021-06-16T14:00:00Z',
    vsr_data: {
      cpu_usage: 12.4,
      license_expiry: '2025-06-16T14:00:00Z',
      version: '1.0.0',
      host_name: 'server_1',
      status: 'OK',
      updated: '2021-06-16T14:00:00Z',
      fastpath: [
        {
          destination: '0.0.0.0/0',
          'next-hop': [
            {
              'next-hop': 'eth0',
              interface: 'eth0',
              active: true,
              uptime: 'PT56184S',
            },
          ],
        },
        {
          destination: '**********/16',
          'next-hop': [
            {
              'next-hop': 'eth0',
              interface: 'eth0',
              active: true,
              uptime: 'PT56184S',
            },
          ],
        },
      ],
    },
  },
};
