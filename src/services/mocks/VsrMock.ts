// export const getVsrMock = {
//   id: 'STRV1SERIALNO123',
//   app: 'test1',
//   version: '1.0.1',
//   sha: 'e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855',
//   started_at: '2021-08-10 12:00:00',
//   up_time: '2 days',
//   ip_address: '*************',
//   poll: true,
//   created: '2021-08-10T12:00:00',
//   updated: '2021-08-10T12:00:00',
//   vsr_data: {
//     cpu_usage: 12.4,
//     license_expiry: '2025-06-16T14:00:00Z',
//     version: '1.0.0',
//     host_name: 'server_1',
//     status: 'OK',
//     updated: '2021-06-16T14:00:00Z',
//     fastpath: [
//       {
//         destination: '0.0.0.0/0',
//         'next-hop': {
//           'next-hop': '**********',
//           interface: 'eth0',
//           active: true,
//           uptime: 'PT70416S',
//         },
//       },
//       {
//         destination: '**********/16',
//         'next-hop': {
//           'next-hop': 'eth0',
//           interface: 'eth0',
//           active: false,
//           uptime: 'PT70416S',
//         },
//       },
//       {
//         destination: '0.0.0.0/0',
//         'next-hop': {
//           'next-hop': '**********',
//           interface: 'eth0',
//           active: true,
//           uptime: 'PT70416S',
//         },
//       },
//       {
//         destination: '**********/16',
//         'next-hop': {
//           'next-hop': 'eth0',
//           interface: 'eth0',
//           active: false,
//           uptime: 'PT70416S',
//         },
//       },
//     ],
//   },
// };

export const getVsrMock = {
  id: 'da-mrl-dev2-dkr-vsr-cav03-01',
  app: 'platform_vsr_agent',
  version: '0.8.0+d20240208163305',
  sha: '4fec038b',
  started_at: '2024-02-08 16:43:15.941154+00:00',
  up_time: '0:00:30.209904',
  ip_address: '*************',
  poll: true,
  created: '2024-02-08T11:40:11.244485Z',
  updated: '2024-02-08T16:43:46.389095Z',
  vsr_data: {
    cpu_usage: 12.0,
    license_expiry: '2024-07-23T05:00:00Z',
    version: 'Virtual Service Router 3.6.4',
    host_name: 'da-mrl-dev2-dkr-vsr-cav03-01',
    status: 'OK',
    updated: '2024-02-09T08:19:33.491652Z',
    fastpath: [
      {
        destination: '0.0.0.0/1',
        'next-hop': [
          {
            'next-hop': '**********',
            interface: 'eth0',
            active: true,
            uptime: 'PT56184S',
          },
          {
            'next-hop': '**********',
            interface: 'test',
            active: true,
            uptime: 'PT56185S',
          },
        ],
      },
      {
        destination: '**********/16',
        'next-hop': [
          {
            'next-hop': 'eth0',
            interface: 'eth0',
            active: true,
            uptime: 'PT56184S',
          },
        ],
      },
      {
        destination: 'fe80::/64',
        'next-hop': [
          {
            'next-hop': 'eth0',
            interface: 'eth0',
            active: true,
            uptime: 'PT56165S',
          },
        ],
      },
    ],
  },
};
