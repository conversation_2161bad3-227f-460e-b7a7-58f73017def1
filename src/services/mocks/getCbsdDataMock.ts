export const getCbsdDataMock = {
  count: 3,
  cbsds: [
    {
      id: '-8254911913963245571',
      clusterId: 'denseair-gke-bmctl-edge-08-1-15-1',
      serialNumber: 'AG-TEST-01-001',
      fccId: 'GOOGWC02',
      state: 'RESERVED',
      requestGrant: false,
      installationParam: {
        latitude: 40.40818791727496,
        longitude: -82.88616299672867,
        height: 4,
        horizontalAccuracy: null,
        verticalAccuracy: null,
        indoorDeployment: false,
        antennaAzimuth: 0,
        antennaDowntilt: 1,
        antennaGain: 37,
        eirpCapability: 47,
        antennaBeamwidth: 65,
        antennaModel: null,
        heightType: 'HEIGHT_TYPE_AGL',
      },
      bandwidths: [40],
      groupingParam: [],
      lowFrequency: 3550000000,
      highFrequency: 3700000000,
      grants: [],
      activeGrants: 0,
      allowedGrants: 1,
      activeHeartbeats: 0,
      displayName: 'AG-TEST',
    },
    {
      id: '-338648305732326387',
      clusterId: 'denseair-gke-bmctl-edge-08-1-15-1',
      serialNumber: 'AG-TEST-01-002',
      fccId: 'GOOGWC02',
      state: 'RESERVED', // activate/deactivate otpions will be NOT enabled
      requestGrant: false,
      installationParam: {
        latitude: 42.40818791727496,
        longitude: -80.88616299672867,
        height: 4,
        horizontalAccuracy: null,
        verticalAccuracy: null,
        indoorDeployment: false,
        antennaAzimuth: 0,
        antennaDowntilt: 1,
        antennaGain: 37,
        eirpCapability: 47,
        antennaBeamwidth: 65,
        antennaModel: null,
        heightType: 'HEIGHT_TYPE_AGL',
      },
      bandwidths: [40],
      groupingParam: [],
      lowFrequency: 3550000000,
      highFrequency: 3700000000,
      grants: [],
      activeGrants: 0,
      allowedGrants: 1,
      activeHeartbeats: 0,
      displayName: 'AG-TEST',
    },
    {
      id: '8526092577482799257',
      clusterId: 'denseair-gke-bmctl-edge-08-1-15-1',
      serialNumber: 'AG-TEST-01-003',
      fccId: 'GOOGWC02',
      state: 'REGISTERED', // activate/deactivate otpions will be enabled
      requestGrant: false,
      installationParam: {
        latitude: 44.40818791727496,
        longitude: -82.88616299672867,
        height: 4,
        horizontalAccuracy: null,
        verticalAccuracy: null,
        indoorDeployment: false,
        antennaAzimuth: 0,
        antennaDowntilt: 1,
        antennaGain: 37,
        eirpCapability: 47,
        antennaBeamwidth: 65,
        antennaModel: null,
        heightType: 'HEIGHT_TYPE_AGL',
      },
      bandwidths: [40],
      groupingParam: [],
      lowFrequency: 3550000000,
      highFrequency: 3700000000,
      grants: [],
      activeGrants: 0,
      allowedGrants: 1,
      activeHeartbeats: 0,
      displayName: 'AG-TEST-01-003',
    },
  ],
};
