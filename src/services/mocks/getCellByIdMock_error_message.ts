export const getCellByRefMock_error_message = {
  sc_v1: [
    {
      id: 'GBMLBKNS000003',
      airspan_node: {
        utc_stamp: '2023-07-06T15:24:04.962443',
        client: 'http://airspan-acp-agent:8080/api/acp/',
        error: {
          status_code: 200,
          message: '{}',
          url: '/acp-millbrook/cells/7',
        },
      },
      particle_board: {
        details: {
          version: 491,
          modeName: 'ERROR_MODE',
          darkMode: true,
          caseOpen: true,
          fanMode: 'ON',
          PID_BW: [180, 50, 12],
          PID_AS: [135, 40, 10],
          PID_SET: 40,
          LNR_SET: 40,
          LNR_P_BW: 360,
          LNR_P_AS: 270,
          overTemp: 40,
          hysteresis: 38,
          tempSensor1: 19.375,
          tempSensor2: 0,
          bwFanSpeed: 60,
          asFanSpeed: 0,
          antennaConfiguration: 1,
          antenna: {
            board1: {
              switch1: 'UNKNOWN',
              switch2: 'UNKNOWN',
              switch3: 'UNKNOWN',
              switch4: 'UNKNOWN',
              switch5: 'UNKNOWN',
              switch6: 'UNKNOWN',
            },
            board2: {
              switch1: 'UNKNOWN',
              switch2: 'UNKNOWN',
              switch3: 'UNKNOWN',
              switch4: 'UNKNOWN',
              switch5: 'UNKNOWN',
              switch6: 'UNKNOWN',
            },
          },
          networkConfiguration: {
            DHCP: false,
            IP: '**************',
            SUB: '*************',
            GW: '***************',
            DNS: '0.0.0.0',
          },
          alarmCodes: [9, 10],
        },
        updated: '2023-07-24T07:56:50.916436+00:00',
        created: '2023-07-24T07:56:50.916436+00:00',
        published_at: '2023-07-24T07:56:52.616313+00:00',
        manager: 'particle_interactions',
        status: 'ERROR',
        status_reasons: ['Antenna board 1 is not connected.', 'Antenna board 2 is not connected.'],
        coreid: '0a10aced202194944a0216bc',
      },
      bw_mmwave: {
        status: 'OK',
      },
      fibrolan_switch: {
        status: 'OK',
      },
    },
  ],
  mesh_root: null,
};
