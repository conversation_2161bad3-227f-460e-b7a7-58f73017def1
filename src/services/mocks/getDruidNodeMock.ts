export const getDruidNodeMockData = {
  id: 'dauk-mrl-d-cav2',
  server: {
    server_id: 'dauk-mrl-d-cav2',
    name: 'dauk-mrl-d-cav2',
    ip_address: '*************',
    node_type: 'MeshRoot',
    access_method: 'ssh',
    last_attempted: '2023-10-23T12:41:00',
    last_contacted: '2023-10-23T12:41:00',
    subsystems: [],
    status: 'OK',
    reason: 'string',
  },
  druid: {
    device_id: 'dauk-mrl-att-druid-nhe',
    status: 'OK', // critical warning
    aspects: {
      enode_b_count: 0,
      iproute_count: 13,
      ipsec_secure_association_count: 9,
      net_device_count: 13,
      plmn_count: 1,
      radio_zone_count: 0,
      sgw_count: 0,
      sgw_session_count: 0,
      enable_5g: false,
      enable_5g_nsa: false,
    },
    details: {
      features: {
        oper_state: 'enabled',
        product_id: '8',
        system_id: '1102081919056657533',
        license_id: '14820895886772892389',
        license_status: '0', // if not zeo then it will expire
        issue_date: '2024-02-14T14:48:46Z',
        expiry_date: '2024-06-30T12:00:00Z',
        binding_date: null,
        supported_until: '2024-06-30T12:00:00Z',
      },
      system: {
        oper_state: 'enabled',
        admin_state: 'unlocked',
        service_state: 'Active',
        system_id: '1102081919056657533',
        license_id: '14820895886772892389',
        product_id:
          '*******.P2.1.18fffcb56::VIRTUAL::rhel8.9::x86_64::14820895886772892389::normal::1102081919056657533',
        software_version: 'Raemis Enterprise *******.P2-1, r18fffcb56.',
        restart_required: '0', //boolean. zero is false .can dump it as we dont have way to restart
        current_time: '2024-06-14T09:49:57.667000Z',
      },
    },
    networks: [
      {
        id: 2,
        ipv4_subnet: '**********',
        ipv4_subnetmask: '***********',
        gateway_ipv4: '0.0.0.0',
        net_device: 'tun_ims',
        metric: '0',
        owner: 'druid_nm',
        raemis_id: '0',
        dnm_managed: false,
      },
      {
        id: 3,
        ipv4_subnet: '**********',
        ipv4_subnetmask: '***********',
        gateway_ipv4: '0.0.0.0',
        net_device: 'tun_local',
        metric: '0',
        owner: 'druid_nm',
        raemis_id: '0',
        dnm_managed: false,
      },
      {
        id: 4,
        ipv4_subnet: '*************',
        ipv4_subnetmask: '***************',
        gateway_ipv4: '0.0.0.0',
        net_device: 'eth0',
        metric: '100',
        owner: 'druid_nm',
        raemis_id: '0',
        dnm_managed: false,
      },
      {
        id: 5,
        ipv4_subnet: '0.0.0.0',
        ipv4_subnetmask: '0.0.0.0',
        gateway_ipv4: '*************',
        net_device: 'eth0',
        metric: '100',
        owner: 'druid_nm',
        raemis_id: '0',
        dnm_managed: false,
      },
      {
        id: 6,
        ipv4_subnet: '*************',
        ipv4_subnetmask: '***************',
        gateway_ipv4: '0.0.0.0',
        net_device: 'bond0.54',
        metric: '400',
        owner: 'druid_nm',
        raemis_id: '0',
        dnm_managed: false,
      },
      {
        id: 7,
        ipv4_subnet: '**************',
        ipv4_subnetmask: '***************',
        gateway_ipv4: '*************',
        net_device: 'bond0.54',
        metric: '400',
        owner: 'druid_nm',
        raemis_id: '0',
        dnm_managed: false,
      },
      {
        id: 8,
        ipv4_subnet: '***************',
        ipv4_subnetmask: '***************',
        gateway_ipv4: '*************',
        net_device: 'bond0.54',
        metric: '400',
        owner: 'druid_nm',
        raemis_id: '0',
        dnm_managed: false,
      },
      {
        id: 9,
        ipv4_subnet: '**************',
        ipv4_subnetmask: '***************',
        gateway_ipv4: '0.0.0.0',
        net_device: 'bond0.64',
        metric: '403',
        owner: 'druid_nm',
        raemis_id: '0',
        dnm_managed: false,
      },
      {
        id: 10,
        ipv4_subnet: '*************',
        ipv4_subnetmask: '***************',
        gateway_ipv4: '**************',
        net_device: 'bond0.64',
        metric: '403',
        owner: 'druid_nm',
        raemis_id: '0',
        dnm_managed: false,
      },
      {
        id: 11,
        ipv4_subnet: '*************',
        ipv4_subnetmask: '***************',
        gateway_ipv4: '0.0.0.0',
        net_device: 'bond0.55',
        metric: '401',
        owner: 'druid_nm',
        raemis_id: '0',
        dnm_managed: false,
      },
      {
        id: 12,
        ipv4_subnet: '*************',
        ipv4_subnetmask: '***************',
        gateway_ipv4: '*************',
        net_device: 'bond0.55',
        metric: '401',
        owner: 'druid_nm',
        raemis_id: '0',
        dnm_managed: false,
      },
      {
        id: 13,
        ipv4_subnet: '***************',
        ipv4_subnetmask: '***************',
        gateway_ipv4: '*************',
        net_device: 'bond0.55',
        metric: '401',
        owner: 'druid_nm',
        raemis_id: '0',
        dnm_managed: false,
      },
      {
        id: 14,
        ipv4_subnet: '*************',
        ipv4_subnetmask: '***************',
        gateway_ipv4: '0.0.0.0',
        net_device: 'bond0.56',
        metric: '402',
        owner: 'druid_nm',
        raemis_id: '0',
        dnm_managed: false,
      },
    ],
    sessions: null,
    created_at: '2024-06-14T08:50:16.848990Z',
  },
  vsr: {
    id: 'STRV1SERIALNO123',
    app: 'platform_vsr_agent',
    version: '1.0.0',
    sha: 'aa7850cb',
    started_at: '2023-11-23 14:59:16.180059+00:00',
    up_time: '0:01:06.545802',
    ip_address: '***************',
    poll: true,
    created: '2021-06-16T14:00:00Z',
    updated: '2021-06-16T14:00:00Z',
    vsr_data: {
      cpu_usage: 12.4,
      license_expiry: '2025-06-16T14:00:00Z',
      version: '1.0.0',
      host_name: 'server_1',
      status: 'OK',
      updated: '2021-06-16T14:00:00Z',
      fastpath: [
        {
          destination: '0.0.0.0/0',
          'next-hop': [
            {
              'next-hop': 'eth0',
              interface: 'eth0',
              active: true,
              uptime: 'PT56184S',
            },
          ],
        },
        {
          destination: '**********/16',
          'next-hop': [
            {
              'next-hop': 'eth0',
              interface: 'eth0',
              active: true,
              uptime: 'PT56184S',
            },
          ],
        },
      ],
    },
  },
  acp: {
    acp_platform_agent_available: true,
    health_status: 'OK',
    health_status_logs: {
      api_responsive: true,
      db_stat: [
        {
          NumberOfRequests: 4,
          ProcessName: 'NMS CBRS Service',
          Time: '2024-01-18T15:43:00',
          TimeInMs: 62,
        },
      ],
      details: {
        cron_active: true,
        cron_freq: '0 */8 * * *',
        disk_used: '3%',
        load: '0.28, 0.11, 0.04',
        memfree: '10422216kB',
        memtotal: '16124028kB',
        top: '1.9 1712 mssql /opt/mssql/bin/sqlservr',
        uptime: '15:43:49 up 1:01',
      },
      info: {
        countryVariant: '(default)',
        databaseEngine:
          "'127.0.0.1' Microsoft SQL Server 2019 (RTM-CU23) (KB5030333) - 15.0.4335.1 (X64) \n\tSep 21 2023 17:28:44 \n\tCopyright (C) 2019 Microsoft Corporation\n\tExpress Edition (64-bit) on Linux (Red Hat Enterprise Linux 8.8 (Ootpa)) <X64>",
        lastUpgradeTime: '2023-11-10T10:20:57',
        machineName: 'ha-secondary-from-golden-ha-acp-snapshot-of-ha-primary',
        nmsSoftwareVersion: '129.20.50.054',
        operatingSystem: 'Red Hat Enterprise Linux 8.8 (Ootpa)',
        physicalMemory: '15746 MB',
      },
      latest_backup:
        'gs://acp-bucket/sql_backup_nr11_129.20.50.054/NETSPAN_129.20.50.054_2024_01_18_14H44M56S174.BAK\ngs://acp-bucket/sql_backup_nr11_129.20.50.054/NETSPANSTATS_129.20.50.054_2024_01_18_14H44M57S387.BAK\n',
      license: [
        {
          id: 2,
          licenseId: 'L179171CE5',
          licenseType: 'Operational',
          licenseVersion: 2,
        },
      ],
      license_valid: true,
      services_cpu: [
        {
          DurationMinutes: 15,
          EndTime: '2024-01-18T15:15:00',
          NmsArchiveServiceCpu: 0.01,
          NmsDiscoveryServiceCpu: 0.14,
          NmsEventServiceCpu: 0.08,
          NmsProvisioningServiceCpu: 0.2,
          NmsSoftwareServiceCpu: 0.01,
          NmsStatisticsServiceCpu: 0.18,
          SqlCpu: 0.3,
          StartTime: '2024-01-18T15:00:00',
          SystemCpu: 1.25,
          WebServerCpu: 0.11,
        },
      ],
      status: 'OK',
    },
    manager_instance: 'marlow',
    url: 'https://192.168.140.63:8181',
  },
};
