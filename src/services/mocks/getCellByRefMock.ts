export const getCellByRefMock = {
  sc_v1: [
    {
      id: 'GBR-MRLW-0001-A',
      type: 'Cell A',
      position: 'Root',
      airspan_node: {
        productType: 'AirSpeed 1900',
        productCode: 'AS19-F380-DSC1',
        gnbCuCp: [
          {
            id: 11,
            cuCpCellList: [
              {
                id: 9,
                cellIdentity: '000000171',
                cellNumber: 1,
                isEnabled: true,
                localCellId: 1,
                plmnInfoList: ['string'],
                defaultXncAllowed: true,
                neighbourManagementGroupId: 1,
                neighbourList: ['string'],
                externalNeighbourList: ['string'],
                securityConfig: {
                  integrity: 'Optional',
                  nullIntegrityPriority: 2,
                  snow3GIntegrityPriority: 0,
                  aesIntegrityPriority: 1,
                  ciphering: 'Optional',
                  nullCipheringPriority: 2,
                  snow3GCipheringPriority: 0,
                  aesCipheringPriority: 1,
                },
                mobilityConfig: {
                  idleMode: {
                    commonReselection: {},
                  },
                  interRatIdleMode: {
                    epsFallback: {},
                  },
                },
                endpointList: [
                  {
                    index: 1,
                    isSameCloud: true,
                    localAddress: '*************',
                    localPort: 37412,
                    remoteId: 'AMF_Druid_VM',
                    remoteAddress: '*************',
                    remotePort: 38412,
                    type: 'F1-C',
                  },
                ],
              },
            ],
          },
        ],
        gnbCuUp: [
          {
            id: 42,
            endpointList: [
              {
                index: 1,
                isSameCloud: true,
                localAddress: '*************',
                localPort: 37412,
                remoteId: 'AMF_Druid_VM',
                remoteAddress: '*************',
                remotePort: 38412,
                type: 'F1-U',
              },
            ],
          },
        ],
        gnbDu: [
          {
            id: 39,
            duCellList: [
              {
                id: 9,
                cellIdentity: '000000171',
                srsCombineOffset: 0,
                srsSequenceId: 231,
                duCellConfiguration: {
                  advancedConfiguration: {
                    downlink256Qam: 'Disabled',
                    reservedForOperatorUse: 'Disabled',
                    pMax: '33',
                    qRxMinLevel: -70,
                    qRxMinLevelSul: '-50',
                    qMinQuality: '-30',
                    sib2Periodicity: '32',
                    sib3Periodicity: '64',
                    sib4Periodicity: '64',
                    defaultPagingCycle: '64',
                    totalPagingFrames: '1/16T',
                    pagingFrameOffset: '0',
                    pagingOccasionsPerPagingFrame: 'One',
                    t300: '600',
                    t301: '600',
                    t310: '200',
                    n310: '1',
                    t311: '1000',
                    n311: '1',
                    t319: '200',
                    powerRampingStep: '2',
                    preambleInitialReceivedTargetPower: '-90',
                    preambleMaxTransmit: '10',
                    preambleFormat: 'B4',
                    rootSequenceType: 'L139',
                    prachConfigIndex: 148,
                    zeroCorrelationZoneConfig: 12,
                    msg1ScsKhz: '30',
                    ssbsPerRach: '1',
                    contentionBasedPreamblePerSsb: 12,
                    numberRachPreambles: 30,
                    msg1Fdm: '1',
                    msg1FrequencyStart: 12,
                    responseWindowSizeSubframes: 'sl20',
                    contentionResolutionTimer: 'sf40',
                    rsrpThresholdSsb: 20,
                    restrictedSet: 'Unrestricted',
                    msg3MaxTxThreshold: 1,
                    msg3TransformPrecoder: 'Enabled',
                    maxNumUeInheritFromGnb: 'Enabled',
                    maxNumUe: 32,
                    frameStructure: '70/20/10',
                    flexibleSlotStructure: '10D4G',
                  },
                },
                cellNumber: 1,
                isEnabled: true,
                adminState: 'Locked',
                localCellId: 1,
                nrPci: 231,
                nrRsi: 0,
                nrTac: 0,
                band: 77,
                pdcchSubcarrierSpacingKhz: '30',
                ssbSubcarrierSpacingKhz: '30',
                nrArfcn: 657000,
                bandwidthMhz: '100',
                bwpSizeMhz: 100,
                bwpStartRb: 0,
                gscn: 8065,
                ssbPeriodicityMs: '20',
                ssbOffsetMs: 0,
                ssbDurationMs: 4,
                plmnInfoList: ['string'],
                inheritPlmnInfoListFromCuCell: true,
                cbrsEnabled: false,
                pciManagementGroupId: 1,
                excludeFromAutoPci: false,
                rachManagementGroupId: 1,
                excludeFromAutoRach: false,
                sectorCarrierProperties: {
                  duMacAddress: 'string',
                  ruSectorCarrierId: 1,
                  maxTxPower: 10,
                  sectorCarrierBandwidthMhz: '100',
                  sectorCarrierNrArfcn: 0,
                },
              },
            ],
            endpointList: [
              {
                index: 1,
                isSameCloud: true,
                localAddress: '*************',
                localPort: 37412,
                remoteId: 'AMF_Druid_VM',
                remoteAddress: '*************',
                remotePort: 38412,
                type: 'F1-C',
              },
            ],
          },
        ],
        gnbRu: [
          {
            id: 40,
            sectorCarriers: [
              {
                centreFrequency: 3855000,
                duplexMode: 'TDD',
                bands: 'n77',
                duCell: 'DU-as1900-f0486900d99c-1 Cell 1',
                ruSectorConfiguration: {
                  gnbRuSectorServiceManagerProperties: {
                    oRanFrontHaulIndicated: 'Enabled',
                    unitOverHeating: 'Enabled',
                    outOfSync: 'Enabled',
                  },
                },
                sectorCarrierId: 1,
                nrArfcn: 657000,
                bandwidth: '100',
                inheritDuConfiguration: true,
              },
            ],
            ruConfiguration: {
              advancedConfiguration: {},
            },
          },
        ],
        nodeProperties: {
          id: 11,
          locationSource: 2,
          name: 'AS1900 - Ravi Lab',
          description: '**************',
          regionId: 1,
          siteId: 1,
          latitude: 51.573909,
          longitude: -0.759858,
          altitude: 85,
          isManaged: true,
          isNbifEventAlarmForwarding: true,
        },
        gnbProperties: {
          primaryPlmnInfo: {
            plmn: {
              mcc: '234',
              mnc: '30',
            },
            snssaiList: [
              {
                sst: 1,
                sd: true,
                sdString: '000000',
              },
            ],
          },
        },
        gnbType: 'Id32Bits',
        gnbId: 23,
        status: 'OK',
      },
      particle_board: {
        details: {
          version: 491,
          modeName: 'ERROR_MODE',
          darkMode: true,
          caseOpen: true,
          fanMode: 'ON',
          PID_BW: [180, 50, 12],
          PID_AS: [135, 40, 10],
          PID_SET: 40,
          LNR_SET: 40,
          LNR_P_BW: 360,
          LNR_P_AS: 270,
          overTemp: 40,
          hysteresis: 38,
          tempSensor1: 19.375,
          tempSensor2: 0,
          bwFanSpeed: 60,
          asFanSpeed: 0,
          antennaConfiguration: 1,
          antenna: {
            board1: {
              switch1: 'UNKNOWN',
              switch2: 'UNKNOWN',
              switch3: 'UNKNOWN',
              switch4: 'UNKNOWN',
              switch5: 'UNKNOWN',
              switch6: 'UNKNOWN',
            },
            board2: {
              switch1: 'UNKNOWN',
              switch2: 'UNKNOWN',
              switch3: 'UNKNOWN',
              switch4: 'UNKNOWN',
              switch5: 'UNKNOWN',
              switch6: 'UNKNOWN',
            },
          },
          networkConfiguration: {
            DHCP: false,
            IP: '**************',
            SUB: '*************',
            GW: '***************',
            DNS: '0.0.0.0',
          },
          alarmCodes: [9, 10],
        },
        updated: '2023-07-24T07:56:50.916436+00:00',
        created: '2023-07-24T07:56:50.916436+00:00',
        published_at: '2023-07-24T07:56:52.616313+00:00',
        manager: 'particle_interactions',
        status: 'ERROR',
        status_reasons: ['Antenna board 1 is not connected.', 'Antenna board 2 is not connected.'],
        coreid: '0a10aced202194944a0216bc',
      },
      bw_mmwave: {
        status: 'OK',
      },
      fibrolan_switch: {
        psu: {
          flDevicePsuIndex: '1',
          flDevicePsuInstalled: 'installed',
          flDevicePsuStatus: 'ok',
          flDevicePsuFanStatus: 'ok',
          flDevicePsuAlarmsEnable: 'enable',
          flDevicePsuAlarmStatus: 'ok',
          flDevicePsuStatusLastChange: '0',
          flDeviceUpdateTableIndex: '0',
          flDeviceUpdateType: '0',
          flDeviceUpdateFileServerType: '0',
          flDeviceUpdateFileServerAddress: '0.0.0.0',
          flDeviceUpdateFileXferDirection: '0',
          flDeviceUpdateFileName: '',
          flDeviceUpdateStart: 'ready',
          flDeviceUpdateStatus: 'notStarted',
          flDeviceUpdateErrorStatus: 'noError',
          flDeviceUpdateErrorCode: '0',
          flDeviceAlarmThresholdTableIndex: '0',
          flDeviceAlarmThresholdType: '0',
          flDeviceAlarmThresholdValue: '0',
          flDeviceAlarmThresholdClearValue: '0',
        },
        cpu: {
          flDeviceCpuIndex: '0',
          flDeviceCpuUtilization: '22',
          flDeviceMemoryUtilization: '39',
          flDeviceNvMemoryUtilization: '0',
          flDeviceCpuAlarmsEnable: 'disable',
          flDeviceCpuAlarmStatus: '1',
          flDeviceCpuStatusLastChange: '0',
        },
        interfaces: [
          {
            ifIndex: '1',
            ifDescr: 'VLAN    1',
            ifType: 'l2vlan',
            ifMtu: '0',
            ifSpeed: '1000000000',
            ifPhysAddress: '00:05:80:08:35:11',
            ifAdminStatus: 'down',
            ifOperStatus: null,
          },
          {
            ifIndex: '180',
            ifDescr: 'VLAN  180',
            ifType: 'l2vlan',
            ifMtu: '0',
            ifSpeed: '1000000000',
            ifPhysAddress: '00:05:80:08:35:11',
            ifAdminStatus: 'up',
            ifOperStatus: null,
          },
          {
            ifIndex: '181',
            ifDescr: 'VLAN  181',
            ifType: 'l2vlan',
            ifMtu: '0',
            ifSpeed: '1000000000',
            ifPhysAddress: '00:05:80:08:35:11',
            ifAdminStatus: 'up',
            ifOperStatus: null,
          },
          {
            ifIndex: '1000001',
            ifDescr: '2.5G 1/1',
            ifType: 'ethernetCsmacd',
            ifMtu: '10240',
            ifSpeed: '2500000000',
            ifPhysAddress: '00:05:80:08:35:12',
            ifAdminStatus: 'up',
            ifOperStatus: null,
          },
          {
            ifIndex: '1000002',
            ifDescr: '2.5G 1/2',
            ifType: 'ethernetCsmacd',
            ifMtu: '10240',
            ifSpeed: '2500000000',
            ifPhysAddress: '00:05:80:08:35:13',
            ifAdminStatus: 'up',
            ifOperStatus: null,
          },
          {
            ifIndex: '1000003',
            ifDescr: '2.5G 1/3',
            ifType: 'ethernetCsmacd',
            ifMtu: '10240',
            ifSpeed: '2500000000',
            ifPhysAddress: '00:05:80:08:35:14',
            ifAdminStatus: 'up',
            ifOperStatus: null,
          },
          {
            ifIndex: '1000004',
            ifDescr: '2.5G 1/4',
            ifType: 'ethernetCsmacd',
            ifMtu: '9216',
            ifSpeed: '1000000000',
            ifPhysAddress: '00:05:80:08:35:15',
            ifAdminStatus: 'up',
            ifOperStatus: null,
          },
          {
            ifIndex: '1000005',
            ifDescr: 'Gi 1/5',
            ifType: 'ethernetCsmacd',
            ifMtu: '10240',
            ifSpeed: '10000000',
            ifPhysAddress: '00:05:80:08:35:16',
            ifAdminStatus: 'up',
            ifOperStatus: null,
          },
          {
            ifIndex: '1000006',
            ifDescr: 'Gi 1/6',
            ifType: 'ethernetCsmacd',
            ifMtu: '10240',
            ifSpeed: '100000000',
            ifPhysAddress: '00:05:80:08:35:17',
            ifAdminStatus: null,
            ifOperStatus: null,
          },
          {
            ifIndex: '1000007',
            ifDescr: '10G 1/7',
            ifType: 'ethernetCsmacd',
            ifMtu: '9216',
            ifSpeed: '4294967295',
            ifPhysAddress: '00:05:80:08:35:18',
            ifAdminStatus: null,
            ifOperStatus: null,
          },
          {
            ifIndex: '1000008',
            ifDescr: '10G 1/8',
            ifType: 'ethernetCsmacd',
            ifMtu: '9216',
            ifSpeed: '4294967295',
            ifPhysAddress: '00:05:80:08:35:19',
            ifAdminStatus: null,
            ifOperStatus: null,
          },
          {
            ifIndex: '1000009',
            ifDescr: 'Gi 1/9',
            ifType: 'ethernetCsmacd',
            ifMtu: '10240',
            ifSpeed: '10000000',
            ifPhysAddress: '00:05:80:08:35:1a',
            ifAdminStatus: null,
            ifOperStatus: null,
          },
          {
            ifIndex: '1000010',
            ifDescr: 'Gi 1/10',
            ifType: 'ethernetCsmacd',
            ifMtu: '10240',
            ifSpeed: '10000000',
            ifPhysAddress: '00:05:80:08:35:1b',
            ifAdminStatus: null,
            ifOperStatus: null,
          },
          {
            ifIndex: '1000011',
            ifDescr: 'Gi 1/11',
            ifType: 'ethernetCsmacd',
            ifMtu: '10240',
            ifSpeed: '10000000',
            ifPhysAddress: '00:05:80:08:35:1c',
            ifAdminStatus: null,
            ifOperStatus: null,
          },
        ],
        system: {
          sysDescr: 'uFalcon-MX/S, firmware ********',
          sysObjectID: '*******.4.1.4467.1000.353',
          sysUpTime: '496591093',
          sysContact: '',
          sysName: 'mcomp-fb-tp-1',
          latitude: null,
          longitude: null,
          sysServices: '3',
          sysORLastChange: null,
        },
        firmware: '********',
        ip_address: '************',
        component_id: '00:05:80:08:35:11',
        monitored: 'Y',
        status: 'OK',
      },
    },
  ],
  mesh_root: {
    id: 'GBR-MRLW-0001-Mesh',
    status: 'OK',
  },
  status: 'OK',
};

// New PB data
// particle_board: {
//   deviceInfo: {
//     version: 396,
//     modeName: 'ERROR_MODE',
//     darkMode: true,
//     caseOpen: true,
//     fanMode: 'ON',
//     overtemp: 40,
//     hysteresis: 38,
//     tempSensor1: 0,
//     tempSensor2: 0,
//     bwFanSpeed: 0,
//     asFanSpeed: 0,
//     antenna: {
//       board1: {
//         switch1: 'UNKNOWN',
//         switch2: 'UNKNOWN',
//         switch3: 'UNKNOWN',
//         switch4: 'UNKNOWN',
//         switch5: 'UNKNOWN',
//         switch6: 'UNKNOWN',
//       },
//       board2: {
//         switch1: 'UNKNOWN',
//         switch2: 'UNKNOWN',
//         switch3: 'UNKNOWN',
//         switch4: 'UNKNOWN',
//         switch5: 'UNKNOWN',
//         switch6: 'UNKNOWN',
//       },
//     },
//     alarmCodes: [11, 9, 10, 12, 22, 18, 20],
//   },
//   coreInfo: {
//     name: 'Pipeline_test_WIFI',
//     last_heard: '2023-04-28T10:27:57.699000+00:00',
//     connected: true,
//     last_handshake_at: '2023-04-26T14:08:42.708000+00:00',
//     deviceID: 'e00fce68361536d212dd4cc7',
//     product_id: 18215,
//   },
//   status: 'ERROR',
// },

// Old PB data
// particle_board: {
//   antennaConfiguration: 1,
//   version: 396,
//   modeName: 'ERROR_MODE',
//   darkMode: true,
//   caseOpen: true,
//   fanMode: 'ON',
//   PID_BW: [180.0, 50.0, 12.0],
//   PID_AS: [135.0, 40.0, 10.0],
//   PID_SET: 40.0,
//   overtemp: 40.0,
//   hysteresis: 38.0,
//   tempSensor1: 0.0,
//   tempSensor2: 0.0,
//   bwFanSpeed: 0,
//   asFanSpeed: 0,
//   antenna: {
//     board1: {
//       switch1: 'UNKNOWN',
//       switch2: 'UNKNOWN',
//       switch3: 'UNKNOWN',
//       switch4: 'UNKNOWN',
//       switch5: 'UNKNOWN',
//       switch6: 'UNKNOWN',
//     },
//     board2: {
//       switch1: 'UNKNOWN',
//       switch2: 'UNKNOWN',
//       switch3: 'UNKNOWN',
//       switch4: 'UNKNOWN',
//       switch5: 'UNKNOWN',
//       switch6: 'UNKNOWN',
//     },
//   },
//   alarmCodes: [11, 9, 10, 12, 22, 18, 20],
// },
