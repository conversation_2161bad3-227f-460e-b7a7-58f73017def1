import { LIFE_CYCLE } from '../data/constants';
import { FacetSummary, SelectedFilters } from '../pages/MetricsCollector/components/ServerFacets/types';
import {
  Alarm,
  AlarmElement,
  Events,
  EventSearchQueryParams,
  ParticleMetrics,
  ParticleMetricsSearchQueryParams,
  ResolveAlarmRequest,
  TransporteMetricsSearchParams,
  TransportMetrics,
  UpdateAlarmRequest,
} from '../types/metricCollector.type';
import { apiClient } from './httpCommon';

export const getEventList = async (params: EventSearchQueryParams): Promise<Events> => {
  const { data } = await apiClient.get('/mc/events/search', {
    params: {
      ...params,
    },
  });
  return data;
};

export const getAlarmList = async (
  limit: number,
  filters: SelectedFilters,
  openAlarms?: boolean,
  lifecycle?: LIFE_CYCLE | null
): Promise<Alarm> => {
  const queryParams: Record<string, string[]> = {};
  for (const outerKey in filters) {
    if (!filters[outerKey]) continue;
    queryParams[outerKey] = Object.entries(filters[outerKey])
      .filter(([, value]) => Boolean(value))
      .map(([innerKey]) => innerKey);
  }

  queryParams.resolved = [openAlarms ? 'false' : 'true'];

  if (lifecycle != null) {
    queryParams.lifecycle = [lifecycle];
  }

  const axiosParams: Record<string, any> = {
    limit: 1000,
    offset: limit - 1000,
    ...queryParams,
  };

  const response = await apiClient.get('/mc/alarms', {
    params: axiosParams,
  });

  return response.data;
};

export const searchParticleMetrics = async (params: ParticleMetricsSearchQueryParams): Promise<ParticleMetrics> => {
  const { data } = await apiClient.get(`/mc/particle_metrics/search`, {
    params: {
      ...params,
    },
  });
  return data;
};

export const updateAlarm = async ({
  alarm_id,
  alarm,
}: {
  alarm_id: string;
  alarm: UpdateAlarmRequest;
}): Promise<AlarmElement> => {
  const { data } = await apiClient.patch(`/mc/alarm/${alarm_id}`, alarm);
  return data;
};

export const resolveAlarm = async ({
  alarm_id,
  alarm,
}: {
  alarm_id: string;
  alarm: ResolveAlarmRequest;
}): Promise<AlarmElement> => {
  const { data } = await apiClient.patch(`/mc/resolve/${alarm_id}`, alarm);
  return data;
};

export const getEventsFacetSummary = async (params: EventSearchQueryParams) => {
  const { data } = await apiClient.get(`/mc/events/facets/summary`, {
    params: {
      ...params,
    },
  });
  // reason to delete eventType from response.data is because eventsFacetSummary is used in events screen
  // and we want to restrict user from seeing alarms or any other eventType on events screen
  delete data.eventType;
  return data;
};

export const getActiveAlarms = async (cell_refs?: string[], node_ids?: string[]): Promise<any> => {
  const params: any = {
    cell_refs: [],
    node_ids: [],
  };

  if (cell_refs) {
    const safeCellRefs = Array.isArray(cell_refs) ? cell_refs : [cell_refs];
    safeCellRefs.forEach((ref) => {
      params['cell_refs'].push(ref);
    });
  }

  if (node_ids) {
    const safeNodeIds = Array.isArray(node_ids) ? node_ids : [node_ids];
    safeNodeIds.forEach((id) => {
      params['node_ids'].push(id);
    });
  }

  if (params.cell_refs.length > 0 || params.node_ids.length > 0) {
    const { data } = await apiClient.get('/mc/alarms/active/count', { params });
    return data;
  }

  return [];
};

export const getAlarmsFacetSummary = async (): Promise<FacetSummary> => {
  const { data } = await apiClient.get(`/mc/alarms/facets/summary`);
  return data;
};

export const searchTransportMetric = async (params: TransporteMetricsSearchParams): Promise<TransportMetrics> => {
  const { data } = await apiClient.get(`/mc/transport_metrics/interfaces/search`, {
    params: {
      ...params,
    },
  });
  return data;
};
