import { apiClient } from './httpCommon';

export const getRollout = async () => {
  const { data } = await apiClient.get('/controller/rollout/');
  return data;
};

export const getDeployment = async () => {
  const { data } = await apiClient.get('/controller/deployment/');
  return data;
};

export const getDeploymentbyId = async (id: any) => {
  const { data } = await apiClient.get(`/controller/deployment/${id}`);
  return data;
};

export const getRolloutById = async (id: any) => {
  const { data } = await apiClient.get(`/controller/rollout/${id}`);
  return data;
};

export const postRollout = async (name: string, description: string) => {
  const { data } = await apiClient.post('/controller/rollout/create', { name, description });
  return data;
};

export const getArtifactNameList = async (type: string) => {
  const { data } = await apiClient.get(`/controller/preflight/compatible_software/${type}`);
  return data;
};

export const postDeployment = async (
  name: string,
  description: string,
  rollout_id: string,
  artifact_name: string,
  deployment_type: string
) => {
  const { data } = await apiClient.post('/controller/deployment/create', {
    name,
    description,
    rollout_id,
    artifact_name,
    deployment_type,
  });
  return data;
};

export const activateRollout = async (rollout_id: string) => {
  const { data } = await apiClient.post(`/controller/rollout/${rollout_id}/activate`);
  return data;
};

export const deleteRollout = async (rollout_id: string) => {
  const { data } = await apiClient.delete(`/controller/rollout/${rollout_id}`);
  return data;
};

export const deleteDeployment = async (deployment_id: string) => {
  const { data } = await apiClient.delete(`/controller/deployment/${deployment_id}`);
  return data;
};
