export enum ComponentTypeEnum {
  AIRSPAN = 'AIRSPAN',
  PART<PERSON><PERSON> = 'PARTICLE',
  BLUWIRELESS = 'BLUWIRELESS',
  FIBROLAN = 'FIBROLAN',
  SIXWIND = 'SIXWIND',
  ANTHOS = 'ANTHOS',
  RAD_K8 = 'RAD_K8',
  SERVER = 'SERVER',
  ACP = 'ACP',
  JUNIPER = 'JUNIPER',
  DRUID = 'DRUID',
  DU = 'DU',
  CUCP = 'CUCP',
  CUUP = 'CUUP',
  POWER = 'POWER',
  AIRSPAN4G = 'AIRSPAN4G',
  PKI = 'PKI',
  UPS = 'UPS',
  E500 = 'E500',
  FORTIMANAGER = 'FORTIMANAGER',
  FORTINET = 'FORTINET',
  NEXUS = 'NEXUS',
}
