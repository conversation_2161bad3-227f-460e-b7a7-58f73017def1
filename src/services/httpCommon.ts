import axios from 'axios';
import { EnvironmentName } from '../../vite.config';
import { AUTH_TOKEN_KEY } from '../data/constants';

function getApiBaseUrl(): string | undefined {
  const env = import.meta.env;
  const env_name: EnvironmentName = env.VITE_ENV_NAME;

  const localStorageSettings = localStorage.getItem('settings');
  let parsedSettings: { baseUrl?: string } | null = null;

  if (env.PROD && localStorageSettings) {
    try {
      parsedSettings = JSON.parse(localStorageSettings.trim());
    } catch (error) {
      console.error('Error parsing settings from localStorage:', error);
    }
  }

  const baseUrlMap: Record<EnvironmentName, string> = {
    dev_1: '/nms/dev1',
    dev2_01: '/nms/dev2',
    test_1: '/nms/test1',
  };
  if (env.DEV) {
    return env_name ? baseUrlMap[env_name] || '/nms' : '/nms';
  }
  if (env.PROD) {
    if (parsedSettings) {
      return parsedSettings.baseUrl;
    } else {
      console.error('Error fetching settings: settings not found');
      return 'placeholder';
    }
  }
}

export const apiClient = axios.create({
  baseURL: getApiBaseUrl(),
  headers: {
    'Content-type': 'application/json',
  },
  timeout: 40000,
  //timeout: 120000,
  paramsSerializer: (params) => {
    // backend does not recognize array params i.e priority[]=high&priority[]=medium
    // unless they are in the form priority=high&priority=medium
    // so we need to convert array params to this form before sending to backend
    const parts = [];
    for (const key in params) {
      if (Array.isArray(params[key])) {
        // For each value in the array, add key=value
        params[key].forEach((value: any) => {
          parts.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
        });
      } else {
        // For non-array values, just add key=value
        parts.push(`${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`);
      }
    }
    return parts.join('&');
  },
});

// interceptor for JWT
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem(AUTH_TOKEN_KEY) || '';
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

//----------Health Check API's----------//
export const getServicesHealthCheck = async () => {
  const { data } = await apiClient.get(`/orc/system_health`);
  return data;
};

export const refreshCellInventory = async () => {
  const { data } = await apiClient.post(`/orc/refresh-inventory`);
  return data;
};
