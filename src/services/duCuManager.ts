//NOTE: AKA Radisys Configuration Manager

import { ValidArfcnsData } from '../pages/OranDuCuManager/formFields/advancedView/configSetForms/SetId2';
import { Cluster, ParameterSets } from '../types/duCuManager.type';
import { apiClient } from './httpCommon';

//----------Config Sets API's----------//
export const getConfigSets = async (): Promise<any> => {
  const { data } = await apiClient.get('/rad/config/config_sets/list');
  return data;
};

export const postConfigSet = async (configSet: any) => {
  const { data } = await apiClient.post('/rad/config/config_sets/create', configSet);
  return data;
};

export const deleteConfigSet = async (configSetId: number | null): Promise<any[]> => {
  const { data } = await apiClient.delete(`/rad/config/config_sets/${configSetId}`);
  return data;
};

export const getParameterSets = async (): Promise<ParameterSets[]> => {
  const { data } = await apiClient.get('/rad/config/parameter_sets/list');
  return data;
};

export const getUserParams = async (set_id: number | null): Promise<any[]> => {
  const { data } = await apiClient.get(`/rad/config/parameter_sets/${set_id}`);
  return data;
};

export const getDUCellConfigModels = async (): Promise<any[]> => {
  const { data } = await apiClient.get(`/rad/config/cell_config_models`);
  return data;
};

export const getBands = async (): Promise<any[]> => {
  const { data } = await apiClient.get(`/rad/config/band_bandwidths`);
  return data;
};

export const getValidArfcns = async (params: {
  band: string | null;
  bandwidth: string | null;
}): Promise<ValidArfcnsData> => {
  const { data } = await apiClient.get('/rad/config/valid_arfcns', { params });
  return data;
};

//----------Custom Resources API's----------//
export const getCluster = async (): Promise<any> => {
  const { data } = await apiClient.get('/rad/config/clusters/list');
  return data;
};

export const getAllDeployments = async (): Promise<any> => {
  const { data } = await apiClient.get('/rad/config/integration/deployments/all');
  return data;
};

export const getDeploymentCustomResources = async (deployment_name: string): Promise<any> => {
  const { data } = await apiClient.get('/rad/config/integration/custom_resources', {
    params: {
      deployment_name,
    },
  });
  return data;
};

export const getDeploymentPods = async (deployment_name: string): Promise<any> => {
  const { data } = await apiClient.get('/rad/config/integration/pods', {
    params: {
      deployment_name,
    },
  });
  return data;
};

export const getCustomResourceBySite = async (payloadData: any): Promise<any> => {
  const { data } = await apiClient.get(
    `/rad/config/clusters/${payloadData.cluster}/custom_resources/list?filter_by_site=${payloadData.site}`
  );
  return data;
};

export const getPods = async (payloadData: number | null): Promise<any> => {
  const { data } = await apiClient.get(`/rad/config/clusters/${payloadData}/pods/list`);
  return data;
};

export const getPodStatus = async (clusterId: number | null, podName: string): Promise<any> => {
  const { data } = await apiClient.get(`/nms/rad/config/status/${clusterId}/pod/${podName}`);
  return data;
};

export const getCustomResource = async (cluster_id: number | null): Promise<any> => {
  const { data } = await apiClient.get(`/rad/config/clusters/${cluster_id}/custom_resources/list`);
  return data;
};

export const getSingleCustomResource = async (cluster_id: number | null, assetName: string): Promise<any> => {
  const { data } = await apiClient.get(`/rad/config/clusters/${cluster_id}/custom_resources/${assetName}`, {
    params: {
      cluster_id: cluster_id,
      cr_name: assetName,
    },
  });
  return data;
};

export const getCustomResourcePath = async (
  cluster_id: number | null,
  cr_name: string | undefined,
  path_str: string
): Promise<any> => {
  const { data } = await apiClient.get(
    `/rad/config/clusters/${cluster_id}/custom_resources/paths/${cr_name}/${path_str}`,
    {
      params: {
        cluster_id: cluster_id,
        cr_name: cr_name,
        path_str: path_str,
      },
    }
  );
  return data;
};

export type PatchCustomResourceUnnamedPayload = {
  cluster_id: number | null;
  cr_name: string;
  path_str: string;
  new_value: string;
};

export const patchCustomResourceUnnamed = async (payload: PatchCustomResourceUnnamedPayload): Promise<any> => {
  const { data } = await apiClient.patch(
    `/rad/config/clusters/${payload.cluster_id}/custom_resources/unnamed/${payload.cr_name}?path=${payload.path_str}&value=${payload.new_value}`
  );
  return data;
};

export const postConfigCustomResource = async (payloadData: any) => {
  const { data } = await apiClient.post(
    `/rad/config/clusters/${payloadData.clusterId}/custom_resources/configCR/${payloadData.crType}/create`,
    payloadData.payload
  );
  return data;
};

export const postDeploymentCustomResource = async (payloadData: any) => {
  const { data } = await apiClient.post(
    `/rad/config/clusters/${payloadData.clusterId}/custom_resources/deploymentCR/${payloadData.crType}/create`,

    payloadData.payload
  );
  return data;
};

export const deleteCustomResource = async (payloadData: any) => {
  const { cluster_id, name, cr_name, version } = payloadData;

  const url = `/rad/config/clusters/${cluster_id}/custom_resources/${cr_name}?version=${version}`;

  const { data } = await apiClient.delete(url);
  return data;
};

export const activateCustomResource = async (payloadData: any) => {
  const { data } = await apiClient.post(
    `/rad/config/clusters/${payloadData.cluster_id}/deployments/activate`,
    payloadData
  );
  return data;
};

export const deActivateCustomResource = async (payloadData: any) => {
  const { data } = await apiClient.post(
    `/rad/config/clusters/${payloadData.cluster_id}/deployments/deactivate`,
    payloadData
  );
  return data;
};

//NOTE: New way to create site CRS: deployments > create_site_crs
export const postCreateCustomResourceForSite = async (payloadData: any) => {
  const { data } = await apiClient.post(`/rad/config/integration/deployments`, payloadData);
  return data;
};

//----------Pod API's----------//

//NOTE: Also pod deletion
export const deleteAllCustomResourceForSite = async (deployment_name: string): Promise<any> => {
  const { data } = await apiClient.delete(`/rad/config/integration/deployments`, {
    params: { deployment_name },
  });
  return data;
};

export const activateAllCustomResourcesForSite = async (deployment_name: string): Promise<any> => {
  const { data } = await apiClient.post(`/rad/config/integration/activate`, null, {
    params: { deployment_name },
  });
  return data;
};

export const deActivateAllCustomResourcesForSite = async (deployment_name: string): Promise<any> => {
  const { data } = await apiClient.post(`/rad/config/integration/deactivate`, null, {
    params: { deployment_name },
  });
  return data;
};

//----------Cluster API's----------//
export const getClusterList = async (): Promise<Cluster[]> => {
  const { data } = await apiClient.get('/rad/config/clusters/list');
  return data;
};

//----------Interface API's----------//
export const postInterface = async (simpleViewInterfaceData: any) => {
  const { data } = await apiClient.post('/rad/config/integration/interfaces/set', simpleViewInterfaceData);
  return data;
};

export type InterfaceListResponse = {
  interfaces: any[];
};

export type PayloadData = {
  deployment_name?: string | null;
  version?: string | null;
  du_cluster?: number | null;
  cucp_cluster?: number | null;
  cuup_cluster?: number | null;
  cucp_site_name?: string | null;
  cuup_site_name?: string | null;
  du_site_name?: string | null;
};

export const getInterfacesList = async (payloadData: PayloadData) => {
  const { data } = await apiClient.get('/rad/config/integration/interfaces/get', {
    params: {
      deployment_name: payloadData.deployment_name,
    },
  });

  if (data && data.cu_cp && Array.isArray(data.cu_cp)) {
    const hasNgCGatewayAddress = data.cu_cp.some((item: { name: string }) => item.name === 'NgC gatewayAddress');

    if (!hasNgCGatewayAddress) {
      data.cu_cp.push({
        name: 'NgC gatewayAddress',
        default: '0.0.0.0',
        target: 'Backhaul Gateway',
      });
    }
  }

  if (data && data.cu_cp2 && Array.isArray(data.cu_cp2)) {
    const hasNgCGatewayAddress = data.cu_cp2.some((item: { name: string }) => item.name === 'NgC gatewayAddress');

    if (!hasNgCGatewayAddress) {
      data.cu_cp2.push({
        name: 'NgC gatewayAddress',
        default: '0.0.0.0',
        target: 'Backhaul Gateway',
      });
    }
  }

  return data;
};

//----------Identity API's----------//
export const getIdentities = async (payloadData: string) => {
  const { data } = await apiClient.get('/rad/config/integration/identities/get', {
    params: {
      deployment_name: payloadData,
    },
  });
  return data;
};

export const postIdentity = async (simpleViewIdentityData: any) => {
  const { data } = await apiClient.post('/rad/config/integration/identities/set', simpleViewIdentityData);
  return data;
};

//----------RU API's----------//
export const getDeploymentRus = async (payloadData: string) => {
  const { data } = await apiClient.get('/rad/config/rus/deployment', {
    params: {
      deployment_name: payloadData,
    },
  });
  return data;
};

export const getAllRus = async () => {
  const { data } = await apiClient.get('/rad/config/rus/all');
  return data;
};

export const getAllRusManifest = async () => {
  const { data } = await apiClient.get('/inv/manifest/ru');
  return data;
};

export const postSetDeploymentRus = async (payloadData: any) => {
  const { data } = await apiClient.post(`/rad/config/rus/deployment`, payloadData);
  return data;
};

//----------RF use cases----------//
export const getRfUseCases = async () => {
  const { data } = await apiClient.get('/rad/config/rus/use_cases');
  return data;
};
