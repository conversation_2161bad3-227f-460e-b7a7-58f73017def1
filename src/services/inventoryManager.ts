import _ from 'lodash';
import {
  Cell,
  CellRequest,
  Contact,
  Country,
  ManagerProps,
  Node,
  NodeComponents,
  NodeRequest,
  Plmn,
  Region,
  Site,
  StreetCellConfiguration,
  UpdateCellRequest,
  UpdateNodeRequest,
} from '../types/InventoryManager.type';
import { apiClient } from './httpCommon';
import { ComponentTypeEnum } from './types';
import { CreateRegionSchema } from '../pages/SiteManager/schema';
import { NetworkManifestForm } from '../pages/Manifests/createNetworkManifest/schema';
import { LIFE_CYCLE } from '../data/constants';

///----------Site API's----------//
export const getSiteList = async (): Promise<Site[]> => {
  const { data } = await apiClient.get('/inv/ref/sites');
  return data;
};

export const getSiteById = async (id: Site['site_id']): Promise<Site> => {
  const { data } = await apiClient.get(`/inv/ref/sites/${id}`);
  return data;
};

export const updateSiteById = async ({
  site_id,
  site,
}: {
  site_id: Site['site_id'];
  site: Omit<Site, 'region_id'>;
}): Promise<Site> => {
  const { data } = await apiClient.patch(`/inv/ref/sites/${site_id}`, site);
  return data;
};

export const getSiteContactList = async (id: Site['site_id']): Promise<Contact[]> => {
  const { data } = await apiClient.get(`/inv/ref/sites/${id}/contacts`);
  return data;
};

export const postSite = async (site: Pick<Site, 'name' & 'address' & 'region_code' & 'country_code'>) => {
  const response = await apiClient.post('/inv/ref/sites', site);
  return response.data;
};

export const deleteSite = async (site_id: Site['site_id']) => {
  const { data } = await apiClient.delete(`/inv/ref/sites/${site_id}`);
  return data;
};

//----------Region API's----------//
export const getRegionList = async (): Promise<Region[]> => {
  const { data } = await apiClient.get('/inv/ref/regions');
  return data;
};

export const getRegionById = async (id: Region['region_id']): Promise<Region> => {
  const { data } = await apiClient.get(`/inv/ref/regions/${id}`);
  return data;
};
export const getRegionStatsList = async (cc?: string, lifecycle?: LIFE_CYCLE | null): Promise<Region[]> => {
  const byCountryCode = cc ? `?cc=${encodeURIComponent(cc)}` : '';
  const byLifecycle = lifecycle != null ? `${byCountryCode ? '&' : '?'}lifecycle=${encodeURIComponent(lifecycle)}` : '';

  const url = `/inv/stats/regions${byCountryCode}${byLifecycle}`;
  const { data } = await apiClient.get(url);
  return data;
};

export const postRegion = async (region: CreateRegionSchema) => {
  const { data } = await apiClient.post('/inv/ref/regions', region);
  return data;
};

export const updateRegionByRegionCode = async ({
  region_code,
  region,
}: {
  region_code: Region['region_code'];
  region: Omit<Region, 'region_code'>;
}) => {
  const { data } = await apiClient.patch(`/inv/ref/regions/${region_code}`, region);
  return data;
};

export const deleteRegion = async (region_code: Region['region_code']) => {
  const { data } = await apiClient.delete(`/inv/ref/regions/${region_code}`);
  return data;
};

//----------Country API's----------//
export const getCountryList = async (): Promise<Country[]> => {
  const { data } = await apiClient.get('/inv/stats/countries');
  return data;
};

//----------Cell API's----------//
export const getCellList = async (cell_ref?: string, cellLimit?: number, lifecycle?: LIFE_CYCLE | null) => {
  const byCellRef = cell_ref ? `&cell_ref=${encodeURIComponent(cell_ref)}` : '';
  const params: {
    limit: number;
    offset: number;
    lifecycle?: LIFE_CYCLE;
  } = {
    limit: 1000,
    offset: cellLimit ? cellLimit - 1000 : 0,
  };

  if (lifecycle != null) {
    params.lifecycle = lifecycle;
  }

  const { data } = await apiClient.get(`/inv/cells?page=1${byCellRef}`, { params });

  return data;
};

export const getCalculateCellRef = async (
  site_id: Site['site_id'],
  oran_split: string,
  placement?: string
): Promise<{
  cell_ref: string;
}> => {
  const ran_type = oran_split === 'eNodeB' ? '4G' : '5G';
  let params = `site_id=${site_id}&ran_type=${ran_type}`;
  if (placement) {
    params += `&placement=${placement}`;
  }
  const { data } = await apiClient.get(`/inv/cells/ref?${params}`);
  return data;
};
export const postCell = async (cell: CellRequest): Promise<Cell> => {
  const { data } = await apiClient.post('/inv/cells', cell);
  return data;
};

export const updateCell = async ({ cell_ref, cell }: { cell_ref: string; cell: UpdateCellRequest }): Promise<Cell> => {
  const { data } = await apiClient.patch(`/inv/cells/${cell_ref}`, cell);
  return data;
};

export const deleteCell = async (cell_ref: string) => {
  const { data } = await apiClient.delete(`/inv/cells/${cell_ref}`);
  return data;
};
//----------Node API's----------//
export const getNodeList = async (node_id?: string, nodeLimit?: number, lifecycle?: LIFE_CYCLE | null) => {
  const byNodeId = node_id ? `&node_id=${encodeURIComponent(node_id)}` : '';

  const params: {
    limit: number;
    offset: number;
    lifecycle?: LIFE_CYCLE;
  } = {
    limit: 1000,
    offset: nodeLimit ? nodeLimit - 1000 : 0,
  };

  if (lifecycle != null) {
    params.lifecycle = lifecycle;
  }

  const { data } = await apiClient.get(`/inv/nodes?page=1${byNodeId}`, { params });

  return data;
};
export const getNodeData = async (node_id?: string): Promise<Node[]> => {
  const { data } = await apiClient.get(`/inv/nodes?node_ids=${node_id}`);
  return data;
};
export const getNodesForCell = async (cell_ref: string): Promise<Node[]> => {
  const { data } = await apiClient.get(`/inv/cells/nodes?cell_ref=${cell_ref}`);
  return data;
};

export const postNode = async ({ cell_ref, node }: { cell_ref: string; node: NodeRequest }): Promise<Node> => {
  const { data } = await apiClient.post(`/inv/cells/${cell_ref}/nodes`, node);
  return data;
};

export const createNode = async (node: NodeRequest): Promise<Node> => {
  const { data } = await apiClient.post(`/inv/nodes`, node);
  return data;
};

export const getNodeComponentsListByNodeId = async (node_id: string): Promise<NodeComponents[]> => {
  const { data } = await apiClient.get(`/inv/nodes/${node_id}/components`);
  return data;
};

export const updateStreetCellConfiguration = async (
  nodeId: string,
  streetCellConfiguration: StreetCellConfiguration
): Promise<StreetCellConfiguration[]> => {
  const { data } = await apiClient.post(`/inv/nodes/${nodeId}/components/streetcell`, streetCellConfiguration);
  return data;
};
export const updateNode = async ({ node_id, node }: { node_id: string; node: UpdateNodeRequest }): Promise<Node> => {
  const { data } = await apiClient.patch(`/inv/cells/nodes/${node_id}`, node);
  return data;
};

//NOTE: Which delete node is this???
export const deleteNode = async (cell_ref: string, node_id: string) => {
  await updateNode({ node_id, node: { site_id: 0 } });
  const { data } = await apiClient.delete(`/inv/cells/${cell_ref}/nodes/${node_id}`);
  return data;
};

export const deleteNodeFromNodeList = async (node_id: string) => {
  const { data } = await apiClient.delete(`/inv/nodes/${node_id}`);
  return data;
};

export const addNodeToCell = async (cell_ref: string, node_id: string) => {
  const { data } = await apiClient.patch(`/inv/cells/${cell_ref}/nodes/${node_id}`);
  return data;
};

export const deleteNodeFromCell = async (cell_ref: string, node_id: string) => {
  const { data } = await apiClient.delete(`/inv/cells/${cell_ref}/nodes/${node_id}`);
  return data;
};

// Manifest API's
export const getStreetCellManifestData = async (cell_serial_no: string) => {
  const { data } = await apiClient.get(`/inv/manifest/streetcell?cell_serial_no=${cell_serial_no}&page=1`);
  return data[0];
};

export const getServerManifestData = async (server_id: string) => {
  const { data } = await apiClient.get(`/inv/manifest/server?server_id=${server_id}&page=1&limit=1000`);
  return data[0];
};

export const getNetworkManifestData = async (server_id: string) => {
  const { data } = await apiClient.get(`/inv/manifest/network?network_id=${server_id}&page=1&limit=1000`);
  return data[0];
};

export const getAirspan4GManifestData = async (serial_no: string) => {
  const { data } = await apiClient.get(`/inv/manifest/airspan4g?serial_no=${serial_no}&page=1&limit=1000`);
  return data[0];
};

export const getClusterManifestData = async (serial_no: string) => {
  const { data } = await apiClient.get(`/inv/manifest/cluster?cluster_id=${serial_no}&page=1&limit=1000`);

  return data[0];
};

export const getPodManifestData = async (serial_no: string) => {
  const { data } = await apiClient.get(`/inv/manifest/pods?pod_name=${serial_no}&page=1&limit=1000`);
  return data[0];
};

export const getPowerManifestData = async (serial_no: string) => {
  const { data } = await apiClient.get(`/inv/manifest/power?serial_no=${serial_no}&page=1&limit=1000`);
  return data[0];
};

export const getRadioManifestData = async (serial_no: string) => {
  const { data } = await apiClient.get('/inv/manifest/ru', {
    params: {
      serial_no: serial_no, // Will be properly encoded
      page: 1,
      limit: 1000,
    },
  });
  return data[0];
};

// export const getDeployedNodeList = async ({
//   deployed = false,
// }: {
//   deployed?: boolean;
// }): Promise<Node[]> => {
//   const { data } = await apiClient.get('/inv/manifest/nodes?page=1&limit=20', {
//     params: {
//       deployed,
//     },
//   });
//   return data;
// };

export const getDeployedNodeList = async ({
  manifest_type,
  deployable,
  node_type,
  checkNode,
}: {
  manifest_type?: string;
  deployable?: boolean;
  node_type?: string;
  checkNode?: boolean;
}): Promise<Node[]> => {
  let params: { [key: string]: any } = {};

  if (node_type) {
    params = { node_type, ...(deployable !== undefined && { deployable }) };
  } else if (manifest_type) {
    params = { manifest_type, ...(deployable !== undefined && { deployable }) };
  }
  if (checkNode) {
    params = { ...params, has_node: false };
  }

  const { data } = await apiClient.get(`/inv/manifest/nodes`, {
    params,
  });
  return data;
};

export const getManager = async (manager_instance: string): Promise<ManagerProps[]> => {
  const { data } = await apiClient.get(`inv/manifest/managers/?page=1&manager_instance=${manager_instance}`);
  return data;
};

export const getNodesManifestData = async (manifestLimit?: number, node_serial_no?: string) => {
  const params: { [key: string]: any } = {
    limit: manifestLimit || 1000,
    offset: manifestLimit ? manifestLimit - 1000 : 0,
  };
  if (node_serial_no) {
    params['node_serial_no'] = node_serial_no;
  }
  const { data } = await apiClient.get('/inv/manifest/nodes?page=1', {
    params,
  });

  return data;
};

export const createNetworkManifest = async (data: NetworkManifestForm) => {
  const response = await apiClient.post('/inv/manifest/network', data);
  return response.data;
};

//----------Plmn API's----------//
export const getPlmnList = async (countryCode?: string): Promise<Plmn[]> => {
  const byCountryCode = countryCode ? `&cc=${countryCode}` : '';
  const { data } = await apiClient.get(`/inv/ref/plmn?page=1${byCountryCode}`, {
    params: {
      limit: 1000,
    },
  });
  return data;
};

export const postPlmns = async (plmns: Pick<Plmn, 'mcc' | 'mnc'>, cell_ref: string) => {
  const response = await apiClient.post(`/inv/cells/${cell_ref}/plmn`, plmns);
  return response.data;
};
export const deletePlmns = async (cell_ref: string, mcc: Plmn['mcc'], mnc: Plmn['mnc']) => {
  const { data } = await apiClient.delete(`/inv/cells/${cell_ref}/plmn`, {
    params: {
      mcc,
      mnc,
    },
  });
  return data;
};

export const getStatusHistory = async ({
  cell_ref,
  node_id,
  limit,
  component_type,
  component_id,
}: {
  cell_ref?: string;
  node_id?: string;
  component_type?: ComponentTypeEnum;
  component_id?: string;
  limit: number;
}) => {
  // if (_.isUndefined(cell_ref) && _.isUndefined(node_id) && _.isUndefined(component_id)) return;

  if (!_.isUndefined(cell_ref)) {
    const { data } = await apiClient.get(`/inv/status/history?cell_ref=${cell_ref}&limit=${limit}`);
    return data;
  }

  if (!_.isUndefined(node_id)) {
    const { data } = await apiClient.get(`/inv/status/history?node_id=${node_id}&limit=${limit}`);
    return data;
  }

  if (!_.isUndefined(component_id)) {
    const { data } = await apiClient.get(
      `/inv/status/history?component_id=${component_id}&component_type=${component_type}&limit=${limit}`
    );
    return data;
  }
};

//----------CBSD API's----------//
export const getInvCluster = async (): Promise<any> => {
  const { data } = await apiClient.get('/inv/manifest/cluster');
  return data;
};

export const getRuManifest = async (): Promise<any> => {
  const { data } = await apiClient.get('/inv/manifest/ru');
  return data;
};

export const getVersionDevicesData = async (manifestLimit?: number) => {
  const { data } = await apiClient.get('/inv/manifest/versions/?page=1', {
    params: {
      limit: 1000,
      offset: manifestLimit ? manifestLimit - 1000 : 0,
    },
  });
  return data;
};
