import { defineConfig } from 'cypress';

export default defineConfig({
  fileServerFolder: 'dist',
  fixturesFolder: 'cypress/fixtures',
  e2e: {
    baseUrl: 'http://localhost:8081/',
    setupNodeEvents(on, config) {
      // for logging
      on('task', {
        log(message) {
          console.log(message);
          return null;
        },
      });
    },
  },
  trashAssetsBeforeRuns: true,
  experimentalMemoryManagement: true,
  viewportHeight: 900,
  viewportWidth: 1200,
  experimentalStudio: true,
});
