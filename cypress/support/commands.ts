/* eslint-disable @typescript-eslint/no-namespace */
/// <reference types="cypress" />
// ***********************************************
// This example commands.ts shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
//
//
// -- This is a parent command --
// Cypress.Commands.add('login', (email, password) => { ... })
//
//
// -- This is a child command --
// Cypress.Commands.add('drag', { prevSubject: 'element'}, (subject, options) => { ... })
//
//
// -- This is a dual command --
// Cypress.Commands.add('dismiss', { prevSubject: 'optional'}, (subject, options) => { ... })
//
//
// -- This will overwrite an existing command --
// Cypress.Commands.overwrite('visit', (originalFn, url, options) => { ... })
//
// declare global {
//   namespace Cypress {
//     interface Chainable {
//       login(email: string, password: string): Chainable<void>
//       drag(subject: string, options?: Partial<TypeOptions>): Chainable<Element>
//       dismiss(subject: string, options?: Partial<TypeOptions>): Chainable<Element>
//       visit(originalFn: CommandOriginalFn, url: string, options: Partial<VisitOptions>): Chainable<Element>
//     }
//   }
// }

Cypress.Commands.add('system_health', () => {
  cy.request({
    method: 'GET',
    url: '/nms/system_health',
  }).its('body');
  // .then((response) => {
  //   // console.log('🚀 ~ file: commands.ts:47 ~ .then ~ response:', response);
  //   // expect(response).to.have.property('airspan-acp-agent');
  // });
});

Cypress.Commands.add('useLogin', () => {
  cy.session('login', () => {
    cy.visit('/'); // Visit the app to ensure the local storage is accessible

    // Set the JWT token in local storage
    // The hash will be different each day. Login to the app and copy the hash from local strorage
    cy.window().then((win) => {
      win.localStorage.setItem(
        'dw_authToken',
        'eyJhbGciOiJSUzI1NiIsImtpZCI6IjMzMEZEQ0M2RDA0QUMzN0M2NDdCN0M3NjBBQ0IxODQxMEJENjUwOEZSUzI1NiIsInR5cCI6ImF0K2p3dCIsIng1dCI6Ik13X2N4dEJLdzN4a2UzeDJDc3NZUVF2V1VJOCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.KxAeEsGL7ocB30x-_1wdAyI8k9rSAB76OK9m_khfTD4-7vIOesOzkV5pJfUCeG7XucR2BTOvFBE2U_T83tb2L2f17NPt8aIbRAU_vIWfi-Fqt4QaVn6mcQ89MSSUCtRJes_5_HGqRJbNu0PzF7lvclkq07HHg7GLayh6wEeBZyWm8ktwv0UrhrCvJCOGh6PNyyeffmANAwNbrVpNLbV98aeS63QJYy62JFtphbO-XcUF96botDHaEBpCiZ9pBuJGYUyzjXA_5oKRp4lw12lJZ3ksNWA-J0Z-3_WM1FGjZVCXQZh2alGCFq9OQKBErc2-73-60W1m6nEyqdVeE2B6Qw'
      );
    });
  });
});

Cypress.Commands.add('navigateToSimpleView', () => {
  cy.get('.chakra-container').find('h2').should('contain', 'O-RAN DU/CU Manager');
  cy.get('.chakra-container')
    .find('[data-testid="simple-view"]')
    .should('exist')
    .within(() => {
      cy.get('button').click();
    });
});

Cypress.Commands.add('navigateToCbrs', () => {
  cy.get('.chakra-container').find('h2').should('contain', 'O-RAN DU/CU Manager');
  cy.get('.chakra-container')
    .find('[data-testid="cbrs-mangement"]')
    .should('exist')
    .within(() => {
      cy.get('button').click();
    });
});

Cypress.Commands.add('navigateToAdvancedView', () => {
  cy.get('.chakra-container').find('h2').should('contain', 'O-RAN DU/CU Manager');
  cy.get('.chakra-container')
    .find('[data-testid="advanced-view"]')
    .should('exist')
    .within(() => {
      cy.get('button').click();
    });
});

declare global {
  namespace Cypress {
    interface Chainable {
      system_health(): void;
      useLogin(): void;
      navigateToSimpleView(): void;
      navigateToAdvancedView(): void;
      navigateToCbrs(): void;
    }
  }
}

export {};
