//TODO: add these tests
/* eslint-disable cypress/no-unnecessary-waiting */
/* eslint-disable cypress/require-data-selectors */

/// <reference types="cypress" />
import 'cypress-real-events';

const createCellForm = '/cell-overview';

describe('Edit a cell and other nodes', () => {
  beforeEach(() => {
    const paths = {
      regions: 'cellCreation/AddCell/mockData/regions.json',
      sites: 'cellCreation/AddCell/mockData/sites.json',
      cellReference: 'cellCreation/AddCell/mockData/cellReference.json',
      postCellResponse: 'cellCreation/AddCell/mockData/postCellResponse.json',
      postNodeResponse: 'cellCreation/AddCell/mockData/postNodeResponse.json',
      serverNodes: 'cellCreation/AddCell/mockData/serverNodes.json',
      switchNodes: 'cellCreation/AddCell/mockData/switchNodes.json',
      streetCell: 'cellCreation/AddCell/mockData/streetCell.json',
    };

    // Logging paths to verify
    cy.task('log', 'Validating fixture paths...');
    cy.task('log', JSON.stringify(paths));

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/inv/ref/regions', {
      fixture: paths.regions,
    }).as('getRegions');

    cy.intercept('GET', '**/inv/ref/sites', {
      fixture: paths.sites,
    }).as('getSites');

    cy.intercept('GET', '**/inv/manifest/nodes?manifest_type=server&deployable=true', {
      fixture: paths.serverNodes,
    }).as('getServerNodes');

    cy.intercept('GET', '**/inv/manifest/nodes?node_type=Switch&deployable=true', {
      fixture: paths.switchNodes,
    }).as('getSwitchNodes');

    cy.intercept('GET', '**/inv/manifest/nodes?manifest_type=streetcell&deployable=true', {
      fixture: paths.streetCell,
    }).as('getStreetCellNodes');

    cy.intercept('GET', '**/inv/manifest/nodes?manifest_type=network&deployable=true', {
      fixture: paths.serverNodes,
    }).as('getNetworkNodes');

    cy.intercept('GET', '**/inv/cells/ref?site_id=*', {
      fixture: paths.cellReference,
    }).as('getCellReference');

    cy.intercept('PATCH', '**/inv/cells/**', {
      statusCode: 200,
      body: { success: true },
    }).as('updateCell');

    cy.intercept('PATCH', '**/inv/cells/nodes/**', {
      statusCode: 200,
      body: { success: true },
    }).as('updateNode');

    // Ensure system health and login before visiting the page
    cy.system_health();
    cy.useLogin();
    cy.visit(createCellForm);
    cy.log('Visited create cell form');
    cy.wait(2000);
    cy.get('table.chakra-table').should('be.visible');
    cy.get('table.chakra-table tbody tr').first().find('[data-testid="cell-main-table-cell-config-icon"]').click();
    cy.get('table.chakra-table tbody tr').first().find('[data-testid="edit-cell"]').click();
  });

  it('edit a lifecycle and orientation of a cell', () => {
    cy.get('form select#lifecycle').should('be.visible').select('OPERATIONAL');
    cy.wait('@updateCell').its('response.statusCode').should('eq', 200);
    cy.get('form select#orientation').should('be.visible').select('South West');
    cy.wait('@updateCell').its('response.statusCode').should('eq', 200);
  });

  it('edit node options of a street cell', () => {
    cy.get('form select#streetCellNodesLifecycle').should('be.visible').select('STAGING');
    cy.wait('@updateNode').its('response.statusCode').should('eq', 200);
    cy.get('form select#streetCellNodesOrientation').should('be.visible').select('South');
    cy.wait('@updateNode').its('response.statusCode').should('eq', 200);
  });
});

export {};
