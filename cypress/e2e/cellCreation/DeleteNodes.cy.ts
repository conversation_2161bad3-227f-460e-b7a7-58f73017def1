/* eslint-disable cypress/no-unnecessary-waiting */
/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />
import 'cypress-real-events';

const cellOverview = '/cell-overview';

describe('Delete cell from cell overview', () => {
  beforeEach(() => {
    const paths = {
      cells: 'cellCreation/DeleteNode/cells.json',
      count: 'cellCreation/DeleteNode/count.json',
      nodeCellRef: 'cellCreation/DeleteNode/nodeCellRef.json',
    };
    cy.intercept('GET', '**/inv/cells**', { fixture: paths.cells }).as('getCells');
    cy.intercept('DELETE', '**/inv/cells/**', {
      statusCode: 200,
      body: { success: true },
    }).as('deleteCell');
    cy.intercept('GET', '**/mc/alarms/active/count/**', { fixture: paths.count }).as('getAlarmsCount');
    cy.intercept('GET', '**/inv/cells/nodes/**', { fixture: paths.nodeCellRef }).as('nodeCellRef');

    cy.system_health();
    cy.useLogin();
    cy.visit(cellOverview);
  });

  it('delete a cell', () => {
    cy.get('table.chakra-table').should('be.visible');

    cy.get('table.chakra-table tbody tr')
      .first()
      .within(() => {
        cy.get('[data-testid="cell-main-table-cell-config-icon"]').click();
        cy.get('[data-testid="delete-cell"]').click({ force: true });
      });

    cy.get('[data-testid="cell-config-modal"]').contains('Delete Cell');
    cy.get('[data-testid="cell-config-modal"]').find('button').contains('Delete').click();

    cy.wait('@deleteCell').its('response.statusCode').should('eq', 200);
  });
});
