/* eslint-disable cypress/no-unnecessary-waiting */
/* eslint-disable cypress/require-data-selectors */

/// <reference types="cypress" />
import 'cypress-real-events';

const createCellForm = '/cell-overview/create';

describe('Add a split 6 street cell and other nodes', () => {
  beforeEach(() => {
    const paths = {
      regions: 'cellCreation/AddCell/mockData/regions.json',
      sites: 'cellCreation/AddCell/mockData/sites.json',
      cellReference: 'cellCreation/AddCell/mockData/cellReference.json',
      postCellResponse: 'cellCreation/AddCell/mockData/postCellResponse.json',
      postNodeResponse: 'cellCreation/AddCell/mockData/postNodeResponse.json',
      serverNodes: 'cellCreation/AddCell/mockData/serverNodes.json',
      switchNodes: 'cellCreation/AddCell/mockData/switchNodes.json',
      streetCell: 'cellCreation/AddCell/mockData/streetCell.json',
    };

    // Logging paths to verify
    cy.task('log', 'Validating fixture paths...');
    cy.task('log', JSON.stringify(paths));

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/inv/ref/regions', {
      fixture: paths.regions,
    }).as('getRegions');

    cy.intercept('GET', '**/inv/ref/sites', {
      fixture: paths.sites,
    }).as('getSites');

    cy.intercept('GET', '**/inv/manifest/nodes?manifest_type=server&deployable=true', {
      fixture: paths.serverNodes,
    }).as('getServerNodes');

    cy.intercept('GET', '**/inv/manifest/nodes?node_type=Switch&deployable=true', {
      fixture: paths.switchNodes,
    }).as('getSwitchNodes');

    cy.intercept('GET', '**/inv/manifest/nodes?manifest_type=streetcell&deployable=true', {
      fixture: paths.streetCell,
    }).as('getStreetCellNodes');

    cy.intercept('GET', '**/inv/manifest/nodes?manifest_type=network&deployable=true', {
      fixture: paths.serverNodes,
    }).as('getNetworkNodes');

    cy.intercept('GET', '**/inv/cells/ref?site_id=*', {
      fixture: paths.cellReference,
    }).as('getCellReference');

    // Skip actual POST requests and simulate success responses
    cy.intercept('POST', '**/inv/cells*', {
      statusCode: 200,
      body: { success: true },
    }).as('postCell');

    cy.intercept('POST', '**/inv/cells/*', {
      statusCode: 200,
      body: { success: true },
    }).as('postNode');

    // Ensure system health and login before visiting the page
    cy.system_health();
    cy.useLogin();
    cy.visit(createCellForm);
    cy.log('Visited create cell form');
  });

  it('create a split-6 cell with only 1 streetcell', () => {
    // Wait for the initial intercepts
    cy.wait('@getRegions', { timeout: 15000 });
    // cy.wait('@getServerNodes', { timeout: 15000 });

    // Ensure the region dropdown is populated
    cy.get('form select#region')
      .should('be.visible')
      .then(($select) => {
        const options = $select.find('option');
        cy.task('log', `Found ${options.length} options in region dropdown.`);
        cy.task(
          'log',
          'Region options: ' +
            options
              .map((index, opt) => opt.innerText)
              .get()
              .join(', ')
        );
        expect(options.length).to.be.greaterThan(1); // Adjust if you expect more or fewer options
      });

    // Select the region first
    cy.get('form select#region').select('Marlow Development United Kingdom');

    // Wait for the site dropdown to be populated
    cy.wait('@getSites', { timeout: 15000 });

    // Ensure site dropdown is populated
    cy.get('form select#site')
      .should('be.visible')
      .then(($select) => {
        const options = $select.find('option');
        cy.task('log', `Found ${options.length} options in site dropdown.`);
        cy.task(
          'log',
          'Site options: ' +
            options
              .map((index, opt) => opt.innerText)
              .get()
              .join(', ')
        );
        expect(options.length).to.be.greaterThan(1); // Adjust if you expect more or fewer options
      });

    // Select the site to trigger the API call
    cy.get('form select#site').select('Ansir Test');

    // Wait for the dynamic API call to retrieve the cell reference
    cy.wait('@getCellReference', { timeout: 15000 }).then((interception) => {
      cy.task('log', 'Intercepted cellReference request: ' + JSON.stringify(interception));
    });

    // cy.get('form label').contains('Cell Reference');
    // cy.wait(1000);

    cy.get('form #cell_ref')
      .should('exist')
      .then((cellRef) => {
        cy.get('form button').contains('Regenerate');
        cy.get('form select#oran_split').should('be.visible').select('Split 6');
        cy.get('form select#lifecycle').should('be.visible').select('COMMISSIONING');
        cy.get('form select#orientation').select('Omni-directional');
        cy.get('form button').contains('Add Node').click();
        cy.get('form select#serial_number')
          .children('option')
          .eq(1)
          .then((element) => {
            const value = element.val() as string | number;
            if (typeof value === 'string' || typeof value === 'number') {
              cy.get('select#serial_number').select(value.toString());
            }
          });
        cy.get('[data-testid="create-cell-button"]').click();
        cy.wait('@postCell').its('response.statusCode').should('eq', 200);
      });
  });

  it('create a split-6 cell with only 1 streetcell, 1 server and 1 switch', () => {
    cy.get('select#region').should('be.visible').select('Marlow Development United Kingdom');
    cy.get('form button').contains('Create site');
    cy.get('form select#site').should('be.visible').select('Ansir Test');
    cy.get('form label').contains('Cell Reference');
    cy.wait(1000);
    cy.get('form button').contains('Regenerate');
    cy.get('form select#oran_split').should('be.visible').select('Split 6');
    cy.get('form select#lifecycle').should('be.visible').select('COMMISSIONING');
    cy.get('form select#orientation').should('be.visible').select('Omni-directional');
    cy.get('form button').contains('Add Node').click();
    cy.get('form button').contains('Add Server').click();
    cy.get('form button').contains('Add Switch').click();
    cy.get('form button').contains('Create');
    cy.get('span').contains('Create another cell?');
    cy.get('button').contains('Back');
  });

  it('create a split-6 cell with 6 streetcell', () => {
    cy.get('select#region').should('be.visible').select('Marlow Development United Kingdom');
    cy.get('form button').contains('Create site');
    cy.get('form select#site').should('be.visible').select('Ansir Test');
    cy.get('form label').contains('Cell Reference');
    cy.wait(1000);
    cy.get('form button').contains('Regenerate');
    cy.get('form select#oran_split').should('be.visible').select('Split 6');
    cy.get('form select#lifecycle').should('be.visible').select('COMMISSIONING');
    cy.get('form select#orientation').should('be.visible').select('Omni-directional');
    cy.get('form button').contains('Add Node');
    cy.get('form button').contains('Add Server');
    cy.get('form button').contains('Add Switch');
    cy.get('form button').contains('Create');
    cy.get('span').contains('Create another cell?');
    cy.get('button').contains('Back');
  });

  it('create a split-6 cell with 6 streetcell and multiple servers if available', () => {
    cy.get('select#region').should('be.visible').select('Marlow Development United Kingdom');
    cy.get('form button').contains('Create site');
    cy.get('form select#site').should('be.visible').select('Ansir Test');
    cy.get('form label').contains('Cell Reference');
    cy.wait(1000);
    cy.get('form button').contains('Regenerate');
    cy.get('form select#oran_split').should('be.visible').select('Split 6');
    cy.get('form select#lifecycle').should('be.visible').select('COMMISSIONING');
    cy.get('form select#orientation').should('be.visible').select('Omni-directional');
    cy.get('form button').contains('Add Node');
    cy.get('form button').contains('Add Server');
    cy.get('form button').contains('Add Switch');
    cy.get('form button').contains('Create');
    cy.get('span').contains('Create another cell?');
    cy.get('button').contains('Back');
  });

  it('create a split-6 cell with 6 streetcell, multiple servers if available and multiple switches if available', () => {
    cy.get('select#region').should('be.visible').select('Marlow Development United Kingdom');
    cy.get('form button').contains('Create site');
    cy.get('form select#site').should('be.visible').select('Ansir Test');
    cy.get('form label').contains('Cell Reference');
    cy.wait(1000);
    cy.get('form button').contains('Regenerate');
    cy.get('form select#oran_split').should('be.visible').select('Split 6');
    cy.get('form select#lifecycle').should('be.visible').select('COMMISSIONING');
    cy.get('form select#orientation').should('be.visible').select('Omni-directional');
    cy.get('form button').contains('Add Node');
    cy.get('form button').contains('Add Server');
    cy.get('form button').contains('Add Switch');
    cy.get('form button').contains('Create');
    cy.get('span').contains('Create another cell?');
    cy.get('button').contains('Back');
  });
});

describe('Add a G Node B street cell and other nodes', () => {
  beforeEach(() => {
    const paths = {
      regions: 'cellCreation/AddCell/mockData/regions.json',
      sites: 'cellCreation/AddCell/mockData/sites.json',
      cellReference: 'cellCreation/AddCell/mockData/cellReference.json',
      postCellResponse: 'cellCreation/AddCell/mockData/postCellResponse.json',
      postNodeResponse: 'cellCreation/AddCell/mockData/postNodeResponse.json',
      serverNodes: 'cellCreation/AddCell/mockData/serverNodes.json',
      switchNodes: 'cellCreation/AddCell/mockData/switchNodes.json',
      streetCell: 'cellCreation/AddCell/mockData/streetCell.json',
    };

    // Logging paths to verify
    cy.task('log', 'Validating fixture paths...');
    cy.task('log', JSON.stringify(paths));

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/inv/ref/regions', {
      fixture: paths.regions,
    }).as('getRegions');

    cy.intercept('GET', '**/inv/ref/sites', {
      fixture: paths.sites,
    }).as('getSites');

    cy.intercept('GET', '**/inv/manifest/nodes?manifest_type=server&deployable=true', {
      fixture: paths.serverNodes,
    }).as('getServerNodes');

    cy.intercept('GET', '**/inv/manifest/nodes?node_type=Switch&deployable=true', {
      fixture: paths.switchNodes,
    }).as('getSwitchNodes');

    cy.intercept('GET', '**/inv/manifest/nodes?manifest_type=streetcell&deployable=true', {
      fixture: paths.streetCell,
    }).as('getStreetCellNodes');

    cy.intercept('GET', '**/inv/manifest/nodes?manifest_type=network&deployable=true', {
      fixture: paths.serverNodes,
    }).as('getNetworkNodes');

    cy.intercept('GET', '**/inv/cells/ref?site_id=*', {
      fixture: paths.cellReference,
    }).as('getCellReference');

    // Skip actual POST requests and simulate success responses
    cy.intercept('POST', '**/inv/cells*', {
      statusCode: 200,
      body: { success: true },
    }).as('postCell');

    cy.intercept('POST', '**/inv/cells/*', {
      statusCode: 200,
      body: { success: true },
    }).as('postNode');

    cy.system_health();
    cy.useLogin();
    cy.visit(createCellForm);
  });

  it('display GNodeB and available nodes', () => {
    cy.get('select#region').should('be.visible').select('Marlow Development United Kingdom');
    cy.get('form button').contains('Create site');
    cy.get('form select#site').should('be.visible').select('Ansir Test');
    cy.get('form label').contains('Cell Reference');
    cy.wait(1000);

    cy.get('form select#oran_split').should('be.visible').select('gNodeB');
    cy.get('form select#lifecycle').should('be.visible').select('COMMISSIONING');
    cy.get('p').contains('G Node B');
    cy.get('p').contains('Server');
    cy.get('p').contains('Switch');
    cy.get('form button').contains('Add Server');
    cy.get('form button').contains('Add Switch');
    cy.get('form button').contains('Create');
    cy.get('span').contains('Create another cell?');
    cy.get('button').contains('Back');
  });
});

describe('Create a site', () => {
  beforeEach(() => {
    const paths = {
      regions: 'cellCreation/AddCell/mockData/regions.json',
      sites: 'cellCreation/AddCell/mockData/sites.json',
      cellReference: 'cellCreation/AddCell/mockData/cellReference.json',
      postCellResponse: 'cellCreation/AddCell/mockData/postCellResponse.json',
      postNodeResponse: 'cellCreation/AddCell/mockData/postNodeResponse.json',
      serverNodes: 'cellCreation/AddCell/mockData/serverNodes.json',
      switchNodes: 'cellCreation/AddCell/mockData/switchNodes.json',
      streetCell: 'cellCreation/AddCell/mockData/streetCell.json',
    };

    // Logging paths to verify
    cy.task('log', 'Validating fixture paths...');
    cy.task('log', JSON.stringify(paths));

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/inv/ref/regions', {
      fixture: paths.regions,
    }).as('getRegions');

    cy.intercept('GET', '**/inv/ref/sites', {
      fixture: paths.sites,
    }).as('getSites');

    cy.intercept('GET', '**/inv/manifest/nodes?manifest_type=server&deployable=true', {
      fixture: paths.serverNodes,
    }).as('getServerNodes');

    cy.intercept('GET', '**/inv/manifest/nodes?node_type=Switch&deployable=true', {
      fixture: paths.switchNodes,
    }).as('getSwitchNodes');

    cy.intercept('GET', '**/inv/manifest/nodes?manifest_type=streetcell&deployable=true', {
      fixture: paths.streetCell,
    }).as('getStreetCellNodes');

    cy.intercept('GET', '**/inv/manifest/nodes?manifest_type=network&deployable=true', {
      fixture: paths.serverNodes,
    }).as('getNetworkNodes');

    cy.intercept('GET', '**/inv/cells/ref?site_id=*', {
      fixture: paths.cellReference,
    }).as('getCellReference');

    // Skip actual POST requests and simulate success responses
    cy.intercept('POST', '**/inv/cells*', {
      statusCode: 200,
      body: { success: true },
    }).as('postCell');

    cy.intercept('POST', '**/inv/cells/*', {
      statusCode: 200,
      body: { success: true },
    }).as('postNode');

    cy.system_health();
    cy.useLogin();
    cy.visit(createCellForm);
  });

  it('display GNodeB and available nodes', () => {
    cy.get('select#region').should('be.visible').select('Marlow Development United Kingdom');
    cy.get('form button').contains('Create site');
    cy.get('form select#site').should('be.visible').select('Ansir Test');
    cy.get('form label').contains('Cell Reference');
    cy.wait(1000);
    cy.get('form button').contains('Regenerate');
    cy.get('form select#oran_split').should('be.visible').select('gNodeB');
    cy.get('form select#lifecycle').should('be.visible').select('COMMISSIONING');
    cy.get('p').contains('G Node B');
    cy.get('p').contains('Server');
    cy.get('p').contains('Switch');
    cy.get('form button').contains('Add Server');
    cy.get('form button').contains('Add Switch');
    cy.get('form button').contains('Create');
    cy.get('span').contains('Create another cell?');
    cy.get('button').contains('Back');
  });
});

export {};
