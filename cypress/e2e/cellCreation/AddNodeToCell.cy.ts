/// <reference types="cypress" />
import 'cypress-real-events';

const cellOverview = '/cell-overview/nodes';

describe('Manage Cells Component Tests', () => {
  beforeEach(() => {
    const paths = {
      nodes: 'cellCreation/AddNodeTocell/nodes.json',
      node: 'cellCreation/AddNodeTocell/node.json',
      cells: 'cellCreation/AddNodeTocell/cells.json',
    };

    // Logging paths to verify
    cy.task('log', 'Validating fixture paths...');
    cy.task('log', JSON.stringify(paths));

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/inv/nodes?node_ids=**', { fixture: paths.node }).as('getNode');
    cy.intercept('GET', '**/inv/nodes**', { fixture: paths.nodes }).as('getNodes');
    cy.intercept('GET', '**/inv/cells**', { fixture: paths.cells }).as('getCells');

    // Mocking POST request for adding a node to a cell
    cy.intercept('PATCH', '**/inv/cells/**', {
      statusCode: 200,
      body: { success: true },
    }).as('postAddNode');

    // Mocking POST request for removing a node from a cell
    cy.intercept('DELETE', '**/inv/cells/**', {
      statusCode: 200,
      body: { success: true },
    }).as('postRemoveNode');

    cy.system_health();
    cy.useLogin();
    cy.visit(cellOverview);
  });

  it('should display Available Cells correctly', () => {
    cy.get('table.chakra-table').should('be.visible');
    cy.get('table.chakra-table tbody tr').first().find('[data-testid="cell-main-table-node-config-icon"]').click();
    cy.get('table.chakra-table tbody tr').first().find('[data-testid="add-node-to-cell"]').click();
    cy.get('[data-testid="available-cells"]').should('be.visible');
    cy.get('[data-testid="available-cells"] > .chakra-heading').should('have.text', 'Available Cells');
    cy.get('[data-testid="available-cells-table"]').should('be.visible');
  });

  it('should display Linked Cells correctly', () => {
    cy.get('table.chakra-table').should('be.visible');
    cy.get('table.chakra-table tbody tr').first().find('[data-testid="cell-main-table-node-config-icon"]').click();
    cy.get('table.chakra-table tbody tr').first().find('[data-testid="add-node-to-cell"]').click();
    cy.get('[data-testid="linked-cells"]').should('be.visible');
    cy.get('[data-testid="linked-cells"] > .chakra-heading').should('have.text', 'Linked Cells');
    cy.get('[data-testid="linked-cells-table"]').should('be.visible');
  });

  it('should allow adding a Cell to the linked list', () => {
    cy.get('table.chakra-table').should('be.visible');
    cy.get('table.chakra-table tbody tr').first().find('[data-testid="cell-main-table-node-config-icon"]').click();
    cy.get('table.chakra-table tbody tr').first().find('[data-testid="add-node-to-cell"]').click();
    cy.get('[data-testid="available-cells"]').should('be.visible');
    cy.get('[data-testid="available-cells"] > .chakra-heading').should('have.text', 'Available Cells');
    cy.get('[data-testid="available-cells-table"]').should('be.visible');
    cy.get('[data-testid="available-cells-table"]').each(($row) => {
      const operator = $row.find('td').eq(1).text();
      // if row exist
      if (operator) {
        cy.get('[data-testid="available-cells-add"]').should('be.visible');
        cy.get('[data-testid="available-cells-add"]').first().click();
        cy.get('#toast-1-title').should('be.visible');
        cy.get('#toast-1-title').should('have.text', 'Node Added.');
      }
    });
  });

  it('should allow removing a Cell from the linked list', () => {
    cy.get('table.chakra-table').should('be.visible');
    cy.get('table.chakra-table tbody tr').first().find('[data-testid="cell-main-table-node-config-icon"]').click();
    cy.get('table.chakra-table tbody tr').first().find('[data-testid="add-node-to-cell"]').click();
    cy.get('[data-testid="linked-cells"]').should('be.visible');
    cy.get('[data-testid="linked-cells"] > .chakra-heading').should('have.text', 'Linked Cells');
    cy.get('[data-testid="linked-cells-table"]').should('be.visible');
    cy.get('[data-testid="linked-cells-table"]').each(($row) => {
      const operator = $row.find('td').eq(1).text();
      // if row exist
      if (operator) {
        cy.get('[data-testid="linked-cells-remove"]').should('be.visible');
        cy.get('[data-testid="linked-cells-remove"]').first().click();
        cy.get('#toast-1-title').should('be.visible');
        cy.get('#toast-1-title').should('have.text', 'Success');
      }
    });
  });
});
