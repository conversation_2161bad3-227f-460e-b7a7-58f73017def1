/* eslint-disable cypress/no-unnecessary-waiting */
/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />
import 'cypress-real-events';

const createCellForm = '/cell-overview/create';
const cellOverview = '/cell-overview';

describe('Load cell creation form', () => {
  beforeEach(() => {
    const paths = {
      regions: 'cellCreation/AddCell/mockData/regions.json',
      sites: 'cellCreation/AddCell/mockData/sites.json',
      cellReference: 'cellCreation/AddCell/mockData/cellReference.json',
      postCellResponse: 'cellCreation/AddCell/mockData/postCellResponse.json',
      postNodeResponse: 'cellCreation/AddCell/mockData/postNodeResponse.json',
      serverNodes: 'cellCreation/AddCell/mockData/serverNodes.json',
      switchNodes: 'cellCreation/AddCell/mockData/switchNodes.json',
      streetCell: 'cellCreation/AddCell/mockData/streetCell.json',
    };

    // Logging paths to verify
    cy.task('log', 'Validating fixture paths...');
    cy.task('log', JSON.stringify(paths));

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/inv/ref/regions', {
      fixture: paths.regions,
    }).as('getRegions');

    cy.intercept('GET', '**/inv/ref/sites', {
      fixture: paths.sites,
    }).as('getSites');

    cy.intercept('GET', '**/inv/manifest/nodes?manifest_type=server&deployable=true', {
      fixture: paths.serverNodes,
    }).as('getServerNodes');

    cy.intercept('GET', '**/inv/manifest/nodes?node_type=Switch&deployable=true', {
      fixture: paths.switchNodes,
    }).as('getSwitchNodes');

    cy.intercept('GET', '**/inv/manifest/nodes?manifest_type=streetcell&deployable=true', {
      fixture: paths.streetCell,
    }).as('getStreetCellNodes');

    cy.intercept('GET', '**/inv/manifest/nodes?manifest_type=network&deployable=true', {
      fixture: paths.serverNodes,
    }).as('getNetworkNodes');

    cy.intercept('GET', '**/inv/cells/ref?site_id=*', {
      fixture: paths.cellReference,
    }).as('getCellReference');

    // Skip actual POST requests and simulate success responses
    cy.intercept('POST', '**/inv/cells*', {
      statusCode: 200,
      body: { success: true },
    }).as('postCell');

    cy.intercept('POST', '**/inv/cells/*', {
      statusCode: 200,
      body: { success: true },
    }).as('postNode');

    // Ensure system health and login before visiting the page
    cy.system_health();
    cy.useLogin();
    cy.visit(createCellForm);
    cy.log('Visited create cell form');
  });

  it('display form', () => {
    cy.get('p').contains('Create cell');
    cy.get('form').should('be.visible');
  });

  it('display split-6 and available nodes', () => {
    cy.get('form select#region').select('Marlow Development United Kingdom');
    cy.get('form button').contains('Create site');
    cy.get('form select#site').select('Ansir Test');
    cy.get('form label').contains('Cell Reference');
    cy.wait(1000);
    cy.get('form button').contains('Regenerate');
    cy.get('form select#oran_split').select('Split 6');
    cy.get('form select#lifecycle').select('COMMISSIONING');
    cy.get('form select#orientation').select('Omni-directional');
    cy.get('form p').contains('Split 6');
    cy.get('form p').contains('Server');
    cy.get('form p').contains('Switch');
    cy.get('form button').contains('Add Node');
    cy.get('form button').contains('Add Server');
    cy.get('form button').contains('Add Switch');
    cy.get('form button').contains('Create cell');
    cy.get('form span').contains('Create another cell?');
    cy.get('form button').contains('Back');
  });

  it('display GNodeB and available nodes', () => {
    cy.get('form select#region').select('Marlow Development United Kingdom');
    cy.get('form button').contains('Create site');
    cy.get('form select#site').select('Ansir Test');
    cy.get('form label').contains('Cell Reference');
    cy.wait(1000);
    cy.get('form button').contains('Regenerate');
    cy.get('form select#oran_split').select('gNodeB');
    cy.get('form select#lifecycle').select('COMMISSIONING');
    cy.get('form p').contains('G Node B');
    cy.get('form p').contains('Server');
    cy.get('form p').contains('Switch');
    cy.get('form button').contains('Add Server');
    cy.get('form button').contains('Add Switch');
    cy.get('form button').contains('Create');
    cy.get('form span').contains('Create another cell?');
    cy.get('form button').contains('Back');
  });
});

export {};
