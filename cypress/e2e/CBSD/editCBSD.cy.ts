/// <reference types="cypress" />

const CBSDUrl = '/oran-du-cu-manager/cbrs-management/cbsd';

describe('Edit CBSD Form', () => {
  beforeEach(() => {
    const paths = {
      ruManifest: 'CBSD/ruManifest.json',
      clusters: 'CBSD/clusters.json',
      search: 'CBSD/search.json',
    };

    // Logging paths to verify
    cy.task('log', 'Validating fixture paths...');
    cy.task('log', JSON.stringify(paths));

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/inv/manifest/cluster', {
      fixture: paths.clusters,
    }).as('getCluster');

    cy.intercept('GET', '**/inv/manifest/ru', {
      fixture: paths.ruManifest,
    }).as('getSerial');

    cy.intercept('GET', '**/orc/cbsd/search**', {
      fixture: paths.search,
    }).as('getCbsdDevices');

    cy.intercept('PATCH', '**/orc/cbsd/**', { statusCode: 200, body: { success: true } }).as('editCBSD');

    // Ensure system health and login before visiting the page
    cy.system_health();
    cy.useLogin();
    cy.visit(CBSDUrl);
    cy.log('Visited cbsd');
    cy.get('table.chakra-table tbody tr')
      .first()
      .within(() => {
        cy.get('[data-testid="cbsd-menu-items-button"]').click();
        cy.get('[data-testid="edit-cbsd"]').click({ force: true });
      });
  });

  it('should have the Update button disabled until one field is changed', () => {
    // The Create button should be disabled initially
    cy.get('[data-testid="edit-cbsd"]').should('be.disabled');

    cy.get('[data-testid="display-name"]').type('Test Device');
    cy.get('[data-testid="fcc-id"]').type('FCC123456');
    cy.get('select#clusterId').should('be.visible').select('denseair-gke-k-bmctl-edge02-1-15-2');

    // The Create button should be enabled because all required fields are filled
    cy.get('[data-testid="edit-cbsd"]').should('be.enabled');
  });

  it('should disable the Update button and show an error if string values are entered in numeric fields of the Installation form', () => {
    cy.get('[data-testid="display-name"]').type('Test Device');
    cy.get('[data-testid="fcc-id"]').type('FCC123456');
    cy.get('select#clusterId').should('be.visible').select('denseair-gke-k-bmctl-edge02-1-15-2');

    cy.get('[data-testid="latitude"]').type('invalid');
    cy.get('[data-testid="longitude"]').type('invalid');
    cy.get('[data-testid="edit-cbsd"]').should('be.disabled');
  });

  it('Successfully edit CBSD form', () => {
    // The Create button should be disabled initially
    cy.get('[data-testid="edit-cbsd"]').should('be.disabled');

    cy.get('[data-testid="display-name"]').type('Test Device');
    cy.get('[data-testid="fcc-id"]').type('FCC123456');
    cy.get('select#clusterId').should('be.visible').select('denseair-gke-k-bmctl-edge02-1-15-2');

    cy.get('[data-testid="edit-cbsd"]').should('be.enabled');

    cy.get('[data-testid="edit-cbsd"]').click();
    cy.contains('The CBSD has been successfully Updated.');
  });
});
