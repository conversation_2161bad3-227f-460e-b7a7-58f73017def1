/// <reference types="cypress" />

describe('Test the Footer component', () => {
  beforeEach(() => {
    cy.system_health();
    cy.useLogin();
    cy.visit('/');
  });

  it('display footer image', () => {
    cy.get('[data-testid="footer-main-image"]').should('be.visible');
  });

  it('display email icon', () => {
    cy.get('[data-testid="footer-email-icon"]').should('be.visible');
  });

  it('display Support Portal icon', () => {
    cy.get('[data-testid="footer-external-link-icon"]').should('be.visible');
    cy.screenshot('footer');
  });
});

export {};
