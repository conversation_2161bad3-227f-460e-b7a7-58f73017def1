/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />
import 'cypress-real-events';

const cellOverViewUrl = '/cell-overview';
describe.skip('Test cell overview component', () => {
  beforeEach(() => {
    cy.system_health();
    cy.useLogin();
    cy.visit(cellOverViewUrl);
  });

  it('display table', () => {
    cy.get('table').should('be.visible');
    cy.screenshot('Table screen shot');
  });

  it('display table cell highlight', () => {
    cy.get('table')
      .find('tbody')
      .find('tr')
      .first()
      .should('have.css', 'background-color')
      .and('eq', 'rgba(0, 0, 0, 0)');
    cy.get('table')
      .find('tbody')
      .find('tr')
      .first()
      .realHover()
      .should('have.css', 'background-color')
      .and('eq', 'rgb(87, 141, 168)');
    cy.screenshot('hover over cell item. background highlight');
  });

  it('display data in table', () => {
    cy.get('table').find('tbody').find('tr').should('have.length', 5);
  });

  it('display the status icons', () => {
    cy.get('[data-testid="cell-main-table-status-icon"]').should('have.length', 5);
  });

  it('display the popover icon', () => {
    cy.get('[data-testid="nms-popover"]').should('have.length', 2);
  });

  it('enable the popover', () => {
    cy.get('[data-testid="nms-popover"]').first().click();
    cy.get('.chakra-popover__popper').should('be.visible');
    cy.screenshot('Popover enabled');
  });
});

//Reimplement when we have version in inventory manager
describe.skip('Test full screen mode', () => {
  // beforeEach(() => {
  //   cy.visit(cellOverViewUrl);
  //   cy.viewport(2000, 2500);
  //   localStorage.setItem('dw_authToken', 'value');
  // });
  beforeEach(() => {
    cy.system_health();
    cy.useLogin();
    cy.visit(cellOverViewUrl);
  });
  it('display fullscreen button', () => {
    cy.contains('Open table in Fullscreen Mode');
  });

  it('enable fullscreen mode', () => {
    cy.contains('Open table in Fullscreen Mode').realClick();
    cy.realPress('Escape');
    //cy.get('table').type('{esc}');
  });
});

describe('Test cell drill down', () => {
  beforeEach(() => {
    cy.system_health();
    cy.useLogin();
    cy.visit(cellOverViewUrl);
  });

  it('display cell split', () => {
    cy.get('table').contains('GBDEV2NS000004').click();
    cy.get('tbody').contains('AABA35300403').should('be.visible');
  });

  it('open cell A', () => {
    cy.get('table').contains('GBDEV2NS000004').click();
    cy.get('tbody').contains('AABA35300403').click();
    cy.get('.chakra-card__header').contains('CU Control plane').should('be.visible');
    cy.get('.chakra-card__header').contains('CU User plane').should('be.visible');
    cy.get('.chakra-card__header').contains('DU').should('be.visible');
    cy.get('.chakra-card__header').contains('RU').should('be.visible');
    cy.get('.chakra-card__header').contains('NODE & XPU').should('be.visible');
    cy.get('.chakra-card__header').contains('MMWave').should('be.visible');
    cy.get('.chakra-card__header').contains('Switch').should('be.visible');
    cy.get('.chakra-card__header').contains('Controller board').should('be.visible');
  });

  it('view reported location link', () => {
    cy.get('table').contains('GBDEV2NS000004').click();
    cy.get('a').should('have.attr', 'href');
    cy.get('a').contains('View installed location').should('be.visible');
    cy.get('a').contains('View installed location').find('svg').should('be.visible');
  });

  //Reimplement when we have version in inventory manager
  //TODO: for the 4 below we need to check what data is going to through and it can display 2  errorCard or normal success card

  it('open airspan modal and click Node & XPU', () => {
    cy.get('table').contains('GBBCH1NS000001').click();
    cy.get('tbody').contains('F0486900D97C').click();
    cy.get('.chakra-card__header').contains('NODE & XPU').should('be.visible');
    cy.get('.chakra-card__header').contains('NODE & XPU').click();
    const labelsToCheck = [
      'XPU DETAILS',
      'Node Rest Id',
      'IP Address',
      'Port',
      'More Data',
      'NODE DETAILS',
      'Node Serial No',
      'Lat',
      'Long',
      'PLMN',
      'MCC',
      'MNC',
      'Managed',
      'NbifEventAlarmForwarding',
      'SERVER DETAILS',
      'Platform Version',
      'Application Version',
      'View in ACP',
    ];
    labelsToCheck.forEach((label) => {
      cy.get('.chakra-card__body').contains(label, { matchCase: false }).should('be.visible');
    });
  });

  it('open airspan modal and Perform RU Actions', () => {
    cy.get('table').contains('GBBCH1NS000001').click();
    cy.get('tbody').contains('F0486900D97C').click();
    cy.get('.chakra-card__header').contains('RU').should('be.visible');
    cy.get('.chakra-card__header').contains('RU').click();
    const labelsToCheck = ['Reset', 'Lock', 'Unlock', 'OUT OF SYNC', 'UNIT OVER HEATING'];
    labelsToCheck.forEach((label) => {
      cy.get('.chakra-card__body').contains(label, { matchCase: false }).should('be.visible');
    });

    //Click Reset button
    cy.contains('button', 'Reset').click();

    // alert should be displayed and confirm all buttons
    cy.get('select').should('exist');
    cy.contains('Select a reason for the Reset action').should('be.visible');
    cy.contains('button', 'Confirm').should('be.disabled');
    cy.contains('button', 'Cancel').should('be.enabled');

    // click dropdown to select an option
    cy.contains('button', 'Click to select an option').click();
    // select option from dropdown
    cy.contains('button', 'Initial configuration').click();
    // confirm button should be enabled, but dont click it
    cy.contains('button', 'Confirm').should('be.enabled');

    //click cancel button and then again click Lock Button
    cy.contains('button', 'Cancel').click();
    cy.contains('button', 'Lock').click();

    // it should display drop down with no option selected , Confirm button should be disabled
    cy.contains('button', 'Click to select an option').should('be.visible');
    cy.contains('button', 'Confirm').should('be.disabled');
    cy.contains('button', 'Cancel').should('be.enabled');
  });

  it('open Switch modal', () => {
    cy.get('table').contains('GBDEV2NS000004').click();
    cy.get('tbody').contains('AABA35300403').click();
    cy.get('.chakra-card__header').contains('Switch').click();
    cy.get('.chakra-card__body').contains('Component id', { matchCase: false }).first().should('be.visible');
  });

  it('open MMWave modal', () => {
    cy.get('table').contains('GBDEV2NS000004').click();
    cy.get('tbody').contains('AABA35300403').click();
    cy.get('.chakra-card__header').contains('MMWave').click();
    cy.get('.chakra-card__body').contains('Component id', { matchCase: false }).first().should('be.visible');
  });

  it('open Controller board modal', () => {
    cy.get('table').contains('GBDEV2NS000004').click();
    cy.get('tbody').contains('AABA35300403').click();
    cy.get('.chakra-card__header').contains('Controller board').click();
    cy.get('.chakra-card__body').contains('Component id', { matchCase: false }).first().should('be.visible');
  });

  it('open ACP modal', () => {
    cy.get('table').contains('GBDEV2NS000062').click();
    cy.get('tbody').contains('GB-DEV2-VM-0001').click();
    cy.get('.chakra-card__header').contains('ACP').click();
    cy.get('.chakra-card__body').contains('Component id', { matchCase: false }).first().should('be.visible');
  });
});

describe('Test Alarms column on cell overview', () => {
  beforeEach(() => {
    cy.system_health();
    cy.useLogin();
    cy.visit(cellOverViewUrl);
  });
  it('should optionally interact with the alarms icon if present', () => {
    cy.get('table.chakra-table').should('be.visible');

    cy.get('table.chakra-table tbody tr')
      .first()
      .then(($row) => {
        const $icon = $row.find('[data-testid="cell-alarms-icon"]');
        if ($icon.length) {
          cy.wrap($icon).click();
          cy.get('table.chakra-table tbody tr').find('[data-testid="cell-alarms-critical"]').should('be.visible');
          cy.get('table.chakra-table tbody tr').find('[data-testid="cell-alarms-major"]').should('be.visible');
          cy.get('table.chakra-table tbody tr').find('[data-testid="cell-alarms-minor"]').should('be.visible');
          cy.get('table.chakra-table tbody tr').find('[data-testid="cell-alarms-warning"]').should('be.visible');
          cy.get('table.chakra-table tbody tr').find('[data-testid="cell-alarms-none"]').should('be.visible');
        } else {
          cy.log('Icon not found, this may be expected behavior based on test data.');
        }
      });
  });
  it('should able to see view alarms', () => {
    cy.get('table.chakra-table').should('be.visible');

    cy.get('table.chakra-table tbody tr')
      .first()
      .then(($row) => {
        const $icon = $row.find('[data-testid="cell-alarms-icon"]');
        if ($icon.length) {
          cy.wrap($icon).click();
          cy.get('table.chakra-table tbody tr').find('[data-testid=view-all-alarms-menu-item]').should('be.visible');
        } else {
          cy.log('Icon not found, this may be expected behavior based on test data.');
        }
      });
  });
});

describe.skip('Test sort functionality on cell table view', () => {
  beforeEach(() => {
    cy.system_health();
    cy.useLogin();
    cy.visit(cellOverViewUrl);
  });

  //Reimplement when we have version in inventory manager
  it('cell Site name ASC', () => {
    cy.get('[data-testid="cell-main-table-site-name-sort"]').click();
    cy.get('table').find('tbody').find('tr').find('td').eq(2).should('have.text', 'new site test');
  });

  //Reimplement when we have version in inventory manager
  it('cell Site name DESC', () => {
    cy.get('[data-testid="cell-main-table-site-name-sort"]').click();
    cy.get('[data-testid="cell-main-table-site-name-sort"]').click();
    cy.get('table').find('tbody').find('tr').find('td').eq(2).should('have.text', 'Ansir Test');
  });

  it('cell Type DESC', () => {
    cy.get('[data-testid="cell-main-table-cell-type"]').click();
    cy.get('table')
      .find('tbody')
      .find('tr')
      .find('td')
      .eq(1)
      //.should('have.text', 'gNodeB');
      .should('have.text', 'gNodeB'); //Mock Data
  });

  it('cell Type ASC', () => {
    cy.get('[data-testid="cell-main-table-cell-type"]').click();
    cy.get('[data-testid="cell-main-table-cell-type"]').click();
    cy.get('table').find('tbody').find('tr').find('td').eq(1).should('have.text', 'Split 6');
    //.should('have.text', 'gNodeA'); //Mock Data
  });

  it('cell country ASC', () => {
    cy.get('[data-testid="cell-main-table-country-code-sort"]').click();
    cy.get('[data-testid="cell-main-table-country-code-sort"]').click();
    cy.get('table').find('tbody').find('tr').find('td').eq(5).should('have.text', 'GBR');
  });

  it('cell country DESC', () => {
    cy.get('[data-testid="cell-main-table-country-code-sort"]').click();
    cy.get('table').find('tbody').find('tr').find('td').eq(5).should('have.text', 'GBR');
  });

  // it keeps on changing
  it('cell region ASC', () => {
    cy.get('[data-testid="cell-main-table-region-sort"]').click();
    cy.get('[data-testid="cell-main-table-region-sort"]').click();
    cy.get('table').find('tbody').find('tr').find('td').eq(4).should('have.text', 'Marlow');
  });

  // it keeps on changing
  it('cell region DESC', () => {
    cy.get('[data-testid="cell-main-table-region-sort"]').click();
    cy.get('table').find('tbody').find('tr').find('td').eq(4).should('have.text', 'Millbrook');
  });

  // it keeps on changing
  it('cell lifecycle ASC', () => {
    cy.get('[data-testid="cell-main-table-lifecycle-sort"]').click();
    cy.get('[data-testid="cell-main-table-lifecycle-sort"]').click();
    cy.get('table').find('tbody').find('tr').find('td').eq(6).should('have.text', 'COMMISSIONING');
  });

  it('cell lifecycle DESC', () => {
    cy.get('[data-testid="cell-main-table-lifecycle-sort"]').click();
    cy.get('table').find('tbody').find('tr').find('td').eq(6).should('have.text', 'OPERATIONAL'); //Mock Data
  });

  //Reimplement when we have version in inventory manager
  it('cell status ASC', () => {
    cy.get('[data-testid="cell-main-table-status-sort"]').click();
    cy.get('[data-testid="cell-main-table-status-sort"]').click();
    cy.get('table')
      .find('tbody')
      .find('tr')
      .find('td')
      .eq(7)
      .find('svg')
      .find('path')
      .should('have.attr', 'fill', 'currentColor');
  });

  //Reimplement when we have version in inventory manager
  it('cell status DESC', () => {
    cy.get('[data-testid="cell-main-table-status-sort"]').click();
    cy.get('table')
      .find('tbody')
      .find('tr')
      .find('td')
      .eq(7)
      .find('svg')
      .find('path')
      .should('have.attr', 'fill', 'currentColor');
  });
});

export {};
