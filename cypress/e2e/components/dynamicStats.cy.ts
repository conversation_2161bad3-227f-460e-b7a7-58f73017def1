/* eslint-disable cypress/require-data-selectors */
import 'cypress-real-events';

const cellOverViewUrl = '/cell-overview';
describe('Test dynamic stats inside switch component', () => {
  beforeEach(() => {
    cy.system_health();
    cy.useLogin();
    cy.visit(cellOverViewUrl);
  });

  it('display table', () => {
    cy.get('table').should('be.visible');
    cy.screenshot('Table screen shot');
  });

  it('Switch dynamic stats should load', () => {
    cy.get('tbody.css-0 > :nth-child(1) > :nth-child(2)').click();
    cy.get('.css-m9it2h > .chakra-table > tbody.css-0 > :nth-child(1) > :nth-child(2)').should('be.visible');
    cy.get('tbody.css-0 > :nth-child(1) > :nth-child(2) > .chakra-text').click();
    cy.get('.chakra-card__header').should('be.visible');
    cy.get('.chakra-card__header').click();
    cy.get(':nth-child(1) > :nth-child(1) > dl > .chakra-stat__label').click();
    cy.get(':nth-child(1) > :nth-child(1) > dl > .chakra-stat__label').should('be.visible');
    cy.get('.chakra-stack > :nth-child(5)').click();
    cy.get('.css-5dg5fm > .chakra-text').click();
    cy.get('.css-5dg5fm > .chakra-text').should('be.visible');
    cy.get('.chakra-card__body > .chakra-stack > .chakra-button').should('be.enabled');
    cy.get(':nth-child(6) > :nth-child(1) > dl > .chakra-stat__number').click();
    cy.get('.chakra-card__body > .chakra-stack > .chakra-button').click();
    cy.get('#chakra-modal--header-\\:rjb\\:').should('be.visible');
    cy.get('#chakra-modal--body-\\:rjb\\: > :nth-child(1)').click();
    cy.get('#chakra-modal--header-\\:rjb\\:').should('be.visible');
  });
});
