/// <reference types="cypress" />
import 'cypress-real-events';

const apiUrl = 'nms/dev2/orc/network/nodes/';
import { JunpierSwitcBoxhMock, SwitchNodeMockData } from '../../../src/services/mocks/juniperSwitchBoxMock';

describe('JuniperBoxSwitch Component Tests with Mock Data', () => {
  beforeEach(() => {
    cy.system_health();
    cy.useLogin();
    cy.visit('cell-overview/nodes');
    const nodeId = 'GB-MARL-SW-0002';

    // API request with the provided mock data
    cy.intercept('GET', `${apiUrl}${nodeId}`, {
      statusCode: 200,
      body: SwitchNodeMockData,
    }).as('getSwitchNodeDetails');

    cy.wait('@getSwitchNodeDetails');
  });
  it('should display table cells correctly', () => {
    cy.get('[data-testid="cell-jnxContainersDescr-0"]').should(
      'contain',
      JunpierSwitcBoxhMock.box.containers[0].jnxContainersDescr
    );
  });

  it('should expand the row when clicked', () => {
    cy.get('[data-testid="expanded-content-0"]')
      .should('be.visible')
      .and('contain', JunpierSwitcBoxhMock.box.containers[0].contents[0].detail?.jnxContentsDescr);
    cy.get('[data-testid="expanded-content-0"]').click();
  });
});
