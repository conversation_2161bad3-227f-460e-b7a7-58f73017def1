const nodeOverViewUrl = '/cell-overview/nodes';

describe('Test Druid Component', function () {
  beforeEach(function () {
    cy.system_health();
    cy.useLogin();
    cy.visit(nodeOverViewUrl);

    // Load fixture data and store it in the `this` context
    cy.fixture('nodeComponent/server/druid-core').then((data) => {
      this.mockServerData1 = data;
    });

    cy.fixture('nodeComponent/server/druid-nhe').then((data) => {
      this.mockServerData2 = data;
    });
  });

  it('should display druid core component', function () {
    const serverData = this.mockServerData1;

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/orc/server/nodes/**', {
      body: serverData,
    }).as('getNodeDetails');

    cy.xpath('//table[contains(@class, "chakra-table")]').should('be.visible');

    cy.contains('GB-MR4G-VM-0001').should('be.visible').click();

    cy.contains('DRUID').should('be.visible').click();

    const dashboardComponents = ['USER LICENSE', 'NETWORK LICENSE', 'CELL LICENSE'];

    dashboardComponents.forEach((component) => {
      cy.contains(component).should('be.visible');

      if (component === 'USER LICENSE') {
        cy.contains('242 / 270').should('be.visible');
      } else if (component === 'NETWORK LICENSE') {
        cy.contains('9 / 10').should('be.visible').click();
        cy.get('[data-testid="network-cards"]').children('div').should('have.length', 9);
        const networkKeys = ['Id', 'Apn', 'Primary Dns', 'Secondary Dns'];

        cy.get('[data-testid="network-cards"]')
          .children('div')
          .each((div, index) => {
            cy.wrap(div).within(() => {
              networkKeys.forEach((key) => {
                cy.contains(key).should('be.visible');
              });
            });
          });
      } else if (component === 'CELL LICENSE') {
        cy.contains('3 / 10').should('be.visible').click();

        const cellStatusAttributes = { Connected: 12, Disconnected: 3, Locked: 0, Unlocked: 15 };
        Object.keys(cellStatusAttributes).forEach((status) => {
          cy.get(`[data-testid="cell-status-${status}"]`).should('be.visible');

          cy.get(`[data-testid="cell-status-${status}"]`).within(() => {
            cy.contains(cellStatusAttributes[status]).should('be.visible');
          });
        });
      }
    });
  });

  it('should display druid nhe component', function () {
    const serverData = this.mockServerData2;

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/orc/server/nodes/**', {
      body: serverData,
    }).as('getNodeDetails');

    cy.contains('GB-MR4G-VM-0002').should('be.visible').click();

    cy.contains('DRUID').should('be.visible').click();

    const dashboardComponents = ['USER LICENSE', 'OPERATOR LICENSE', 'CELL LICENSE'];

    dashboardComponents.forEach((component) => {
      cy.contains(`${component}`).should('be.visible');

      if (component === 'USER LICENSE') {
        cy.contains('0 / 400').should('be.visible');
      } else if (component === 'OPERATOR LICENSE') {
        cy.contains('4 / 20').should('be.visible').click();
        cy.get('[data-testid="operator-cards"]').children('div').should('have.length', 4);
        const operatorKeys = [
          'PLMN',
          'Operational State',
          'Admin State',
          'S1 Client Type',
          'Enb Name',
          'Cell Identity',
          'Max Downlink Bandwidth',
          'Max Uplink Bandwidth',
        ];

        cy.get('[data-testid="operator-cards"]')
          .children('div')
          .each((div, index) => {
            cy.wrap(div).within(() => {
              operatorKeys.forEach((key) => {
                cy.contains(key).should('be.visible');
              });
            });
          });
      } else if (component === 'CELL LICENSE') {
        cy.contains('4 / 100').should('be.visible').click();

        const cellStatusAttributes = { Connected: 4, Disconnected: 0, Locked: 0, Unlocked: 4 };
        Object.keys(cellStatusAttributes).forEach((status) => {
          cy.get(`[data-testid="cell-status-${status}"]`).should('be.visible');

          cy.get(`[data-testid="cell-status-${status}"]`).within(() => {
            cy.contains(cellStatusAttributes[status]).should('be.visible');
          });
        });
      }
    });
  });
});
