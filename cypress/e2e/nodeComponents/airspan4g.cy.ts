const nodeOverViewUrl = '/cell-overview/nodes';

describe('Test Airspan4g Component', function () {
  beforeEach(function () {
    cy.system_health();
    cy.useLogin();
    cy.visit(nodeOverViewUrl);

    // Load fixture data and store it in the `this` context
    cy.fixture('nodeComponent/airspan4g/airspan4g').then((data) => {
      this.mockAirspan4gData = data;
    });

    cy.fixture('nodeComponent/airspan4g/serverActions/postServerAction').then((data) => {
      this.mockPostServerAction = data;
    });

    cy.fixture('nodeComponent/airspan4g/serverActions/getServerAction').then((data) => {
      this.mockGetServerAction = data;
    });
  });

  it('should display airspan4g component ', function () {
    const airspan4gData = this.mockAirspan4gData;

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/orc/nodes/4g/**', {
      body: airspan4gData,
    }).as('getNodeDetails');

    cy.xpath('//table[contains(@class, "chakra-table")]').should('be.visible');

    // Find the `tr` element whose second `td` contains the text "Server-VM" and click it
    cy.xpath('//table[contains(@class, "chakra-table")]//tbody//tr[td[2][text()="Airspan4G"]]').first().click();

    // Validate the presence of nodes-nodeComponents and cells-node-ServerComponents
    cy.get('tr.css-1kk81s').within(() => {
      cy.xpath('//div[@data-testid="nodes-nodeComponents"]').should('be.visible');
      // Validate the card body is shown and visible
      cy.xpath('//div[contains(@class, "chakra-card__body")]').should('be.visible');

      // Basic Info
      cy.xpath('//p[text()="Basic Info"]').should('be.visible');
      cy.xpath('//div[@data-testid="airspan4g-config-card"]').should('be.visible');
      cy.xpath('//div[@data-testid="airspan4g-config-card"]').click();

      cy.get('div[data-testid="airspan4g-config-card"]').within(() => {
        cy.xpath('//p[text()="Basic Info"]').should('be.visible');

        // convert it to a loop
        const cellCards = ['lte-cell-card-1', 'lte-cell-card-2'];
        cellCards.forEach((cellCard) => {
          cy.xpath(`//div[@data-testid="${cellCard}"]`).should('be.visible');
          // Click on the first cell card
          cy.xpath(`//div[@data-testid="${cellCard}"]`).click();

          cy.get(`div[data-testid="${cellCard}"]`).within(() => {
            cy.xpath('//p[text()="Basic Info"]').should('be.visible');
            cy.xpath('//p[text()="CBSD Details"]').should('be.visible');

            const profileCards = [
              'Radio Profile-card',
              'Embms Profile-card',
              'Traffic Management Profile-card',
              'Call Trace Profile-card',
            ];

            profileCards.forEach((cardTestId) => {
              cy.xpath(`//div[@data-testid="${cardTestId}"]`).should('be.visible');
              cy.xpath(`//div[@data-testid="${cardTestId}"]`).first().click();

              cy.xpath(`//div[@data-testid="${cardTestId}"]`)
                .first()
                .within(() => {
                  cy.xpath('//p[text()="id"]').should('be.visible');
                });
            });

            cy.xpath('//div[@data-testid="CSFB CDMA 2k Mobility-card"]').should('be.visible');

            cy.xpath('//div[@data-testid="CSFB CDMA 2k Sib8-card"]').should('be.visible');
          });
        });
      });
    });
  });

  it.only('test control actions', function () {
    const airspan4gData = this.mockAirspan4gData;

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/orc/nodes/4g/**', {
      body: airspan4gData,
    }).as('getNodeDetails');

    cy.intercept('POST', '**/orc/actions/**', {
      body: this.mockPostServerAction,
    }).as('postServerAction');

    cy.intercept('GET', '**/orc/actions/operation/**', {
      body: this.mockGetServerAction,
    }).as('getServerAction');

    cy.xpath('//table[contains(@class, "chakra-table")]').should('be.visible');

    // Find the `tr` element whose second `td` contains the text "Server-VM" and click it
    cy.xpath('//table[contains(@class, "chakra-table")]//tbody//tr[td[2][text()="Airspan4G"]]').first().click();

    // Validate the presence of nodes-nodeComponents and cells-node-ServerComponents
    cy.get('tr.css-1kk81s').within(() => {
      cy.xpath('//div[@data-testid="nodes-nodeComponents"]').should('be.visible');
      // Validate the card body is shown and visible
      cy.xpath('//div[contains(@class, "chakra-card__body")]').should('be.visible');

      // Basic Info
      cy.xpath('//p[text()="Basic Info"]').should('be.visible');

      // scroll to the right
      cy.xpath('//div[@data-testid="airspan4g-config-card"]').scrollIntoView();

      const buttons = { Reset: 'reset-button', Lock: 'lock-button', Unlock: 'unlock-button' };
      Object.keys(buttons).forEach((button) => {
        cy.xpath(`//button[@data-testid="${buttons[button]}"]`).should('be.visible');
        cy.xpath(`//button[@data-testid="${buttons[button]}"]`).click();

        // cy.xpath('//p[text()="Select a reason for the Reset action"]').should('be.visible');
        cy.xpath('//button[@data-testid="confirm-button"]').should('be.disabled');
        cy.xpath('//button[@data-testid="cancel-button"]').should('be.enabled');

        // click dropdown to select an option
        cy.xpath('//span[text()="Click to select an option"]').should('be.visible').click({ force: true });
        // select option from dropdown
        cy.xpath('//button[text()="Initial configuration"]').should('be.visible').click();
        cy.xpath('//button[@data-testid="confirm-button"]').should('be.enabled').click();

        cy.xpath(`//p[text()="${button}"]`).should('be.visible');
        cy.xpath('//p[text()="Action"]').should('be.visible');
        cy.get('svg[color="green"]').should('exist');
      });
    });
  });
});
