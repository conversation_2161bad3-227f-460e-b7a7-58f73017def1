const nodeOverViewUrl = '/cell-overview/nodes';

describe('Test Server All CPU  Component', function () {
  beforeEach(function () {
    cy.system_health();
    cy.useLogin();
    cy.visit(nodeOverViewUrl);

    // Load fixture data and store it in the `this` context
    cy.fixture('nodeComponent/server/server-all-cpu').then((data) => {
      this.mockServerData = data;
    });
  });

  it('should display server component with connectivity present', function () {
    const serverData = this.mockServerData;

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/orc/server/nodes/**', {
      body: serverData,
    }).as('getNodeDetails');

    cy.xpath('//table[contains(@class, "chakra-table")]').should('be.visible');

    // Find the `tr` element whose second `td` contains the text "Server-VM" and click it
    cy.xpath('//table[contains(@class, "chakra-table")]//tbody//tr[td[2][text()="Server-VM"]]').first().click();

    // Validate the presence of nodes-nodeComponents and cells-node-ServerComponents
    cy.get('tr.css-1kk81s').within(() => {
      cy.xpath('//div[@data-testid="nodes-nodeComponents"]').should('be.visible');
      cy.xpath('//div[@data-testid="cells-node-ServerComponents"]').should('be.visible');

      // Click the Server card header div
      cy.xpath('//div[@data-testid="cells-node-ServerComponents"]/div/div[1]').click();

      // Validate the card body is shown and visible
      cy.xpath('//div[contains(@class, "chakra-card__body")]').should('be.visible');

      // Data-driven validation
      const details = [
        { label: 'IP Address:', value: serverData.server.ip_address, selector: 'tr[1]/td[2]/span' },
        { label: 'Name:', value: serverData.server.name, selector: 'tr[2]/td[2]/span' },
        { label: 'Connectivity:', value: 'green', selector: 'tr[3]/td[2]/svg', isColor: true },
        // { label: 'Last Contacted:', value: serverData.server.last_contacted, selector: 'tr[4]/td[2]/p', isDate: true },
        { label: 'Last Attempted:', value: serverData.server.last_attempted, selector: 'tr[4]/td[2]/p', isDate: true },
      ];

      details.forEach((detail) => {
        // Validate label
        cy.xpath(
          `//div[contains(@class, "chakra-card__body")]/div/table/tbody/${detail.selector.replace(
            /\/td\[2\]\/.*/,
            '/td[1]/p'
          )}`
        )
          .invoke('text')
          .then((text) => {
            expect(text.trim()).to.equal(detail.label);
          });

        // Validate value
        if (detail.isColor) {
          // XPath doesnt work for svg element
          cy.get('div.chakra-card__body > div > table > tbody > tr:nth-child(3) > td:nth-child(2) > svg')
            .should('be.visible')
            .invoke('attr', 'color')
            .should('equal', detail.value);
        } else if (detail.isDate) {
          cy.xpath(`//div[contains(@class, "chakra-card__body")]/div/table/tbody/${detail.selector}`)
            .invoke('text')
            .then((text) => {
              const actualDate = new Date(text.trim());
              const expectedDate = new Date(detail.value);

              // Format the dates to a common format for comparison
              const formatDate = (date) => date.toISOString().split('.')[0].replace('T', ' ');

              expect(formatDate(actualDate)).to.equal(formatDate(expectedDate));
            });
        } else {
          cy.xpath(`//div[contains(@class, "chakra-card__body")]/div/table/tbody/${detail.selector}`)
            .invoke('text')
            .then((text) => {
              expect(text.trim()).to.equal(detail.value);
            });
        }
      });
    });
  });

  it('should display server component with connectivity absent', function () {
    const serverData = this.mockServerData;
    serverData.server.last_contacted = null;

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/orc/server/nodes/**', {
      body: serverData,
    }).as('getNodeDetails');

    cy.xpath('//table[contains(@class, "chakra-table")]').should('be.visible');

    // Find the `tr` element whose second `td` contains the text "Server-VM" and click it
    cy.xpath('//table[contains(@class, "chakra-table")]//tbody//tr[td[2][text()="Server-VM"]]').first().click();

    // Validate the presence of nodes-nodeComponents and cells-node-ServerComponents
    cy.get('tr.css-1kk81s').within(() => {
      cy.xpath('//div[@data-testid="nodes-nodeComponents"]').should('be.visible');
      cy.xpath('//div[@data-testid="cells-node-ServerComponents"]').should('be.visible');

      // Click the Server card header div
      cy.xpath('//div[@data-testid="cells-node-ServerComponents"]/div/div[1]').click();

      // Validate the card body is shown and visible
      cy.xpath('//div[contains(@class, "chakra-card__body")]').should('be.visible');

      // Data-driven validation
      const details = [
        { label: 'IP Address:', value: serverData.server.ip_address, selector: 'tr[1]/td[2]/span' },
        { label: 'Name:', value: serverData.server.name, selector: 'tr[2]/td[2]/span' },
        { label: 'Connectivity:', value: 'red', selector: 'tr[3]/td[2]/svg', isColor: true },
        // { label: 'Last Contacted:', value: 'Never', selector: 'tr[4]/td[2]/p' },
        { label: 'Last Attempted:', value: serverData.server.last_attempted, selector: 'tr[4]/td[2]/p', isDate: true },
      ];

      details.forEach((detail) => {
        // Validate label
        cy.xpath(
          `//div[contains(@class, "chakra-card__body")]/div/table/tbody/${detail.selector.replace(
            /\/td\[2\]\/.*/,
            '/td[1]/p'
          )}`
        )
          .invoke('text')
          .then((text) => {
            expect(text.trim()).to.equal(detail.label);
          });

        // Validate value
        if (detail.isColor) {
          // XPath doesnt work for svg element
          cy.get('div.chakra-card__body > div > table > tbody > tr:nth-child(3) > td:nth-child(2) > svg')
            .should('be.visible')
            .invoke('attr', 'color')
            .should('equal', detail.value);
        } else if (detail.isDate) {
          cy.xpath(`//div[contains(@class, "chakra-card__body")]/div/table/tbody/${detail.selector}`)
            .invoke('text')
            .then((text) => {
              const actualDate = new Date(text.trim());
              const expectedDate = new Date(detail.value);

              // Format the dates to a common format for comparison
              const formatDate = (date) => date.toISOString().split('.')[0].replace('T', ' ');

              expect(formatDate(actualDate)).to.equal(formatDate(expectedDate));
            });
        } else {
          cy.xpath(`//div[contains(@class, "chakra-card__body")]/div/table/tbody/${detail.selector}`)
            .invoke('text')
            .then((text) => {
              expect(text.trim()).to.equal(detail.value);
            });
        }
      });
    });
  });

  it('Should display NTP details with NTP Running', function () {
    const serverData = this.mockServerData;

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/orc/server/nodes/**', {
      body: serverData,
    }).as('getNodeDetails');

    cy.xpath('//table[contains(@class, "chakra-table")]').should('be.visible');

    // Find the `tr` element whose second `td` contains the text "Server-VM" and click it
    cy.xpath('//table[contains(@class, "chakra-table")]//tbody//tr[td[2][text()="Server-VM"]]').first().click();

    // Validate the presence of nodes-nodeComponents and cells-node-ServerComponents
    cy.get('tr.css-1kk81s').within(() => {
      cy.xpath('//div[@data-testid="nodes-nodeComponents"]').should('be.visible');
      cy.xpath('//div[@data-testid="cells-node-ServerComponents"]').should('be.visible');

      // Click the Server card header div
      cy.xpath('//div[@data-testid="cells-node-ServerComponents"]/div/div[1]').click();

      //  Verify NTP Card Header title
      const ntpEleXpath = '//div[contains(@class, "chakra-card__body")]/div[1]/div[2]/div[2]';
      cy.xpath(ntpEleXpath + '/div[1]/div[1]/h2/div[1]/p')
        .should('be.visible')
        .invoke('text')
        .then((text) => {
          expect(text.trim()).to.equal('NTP');
        });

      // Click the NTP card header
      cy.xpath(ntpEleXpath).click();

      // Validate the NTP card body is shown and visible
      cy.xpath(ntpEleXpath)
        .first()
        .within(() => {
          const tBodyXpath = '//div[contains(@class, "chakra-card__body")]/div[1]/div[1]/table/tbody';
          cy.xpath(tBodyXpath).should('be.visible');

          const ntpDetails = serverData.server.subsystems[3].results;
          Object.entries(ntpDetails).forEach(([key, value], index) => {
            // Vefiry keys
            cy.xpath(`${tBodyXpath}/tr[${index + 1}]/td[1]/p`)
              .invoke('text')
              .then((text) => {
                expect(text.trim()).to.equal(`${key}:`);
              });

            //   Verify values
            if (index <= 3) {
              cy.xpath(`${tBodyXpath}/tr[${index + 1}]/td[2]/p`)
                .invoke('text')
                .then((text) => {
                  expect(text.trim()).to.equal(`${value}`);
                });
            }

            if (index === 4) {
              // verify System_clock_synchronized
              cy.get(
                'div.chakra-card__body > div:nth-child(1) > div:nth-child(1) > table tbody tr:nth-child(5) > td:nth-child(2) > svg'
              )
                .should('be.visible')
                .invoke('attr', 'color')
                .should('equal', 'green');
            }

            if (index === 5) {
              // Verify NTP_service
              cy.get(
                'div.chakra-card__body > div:nth-child(1) > div:nth-child(1) > table tbody tr:nth-child(6) > td:nth-child(2) > svg'
              )
                .should('be.visible')
                .invoke('attr', 'color')
                .should('equal', 'green');
            }

            if (index === 6) {
              // Verify RTC_in_local_TZ
              let color;
              if (value === 'active') {
                color = 'green';
              } else {
                color = 'red';
              }
              cy.get(
                'div.chakra-card__body > div:nth-child(1) > div:nth-child(1) > table tbody tr:nth-child(7) > td:nth-child(2) > svg'
              )
                .should('be.visible')
                .invoke('attr', 'color')
                .should('equal', color);
            }
          });
        });
    });
  });

  it('Should display NTP details with NTP Inactive', function () {
    const serverData = this.mockServerData;
    // Set NTP_service to inactive
    serverData.server.subsystems[3].results.NTP_service = 'inactive';

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/orc/server/nodes/**', {
      body: serverData,
    }).as('getNodeDetails');

    cy.xpath('//table[contains(@class, "chakra-table")]').should('be.visible');

    // Find the `tr` element whose second `td` contains the text "Server-VM" and click it
    cy.xpath('//table[contains(@class, "chakra-table")]//tbody//tr[td[2][text()="Server-VM"]]').first().click();

    // Validate the presence of nodes-nodeComponents and cells-node-ServerComponents
    cy.get('tr.css-1kk81s').within(() => {
      cy.xpath('//div[@data-testid="nodes-nodeComponents"]').should('be.visible');
      cy.xpath('//div[@data-testid="cells-node-ServerComponents"]').should('be.visible');

      // Click the Server card header div
      cy.xpath('//div[@data-testid="cells-node-ServerComponents"]/div/div[1]').click();

      //   Verify NTP Card Header title
      const ntpEleXpath = '//div[contains(@class, "chakra-card__body")]/div[1]/div[2]/div[2]';
      cy.xpath(ntpEleXpath + '/div[1]/div[1]/h2/div[1]/p')
        .should('be.visible')
        .invoke('text')
        .then((text) => {
          expect(text.trim()).to.equal('NTP');
        });

      // Click the NTP card header
      cy.xpath(ntpEleXpath).click();

      // Validate the NTP card body is shown and visible
      cy.xpath(ntpEleXpath)
        .first()
        .within(() => {
          const tBodyXpath = '//div[contains(@class, "chakra-card__body")]/div[1]/div[1]/table//tbody';
          cy.xpath(tBodyXpath).should('be.visible');

          const ntpDetails = serverData.server.subsystems[3].results;
          const keys = Object.keys(ntpDetails);
          Object.entries(ntpDetails).forEach(([key, value], index) => {
            // Vefiry keys
            cy.xpath(`${tBodyXpath}/tr[${index + 1}]/td[1]/p`)
              .invoke('text')
              .then((text) => {
                expect(text.trim()).to.equal(`${key}:`);
              });

            //   Verify values
            if (index <= 3) {
              cy.xpath(`${tBodyXpath}/tr[${index + 1}]/td[2]/p`)
                .invoke('text')
                .then((text) => {
                  expect(text.trim()).to.equal(`${value}`);
                });
            }

            if (index === 4) {
              // verify System_clock_synchronized

              cy.get(
                'div.chakra-card__body > div:nth-child(1) > div:nth-child(1) > table tbody tr:nth-child(5) > td:nth-child(2) > svg'
              )
                .should('be.visible')
                .invoke('attr', 'color')
                .should('equal', 'green');
            }

            if (index === 5) {
              // Verify NTP_service
              cy.get(
                'div.chakra-card__body > div:nth-child(1) > div:nth-child(1) > table tbody tr:nth-child(6) > td:nth-child(2) > svg'
              )
                .should('be.visible')
                .invoke('attr', 'color')
                .should('equal', 'red');
            }

            if (index === 6) {
              // Verify RTC_in_local_TZ
              let color;
              if (value === 'active') {
                color = 'green';
              } else {
                color = 'red';
              }
              cy.get(
                'div.chakra-card__body > div:nth-child(1) > div:nth-child(1) > table tbody tr:nth-child(7) > td:nth-child(2) > svg'
              )
                .should('be.visible')
                .invoke('attr', 'color')
                .should('equal', color);
            }
          });
        });
    });
  });

  it('Should display NTP details with No Clock Sync', function () {
    const serverData = this.mockServerData;
    // Set System_clock_synchronized to inactive
    serverData.server.subsystems[3].results.System_clock_synchronized = 'no';

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/orc/server/nodes/**', {
      body: serverData,
    }).as('getNodeDetails');

    cy.xpath('//table[contains(@class, "chakra-table")]').should('be.visible');

    // Find the `tr` element whose second `td` contains the text "Server-VM" and click it
    cy.xpath('//table[contains(@class, "chakra-table")]//tbody//tr[td[2][text()="Server-VM"]]').first().click();

    // Validate the presence of nodes-nodeComponents and cells-node-ServerComponents
    cy.get('tr.css-1kk81s').within(() => {
      cy.xpath('//div[@data-testid="nodes-nodeComponents"]').should('be.visible');
      cy.xpath('//div[@data-testid="cells-node-ServerComponents"]').should('be.visible');

      // Click the Server card header div
      cy.xpath('//div[@data-testid="cells-node-ServerComponents"]/div/div[1]').click();

      //   Verify NTP Card Header title
      const ntpEleXpath = '//div[contains(@class, "chakra-card__body")]/div[1]/div[2]/div[2]';
      cy.xpath(ntpEleXpath + '/div[1]/div[1]/h2/div[1]/p')
        .should('be.visible')
        .invoke('text')
        .then((text) => {
          expect(text.trim()).to.equal('NTP');
        });

      // Click the NTP card header
      cy.xpath(ntpEleXpath).click();

      // Validate the NTP card body is shown and visible
      cy.xpath(ntpEleXpath)
        .first()
        .within(() => {
          const tBodyXpath = '//div[contains(@class, "chakra-card__body")]/div[1]/div[1]/table//tbody';
          cy.xpath(tBodyXpath).should('be.visible');

          const ntpDetails = serverData.server.subsystems[3].results;
          const keys = Object.keys(ntpDetails);
          Object.entries(ntpDetails).forEach(([key, value], index) => {
            // Vefiry keys
            cy.xpath(`${tBodyXpath}/tr[${index + 1}]/td[1]/p`)
              .invoke('text')
              .then((text) => {
                expect(text.trim()).to.equal(`${key}:`);
              });

            //   Verify values
            if (index <= 3) {
              cy.xpath(`${tBodyXpath}/tr[${index + 1}]/td[2]/p`)
                .invoke('text')
                .then((text) => {
                  expect(text.trim()).to.equal(`${value}`);
                });
            }

            if (index === 4) {
              // verify System_clock_synchronized
              cy.get(
                'div.chakra-card__body > div:nth-child(1) > div:nth-child(1) > table tbody tr:nth-child(5) > td:nth-child(2) > svg'
              )
                .should('be.visible')
                .invoke('attr', 'color')
                .should('equal', 'red');
            }

            if (index === 5) {
              // Verify NTP_service

              cy.get(
                'div.chakra-card__body > div:nth-child(1) > div:nth-child(1) > table tbody tr:nth-child(6) > td:nth-child(2) > svg'
              )
                .should('be.visible')
                .invoke('attr', 'color')
                .should('equal', 'green');
            }

            if (index === 6) {
              // Verify RTC_in_local_TZ
              let color;
              if (value === 'active') {
                color = 'green';
              } else {
                color = 'red';
              }
              cy.get(
                'div.chakra-card__body > div:nth-child(1) > div:nth-child(1) > table tbody tr:nth-child(7) > td:nth-child(2) > svg'
              )
                .should('be.visible')
                .invoke('attr', 'color')
                .should('equal', color);
            }
          });
        });
    });
  });

  it('Should display NTP details with No NTP Running', function () {
    const serverData = this.mockServerData;
    // Set NTP_service to inactive
    serverData.server.subsystems[3].results.NTP_service = 'inactive';
    // Set System_clock_synchronized to inactive
    serverData.server.subsystems[3].results.System_clock_synchronized = 'no';

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/orc/server/nodes/**', {
      body: serverData,
    }).as('getNodeDetails');

    cy.xpath('//table[contains(@class, "chakra-table")]').should('be.visible');

    // Find the `tr` element whose second `td` contains the text "Server-VM" and click it
    cy.xpath('//table[contains(@class, "chakra-table")]//tbody//tr[td[2][text()="Server-VM"]]').first().click();

    // Validate the presence of nodes-nodeComponents and cells-node-ServerComponents
    cy.get('tr.css-1kk81s').within(() => {
      cy.xpath('//div[@data-testid="nodes-nodeComponents"]').should('be.visible');
      cy.xpath('//div[@data-testid="cells-node-ServerComponents"]').should('be.visible');

      // Click the Server card header div
      cy.xpath('//div[@data-testid="cells-node-ServerComponents"]/div/div[1]').click();

      //   Verify NTP Card Header title
      const ntpEleXpath = '//div[contains(@class, "chakra-card__body")]/div[1]/div[2]/div[2]';
      cy.xpath(ntpEleXpath + '/div[1]/div[1]/h2/div[1]/p')
        .should('be.visible')
        .invoke('text')
        .then((text) => {
          expect(text.trim()).to.equal('NTP');
        });

      // Click the NTP card header
      cy.xpath(ntpEleXpath).click();

      // Validate the NTP card body is shown and visible
      cy.xpath(ntpEleXpath)
        .first()
        .within(() => {
          const tBodyXpath = '//div[contains(@class, "chakra-card__body")]/div[1]/div[1]/table//tbody';
          cy.xpath(tBodyXpath).should('be.visible');

          const ntpDetails = serverData.server.subsystems[3].results;
          const keys = Object.keys(ntpDetails);
          Object.entries(ntpDetails).forEach(([key, value], index) => {
            // Vefiry keys
            cy.xpath(`${tBodyXpath}/tr[${index + 1}]/td[1]/p`)
              .invoke('text')
              .then((text) => {
                expect(text.trim()).to.equal(`${key}:`);
              });

            //   Verify values
            if (index <= 3) {
              cy.xpath(`${tBodyXpath}/tr[${index + 1}]/td[2]/p`)
                .invoke('text')
                .then((text) => {
                  expect(text.trim()).to.equal(`${value}`);
                });
            }

            if (index === 4) {
              // verify System_clock_synchronized

              cy.get(
                'div.chakra-card__body > div:nth-child(1) > div:nth-child(1) > table tbody tr:nth-child(5) > td:nth-child(2) > svg'
              )
                .should('be.visible')
                .invoke('attr', 'color')
                .should('equal', 'red');
            }

            if (index === 5) {
              // Verify NTP_service

              cy.get(
                'div.chakra-card__body > div:nth-child(1) > div:nth-child(1) > table tbody tr:nth-child(6) > td:nth-child(2) > svg'
              )
                .should('be.visible')
                .invoke('attr', 'color')
                .should('equal', 'red');
            }

            if (index === 6) {
              // Verify RTC_in_local_TZ
              let color;
              if (value === 'active') {
                color = 'green';
              } else {
                color = 'red';
              }
              cy.get(
                'div.chakra-card__body > div:nth-child(1) > div:nth-child(1) > table tbody tr:nth-child(7) > td:nth-child(2) > svg'
              )
                .should('be.visible')
                .invoke('attr', 'color')
                .should('equal', color);
            }
          });
        });
    });
  });

  it('Should display All CPU details', function () {
    const serverData = this.mockServerData;

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/orc/server/nodes/**', {
      body: serverData,
    }).as('getNodeDetails');

    cy.xpath('//table[contains(@class, "chakra-table")]').should('be.visible');

    // Find the `tr` element whose second `td` contains the text "Server-VM" and click it
    cy.xpath('//table[contains(@class, "chakra-table")]//tbody//tr[td[2][text()="Server-VM"]]').first().click();

    // Validate the presence of nodes-nodeComponents and cells-node-ServerComponents
    cy.get('tr.css-1kk81s').within(() => {
      cy.xpath('//div[@data-testid="nodes-nodeComponents"]').should('be.visible');
      cy.xpath('//div[@data-testid="cells-node-ServerComponents"]').should('be.visible');

      // Click the Server card header div
      cy.xpath('//div[@data-testid="cells-node-ServerComponents"]/div/div[1]').click();

      const cpuEleXpath = '//div[contains(@class, "chakra-card__body")]/div[1]/div[2]/div[5]';
      cy.xpath(cpuEleXpath + '/div[1]/div[1]/h2/div[1]/p')
        .should('be.visible')
        .invoke('text')
        .then((text) => {
          expect(text.trim()).to.equal('CPU');
        });

      // Validate the CPU card body is shown and visible
      cy.xpath(cpuEleXpath).click();

      cy.xpath(cpuEleXpath).within(() => {
        cy.xpath('//div[contains(@class, "chakra-card__body")]').should('be.visible');
        cy.xpath('//div[contains(@class, "chakra-card__body")]/div[1]/div[1]/p')
          .invoke('text')
          .then((text) => {
            expect(text.trim()).to.equal('All');
          });
      });
    });
  });

  it('Should display Memory details', function () {
    const serverData = this.mockServerData;

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/orc/server/nodes/**', {
      body: serverData,
    }).as('getNodeDetails');

    cy.xpath('//table[contains(@class, "chakra-table")]').should('be.visible');

    // Find the `tr` element whose second `td` contains the text "Server-VM" and click it
    cy.xpath('//table[contains(@class, "chakra-table")]//tbody//tr[td[2][text()="Server-VM"]]').first().click();

    // Validate the presence of nodes-nodeComponents and cells-node-ServerComponents
    cy.get('tr.css-1kk81s').within(() => {
      cy.xpath('//div[@data-testid="nodes-nodeComponents"]').should('be.visible');
      cy.xpath('//div[@data-testid="cells-node-ServerComponents"]').should('be.visible');

      // Click the Server card header div
      cy.xpath('//div[@data-testid="cells-node-ServerComponents"]/div/div[1]').click();

      // Validate the Mem card body is shown and visible
      const memEleXpath = '//div[contains(@class, "chakra-card__body")]/div[1]/div[2]/div[4]';

      cy.xpath(memEleXpath).click();

      cy.xpath(memEleXpath).within(() => {
        cy.xpath('//div[contains(@class, "chakra-card__body")]').should('be.visible');
        cy.xpath('//div[contains(@class, "chakra-card__body")]/div[1]/div[1]/p')
          .invoke('text')
          .then((text) => {
            expect(text.trim()).to.equal('Mem');
          });
      });
    });
  });

  it('Should display Disk details', function () {
    const serverData = this.mockServerData;

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/orc/server/nodes/**', {
      body: serverData,
    }).as('getNodeDetails');

    cy.xpath('//table[contains(@class, "chakra-table")]').should('be.visible');

    // Find the `tr` element whose second `td` contains the text "Server-VM" and click it
    cy.xpath('//table[contains(@class, "chakra-table")]//tbody//tr[td[2][text()="Server-VM"]]').first().click();

    // Validate the presence of nodes-nodeComponents and cells-node-ServerComponents
    cy.get('tr.css-1kk81s').within(() => {
      cy.xpath('//div[@data-testid="nodes-nodeComponents"]').should('be.visible');
      cy.xpath('//div[@data-testid="cells-node-ServerComponents"]').should('be.visible');

      // Click the Server card header div
      cy.xpath('//div[@data-testid="cells-node-ServerComponents"]/div/div[1]').click();

      // Click the Disk card header
      const diskEleXpath = '//div[contains(@class, "chakra-card__body")]/div[1]/div[2]/div[3]';
      cy.xpath(diskEleXpath).click();

      // Validate the Disk card body is shown and visible
      cy.xpath(diskEleXpath)
        .first()
        .within(() => {
          cy.xpath('//div[contains(@class, "chakra-card__body")]').should('be.visible');
          cy.xpath('//div[contains(@class, "chakra-card__body")]/div[1]/div[1]/p')
            .invoke('text')
            .then((text) => {
              expect(text.trim()).to.equal('Total');
            });
        });
    });
  });
});

describe('Test Server PM CPU  Component', function () {
  beforeEach(function () {
    cy.system_health();
    cy.useLogin();
    cy.visit(nodeOverViewUrl);

    // Load fixture data and store it in the `this` context
    cy.fixture('nodeComponent/server/server-pm-cpu').then((data) => {
      this.mockServerData = data;
    });
  });

  it('Should display PM CPU details', function () {
    const serverData = this.mockServerData;

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/orc/server/nodes/**', {
      body: serverData,
    }).as('getNodeDetails');

    cy.xpath('//table[contains(@class, "chakra-table")]').should('be.visible');

    // Find the `tr` element whose second `td` contains the text "Server-VM" and click it
    cy.xpath('//table[contains(@class, "chakra-table")]//tbody//tr[td[2][text()="Server-VM"]]').first().click();

    // Validate the presence of nodes-nodeComponents and cells-node-ServerComponents
    cy.get('tr.css-1kk81s').within(() => {
      cy.xpath('//div[@data-testid="nodes-nodeComponents"]').should('be.visible');
      cy.xpath('//div[@data-testid="cells-node-ServerComponents"]').should('be.visible');

      // Click the Server card header div
      cy.xpath('//div[@data-testid="cells-node-ServerComponents"]/div/div[1]').click();

      const cpuEleXpath = '//div[contains(@class, "chakra-card__body")]/div[1]/div[2]/div[5]';
      cy.xpath(cpuEleXpath + '/div[1]/div[1]/h2/div[1]/p')
        .invoke('text')
        .then((text) => {
          expect(text.trim()).to.equal('CPU');
        });

      // Validate the CPU card body is shown and visible
      cy.xpath(cpuEleXpath).click();

      cy.xpath(cpuEleXpath).within(() => {
        cy.xpath('//div[contains(@class, "chakra-card__body")]').should('be.visible');
        cy.xpath('//div[contains(@class, "chakra-card__body")]/div[1]/div[1]/p')
          .invoke('text')
          .then((text) => {
            expect(text.trim()).to.equal('PM');
          });
      });
    });
  });
});

describe('Test Server AM CPU  Component', function () {
  beforeEach(function () {
    cy.system_health();
    cy.useLogin();
    cy.visit(nodeOverViewUrl);

    // Load fixture data and store it in the `this` context
    cy.fixture('nodeComponent/server/server-am-cpu').then((data) => {
      this.mockServerData = data;
    });
  });

  it('Should display PM CPU details', function () {
    const serverData = this.mockServerData;

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/orc/server/nodes/**', {
      body: serverData,
    }).as('getNodeDetails');

    cy.xpath('//table[contains(@class, "chakra-table")]').should('be.visible');

    // Find the `tr` element whose second `td` contains the text "Server-VM" and click it
    cy.xpath('//table[contains(@class, "chakra-table")]//tbody//tr[td[2][text()="Server-VM"]]').first().click();

    // Validate the presence of nodes-nodeComponents and cells-node-ServerComponents
    cy.get('tr.css-1kk81s').within(() => {
      cy.xpath('//div[@data-testid="nodes-nodeComponents"]').should('be.visible');
      cy.xpath('//div[@data-testid="cells-node-ServerComponents"]').should('be.visible');

      // Click the Server card header div
      cy.xpath('//div[@data-testid="cells-node-ServerComponents"]/div/div[1]').click();

      const cpuEleXpath = '//div[contains(@class, "chakra-card__body")]/div[1]/div[2]/div[5]';
      cy.xpath(cpuEleXpath + '/div[1]/div[1]/h2/div[1]/p')
        .should('be.visible')
        .invoke('text')
        .then((text) => {
          expect(text.trim()).to.equal('CPU');
        });

      // Validate the CPU card body is shown and visible
      cy.xpath(cpuEleXpath).click();

      cy.xpath(cpuEleXpath).within(() => {
        cy.xpath('//div[contains(@class, "chakra-card__body")]').should('be.visible');
        cy.xpath('//div[contains(@class, "chakra-card__body")]/div[1]/div[1]/p')
          .invoke('text')
          .then((text) => {
            expect(text.trim()).to.equal('AM');
          });
      });
    });
  });
});

describe('Test Server CPU No Sysstat package error', function () {
  beforeEach(function () {
    cy.system_health();
    cy.useLogin();
    cy.visit(nodeOverViewUrl);

    // Load fixture data and store it in the `this` context
    cy.fixture('nodeComponent/server/server-all-cpu').then((data) => {
      this.mockServerData = data;
    });
  });

  it('Should display CPU error details', function () {
    const serverData = this.mockServerData;
    const cpuErrorDetails = {
      name: 'cpu',
      status: 'ERROR',
      cause: 'sysstat package not installed',
      reason: '/bin/sh: mpstat: command not found',
      repairs: ['Install sysstat package'],
      results: {
        error: '/bin/sh: mpstat: command not found',
      },
    };
    serverData.server.subsystems[0] = cpuErrorDetails;
    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/orc/server/nodes/**', {
      body: serverData,
    }).as('getNodeDetails');

    cy.xpath('//table[contains(@class, "chakra-table")]').should('be.visible');

    // Find the `tr` element whose second `td` contains the text "Server-VM" and click it
    cy.xpath('//table[contains(@class, "chakra-table")]//tbody//tr[td[2][text()="Server-VM"]]').first().click();

    // Validate the presence of nodes-nodeComponents and cells-node-ServerComponents
    cy.get('tr.css-1kk81s').within(() => {
      cy.xpath('//div[@data-testid="nodes-nodeComponents"]').should('be.visible');
      cy.xpath('//div[@data-testid="cells-node-ServerComponents"]').should('be.visible');

      // Click the Server card header div
      cy.xpath('//div[@data-testid="cells-node-ServerComponents"]/div/div[1]').click();

      const cpuComponentXpath = '//div[contains(@class, "chakra-card__body")]/div[1]/div[2]/div[5]';
      // Validate the CPU card body is shown and visible
      cy.xpath(cpuComponentXpath).click();

      cy.xpath(cpuComponentXpath + '/div[1]/div[1]/h2/div[1]/p')
        .should('be.visible')
        .invoke('text')
        .then((text) => {
          expect(text.trim()).to.equal('CPU');
        });

      const cpuBodyXpath = '/div[1]/div[3]/div[1]/table/tbody';
      const mappings = [
        { data_key: 'reason', key: 'Reason:', value: cpuErrorDetails.reason },
        { data_key: 'cause', key: 'Cause:', value: cpuErrorDetails.cause },
        { data_key: 'repairs', key: 'Repairs:', value: cpuErrorDetails.repairs },
      ];
      mappings.forEach((mapping, index) => {
        cy.xpath(cpuComponentXpath + cpuBodyXpath + `/tr[${index + 1}]/td[1]/p`)
          .invoke('text')
          .then((text) => {
            expect(text.trim()).to.equal(mapping.key);
          });

        cy.xpath(cpuComponentXpath + cpuBodyXpath + `/tr[${index + 1}]/td[2]/p`)
          .invoke('text')
          .then((text) => {
            expect(text.trim()).to.equal(index === 2 ? mapping.value[0] : mapping.value);
          });
      });
    });
  });
});

describe.only('Test Server PTP  Component', function () {
  beforeEach(function () {
    cy.system_health();
    cy.useLogin();
    cy.visit(nodeOverViewUrl);

    // Load fixture data and store it in the `this` context
    cy.fixture('nodeComponent/server/server-all-cpu').then((data) => {
      this.mockServerData = data;
    });
  });

  it('Should display PTP details with clock status as UNLOCKED', function () {
    const serverData = this.mockServerData;
    serverData.server.timing_type = 'PTP';
    // we have NTP data at 3rd Index, so replace it with PTP data
    const ptpDetails = {
      name: 'ptp',
      status: 'OK',
      cause: 'Report',
      reason: null,
      repairs: [''],
      results: {
        kind: 'PtpState',
        metadata: {
          creationTimestamp: '2024-07-22T14:38:49Z',
          name: 'ptpstate-object',
        },
        spec: {
          clockStatus: 'UNLOCKED',
        },
      },
    };

    serverData.server.subsystems.push(ptpDetails);

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/orc/server/nodes/**', {
      body: serverData,
    }).as('getNodeDetails');

    cy.xpath('//table[contains(@class, "chakra-table")]').should('be.visible');

    // Find the `tr` element whose second `td` contains the text "Server-VM" and click it
    cy.xpath('//table[contains(@class, "chakra-table")]//tbody//tr[td[2][text()="Server-VM"]]').first().click();

    // Validate the presence of nodes-nodeComponents and cells-node-ServerComponents
    cy.get('tr.css-1kk81s').within(() => {
      cy.xpath('//div[@data-testid="nodes-nodeComponents"]').should('be.visible');
      cy.xpath('//div[@data-testid="cells-node-ServerComponents"]').should('be.visible');

      // Click the Server card header div
      cy.xpath('//div[@data-testid="cells-node-ServerComponents"]/div/div[1]').click();

      //  Verify PTP Card Header title
      const ptpEleXpath = '//div[contains(@class, "chakra-card__body")]/div[1]/div[2]/div[3]';
      cy.xpath(ptpEleXpath + '/div[1]/div[1]/h2/div[1]/p')
        .should('be.visible')
        .invoke('text')
        .then((text) => {
          expect(text.trim()).to.equal('PTP');
        });

      // Click the PTP card header
      cy.xpath(ptpEleXpath).click();

      // Validate the PTP card body is shown and visible
      cy.xpath(ptpEleXpath)
        .first()
        .within(() => {
          const tBodyXpath = '//div[contains(@class, "chakra-card__body css-1idwstw")]/div[1]/div[1]/div[1]/p';
          cy.xpath(tBodyXpath).should('be.visible');

          cy.get('div.chakra-card__body.css-1idwstw > div:first-child > div:first-child > div:first-child > svg')
            .should('be.visible')
            .invoke('attr', 'color')
            .should('equal', ptpDetails.results.spec.clockStatus === 'UNLOCKED' ? 'red' : 'green');
        });
    });
  });

  it('Should display PTP details with clock status as LOCKED', function () {
    const serverData = this.mockServerData;
    serverData.server.timing_type = 'PTP';
    // we have NTP data at 3rd Index, so replace it with PTP data
    const ptpDetails = {
      name: 'ptp',
      status: 'ERROR',
      cause: 'PTP No Clock Sync',
      reason: 'System clock not synced',
      repairs: ['Check PTP configuration', 'Check linuxptp service is running on server'],
      results: {
        kind: 'PtpState',
        metadata: {
          creationTimestamp: '2024-07-22T14:38:49Z',
          name: 'ptpstate-object',
        },
        spec: {
          clockStatus: 'UNLOCKED',
        },
      },
    };
    serverData.server.subsystems.push(ptpDetails);

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/orc/server/nodes/**', {
      body: serverData,
    }).as('getNodeDetails');

    cy.xpath('//table[contains(@class, "chakra-table")]').should('be.visible');

    // Find the `tr` element whose second `td` contains the text "Server-VM" and click it
    cy.xpath('//table[contains(@class, "chakra-table")]//tbody//tr[td[2][text()="Server-VM"]]').first().click();

    // Validate the presence of nodes-nodeComponents and cells-node-ServerComponents
    cy.get('tr.css-1kk81s').within(() => {
      cy.xpath('//div[@data-testid="nodes-nodeComponents"]').should('be.visible');
      cy.xpath('//div[@data-testid="cells-node-ServerComponents"]').should('be.visible');

      // Click the Server card header div
      cy.xpath('//div[@data-testid="cells-node-ServerComponents"]/div/div[1]').click();

      //  Verify PTP Card Header title
      const ptpEleXpath = '//div[contains(@class, "chakra-card__body")]/div[1]/div[2]/div[3]';
      cy.xpath(ptpEleXpath + '/div[1]/div[1]/h2/div[1]/p')
        .should('be.visible')
        .invoke('text')
        .then((text) => {
          expect(text.trim()).to.equal('PTP');
        });

      // Click the PTP card header
      cy.xpath(ptpEleXpath).click();

      // Validate the PTP card body is shown and visible
      cy.xpath(ptpEleXpath)
        .first()
        .within(() => {
          // // verify PTP Clock Status
          cy.get(
            'div:first-child > div:nth-child(3) > div:first-child > div:first-child > table > tbody > tr:nth-child(1) > td:nth-child(2) > svg'
          )
            .should('be.visible')
            .invoke('attr', 'color')
            .should('equal', ptpDetails.results.spec.clockStatus === 'UNLOCKED' ? 'red' : 'green');

          // Verify other PTP Details
          const cpuBodyXpath = '/div[1]/div[3]/div[1]/div[1]/table/tbody';
          const mappings = [
            { data_key: 'reason', key: 'Reason:', value: ptpDetails.reason },
            { data_key: 'cause', key: 'Cause:', value: ptpDetails.cause },
            { data_key: 'repairs', key: 'Repairs:', value: ptpDetails.repairs },
          ];
          mappings.forEach((mapping, index) => {
            cy.xpath(ptpEleXpath + cpuBodyXpath + `/tr[${index + 2}]/td[1]/p`)
              .invoke('text')
              .then((text) => {
                expect(text.trim()).to.equal(mapping.key);
              });

            // We have two repairs hence its  a list
            const valueXpath =
              index === 2
                ? ptpEleXpath + cpuBodyXpath + `/tr[${index + 2}]/td[2]/ul/li[1]/p`
                : ptpEleXpath + cpuBodyXpath + `/tr[${index + 2}]/td[2]/p`;

            cy.xpath(valueXpath)
              .invoke('text')
              .then((text) => {
                expect(text.trim()).to.equal(index === 2 ? mapping.value[0] : mapping.value);
              });
          });
        });
    });
  });

  it('Should display PTP details with clock status as FREERUN', function () {
    const serverData = this.mockServerData;
    serverData.server.timing_type = 'PTP';
    // we have NTP data at 3rd Index, so replace it with PTP data
    const ptpDetails = {
      name: 'ptp',
      status: 'ERROR',
      cause: 'PTP service failure',
      reason: 'PTP is in freerun',
      repairs: ['Check health of linuxptp pod'],
      results: {
        kind: 'PtpState',
        metadata: {
          creationTimestamp: '2024-07-22T14:38:49Z',
          name: 'ptpstate-object',
        },
        spec: {
          clockStatus: 'FREERUN',
        },
      },
    };
    serverData.server.subsystems.push(ptpDetails);

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/orc/server/nodes/**', {
      body: serverData,
    }).as('getNodeDetails');

    cy.xpath('//table[contains(@class, "chakra-table")]').should('be.visible');

    // Find the `tr` element whose second `td` contains the text "Server-VM" and click it
    cy.xpath('//table[contains(@class, "chakra-table")]//tbody//tr[td[2][text()="Server-VM"]]').first().click();

    // Validate the presence of nodes-nodeComponents and cells-node-ServerComponents
    cy.get('tr.css-1kk81s').within(() => {
      cy.xpath('//div[@data-testid="nodes-nodeComponents"]').should('be.visible');
      cy.xpath('//div[@data-testid="cells-node-ServerComponents"]').should('be.visible');

      // Click the Server card header div
      cy.xpath('//div[@data-testid="cells-node-ServerComponents"]/div/div[1]').click();

      //  Verify PTP Card Header title
      const ptpEleXpath = '//div[contains(@class, "chakra-card__body")]/div[1]/div[2]/div[3]';
      cy.xpath(ptpEleXpath + '/div[1]/div[1]/h2/div[1]/p')
        .should('be.visible')
        .invoke('text')
        .then((text) => {
          expect(text.trim()).to.equal('PTP');
        });

      // Click the PTP card header
      cy.xpath(ptpEleXpath).click();

      // Validate the PTP card body is shown and visible
      cy.xpath(ptpEleXpath)
        .first()
        .within(() => {
          // // verify PTP Clock Status
          cy.get(
            'div:first-child > div:nth-child(3) > div:first-child > div:first-child > table > tbody > tr:nth-child(1) > td:nth-child(2) > svg'
          )
            .should('be.visible')
            .invoke('attr', 'color')
            .should('equal', 'red');

          // Verify other PTP Details
          const cpuBodyXpath = '/div[1]/div[3]/div[1]/div[1]/table/tbody';
          const mappings = [
            { data_key: 'reason', key: 'Reason:', value: ptpDetails.reason },
            { data_key: 'cause', key: 'Cause:', value: ptpDetails.cause },
            { data_key: 'repairs', key: 'Repairs:', value: ptpDetails.repairs },
          ];
          mappings.forEach((mapping, index) => {
            cy.xpath(ptpEleXpath + cpuBodyXpath + `/tr[${index + 2}]/td[1]/p`)
              .invoke('text')
              .then((text) => {
                expect(text.trim()).to.equal(mapping.key);
              });

            const valueXpath = ptpEleXpath + cpuBodyXpath + `/tr[${index + 2}]/td[2]/p`;
            cy.xpath(valueXpath)
              .invoke('text')
              .then((text) => {
                expect(text.trim()).to.equal(index === 2 ? mapping.value[0] : mapping.value);
              });
          });
        });
    });
  });

  it.only('Should display PTP details with clock status as NOT_IN_USE', function () {
    const serverData = this.mockServerData;
    serverData.server.timing_type = 'PTP';
    // we have NTP data at 3rd Index, so replace it with PTP data
    const ptpDetails = {
      name: 'ptp',
      status: 'ERROR',
      cause: 'PTP inactive',
      reason: 'PTP service not active',
      repairs: ['Check linuxptp pod is running', 'Update server manifest to alternative timing_source'],
      results: {
        kind: 'PtpState',
        metadata: {
          creationTimestamp: '2024-07-22T14:38:49Z',
          name: 'ptpstate-object',
        },
        spec: {
          clockStatus: 'NOT_IN_USE',
        },
      },
    };
    serverData.server.subsystems.push(ptpDetails);

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/orc/server/nodes/**', {
      body: serverData,
    }).as('getNodeDetails');

    cy.xpath('//table[contains(@class, "chakra-table")]').should('be.visible');

    // Find the `tr` element whose second `td` contains the text "Server-VM" and click it
    cy.xpath('//table[contains(@class, "chakra-table")]//tbody//tr[td[2][text()="Server-VM"]]').first().click();

    // Validate the presence of nodes-nodeComponents and cells-node-ServerComponents
    cy.get('tr.css-1kk81s').within(() => {
      cy.xpath('//div[@data-testid="nodes-nodeComponents"]').should('be.visible');
      cy.xpath('//div[@data-testid="cells-node-ServerComponents"]').should('be.visible');

      // Click the Server card header div
      cy.xpath('//div[@data-testid="cells-node-ServerComponents"]/div/div[1]').click();

      //  Verify PTP Card Header title
      const ptpEleXpath = '//div[contains(@class, "chakra-card__body")]/div[1]/div[2]/div[3]';
      cy.xpath(ptpEleXpath + '/div[1]/div[1]/h2/div[1]/p')
        .should('be.visible')
        .invoke('text')
        .then((text) => {
          expect(text.trim()).to.equal('PTP');
        });

      // Click the PTP card header
      cy.xpath(ptpEleXpath).click();

      // Validate the PTP card body is shown and visible
      cy.xpath(ptpEleXpath)
        .first()
        .within(() => {
          // // verify PTP Clock Status
          cy.get(
            'div:first-child > div:nth-child(3) > div:first-child > div:first-child > table > tbody > tr:nth-child(1) > td:nth-child(2) > svg'
          )
            .should('be.visible')
            .invoke('attr', 'color')
            .should('equal', 'gray');

          // Verify other PTP Details
          const cpuBodyXpath = '/div[1]/div[3]/div[1]/div[1]/table/tbody';
          const mappings = [
            { data_key: 'reason', key: 'Reason:', value: ptpDetails.reason },
            { data_key: 'cause', key: 'Cause:', value: ptpDetails.cause },
            // { data_key: 'repairs', key: 'Repairs:', value: ptpDetails.repairs },
          ];
          mappings.forEach((mapping, index) => {
            cy.xpath(ptpEleXpath + cpuBodyXpath + `/tr[${index + 2}]/td[1]/p`)
              .invoke('text')
              .then((text) => {
                expect(text.trim()).to.equal(mapping.key);
              });

            // We have two repairs hence its  a list
            const valueXpath = ptpEleXpath + cpuBodyXpath + `/tr[${index + 2}]/td[2]/p`;

            cy.xpath(valueXpath)
              .invoke('text')
              .then((text) => {
                expect(text.trim()).to.equal(index === 2 ? mapping.value[0] : mapping.value);
              });
          });
        });
    });
  });
});
