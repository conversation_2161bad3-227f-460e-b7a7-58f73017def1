/* eslint-disable cypress/no-unnecessary-waiting */
/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />
import 'cypress-real-events';

const cellOverViewUrl = '/cell-overview';
describe('Test cell overview component', () => {
  beforeEach(() => {
    cy.system_health();
    cy.useLogin();
    cy.visit(cellOverViewUrl);
  });

  it('display table', () => {
    cy.get('table').should('be.visible');
    cy.screenshot('Table screen shot');
  });

  it('display table cell highlight', () => {
    cy.get('table')
      .find('tbody')
      .find('tr')
      .first()
      .should('have.css', 'background-color')
      .and('eq', 'rgba(0, 0, 0, 0)');
    cy.get('table')
      .find('tbody')
      .find('tr')
      .first()
      .realHover()
      .should('have.css', 'background-color')
      .and('eq', 'rgb(87, 141, 168)'); //Was breaking why?
    //.and('eq', 'rgba(0, 0, 0, 0)');
    cy.screenshot('hover over cell item. background highlight');
  });

  it('display data in table', () => {
    cy.get('table').find('tbody').find('tr').should('have.length.greaterThan', 1);
  });

  it('display the status icons', () => {
    cy.get('[data-testid="cell-main-table-status-icon"]').should('have.length.greaterThan', 1);
  });

  it('hover on the status icons', () => {
    cy.get('[data-testid="cell-main-table-status-icon"]').first().realHover();
  });

  //NOTE: need to have higher privilege to access this stuff
  //TODO: Add cell
  it('Add a cell and make sure it present in cellOverview Cell tab', () => {
    cy.get('[data-testid="create-cell-button"]').should('be.visible').should('have.text', 'Create cell');
  });
  //TODO: delete cell
  //TODO: edit cell
  //TODO: Add node
  //TODO: edit node
  //TODO: delete node
});

describe('Test tabs', () => {
  beforeEach(() => {
    cy.system_health();
    cy.useLogin();
    cy.visit(cellOverViewUrl);
  });

  //TODO: open nodes tab and check the data
  it('open nodes tab and click through', () => {
    cy.get('.chakra-tabs__tablist .chakra-tabs__tab').should('have.length', 2);
    cy.get('.chakra-tabs__tablist .chakra-tabs__tab:last-child ').click();
    cy.get('thead').contains('Node Type').should('be.visible');
    cy.get('tbody').get('tr').should('have.length.greaterThan', 1);
    cy.get('tbody').get('tr').eq(1).click();
    cy.get('[data-testid="nodes-nodeComponents"]').should('be.visible');
  });
  //TODO: open cells tab and check the data
  it('open cells tab and click through', () => {
    cy.get('.chakra-tabs__tablist .chakra-tabs__tab').should('have.length', 2);
    cy.get('.chakra-tabs__tablist .chakra-tabs__tab:last-child ').click();
    cy.get('h2').contains('Nodes').should('be.visible');
    cy.get('.chakra-tabs__tablist .chakra-tabs__tab:first-child ').click();
    cy.get('h2').contains('Cells').should('be.visible');
    cy.get('tbody').get('tr').should('have.length.greaterThan', 1);
    cy.get('tbody').get('tr').eq(1).click();
    cy.get('[data-testid="cells-node-streetCell"]').should('be.visible');
    cy.get('[data-testid="cells-node-streetCell"]').eq(0).click();
    cy.get('[data-testid="nodes-nodeComponents"]').should('be.visible');
  });
  //TODO: switch between nodes to cells and vice versa
});

describe('Test cell drill down', () => {
  beforeEach(() => {
    cy.system_health();
    cy.useLogin();
    //localStorage.setItem('dw_authToken', tokenValue);
    cy.visit(cellOverViewUrl);
  });

  it('display cell split', () => {
    cy.get('table').contains('GBDEV2NS000001').click();
    cy.get('tbody').contains('AABA31700001').should('be.visible');
  });

  it('open cell A', () => {
    cy.get('table').contains('GBDEV2NS000001').click();
    cy.get('tbody').contains('AABA31700001').click();
    //cy.wait('1000').then(() => {
    cy.wait(5000).then(() => {
      // Wait for up to 5 seconds
      cy.get('[data-testid="cells-node-StreetCellComponents"]').contains('5G CU Control plane').should('be.visible');
      cy.get('[data-testid="cells-node-StreetCellComponents"]').contains('5G CU User plane').should('be.visible');
    });
    cy.get('.chakra-card__header').contains('5G DU').should('be.visible');
    cy.get('.chakra-card__header').contains('5G RU').should('be.visible');
    cy.get('.chakra-card__header').contains('MMWave').should('be.visible');
    cy.get('.chakra-card__header').contains('Switch').should('be.visible');
    cy.get('.chakra-card__header').contains('Controller board').should('be.visible');
    //});
  });

  it('view reported location link', () => {
    cy.get('table').contains('GBDEV2NS000001').click();
    cy.get('a').should('have.attr', 'href');
    cy.get('a').contains('View installed location').should('be.visible');
    cy.get('a').contains('View installed location').find('svg').should('be.visible');
  });

  //Reimplement when we have version in inventory manager
  //TODO: for the 4 below we need to check what data is going to through and it can display 2  errorCard or normal success card
  it('open airspan modal', () => {
    cy.get('table').contains('GBDEV2NS000001').click();
    cy.get('tbody').contains('AABA31700001').click();
    cy.wait(5000).then(() => {
      // Wait for up to 5 seconds
      cy.get('.chakra-card__header').contains('5G RU').click();
    });
    //cy.get('.chakra-card__body').contains('Component id', { matchCase: false }).first().should('be.visible'); //This can fail if the data is not there
    cy.get('.chakra-card__body').should('be.visible');
  });

  it('open Switch modal', () => {
    cy.get('table').contains('GBDEV2NS000001').click();
    cy.get('tbody').contains('AABA31700001').click();
    cy.wait(5000).then(() => {
      // Wait for up to 5 seconds
      cy.get('.chakra-card__header').contains('Switch').click();
    });
    cy.get('.chakra-card__body').contains('Component id', { matchCase: false }).first().should('be.visible');
  });

  it('open MMWave modal', () => {
    cy.get('table').contains('GBDEV2NS000001').click();
    cy.get('tbody').contains('AABA31700001').click();
    cy.wait(5000).then(() => {
      cy.get('.chakra-card__header').contains('MMWave').click();
    });
    cy.get('.chakra-card__body').contains('Component id', { matchCase: false }).first().should('be.visible');
  });

  it('open Controller board modal', () => {
    cy.get('table').contains('GBDEV2NS000001').click();
    cy.get('tbody').contains('AABA31700001').click();
    cy.wait(5000).then(() => {
      cy.get('.chakra-card__header').contains('Controller board').click();
    });
    cy.get('.chakra-card__body').contains('Component id', { matchCase: false }).first().should('be.visible');
  });
});

describe('Test sort functionality on cell table view', () => {
  beforeEach(() => {
    cy.system_health();
    cy.useLogin();
    //localStorage.setItem('dw_authToken', tokenValue);
    cy.visit(cellOverViewUrl);
  });

  //Reimplement when we have version in inventory manager
  it('cell Site name ASC', () => {
    cy.get('table').find('thead').find('tr').find('th').eq(2).should('have.text', 'Site name').click();
    cy.get('table').find('tbody').find('tr').find('td').eq(2).should('have.text', 'Ansir Test');
  });

  //Reimplement when we have version in inventory manager
  it('cell Site name DESC', () => {
    cy.get('table').find('thead').find('tr').find('th').eq(2).should('have.text', 'Site name').click();
    cy.get('table').find('thead').find('tr').find('th').eq(2).should('have.text', 'Site name').click();
    cy.get('table').find('tbody').find('tr').find('td').eq(2).should('have.text', 'new site test');
  });

  it('cell Type DESC', () => {
    cy.get('table').find('thead').find('tr').find('th').eq(1).should('have.text', 'Cell Type').click();
    cy.get('table').find('tbody').find('tr').find('td').eq(1).should('have.text', 'Split 6'); //Mock Data
  });

  it('cell Type ASC', () => {
    cy.get('table').find('thead').find('tr').find('th').eq(1).should('have.text', 'Cell Type').click();
    cy.get('table').find('thead').find('tr').find('th').eq(1).should('have.text', 'Cell Type').click();
    cy.get('table').find('tbody').find('tr').find('td').eq(1).should('have.text', 'gNodeB');
    //.should('have.text', 'gNodeA'); //Mock Data
  });

  it('cell country code ASC', () => {
    cy.get('table').find('thead').find('tr').find('th').eq(5).should('have.text', 'Country code').click();
    cy.get('table').find('tbody').find('tr').find('td').eq(5).should('have.text', 'GBR');
  });

  it('cell country code DESC', () => {
    cy.get('table').find('thead').find('tr').find('th').eq(5).should('have.text', 'Country code').click();
    cy.get('table').find('thead').find('tr').find('th').eq(5).should('have.text', 'Country code').click();
    cy.get('table').find('tbody').find('tr').find('td').eq(5).should('have.text', 'GBR');
  });

  // it keeps on changing
  it('cell region name ASC', () => {
    cy.get('table').find('thead').find('tr').find('th').eq(4).should('have.text', 'Region Name').click();
    cy.get('table').find('tbody').find('tr').find('td').eq(4).should('have.text', 'Marlow');
  });

  // it keeps on changing
  it('cell region DESC', () => {
    //cy.get('[data-testid="cell-main-table-region-sort"]').click();
    cy.get('table').find('thead').find('tr').find('th').eq(4).should('have.text', 'Region Name').click();
    cy.get('table').find('thead').find('tr').find('th').eq(4).should('have.text', 'Region Name').click();
    cy.get('table').find('tbody').find('tr').find('td').eq(4).should('have.text', 'Millbrook');
  });

  // it keeps on changing
  it('cell lifecycle ASC', () => {
    cy.get('table').find('thead').find('tr').find('th').eq(6).should('have.text', 'Lifecycle').click();
    cy.get('table').find('tbody').find('tr').find('td').eq(6).should('have.text', 'COMMISSIONING');
  });

  it('cell lifecycle DESC', () => {
    cy.get('table').find('thead').find('tr').find('th').eq(6).should('have.text', 'Lifecycle').click();
    cy.get('table').find('thead').find('tr').find('th').eq(6).should('have.text', 'Lifecycle').click();
    cy.get('table').find('tbody').find('tr').find('td').eq(6).should('have.text', 'OPERATIONAL'); //Mock Data
  });

  //Reimplement when we have version in inventory manager
  it('cell status ASC', () => {
    cy.get('table').find('thead').find('tr').find('th').eq(7).should('have.text', 'Status').click();
    cy.get('table')
      .find('tbody')
      .find('tr')
      .find('td')
      .eq(7)
      .find('svg')
      .find('path')
      .should('have.attr', 'fill', 'currentColor');
  });

  //Reimplement when we have version in inventory manager
  it('cell status DESC', () => {
    cy.get('table').find('thead').find('tr').find('th').eq(7).should('have.text', 'Status').click();
    cy.get('table')
      .find('tbody')
      .find('tr')
      .find('td')
      .eq(7)
      .find('svg')
      .find('path')
      .should('have.attr', 'fill', 'currentColor');
  });
});

describe.skip('Test full screen mode', () => {
  beforeEach(() => {
    cy.system_health();
    cy.useLogin();
    //localStorage.setItem('dw_authToken', tokenValue);
    cy.visit(cellOverViewUrl);
  });
  it.skip('display fullscreen button', () => {
    cy.contains('Open table in Fullscreen Mode');
  });

  it.skip('enable fullscreen mode', () => {
    cy.contains('Open table in Fullscreen Mode').realClick();
    cy.realPress('Escape');
    //cy.get('table').type('{esc}');
  });
});

export {};
