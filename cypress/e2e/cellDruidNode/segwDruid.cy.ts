/* eslint-disable cypress/unsafe-to-chain-command */
/* eslint-disable cypress/no-unnecessary-waiting */
/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />

import 'cypress-real-events';
const nodeOverViewUrl = '/cell-overview/nodes';

describe('DruidCard Component Tests', () => {
  beforeEach(() => {
    const paths = {
      sites: 'cellCreation/AddCell/mockData/sites.json',
      cellReference: 'cellCreation/AddCell/mockData/cellReference.json',
      nodes: 'cellDruid/nodes.json',
      serverNodes: 'cellDruid/serverNodes.json',
      cells: 'common/cells.json',
      countries: 'common/countries.json',
      activeAlarmsCount: 'common/activeAlarmsCount.json',
      regions: 'common/regions.json',
    };

    // Logging paths to verify
    cy.task('log', 'Validating fixture paths...');
    cy.task('log', JSON.stringify(paths));

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/inv/stats/regions', {
      fixture: paths.regions,
    }).as('getRegions');

    cy.intercept('GET', '**/inv/stats/countries**', { fixture: paths.countries }).as('getCountries');

    cy.intercept('GET', '**/mc/alarms/active/count**', { fixture: paths.activeAlarmsCount }).as('getActiveCount');

    cy.intercept('GET', '**/inv/cells**', { fixture: paths.cells }).as('getCells');

    cy.intercept('GET', '**/inv/ref/sites', {
      fixture: paths.sites,
    }).as('getSites');

    cy.intercept('GET', '**/inv/cells/ref?site_id=*', {
      fixture: paths.cellReference,
    }).as('getCellReference');

    cy.intercept('GET', '**/orc/server/nodes/**', {
      fixture: paths.serverNodes,
    }).as('getServerNodes');

    cy.intercept('GET', '**/inv/nodes**', {
      fixture: paths.nodes,
    }).as('getNodes');

    cy.system_health();
    cy.useLogin();
    cy.visit(nodeOverViewUrl);
  });

  it('verify DRUID  View Network Routes table is rendering', () => {
    cy.get('table.chakra-table').should('be.visible');
    cy.get('table.chakra-table tbody tr').first().click();
    cy.get('[data-testid="druid-card"]').should('be.visible').first().click();
    cy.get('button').contains('View Network Routes').should('be.visible');
    cy.get('button').contains('View Network Routes').click();
    cy.get('[data-testid="modal-header"]').should('be.visible').contains('Network Routes');
  });

  it('verify DRUID SeGW Information modal is exists', () => {
    cy.get('table.chakra-table').should('be.visible');
    cy.get('table.chakra-table tbody tr').first().click();
    cy.get('[data-testid="druid-card"]').should('be.visible').first().click();
    cy.get('button').contains('SeGW Information').should('be.visible');
    cy.get('button').contains('SeGW Information').click();
    cy.get('[data-testid="modal-header"]').should('be.visible').contains('SeGW Information');
  });

  it('verify DRUID SeGW Information tables is rendering', () => {
    cy.get('table.chakra-table').should('be.visible');
    cy.get('table.chakra-table tbody tr').first().click();
    cy.get('[data-testid="druid-card"]').should('be.visible').first().click();
    cy.get('button').contains('SeGW Information').should('be.visible');
    cy.get('button').contains('SeGW Information').click();
    cy.get('[data-testid="modal-header"]').should('be.visible').contains('SeGW Information');
    cy.get('[data-testid="modal-table-title"]').should('be.visible');
    cy.get('[data-testid="modal-table-title"]').should('have.text', 'Ipsec Certificate');
    cy.get(
      '.css-1wxtb3a > [data-testid="dataTable-container"] > .chakra-table > tbody.css-0 > :nth-child(1) > :nth-child(1)'
    ).should('have.text', 'denseair_root.pem');
    cy.get(
      '.css-1wxtb3a > [data-testid="dataTable-container"] > .chakra-table > tbody.css-0 > :nth-child(1) > :nth-child(2)'
    ).should('have.text', 'C=GB, O=Dense Air Ltd, CN=Root');
  });

  it('should navigate between DRUID SeGW Information tables', () => {
    cy.get('table.chakra-table').should('be.visible');
    cy.get('table.chakra-table tbody tr').first().click();
    cy.get('[data-testid="druid-card"]').should('be.visible').first().click();
    cy.get('button').contains('SeGW Information').should('be.visible');
    cy.get('button').contains('SeGW Information').click();
    cy.get('[data-testid="modal-header"]').should('be.visible').contains('SeGW Information');
    cy.get('[data-testid="modal-table-title"]').should('be.visible');
    cy.get('[data-testid="modal-table-title"]').should('have.text', 'Ipsec Certificate');
    // back button should be disabled intially
    cy.get('[data-testid="table-back"]').should('be.visible');
    cy.get('[data-testid="table-back"]').should('have.text', 'Back');
    cy.get('[data-testid="table-back"]').should('be.disabled');
    // next button should be enabled
    cy.get('[data-testid="table-next"]').should('be.visible');
    cy.get('[data-testid="table-next"]').should('be.enabled');
    cy.get('[data-testid="table-next"]').should('have.text', 'Next');
    // navigatine to 2nd table
    cy.get('[data-testid="table-next"]').click();
    cy.get('[data-testid="modal-table-title"]').should('be.visible');
    cy.get('[data-testid="modal-table-title"]').should('have.text', 'Ipsec Private Key');
    // navigatine to 3rd table
    cy.get('[data-testid="table-next"]').click();
    cy.get('[data-testid="modal-table-title"]').should('be.visible');
    cy.get('[data-testid="modal-table-title"]').should('have.text', 'Ipsec Secure Association');
    // navigatine to 4rd table
    cy.get('[data-testid="table-next"]').click();
    cy.get('[data-testid="modal-table-title"]').should('be.visible');
    cy.get('[data-testid="modal-table-title"]').should('have.text', 'S1client');
    // next button should be disabled on last table
    cy.get('[data-testid="table-next"]').should('have.text', 'Next');
    cy.get('[data-testid="table-next"]').should('be.disabled');
    // navigating to back to 3rd table
    cy.get('[data-testid="table-back"]').should('have.text', 'Back');
    cy.get('[data-testid="table-back"]').should('be.enabled');
    cy.get('[data-testid="table-back"]').click();
    cy.get('[data-testid="modal-table-title"]').should('be.visible');
    cy.get('[data-testid="modal-table-title"]').should('have.text', 'Ipsec Secure Association');
  });
});
