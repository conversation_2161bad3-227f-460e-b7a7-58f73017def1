/* eslint-disable cypress/unsafe-to-chain-command */
/* eslint-disable cypress/no-unnecessary-waiting */
/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />

import 'cypress-real-events';
const nodeOverViewUrl = '/cell-overview/nodes';

describe.skip('DruidCard Component Tests', () => {
  beforeEach(() => {
    const paths = {
      sites: 'cellCreation/AddCell/mockData/sites.json',
      cellReference: 'cellCreation/AddCell/mockData/cellReference.json',
      nodes: 'cellDruid/nodes.json',
      serverNodes: 'cellDruid/serverNodes.json',
      cells: 'common/cells.json',
      countries: 'common/countries.json',
      activeAlarmsCount: 'common/activeAlarmsCount.json',
      regions: 'common/regions.json',
    };

    // Logging paths to verify
    cy.task('log', 'Validating fixture paths...');
    cy.task('log', JSON.stringify(paths));

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/inv/stats/regions', {
      fixture: paths.regions,
    }).as('getRegions');

    cy.intercept('GET', '**/inv/stats/countries**', { fixture: paths.countries }).as('getCountries');

    cy.intercept('GET', '**/mc/alarms/active/count**', { fixture: paths.activeAlarmsCount }).as('getActiveCount');

    cy.intercept('GET', '**/inv/cells**', { fixture: paths.cells }).as('getCells');

    cy.intercept('GET', '**/inv/ref/sites', {
      fixture: paths.sites,
    }).as('getSites');

    cy.intercept('GET', '**/inv/cells/ref?site_id=*', {
      fixture: paths.cellReference,
    }).as('getCellReference');

    cy.intercept('GET', '**/orc/server/nodes/**', {
      fixture: paths.serverNodes,
    }).as('getServerNodes');

    cy.intercept('GET', '**/inv/nodes**', {
      fixture: paths.nodes,
    }).as('getNodes');

    cy.system_health();
    cy.useLogin();
    cy.visit(nodeOverViewUrl);
  });

  it('Druid Card Details', () => {
    cy.get('table.chakra-table').should('be.visible');
    cy.get('table.chakra-table tbody tr').first().click();
    cy.get('[data-testid="druid-card"]').should('be.visible').first().click();
    cy.get(':nth-child(1) > [data-testid="stats-label-box"] > :nth-child(1) > dl > [data-testid="stats-label"]').should(
      'be.visible'
    );
    cy.get(':nth-child(1) > [data-testid="stats-label-box"] > :nth-child(1) > dl > [data-testid="stats-label"]').should(
      'have.text',
      'Device Id'
    );
    cy.get(':nth-child(1) > [data-testid="stats-label-box"] > :nth-child(2) > dl > [data-testid="stats-label"]').should(
      'be.visible'
    );
    cy.get(':nth-child(1) > [data-testid="stats-label-box"] > :nth-child(2) > dl > [data-testid="stats-label"]').should(
      'have.text',
      'Sessions'
    );
    cy.get(':nth-child(2) > [data-testid="stats-header-box"] > [data-testid="stats-header"]').should('be.visible');
    cy.get(':nth-child(2) > [data-testid="stats-header-box"] > [data-testid="stats-header"]').should(
      'have.text',
      'Features'
    );
    cy.get(':nth-child(2) > :nth-child(2) > :nth-child(1) > dl > [data-testid="stats-label"]').should('be.visible');
    cy.get(':nth-child(2) > :nth-child(2) > :nth-child(1) > dl > [data-testid="stats-label"]').should(
      'have.text',
      'Operational State'
    );
    cy.get(':nth-child(2) > :nth-child(2) > :nth-child(2) > dl > [data-testid="stats-label"]').should('be.visible');
    cy.get(':nth-child(2) > :nth-child(2) > :nth-child(2) > dl > [data-testid="stats-label"]').should(
      'have.text',
      'System Id'
    );
    cy.get(':nth-child(3) > [data-testid="stats-header-box"] > [data-testid="stats-header"]').should('be.visible');
    cy.get(':nth-child(3) > [data-testid="stats-header-box"] > [data-testid="stats-header"]').should(
      'have.text',
      'System'
    );
    cy.get(':nth-child(3) > :nth-child(2) > :nth-child(1) > dl > [data-testid="stats-label"]').should('be.visible');
    cy.get(':nth-child(3) > :nth-child(2) > :nth-child(1) > dl > [data-testid="stats-label"]').should(
      'have.text',
      'Operational State'
    );
    cy.get(':nth-child(3) > :nth-child(2) > :nth-child(2) > dl > [data-testid="stats-label"]').should('be.visible');
    cy.get(':nth-child(3) > :nth-child(2) > :nth-child(2) > dl > [data-testid="stats-label"]').should(
      'have.text',
      'Admin State'
    );
  });

  it('verify DRUID more data table is rendering', () => {
    cy.get('table.chakra-table').should('be.visible');
    cy.get('table.chakra-table tbody tr').first().click();
    cy.get('[data-testid="druid-card"]').should('be.visible').first().click();
    cy.get('button').contains('More Data').should('be.visible');
    cy.get('button').contains('More Data').click();
    cy.get('[data-testid="modal-header"]').should('be.visible').contains('Network Routes');
  });
});

describe('Druid Component Restart Button Functionality', function () {
  beforeEach(function () {
    cy.system_health();
    cy.useLogin();
    cy.visit(nodeOverViewUrl);

    cy.fixture('cellDruid/restartDruid/postApiDruidRestart').then((data) => {
      this.mockDruidRestartData = data;
    });

    cy.fixture('cellDruid/restartDruid/getApiDruidRestartStatusFailed').then((data) => {
      this.mockDruidRestartStatusFailedData = data;
    });

    cy.fixture('cellDruid/restartDruid/getApiDruidRestartStatusSuccessfull').then((data) => {
      this.mockDruidRestartStatusSuccessfullData = data;
    });
  });

  it.only('Should display Permission Denied error', function () {
    // Mocking network requests with the exact URLs
    cy.intercept('POST', '**/orc/actions/action_druid_restart', {
      body: this.mockDruidRestartData,
    }).as('actionDruidRestart');

    cy.intercept('GET', '**/orc/actions/operation/*', {
      body: this.mockDruidRestartStatusFailedData,
    }).as('actionDruidRestartStatusFailed');

    cy.get('tbody.css-0 > :nth-child(49)').click();

    cy.xpath('//p[text()="DRUID"]').click();

    cy.xpath('//p[text()="Restart "]').click();

    cy.xpath('//button[text()="Confirm"]').should('be.disabled');
    cy.xpath('//span[text()="Click to select an option"]').then(($span) => {
      $span.css('pointer-events', 'auto');
      cy.wrap($span).click();
    });

    cy.xpath('//button[text()="Under development"]').click();
    cy.xpath('//button[text()="Confirm"]').should('be.enabled');

    cy.xpath('//button[text()="Confirm"]').click();

    // a successfully toast message is displayed
    cy.xpath('//div[text()="Restart Action created successfully."]').should('be.visible');

    // permission denied error
    cy.xpath('//p[text()="Permission Denied"]').should('be.visible');
  });

  it.only('Should display successfull check mark on restart', function () {
    // Mocking network requests with the exact URLs
    cy.intercept('POST', '**/orc/actions/action_druid_restart', {
      body: this.mockDruidRestartData,
    }).as('actionDruidRestart');

    cy.intercept('GET', '**/orc/actions/operation/*', {
      body: this.mockDruidRestartStatusSuccessfullData,
    }).as('actionDruidRestartStatusSuccessfull');

    cy.get('tbody.css-0 > :nth-child(49)').click();

    cy.xpath('//p[text()="DRUID"]').click();

    cy.xpath('//p[text()="Restart "]').click();

    cy.xpath('//button[text()="Confirm"]').should('be.disabled');
    cy.xpath('//span[text()="Click to select an option"]').then(($span) => {
      $span.css('pointer-events', 'auto');
      cy.wrap($span).click();
    });

    cy.xpath('//button[text()="Under development"]').click();
    cy.xpath('//button[text()="Confirm"]').should('be.enabled');

    cy.xpath('//button[text()="Confirm"]').click();

    // a successfully toast message is displayed
    cy.xpath('//div[text()="Restart Action created successfully."]').should('be.visible');

    // for some reason svg element has only below attributes and doesnt have color green, but i have verified on UI it has green color
    // stroke: currentColor
    // fill: currentColor
    // stroke-width: 0
    // viewBox: 0 0 24 24
    // height: 25
    // width: 25

    cy.get('div[data-testid="druid-card"] > div:nth-child(1) > div:nth-child(2) > div:nth-child(1) > div:nth-child(1)')
      .scrollIntoView()
      .within(() => {
        cy.get('div').should('be.visible');
      })
      .within(() => {
        cy.get('svg').should('be.visible');
      });
  });
});
