/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />
import 'cypress-real-events';

const rolloutsURL = '/software-upgrade/rollout/create';

describe('Create Rollout', () => {
  beforeEach(() => {
    const paths = {
      rollouts: 'softwareUpgrade/rollouts.json',
      deployments: 'softwareUpgrade/deployments.json',
      rollout: 'softwareUpgrade/rollout.json',
      deployment: 'softwareUpgrade/deployment.json',
      createRollout: 'softwareUpgrade/createRollout.json',
      artifactNameList: 'softwareUpgrade/artifactNameList.json',
      createDeployment: 'softwareUpgrade/createDeployment.json',
    };

    // Logging paths to verify
    cy.task('log', 'Validating fixture paths...');
    cy.task('log', JSON.stringify(paths));

    // Intercepts
    cy.intercept('GET', '**/controller/rollout/', { fixture: paths.rollouts }).as('getRollouts');
    cy.intercept('GET', '**/controller/deployment/', { fixture: paths.deployments }).as('getDeployments');
    cy.intercept('POST', '**/controller/rollout/create', { fixture: paths.createRollout }).as('postRollout');
    cy.intercept('GET', '**/controller/preflight/compatible_software/E500', { fixture: paths.artifactNameList }).as(
      'getArtifactNameList'
    );
    cy.intercept('POST', '**/controller/deployment/create', { fixture: paths.createDeployment }).as('postDeployment');

    // Mock system health and login
    cy.system_health();
    cy.useLogin();
    cy.visit(rolloutsURL);
    cy.log('Visited Rollouts Page');
    // eslint-disable-next-line cypress/no-unnecessary-waiting
    cy.wait(2000);
  });

  it('Should create rollout and deployment', () => {
    cy.get('[data-testid="display-name"]').should('be.visible');
    cy.get('[data-testid="display-name"]').type('test rollout');

    cy.get('[data-testid="rollout-description"]').should('be.visible');
    cy.get('[data-testid="rollout-description"]').type('test rollout description');

    // create rollout
    cy.get('[data-testid="submit-button"]').should('be.visible');
    cy.get('[data-testid="submit-button"]').click();

    cy.wait('@postRollout');
    cy.get('[data-testid="deployment-container"]').should('be.visible');

    cy.get('[data-testid="rollout_id"]').should('be.visible');
    cy.get('[data-testid="rollout_id"]').should('be.disabled');

    cy.get('[data-testid="deploymentName"]').should('be.visible');
    cy.get('[data-testid="deploymentName"]').type('test deployment');

    cy.get('[data-testid="deploymentDescription"]').should('be.visible');
    cy.get('[data-testid="deploymentDescription"]').type('test deployment description');

    cy.get('[data-testid="deploymentType"]').should('be.visible').select('E500');

    cy.wait('@getArtifactNameList');

    cy.get('[data-testid="artifactName"]').should('be.visible').select('sep_24');

    // create deployment
    cy.get('[data-testid="addmore-button"]').should('be.visible');
    cy.get('[data-testid="addmore-button"]').click();

    // Wait for the deployment creation API call
    cy.wait('@postDeployment');

    cy.get('[data-testid="deploymentName"]').should('be.visible');
    cy.get('[data-testid="deploymentName"]').type('test 2 deployment');

    cy.get('[data-testid="deploymentDescription"]').should('be.visible');
    cy.get('[data-testid="deploymentDescription"]').type('test deployment description');

    cy.get('[data-testid="deploymentType"]').should('be.visible').select('E500');

    cy.wait('@getArtifactNameList');

    cy.get('[data-testid="artifactName"]').should('be.visible').select('sep_24');

    // create deployment
    cy.get('[data-testid="submit-button"]').should('be.visible');
    cy.get('[data-testid="submit-button"]').click();

    // Wait for the deployment creation API call
    cy.wait('@postDeployment');

    // Check that the URL has changed to '/software-upgrade/rollout'
    cy.url().should('include', '/software-upgrade/rollout');
  });
});
