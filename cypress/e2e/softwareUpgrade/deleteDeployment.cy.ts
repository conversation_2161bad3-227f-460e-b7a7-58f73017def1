/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />
import 'cypress-real-events';

const rolloutsURL = '/software-upgrade/deployment';

describe('Delete Deployment', () => {
  beforeEach(() => {
    const paths = {
      rollouts: 'softwareUpgrade/rollouts.json',
      deployments: 'softwareUpgrade/deployments.json',
      rollout: 'softwareUpgrade/rollout.json',
      deployment: 'softwareUpgrade/deployment.json',
      createRollout: 'softwareUpgrade/createRollout.json',
      artifactNameList: 'softwareUpgrade/artifactNameList.json',
      createDeployment: 'softwareUpgrade/createDeployment.json',
    };

    // Logging paths to verify
    cy.task('log', 'Validating fixture paths...');
    cy.task('log', JSON.stringify(paths));

    // Intercepts
    cy.intercept('GET', '**/controller/rollout/', { fixture: paths.rollouts }).as('getRollouts');
    cy.intercept('GET', '**/controller/deployment/', { fixture: paths.deployments }).as('getDeployments');
    cy.intercept('POST', '**/controller/rollout/create', { fixture: paths.createRollout }).as('postRollout');
    cy.intercept('GET', '**/controller/preflight/compatible_software/E500', { fixture: paths.artifactNameList }).as(
      'getArtifactNameList'
    );
    cy.intercept('POST', '**/controller/deployment/create', { fixture: paths.createDeployment }).as('postDeployment');
    cy.intercept('POST', '**/controller/rollout/d7867591-ae02-4089-be6b-e5addeef8191/activate', {
      statusCode: 200,
      body: { success: true },
    }).as('activate');

    cy.intercept('POST', '**/controller/rollout/d7867591-ae02-4089-be6b-e5addeef8191', {
      statusCode: 200,
      body: { success: true },
    }).as('deleteRollout');

    cy.intercept('DELETE', '**/controller/deployment/b5fa778e-d6af-45a4-8793-e8cb73e0a410', {
      statusCode: 200,
      body: { success: true },
    }).as('deleteDeployment');

    // Mock system health and login
    cy.system_health();
    cy.useLogin();
    cy.visit(rolloutsURL);
    cy.log('Visited Rollouts Page');
    // eslint-disable-next-line cypress/no-unnecessary-waiting
    cy.wait(2000);
  });

  it('delete a deployment', () => {
    cy.get('table.chakra-table').should('be.visible');

    cy.get('table.chakra-table tbody tr')
      .first()
      .within(() => {
        cy.get('[data-testid="deployment-menu-items"]').click();
        cy.get('[data-testid="delete-deployment"]').click({ force: true });
      });

    cy.get('[data-testid="delete-config-modal"]').find('button').contains('Delete').click();

    cy.wait('@deleteDeployment').its('response.statusCode').should('eq', 200);
  });
});
