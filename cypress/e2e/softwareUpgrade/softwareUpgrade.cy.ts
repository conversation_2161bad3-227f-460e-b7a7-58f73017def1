/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />
import 'cypress-real-events';

const rolloutsURL = '/software-upgrade/rollouts';

describe('Test Software Upgrade Component', () => {
  beforeEach(() => {
    const paths = {
      rollouts: 'softwareUpgrade/rollouts.json',
      deployments: 'softwareUpgrade/deployments.json',
      rollout: 'softwareUpgrade/rollout.json',
      deployment: 'softwareUpgrade/deployment.json',
    };

    // Logging paths to verify
    cy.task('log', 'Validating fixture paths...');
    cy.task('log', JSON.stringify(paths));

    // Intercepts
    cy.intercept('GET', '**/controller/rollout', { fixture: paths.rollouts }).as('getRollouts');
    cy.intercept('GET', '**/controller/rollout/**', { fixture: paths.rollout }).as('getRollout');
    cy.intercept('GET', '**/controller/deployments', { fixture: paths.deployments }).as('getDeployments');
    cy.intercept('GET', '**/controller/deployment/**', { fixture: paths.deployment }).as('getDeployment');

    // Mock system health and login
    cy.system_health();
    cy.useLogin();
    cy.visit(rolloutsURL);
    cy.log('Visited Rollouts Page');
    // eslint-disable-next-line cypress/no-unnecessary-waiting
    cy.wait(2000);
  });

  it('Should display Rollouts table and data', () => {
    cy.get('table.chakra-table').should('be.visible');
    cy.get('table.chakra-table tbody tr').should('have.length.greaterThan', 0);

    // Verify the Rollouts columns
    cy.get('table.chakra-table th').contains('Rollout ID').should('be.visible');
    cy.get('table.chakra-table th').contains('Name').should('be.visible');
    cy.get('table.chakra-table th').contains('Description').should('be.visible');
    cy.get('table.chakra-table th').contains('Created At').should('be.visible');
  });

  it('Should expand and display Deployment details in RolloutDetails', () => {
    cy.get('table.chakra-table tbody tr').first().click();
    cy.get('[data-testid="innerComponent-Deployments"]').should('be.visible').and('have.text', 'Deployments');

    // Verify the columns in the Deployments table
    cy.get('table.chakra-table th').contains('Deployment ID').should('be.visible');
    cy.get('table.chakra-table th').contains('Name').should('be.visible');
    cy.get('table.chakra-table th').contains('Description').should('be.visible');
    cy.get('table.chakra-table th').contains('Artifact Name').should('be.visible');
    cy.get('table.chakra-table th').contains('Deployment Type').should('be.visible');
    cy.get('table.chakra-table th').contains('Rollout ID').should('be.visible');
    cy.get('table.chakra-table th').contains('Status').should('be.visible');
  });

  it('Should expand and display Event details in RolloutDetails', () => {
    cy.get('table.chakra-table tbody tr').first().click();
    cy.get('[data-testid="innerComponent-Events"]').should('be.visible').and('have.text', 'Events');

    // Verify the columns in the Events table
    cy.get('table.chakra-table th').contains('Action').should('be.visible');
    cy.get('table.chakra-table th').contains('User').should('be.visible');
    cy.get('table.chakra-table th').contains('Created At').should('be.visible');
  });

  it('Should expand and display Contact details in RolloutDetails', () => {
    cy.get('table.chakra-table tbody tr').first().click();
    cy.get('[data-testid="innerComponent-Contacts"]').should('be.visible').and('have.text', 'Contacts');
  });

  it('Should be able to render Deployment tab and table', () => {
    cy.get('#tabs-\\:rl\\:--tab-1').should('be.visible').and('have.text', 'Deployment');
    cy.get('#tabs-\\:rl\\:--tab-1').click();
    cy.get('table.chakra-table').should('be.visible');
    cy.get('table.chakra-table tbody tr').should('have.length.greaterThan', 0);

    // Verify the columns in the Deployments table
    cy.get('table.chakra-table th').contains('Deployment ID').should('be.visible');
    cy.get('table.chakra-table th').contains('Rollout ID').should('be.visible');
    cy.get('table.chakra-table th').contains('Name').should('be.visible');
    cy.get('table.chakra-table th').contains('Description').should('be.visible');
    cy.get('table.chakra-table th').contains('Artifact Name').should('be.visible');
    cy.get('table.chakra-table th').contains('Deployment Type').should('be.visible');
    cy.get('table.chakra-table th').contains('Status').should('be.visible');
  });
});
