/* eslint-disable cypress/no-unnecessary-waiting */
/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />
import 'cypress-real-events';

const oRanDuCuManagerUrl = '/oran-du-cu-manager';

describe('Test cell O-RAN DU CU Manager landing page', () => {
  beforeEach(() => {
    cy.system_health();
    cy.useLogin();
    cy.visit(oRanDuCuManagerUrl);
  });

  it('display advanced view Landing page', () => {
    cy.navigateToAdvancedView();

    cy.get('.chakra-container').find('[data-testid="how-to-use"]').should('contain', 'How to use:');
    cy.get('.chakra-container').find('[data-testid="config-sets"]').should('exist');
    cy.get('.chakra-container').find('[data-testid="custom-resources"]').should('exist');
    cy.get('.chakra-container').find('[data-testid="pods"]').should('exist');
    cy.get('.chakra-container').find('[data-testid="clusters"]').should('exist');
    cy.get('[data-testid="ORAN-DU-CU-Manager-App-Bar"]').should('exist');
  });

  it('display config sets', () => {
    cy.navigateToAdvancedView();

    cy.get('.chakra-container').find('[data-testid="config-sets"]').find('button').click();
    cy.get('.chakra-container').should('contain', 'Config sets');
    cy.get('.chakra-container').find('h2').should('contain', 'Config set manager');
    cy.get('[data-testid="ORAN-DU-CU-Manager-App-Bar"]').should('exist');
    cy.get('body').then((body) => {
      if (body.find('.chakra-table').length > 0) {
        // The table exists
        cy.get('.chakra-table').should('exist');
      } else if (body.find('.chakra-table:contains("No data")').length > 0) {
        // The "No data" text exists
        cy.contains('No data').should('exist');
      } else {
        // Neither the table nor the "No data" text exists
        throw new Error('Neither table nor "No data" text found');
      }
    });
  });

  it('display custom resources', () => {
    cy.navigateToAdvancedView();

    cy.get('.chakra-container').find('[data-testid="custom-resources"]').find('button').click();
    cy.get('.chakra-container').should('contain', 'Custom resources');
    cy.get('.chakra-container').find('h2').should('contain', 'Custom resources');
    cy.get('[data-testid="ORAN-DU-CU-Manager-App-Bar"]').should('exist');
  });

  it('display pods', () => {
    cy.navigateToAdvancedView();

    cy.get('.chakra-container').find('[data-testid="pods"]').find('button').click();
    cy.get('.chakra-container').should('contain', 'Pods');
    cy.get('.chakra-container').find('h2').should('contain', 'Pods');
    cy.get('[data-testid="ORAN-DU-CU-Manager-App-Bar"]').should('exist');
  });

  it('display clusters', () => {
    cy.navigateToAdvancedView();

    cy.get('.chakra-container').find('[data-testid="clusters"]').find('button').click();
    cy.get('.chakra-container').should('contain', 'Clusters');
    cy.get('.chakra-container').find('h2').should('contain', 'Clusters');
    cy.get('[data-testid="ORAN-DU-CU-Manager-App-Bar"]').should('exist');
    cy.get('body').then((body) => {
      if (body.find('.chakra-table').length > 0) {
        // The table exists
        cy.get('.chakra-table').should('exist');
      } else if (body.find('.chakra-table:contains("No data")').length > 0) {
        // The "No data" text exists
        cy.contains('No data').should('exist');
      } else {
        // Neither the table nor the "No data" text exists
        throw new Error('Neither table nor "No data" text found');
      }
    });
  });
});
