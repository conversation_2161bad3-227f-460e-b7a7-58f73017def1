/* eslint-disable cypress/no-unnecessary-waiting */
/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />
import 'cypress-real-events';

const oRanDuCuManagerUrl = '/oran-du-cu-manager';

describe('Test Create DU Custom Resource', () => {
  beforeEach(() => {
    cy.system_health();
    cy.useLogin();
    cy.visit(oRanDuCuManagerUrl);
  });

  // Create deployment custom resource
  it('Create config set for CUUP Deployment', () => {
    cy.navigateToAdvancedView();
    cy.get('.chakra-container').find('[data-testid="custom-resources"]').find('button').click();
    cy.get('.chakra-container').find('h2').should('contain', 'Custom resources');
    cy.get('[data-testid="ORAN-DU-CU-CustomResource-Manager"]').should('exist');

    cy.get('[data-testid="ORAN-DU-CU-CustomResource-Manager"]')
      .find('select.chakra-select')
      .first()
      .should('exist')
      .select('11: denseair-gke-bmctl-edge-5-1-16-6');

    cy.get('.chakra-accordion').find('h2').should('contain', 'table for selected cluster');

    cy.get('[data-testid="ORAN-DU-CU-CustomResource-Manager"]')
      .find('select.chakra-select')
      .last()
      .should('exist')
      .select('CuUp');

    cy.get('input[type="text"]')
      .clear()
      .type('cypress-create-cr-cuup-site-name', { delay: 100 })
      .should('have.value', 'cypress-create-cr-cuup-site-name');

    cy.get('.chakra-alert').last().find('p').should('contain', 'CuUp requires the following custom resources:');

    cy.get('.chakra-heading').should('contain', 'Missing CRs');

    cy.get('.chakra-card')
      .should('contain', 'CuUpDeployment')
      .contains('button.chakra-button', 'Create CuUpDeployment')
      .should('exist')
      .click();

    cy.get('.chakra-container').find('h2').should('contain', 'Create deployment custom resource');

    cy.get('select.chakra-select').should('exist').select('cu_up_deployment');

    cy.get('.chakra-container')
      .contains('button.chakra-button', 'Create config custom resource')
      .should('exist')
      .click();

    cy.get('.chakra-alert').should('contain', 'Deployment custom resource has been created successfully.');
  });

  // Create app config custom resource
  it('Create config set for CUUP App Config', () => {
    cy.navigateToAdvancedView();
    cy.get('.chakra-container').find('[data-testid="custom-resources"]').find('button').click();
    cy.get('.chakra-container').find('h2').should('contain', 'Custom resources');
    cy.get('[data-testid="ORAN-DU-CU-CustomResource-Manager"]').should('exist');

    cy.get('[data-testid="ORAN-DU-CU-CustomResource-Manager"]')
      .find('select.chakra-select')
      .first()
      .should('exist')
      .select('11: denseair-gke-bmctl-edge-5-1-16-6');

    cy.get('.chakra-accordion').find('h2').should('contain', 'table for selected cluster');

    cy.get('[data-testid="ORAN-DU-CU-CustomResource-Manager"]')
      .find('select.chakra-select')
      .last()
      .should('exist')
      .select('CuUp');

    cy.get('input[type="text"]')
      .clear()
      .type('cypress-create-cr-cuup-site-name', { delay: 100 })
      .should('have.value', 'cypress-create-cr-cuup-site-name');

    cy.get('.chakra-alert').last().find('p').should('contain', 'CuUp requires the following custom resources:');

    cy.get('.chakra-heading').should('contain', 'Missing CRs');

    cy.get('.chakra-card')
      .should('contain', 'CuUpAppConfig')
      .contains('button.chakra-button', 'Create CuUpAppConfig')
      .should('exist')
      .click();
    cy.get('.chakra-container').find('h2').should('contain', 'Create Config Custom Resource');
    cy.get('select.chakra-select').should('exist').select('cuupappconfigs');

    cy.get('.chakra-accordion').contains('div', 'cuupappconfigs').click();
    cy.get('.chakra-accordion')
      .contains('button', 'Cypress_Test_Value_CU_UP_App_Config-Identity-and-Connectivity')
      .should('exist')
      .click();

    cy.get('.chakra-container')
      .contains('button.chakra-button', 'Create config custom resource')
      .should('exist')
      .click();

    cy.get('.chakra-alert').should('contain', 'Custom resource created.');
  });

  //Check if all the custom resources are created
  it('Check all CRs are created and you can proceed to creating a pod', () => {
    cy.navigateToAdvancedView();

    cy.get('.chakra-container').find('[data-testid="custom-resources"]').find('button').click();

    cy.get('.chakra-container').find('h2').should('contain', 'Custom resources');

    cy.get('[data-testid="ORAN-DU-CU-CustomResource-Manager"]').should('exist');

    cy.get('[data-testid="ORAN-DU-CU-CustomResource-Manager"]')
      .find('select.chakra-select')
      .first()
      .should('exist')
      .select('11: denseair-gke-bmctl-edge-5-1-16-6');

    cy.get('.chakra-accordion').find('h2').should('contain', 'table for selected cluster');

    cy.get('[data-testid="ORAN-DU-CU-CustomResource-Manager"]')
      .find('select.chakra-select')
      .last()
      .should('exist')
      .select('CuUp');

    cy.get('input[type="text"]')
      .clear()
      .type('cypress-create-cr-cuup-site-name', { delay: 100 })
      .should('have.value', 'cypress-create-cr-cuup-site-name');

    cy.get('.chakra-alert').last().find('p').should('contain', 'CuUp requires the following custom resources:');

    cy.get('.chakra-heading').should('contain', 'Complete CRs');
    cy.get('.chakra-container').contains('button', 'Create Pod').should('exist');
  });
});
