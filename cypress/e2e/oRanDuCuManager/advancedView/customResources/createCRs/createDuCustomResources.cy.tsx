/* eslint-disable cypress/no-unnecessary-waiting */
/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />
import 'cypress-real-events';

const oRanDuCuManagerUrl = '/oran-du-cu-manager';

//NOTE: this code: cy.intercept('GET', '/nms/dev1/rad/config/parameter_sets/8').as('inputRenderTrigger');
// will need to be changed on a per environment basis. /nms/dev1/ will need to be changed to /nms/dev2/ or /nms/test1/ etc.
// it will need to be injected in when we push the code to the environment.

describe('Test Create DU Custom Resource', () => {
  beforeEach(() => {
    cy.system_health();
    cy.useLogin();
    cy.visit(oRanDuCuManagerUrl);
  });

  // Create deployment custom resource
  it('Create config set for DU Deployment - Identity and Connectivity', () => {
    cy.navigateToAdvancedView();

    cy.get('.chakra-container').find('[data-testid="custom-resources"]').find('button').click();

    cy.get('.chakra-container').find('h2').should('contain', 'Custom resources');
    cy.get('[data-testid="ORAN-DU-CU-CustomResource-Manager"]').should('exist');

    cy.get('[data-testid="ORAN-DU-CU-CustomResource-Manager"]')
      .find('select.chakra-select')
      .first()
      .should('exist')
      .select('11: denseair-gke-bmctl-edge-5-1-16-6');

    cy.get('.chakra-accordion').find('h2').should('contain', 'table for selected cluster');

    cy.get('[data-testid="ORAN-DU-CU-CustomResource-Manager"]')
      .find('select.chakra-select')
      .last()
      .should('exist')
      .select('Du');

    cy.get('input[type="text"]')
      .clear()
      .type('cypress-test-value-create-cr-du-site-name', { delay: 100 })
      .should('have.value', 'cypress-test-value-create-cr-du-site-name');

    cy.get('.chakra-alert').last().find('p').should('contain', 'Du requires the following custom resources:');

    cy.get('.chakra-heading').should('contain', 'Missing CRs');
    cy.get('.chakra-card')
      .should('contain', 'Split6DuDeployment')
      .contains('button.chakra-button', 'Create Split6DuDeployment')
      .should('exist')
      .click();
    cy.get('.chakra-container').find('h2').should('contain', 'Create deployment custom resource');
    cy.get('select.chakra-select').should('exist').select('du_deployment');

    cy.get('.chakra-container')
      .contains('button.chakra-button', 'Create config custom resource')
      .should('exist')
      .click();
  });

  // Create app config custom resource
  it('Create config set for Split 6 DU - App Config', () => {
    cy.navigateToAdvancedView();

    cy.get('.chakra-container').find('[data-testid="custom-resources"]').find('button').click();

    cy.get('.chakra-container').find('h2').should('contain', 'Custom resources');
    cy.get('[data-testid="ORAN-DU-CU-CustomResource-Manager"]').should('exist');

    cy.get('[data-testid="ORAN-DU-CU-CustomResource-Manager"]')
      .find('select.chakra-select')
      .first()
      .should('exist')
      .select('11: denseair-gke-bmctl-edge-5-1-16-6');

    cy.get('.chakra-accordion').find('h2').should('contain', 'table for selected cluster');

    cy.get('[data-testid="ORAN-DU-CU-CustomResource-Manager"]')
      .find('select.chakra-select')
      .last()
      .should('exist')
      .select('Du');

    cy.get('input[type="text"]')
      .clear()
      .type('cypress-test-value-create-cr-du-site-name', { delay: 100 })
      .should('have.value', 'cypress-test-value-create-cr-du-site-name');

    cy.get('.chakra-alert').last().find('p').should('contain', 'Du requires the following custom resources:');

    cy.get('.chakra-heading').should('contain', 'Missing CRs');
    cy.get('.chakra-card')
      .should('contain', 'Split6DuAppConfig')
      .contains('button.chakra-button', 'Create Split6DuAppConfig')
      .should('exist')
      .click();
    cy.get('.chakra-container').find('h2').should('contain', 'Create Config Custom Resource');
    cy.get('select.chakra-select').should('exist').select('split6duappconfigs');

    cy.get('.chakra-container')
      .contains('button.chakra-button', 'Create config custom resource')
      .should('exist')
      .click();
  });

  // Create cell config custom resource
  // it('Create config set for Du - Cell Config - NO CONFIG SET', () => {
  //   cy.navigateToAdvancedView();

  //   cy.get('.chakra-container').find('[data-testid="custom-resources"]').find('button').click();

  //   cy.get('.chakra-container').find('h2').should('contain', 'Custom resources');
  //   cy.get('[data-testid="ORAN-DU-CU-CustomResource-Manager"]').should('exist');

  //   cy.get('[data-testid="ORAN-DU-CU-CustomResource-Manager"]')
  //     .find('select.chakra-select')
  //     .first()
  //     .should('exist')
  //     .select('11: denseair-gke-bmctl-edge-5-1-16-6');

  //   cy.get('.chakra-accordion').find('h2').should('contain', 'table for selected cluster');

  //   cy.get('[data-testid="ORAN-DU-CU-CustomResource-Manager"]')
  //     .find('select.chakra-select')
  //     .last()
  //     .should('exist')
  //     .select('Du');

  //   cy.get('input[type="text"]')
  //     .clear()
  //     .type('cypress-test-value-create-cr-du-site-name', { delay: 100 })
  //     .should('have.value', 'cypress-test-value-create-cr-du-site-name');

  //   cy.get('.chakra-alert').last().find('p').should('contain', 'Du requires the following custom resources:');

  //   cy.get('.chakra-heading').should('contain', 'Missing CRs');
  //   cy.get('.chakra-card')
  //     .should('contain', 'DuCellConfig')
  //     .contains('button.chakra-button', 'Create DuCellConfig')
  //     .should('exist')
  //     .click();
  //   cy.get('.chakra-container').find('h2').should('contain', 'Create Config Custom Resource');
  //   cy.get('select.chakra-select').should('exist').select('ducellconfigs');

  //   cy.get('.chakra-container')
  //     .contains('button.chakra-button', 'Create config custom resource')
  //     .should('exist')
  //     .click();
  // });

  // Create cell config custom resource
  it('Create config set for Du - Cell Config - MULTI CONFIG SET', () => {
    cy.navigateToAdvancedView();

    cy.get('.chakra-container').find('[data-testid="custom-resources"]').find('button').click();

    cy.get('.chakra-container').find('h2').should('contain', 'Custom resources');
    cy.get('[data-testid="ORAN-DU-CU-CustomResource-Manager"]').should('exist');

    cy.get('[data-testid="ORAN-DU-CU-CustomResource-Manager"]')
      .find('select.chakra-select')
      .first()
      .should('exist')
      .select('11: denseair-gke-bmctl-edge-5-1-16-6');

    cy.get('.chakra-accordion').find('h2').should('contain', 'table for selected cluster');

    cy.get('[data-testid="ORAN-DU-CU-CustomResource-Manager"]')
      .find('select.chakra-select')
      .last()
      .should('exist')
      .select('Du');

    cy.get('input[type="text"]')
      .clear()
      .type('cypress-test-value-create-cr-du-site-name', { delay: 100 })
      .should('have.value', 'cypress-test-value-create-cr-du-site-name');

    cy.get('.chakra-alert').last().find('p').should('contain', 'Du requires the following custom resources:');

    cy.get('.chakra-heading').should('contain', 'Missing CRs');
    cy.get('.chakra-card')
      .should('contain', 'DuCellConfig')
      .contains('button.chakra-button', 'Create DuCellConfig')
      .should('exist')
      .click();
    cy.get('.chakra-container').find('h2').should('contain', 'Create Config Custom Resource');
    cy.get('select.chakra-select').should('exist').select('ducellconfigs');

    cy.get('.chakra-container').contains('button.chakra-button', 'Create config custom resource').should('exist');
    cy.get('.chakra-container').contains('button', 'ducellconfigs').should('exist').click();
    cy.get('.chakra-accordion').contains('button', 'Cypress_Test_Value_DU_Cell_Config-MAC').should('exist').click();
    cy.get('.chakra-accordion')
      .contains('button', 'Cypress_Test_Value_DU_Cell_Config-SIB1/Power')
      .should('exist')
      .click();
    cy.get('.chakra-container')
      .contains('button.chakra-button', 'Create config custom resource')
      .should('exist')
      .click();
  });

  //Check if all the custom resources are created
  it('Check all CRs are created and you can proceed to creating a pod', () => {
    cy.navigateToAdvancedView();

    cy.get('.chakra-container').find('[data-testid="custom-resources"]').find('button').click();

    cy.get('.chakra-container').find('h2').should('contain', 'Custom resources');
    cy.get('[data-testid="ORAN-DU-CU-CustomResource-Manager"]').should('exist');

    cy.get('[data-testid="ORAN-DU-CU-CustomResource-Manager"]')
      .find('select.chakra-select')
      .first()
      .should('exist')
      .select('11: denseair-gke-bmctl-edge-5-1-16-6');

    cy.get('.chakra-accordion').find('h2').should('contain', 'table for selected cluster');

    cy.get('[data-testid="ORAN-DU-CU-CustomResource-Manager"]')
      .find('select.chakra-select')
      .last()
      .should('exist')
      .select('Du');

    cy.get('input[type="text"]')
      .clear()
      .type('cypress-test-value-create-cr-du-site-name', { delay: 100 })
      .should('have.value', 'cypress-test-value-create-cr-du-site-name');

    cy.get('.chakra-alert').last().find('p').should('contain', 'Du requires the following custom resources:');

    cy.get('.chakra-heading').should('contain', 'Complete CRs');
    cy.get('.chakra-container').contains('button', 'Create Pod').should('exist');
  });
});
