/* eslint-disable cypress/no-unnecessary-waiting */
/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />
import 'cypress-real-events';

const oRanDuCuManagerUrl = '/oran-du-cu-manager';

describe('Test cell O-RAN DU cell and app config sets deletion', () => {
  beforeEach(() => {
    cy.system_health();
    cy.useLogin();
    cy.visit(oRanDuCuManagerUrl);
  });

  it('Delete config set for DU Cell Config - Cell_Identity', () => {
    cy.navigateToAdvancedView();
    cy.get('.chakra-container').find('[data-testid="config-sets"]').find('button').click();
    cy.get('.chakra-tabs .chakra-tabs__tablist')
      .find('button')
      .should('have.length', 5)
      .contains('DU cell configs')
      .click();
    cy.get('table')
      .contains('td', 'Cypress_Test_Value_DU_Cell_Config-Cell_Identity')
      .should('be.visible')
      .parents('tr')
      .within(() => {
        cy.get('button[data-testid="du-cu-manager-table-list-config-icon"]').click();
        cy.get('[data-testid="delete-cell"]').click({ force: true });
      });
    cy.get('.chakra-toast').should('exist');
    //cy.get('.chakra-alert').should('have.text', 'Config set deleted.');
  });

  it('Delete config set for DU Cell Config - Frequency and SSB', () => {
    cy.navigateToAdvancedView();
    cy.get('.chakra-container').find('[data-testid="config-sets"]').find('button').click();
    cy.get('.chakra-tabs .chakra-tabs__tablist')
      .find('button')
      .should('have.length', 5)
      .contains('DU cell configs')
      .click();
    cy.get('table')
      .contains('td', 'Cypress_Test_Value_DU_Cell_Config-Frequency-and-SSB-Band-40')
      .should('be.visible')
      .parents('tr')
      .within(() => {
        cy.get('button[data-testid="du-cu-manager-table-list-config-icon"]').click();
        cy.get('[data-testid="delete-cell"]').click({ force: true });
      });
    cy.get('.chakra-toast').should('exist');
    //cy.get('.chakra-alert').should('have.text', 'Config set deleted.');
  });

  it('Delete config set for DU Cell Config - SIB1 / Power', () => {
    cy.navigateToAdvancedView();
    cy.get('.chakra-container').find('[data-testid="config-sets"]').find('button').click();
    cy.get('.chakra-tabs .chakra-tabs__tablist')
      .find('button')
      .should('have.length', 5)
      .contains('DU cell configs')
      .click();
    cy.get('table')
      .contains('td', 'Cypress_Test_Value_DU_Cell_Config-SIB1/Power')
      .should('be.visible')
      .parents('tr')
      .within(() => {
        cy.get('button[data-testid="du-cu-manager-table-list-config-icon"]').click();
        cy.get('[data-testid="delete-cell"]').should('be.visible').click();
      });
    cy.get('.chakra-toast').should('exist');
    //cy.get('.chakra-alert').should('have.text', 'Config set deleted..');
  });

  it('Delete config set for DU Cell Config - Timers', () => {
    cy.navigateToAdvancedView();
    cy.get('.chakra-container').find('[data-testid="config-sets"]').find('button').click();
    cy.get('.chakra-tabs .chakra-tabs__tablist')
      .find('button')
      .should('have.length', 5)
      .contains('DU cell configs')
      .click();
    cy.get('table')
      .contains('td', 'Cypress_Test_Value_DU_Cell_Config-Timers')
      .should('be.visible')
      .parents('tr')
      .within(() => {
        cy.get('button[data-testid="du-cu-manager-table-list-config-icon"]').click();
        cy.get('[data-testid="delete-cell"]').should('be.visible').click();
      });
    cy.get('.chakra-toast').should('exist');
    //cy.get('.chakra-alert').should('have.text', 'Config set deleted.');
  });

  it('Delete config set for DU Cell Config - MAC', () => {
    cy.navigateToAdvancedView();
    cy.get('.chakra-container').find('[data-testid="config-sets"]').find('button').click();
    cy.get('.chakra-tabs .chakra-tabs__tablist')
      .find('button')
      .should('have.length', 5)
      .contains('DU cell configs')
      .click();
    cy.get('table')
      .contains('td', 'Cypress_Test_Value_DU_Cell_Config-MAC')
      .should('be.visible')
      .parents('tr')
      .within(() => {
        cy.get('button[data-testid="du-cu-manager-table-list-config-icon"]').click();
        //cy.get('[data-testid="delete-cell"]').should('be.visible').click();//WHY????
        cy.get('[data-testid="delete-cell"]').click({ force: true });
        // cy.get('button')
        //   .should('have.text', 'Delete config set name - Cypress_Test_Value_DU_Cell_Config-MAC')
        //   .eq(1)
        //   .click();
      });
    cy.get('.chakra-toast').should('exist');
    //cy.get('.chakra-alert').should('have.text', 'Config set deleted.Config set has been deleted successfully.');
  });

  it('Delete config set for DU App Config - Identity and Connectivity', () => {
    cy.navigateToAdvancedView();
    cy.get('.chakra-container').find('[data-testid="config-sets"]').find('button').click();
    cy.get('.chakra-tabs .chakra-tabs__tablist')
      .find('button')
      .should('have.length', 5)
      .contains('Split-6 DU app configs')
      .click();
    cy.get('table')
      .contains('td', 'Cypress_Test_Value_DU_App_Config-Identity-and-Connectivity')
      .should('be.visible')
      .parents('tr')
      .within(() => {
        cy.get('button[data-testid="du-cu-manager-table-list-config-icon"]').click();
        cy.get('[data-testid="delete-cell"]').click({ force: true });
      });
    cy.get('.chakra-toast').should('exist');
    //cy.get('.chakra-alert').should('have.text', 'Config set deleted.Config set has been deleted successfully.');
  });
});

describe('Test cell O-RAN CU CP app config sets deletion', () => {
  beforeEach(() => {
    cy.system_health();
    cy.useLogin();
    cy.visit(oRanDuCuManagerUrl);
  });

  it('Delete config set for CU CP app config set', () => {
    cy.navigateToAdvancedView();
    cy.get('.chakra-container').find('[data-testid="config-sets"]').find('button').click();
    cy.get('.chakra-tabs .chakra-tabs__tablist')
      .find('button')
      .should('have.length', 5)
      .contains('CU_CP app configs')
      .click();
    cy.get('table')
      .contains('td', 'Cypress_Test_Value_CU_CP_App_Config-Identity-and-Connectivity')
      .should('be.visible')
      .parents('tr')
      .within(() => {
        cy.get('button[data-testid="du-cu-manager-table-list-config-icon"]').click();
        cy.get('[data-testid="delete-cell"]').click({ force: true });
      });
    cy.get('.chakra-toast').should('exist');
    //cy.get('.chakra-alert').should('have.text', 'Config set deleted.Config set has been deleted successfully.');
  });
});

describe('Test cell O-RAN CU UP app config sets deletion', () => {
  beforeEach(() => {
    cy.system_health();
    cy.useLogin();
    cy.visit(oRanDuCuManagerUrl);
  });

  it('Delete config set for CU UP app config set', () => {
    cy.navigateToAdvancedView();
    cy.get('.chakra-container').find('[data-testid="config-sets"]').find('button').click();
    cy.get('.chakra-tabs .chakra-tabs__tablist')
      .find('button')
      .should('have.length', 5)
      .contains('CU_UP app configs')
      .click();
    cy.get('table')
      .contains('td', 'Cypress_Test_Value_CU_UP_App_Config-Identity-and-Connectivity')
      .should('be.visible')
      .parents('tr')
      .within(() => {
        cy.get('button[data-testid="du-cu-manager-table-list-config-icon"]').click();
        cy.get('[data-testid="delete-cell"]').click({ force: true });
      });
    cy.get('.chakra-toast').should('exist');
    //cy.get('.chakra-alert').should('have.text', 'Config set deleted.Config set has been deleted successfully.');
  });
});

describe('Test cell O-RAN CU cell config sets deletion', () => {
  beforeEach(() => {
    cy.system_health();
    cy.useLogin();
    cy.visit(oRanDuCuManagerUrl);
  });

  it('Delete config set for CU CP app config set', () => {
    cy.navigateToAdvancedView();
    cy.get('.chakra-container').find('[data-testid="config-sets"]').find('button').click();
    cy.get('.chakra-tabs .chakra-tabs__tablist')
      .find('button')
      .should('have.length', 5)
      .contains('CU cell configs')
      .click();
    cy.get('table')
      .contains('td', 'Cypress_Test_Value_CU_CP_Cell_Config-Identity')
      .should('be.visible')
      .parents('tr')
      .within(() => {
        cy.get('button[data-testid="du-cu-manager-table-list-config-icon"]').click();
        cy.get('[data-testid="delete-cell"]').click({ force: true });
      });
    cy.get('.chakra-toast').should('exist');
    //cy.get('.chakra-alert').should('have.text', 'Config set deleted.Config set has been deleted successfully.');
  });
});
