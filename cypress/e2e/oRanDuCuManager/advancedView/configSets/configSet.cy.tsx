/* eslint-disable cypress/no-unnecessary-waiting */
/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />
import 'cypress-real-events';

const oRanDuCuManagerUrl = '/oran-du-cu-manager';

describe('Test cell O-RAN DU CU Config sets Tables', () => {
  beforeEach(() => {
    cy.system_health();
    cy.useLogin();
    cy.visit(oRanDuCuManagerUrl);
  });

  it('display config set tables', () => {
    cy.navigateToAdvancedView();
    cy.get('.chakra-container').find('[data-testid="config-sets"]').find('button').click();
    cy.get('.chakra-container').should('contain', 'Config sets');
    cy.get('.chakra-container').find('h2').should('contain', 'Config set manager');
    cy.get('[data-testid="ORAN-DU-CU-Manager-App-Bar"]').should('exist');
    cy.get('.chakra-container').then((body) => {
      if (body.find('.chakra-table').length > 0) {
        cy.get('.chakra-table').should('exist');
      } else if (body.find('.chakra-text:contains("No data")').length > 0) {
        cy.contains('No data').should('exist');
      } else {
        throw new Error('Neither table nor "No data" text found');
      }
    });
  });

  it('display DU cell config table', () => {
    cy.navigateToAdvancedView();
    cy.get('.chakra-container').find('[data-testid="config-sets"]').find('button').click();
    cy.get('.chakra-tabs .chakra-tabs__tablist')
      .find('button')
      .should('have.length', 5)
      .contains('DU cell configs')
      .click()
      .then(($selectedButton) => {
        const selectedButtonId = $selectedButton.attr('id');
        cy.log('Selected Button ID:', selectedButtonId);
        const panelId = selectedButtonId?.replace('--tab-', '--tabpanel-');
        cy.get(`[id="${panelId}"]`)
          .should('exist')
          .then((body) => {
            if (body.find('.chakra-table').length > 0) {
              cy.get('.chakra-table').should('exist');
            } else if (body.find('.chakra-text:contains("No data")').length > 0) {
              cy.contains('No data').should('exist');
            } else {
              throw new Error('Neither table nor "No data" text found');
            }
          });
      });
  });

  it('display Split-6 DU app config table', () => {
    cy.navigateToAdvancedView();
    cy.get('.chakra-container').find('[data-testid="config-sets"]').find('button').click();
    cy.get('.chakra-tabs .chakra-tabs__tablist')
      .find('button')
      .should('have.length', 5)
      .contains('Split-6 DU app configs')
      .click()
      .then(($selectedButton) => {
        const selectedButtonId = $selectedButton.attr('id');
        cy.log('Selected Button ID:', selectedButtonId);
        const panelId = selectedButtonId?.replace('--tab-', '--tabpanel-');
        cy.get(`[id="${panelId}"]`)
          .should('exist')
          .then((body) => {
            if (body.find('.chakra-table').length > 0) {
              cy.get('.chakra-table').should('exist');
            } else if (body.find('.chakra-text:contains("No data")').length > 0) {
              cy.contains('No data').should('exist');
            } else {
              throw new Error('Neither table nor "No data" text found');
            }
          });
      });
  });
  it('display CU_CP app config table', () => {
    cy.navigateToAdvancedView();
    cy.get('.chakra-container').find('[data-testid="config-sets"]').find('button').click();
    cy.get('.chakra-tabs .chakra-tabs__tablist')
      .find('button')
      .should('have.length', 5)
      .contains('CU_CP app configs')
      .click()
      .then(($selectedButton) => {
        const selectedButtonId = $selectedButton.attr('id');
        cy.log('Selected Button ID:', selectedButtonId);
        const panelId = selectedButtonId?.replace('--tab-', '--tabpanel-');
        cy.get(`[id="${panelId}"]`)
          .should('exist')
          .then((body) => {
            if (body.find('.chakra-table').length > 0) {
              cy.get('.chakra-table').should('exist');
            } else if (body.find('.chakra-text:contains("No data")').length > 0) {
              cy.contains('No data').should('exist');
            } else {
              throw new Error('Neither table nor "No data" text found');
            }
          });
      });
  });

  it('display CU_UP app config table', () => {
    cy.navigateToAdvancedView();
    cy.get('.chakra-container').find('[data-testid="config-sets"]').find('button').click();
    cy.get('.chakra-tabs .chakra-tabs__tablist')
      .find('button')
      .should('have.length', 5)
      .contains('CU_UP app configs')
      .click()
      .then(($selectedButton) => {
        const selectedButtonId = $selectedButton.attr('id');
        cy.log('Selected Button ID:', selectedButtonId);
        const panelId = selectedButtonId?.replace('--tab-', '--tabpanel-');
        cy.get(`[id="${panelId}"]`)
          .should('exist')
          .then((body) => {
            if (body.find('.chakra-table').length > 0) {
              cy.get('.chakra-table').should('exist');
            } else if (body.find('.chakra-text:contains("No data")').length > 0) {
              cy.contains('No data').should('exist');
            } else {
              throw new Error('Neither table nor "No data" text found');
            }
          });
      });
  });

  it('display CU cell config table', () => {
    cy.navigateToAdvancedView();
    cy.get('.chakra-container').find('[data-testid="config-sets"]').find('button').click();
    cy.get('.chakra-tabs .chakra-tabs__tablist')
      .find('button')
      .should('have.length', 5)
      .contains('CU cell configs')
      .click()
      .then(($selectedButton) => {
        const selectedButtonId = $selectedButton.attr('id');
        cy.log('Selected Button ID:', selectedButtonId);
        const panelId = selectedButtonId?.replace('--tab-', '--tabpanel-');
        cy.get(`[id="${panelId}"]`)
          .should('exist')
          .then((body) => {
            if (body.find('.chakra-table').length > 0) {
              cy.get('.chakra-table').should('exist');
            } else if (body.find('.chakra-text:contains("No data")').length > 0) {
              cy.contains('No data').should('exist');
            } else {
              throw new Error('Neither table nor "No data" text found');
            }
          });
      });
  });
});
