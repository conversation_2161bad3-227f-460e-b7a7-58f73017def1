/* eslint-disable cypress/unsafe-to-chain-command */
/* eslint-disable cypress/no-unnecessary-waiting */
/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />

import 'cypress-real-events';

const cellOverCellsViewUrl = '/cell-overview';
describe('Cells DU card Component Tests', () => {
  beforeEach(() => {
    const paths = {
      podCellGdcv1Node: 'OranDuCuManager/cellOverView/nodes/PodGdvc.json',
      podCell: 'OranDuCuManager/cellOverView/cells/PodCell.json',
      podRu: 'OranDuCuManager/cellOverView/nodes/PodRadioRu.json',
    };

    // Logging
    cy.task('log', 'Validating fixture paths...');
    cy.task('log', JSON.stringify(paths));

    //Capture API calls
    cy.intercept('GET', '**/inv/cells?page=1&limit=1000&offset=0', {
      fixture: paths.podCell,
    }).as('getCells');
    cy.intercept('GET', '**/orc/anthos/cluster_status/GB-**', {
      fixture: paths.podCellGdcv1Node,
    }).as('getGdcv1Node');
    cy.intercept('GET', '**/orc/radio/nodes/**', { fixture: paths.podRu, middleware: true }).as('getRadioNode');

    cy.system_health();
    cy.useLogin();
    cy.visit(cellOverCellsViewUrl);
  });

  it('open cell and GDVC', () => {
    cy.get('table').contains('GBDEV2NB000002').click();
    cy.get('tbody').contains('GB-DEV2-GDCV-0001').should('be.visible').click();
    cy.get('tbody .chakra-card__header').contains('denseair-gke-bmctl-edge-5-1-16-6').should('be.visible').click();
    cy.get('tbody .chakra-card__header').contains('dauk-mrl-d-gdcv-host05.denseair.net').should('be.visible').click();
    cy.get('tbody .chakra-card__header')
      .contains('dauk-mrl-d-gdcv-host05.denseair.net')
      .parents('.chakra-card')
      .each(($card) => {
        cy.wrap($card).within(() => {
          cy.get('[data-testid="sub-cluster-details-body"]').find('p.chakra-text').should('contain.text', 'Storage');
        });
      });
    cy.get('tbody .chakra-card__header')
      .contains('dauk-mrl-d-gdcv-host05.denseair.net')
      .parents('.chakra-card')
      .each(($card) => {
        cy.wrap($card).within(() => {
          cy.get('[data-testid="sub-cluster-details-body"]').find('p.chakra-text').should('contain.text', 'Conditions');
        });
      });
    cy.get('tbody .chakra-card__header')
      .contains('dauk-mrl-d-gdcv-host05.denseair.net')
      .parents('.chakra-card')
      .each(($card) => {
        cy.wrap($card).within(() => {
          cy.get('[data-testid="sub-cluster-details-body"]').find('p.chakra-text').should('contain.text', 'Containers');
        });
      });

    cy.get('tbody .chakra-card__header').contains('dauk-mrl-d-gdcv-host04.denseair.net').should('be.visible').click();
    cy.get('tbody .chakra-card__header')
      .contains('dauk-mrl-d-gdcv-host04.denseair.net')
      .parents('.chakra-card')
      .each(($card) => {
        cy.wrap($card).within(() => {
          cy.get('[data-testid="sub-cluster-details-body"]').find('p.chakra-text').should('contain.text', 'Storage');
        });
      });
    cy.get('tbody .chakra-card__header')
      .contains('dauk-mrl-d-gdcv-host04.denseair.net')
      .parents('.chakra-card')
      .each(($card) => {
        cy.wrap($card).within(() => {
          cy.get('[data-testid="sub-cluster-details-body"]').find('p.chakra-text').should('contain.text', 'Conditions');
        });
      });
    cy.get('tbody .chakra-card__header')
      .contains('dauk-mrl-d-gdcv-host04.denseair.net')
      .parents('.chakra-card')
      .each(($card) => {
        cy.wrap($card).within(() => {
          cy.get('[data-testid="sub-cluster-details-body"]').find('p.chakra-text').should('contain.text', 'Containers');
        });
      });
  });

  it.only('open cell and Radio/RU', () => {
    cy.get('table').contains('GBDEV2NB000002').click();
    cy.get('tbody').contains('GB-DEV2-RU-0001').should('be.visible').click();
    cy.wait('@getRadioNode');
    cy.get('[data-testid="cells-node-pod-radio-components"]').should('be.visible');
    cy.get('[data-testid="cells-node-pod-radio-components"]').should('not.have.text', 'Loading...');
    cy.get('[data-testid="cells-node-pod-radio-components"]', { timeout: 10000 }).should('contain.text', 'Device Info');
    cy.get('[data-testid="cells-node-pod-radio-components"]', { timeout: 10000 }).should('contain.text', 'OS');
    cy.get('[data-testid="cells-node-pod-radio-components"]', { timeout: 10000 }).should(
      'contain.text',
      'Hardware Info'
    );
    cy.get('[data-testid="cells-node-pod-radio-components"]', { timeout: 10000 }).should(
      'contain.text',
      'Ethernet Info'
    );
    cy.get('[data-testid="cells-node-pod-radio-components"]', { timeout: 10000 }).should(
      'contain.text',
      'Timing status'
    );

    cy.get('[data-testid="cpu-card-header"]').click();
    cy.get('[data-testid="cpu-card-body"]').should('be.visible');
    cy.get('[data-testid="disk-card-header"]').click();
    cy.get('[data-testid="disk-card-body"]').should('be.visible');
    cy.get('[data-testid="memory-card-header"]').click();
    cy.get('[data-testid="memory-card-body"]').should('be.visible');
    cy.get('[data-testid="network-card-header"]').click();
    cy.get('[data-testid="network-card-body"]').should('be.visible');
  });

  //Error cards for the pods
  it.skip('open error cell and DU', () => {
    cy.get('table').contains('GBDEV2NB000002').click();
    cy.get('tbody').contains('GB-MARL-DU-0001').should('be.visible').click();
    cy.get('[data-testid="node-comp-error-card"]').find('p.chakra-text').should('contain.text', 'DU');
  });

  it.skip('open error cell and Radio/RU', () => {
    cy.get('table').contains('GBDEV2NB000002').click();
    cy.get('tbody').contains('GB-DEV2-RU-0001').should('be.visible').click();
    cy.get('[data-testid="node-comp-error-card"]').find('p.chakra-text').should('contain.text', 'RU');
  });

  it.skip('open error cell and CUCP', () => {
    cy.get('table').contains('GBDEV2NB000002').click();
    cy.get('tbody').contains('GB-MARL-CUCP-0001').should('be.visible').click();
    cy.get('[data-testid="node-comp-error-card"]').find('p.chakra-text').should('contain.text', 'CU Control plane');
  });

  it.skip('open error cell and CUUP', () => {
    cy.get('table').contains('GBDEV2NB000002').click();
    cy.get('tbody').contains('GB-MARL-CUUP-0001').should('be.visible').click();
    cy.get('[data-testid="node-comp-error-card"]').find('p.chakra-text').should('contain.text', 'CU User plane');
  });
});
