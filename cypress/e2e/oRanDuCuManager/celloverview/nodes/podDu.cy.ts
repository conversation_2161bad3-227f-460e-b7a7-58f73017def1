/* eslint-disable cypress/unsafe-to-chain-command */
/* eslint-disable cypress/no-unnecessary-waiting */
/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />

import 'cypress-real-events';
describe('Nodes DU card Component Tests', () => {
  const cellOverNodesViewUrl = '/cell-overview/nodes';

  beforeEach(() => {
    const paths = {
      nodes: 'clusterNode/nodes.json',
      podDu: 'OranDuCuManager/cellOverView/nodes/PodDu.json',
    };

    // Logging paths to verify
    cy.task('log', 'Validating fixture paths...');
    cy.task('log', JSON.stringify(paths));

    cy.intercept('GET', '**/inv/nodes**', {
      fixture: paths.nodes,
    }).as('getNodes');

    cy.intercept('GET', '**/orc/pod/nodes/G**', {
      fixture: paths.podDu,
    }).as('getDuNode');

    cy.system_health();
    cy.useLogin();
    cy.visit(cellOverNodesViewUrl);
  });

  it('Nodes pod Du card', () => {
    // Get nodes
    cy.get('table.chakra-table').should('be.visible');
    cy.get('table.chakra-table tbody tr').contains('td', 'GB-MARL-DU-0003').first().click();
    cy.get('[data-testid="nodes-nodeComponents"]').should('be.visible');

    // Open node
    cy.get('[data-testid="sub-cluster-render-details-container"]').should('be.visible');
    cy.get('[data-testid="sub-cluster-render-details-header-Conditions"]').should('be.visible');
    cy.get('[data-testid="sub-cluster-render-details-header-Conditions"]').should('have.text', 'Conditions');
    cy.get('[data-testid="sub-cluster-render-details-body"]').should('be.visible');
    cy.get('[data-testid="sub-cluster-render-details-body"]').find('div').first().click();
    cy.get('[data-testid="sub-cluster-render-details-body"]')
      .find('div')
      .find('.chakra-table tbody tr td p')
      .eq(0)
      .should('have.text', 'Last Probe:');
    cy.get('[data-testid="sub-cluster-render-details-body"]')
      .find('div')
      .find('.chakra-table tbody tr td p')
      .eq(2)
      .should('have.text', 'Last Transition:');
    cy.get('[data-testid="sub-cluster-render-details-body"]')
      .find('div')
      .find('.chakra-table tbody tr td p')
      .eq(4)
      .should('have.text', 'Message:');
    cy.get('[data-testid="sub-cluster-render-details-body"]')
      .find('div')
      .find('.chakra-table tbody tr td p')
      .eq(5)
      .should('have.text', 'Reason:');

    cy.get('[data-testid="sub-cluster-render-details-body"]').should('be.visible');

    // Open pod details
    cy.contains('button', 'Pod details Data').click();
    cy.get('.chakra-modal__content')
      .should('be.visible')
      .should('contain.text', 'Component data')
      .find('p')
      .first()
      .should('contain.text', 'conditions.last_transition_time');
    cy.get('.chakra-modal__close-btn').click();

    // Open custom resource data
    cy.contains('button', 'Custom resource Data').click();
    cy.get('.chakra-modal__content')
      .should('exist')
      .should('contain.text', 'du-l2 custom resource')
      .find('p')
      .first()
      .should('contain.text', 'press ctrl+f(win) / cmd+f(mac) to search');
    cy.get('.chakra-modal__close-btn').click();
  });
});
