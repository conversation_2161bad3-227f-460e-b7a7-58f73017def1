/* eslint-disable cypress/unsafe-to-chain-command */
/* eslint-disable cypress/no-unnecessary-waiting */
/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />

import 'cypress-real-events';
describe('Nodes Radio/RU card  Component Tests', () => {
  const cellOverViewUrl = '/cell-overview/nodes';

  beforeEach(() => {
    const paths = {
      nodes: 'clusterNode/nodes.json',
      podRu: 'OranDuCuManager/cellOverView/nodes/PodRadioRu.json',
    };

    // Logging paths to verify
    cy.task('log', 'Validating fixture paths...');
    cy.task('log', JSON.stringify(paths));

    cy.intercept('GET', '**/inv/nodes**', {
      fixture: paths.nodes,
    }).as('getNodes');

    cy.intercept('GET', '**/orc/radio/nodes/**', { fixture: paths.podRu, middleware: true }).as('getRadioNode');

    cy.system_health();
    cy.useLogin();
    cy.visit(cellOverViewUrl);
  });

  it('Nodes pod Radio/RU card', () => {
    cy.wait('@getNodes');
    // Get nodes
    cy.get('table.chakra-table').should('be.visible');
    cy.get('table.chakra-table tbody tr').contains('td', 'GB-DEV2-RU-0001').first().click();
    cy.get('[data-testid="nodes-nodeComponents"]').should('be.visible');

    cy.wait('@getRadioNode');

    // Open node
    cy.get('[data-testid="cells-node-pod-radio-components"]').should('be.visible');
    cy.get('[data-testid="cells-node-pod-radio-components"]').should('not.have.text', 'Loading...');
    cy.get('[data-testid="cells-node-pod-radio-components"]', { timeout: 10000 }).should('contain.text', 'Device Info');
    cy.get('[data-testid="cells-node-pod-radio-components"]', { timeout: 10000 }).should('contain.text', 'OS');
    cy.get('[data-testid="cells-node-pod-radio-components"]', { timeout: 10000 }).should(
      'contain.text',
      'Hardware Info'
    );
    cy.get('[data-testid="cells-node-pod-radio-components"]', { timeout: 10000 }).should(
      'contain.text',
      'Ethernet Info'
    );
    cy.get('[data-testid="cells-node-pod-radio-components"]', { timeout: 10000 }).should(
      'contain.text',
      'Timing status'
    );

    // Open pod details
    cy.contains('button', 'RU Data').click();
    cy.get('.chakra-modal__content-container')
      .scrollIntoView()
      .should('be.visible')
      .should('contain.text', 'Component data')
      .should('contain.text', 'device_info.DeviceID');
    cy.get('.chakra-modal__close-btn').click();
  });
});
