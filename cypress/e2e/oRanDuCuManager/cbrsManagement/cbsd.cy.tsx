/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />
import 'cypress-real-events';

const cbrsManagementUrl = '/oran-du-cu-manager/cbrs-management/cbsd/';

describe('CBRS Overview', () => {
  beforeEach(() => {
    const paths = {
      search: 'cbrsManagement/search.json',
      activate: 'cbrsManagement/activate.json',
      deactivate: 'cbrsManagement/deactivate.json',
    };

    // Logging paths to verify
    cy.task('log', 'Validating fixture paths...');
    cy.task('log', JSON.stringify(paths));

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/orc/cbsd/search', {
      fixture: paths.search,
    }).as('getCbsdDevices');

    cy.intercept('PATCH', '**/grant/activate', {
      fixture: paths.activate,
    }).as('activate');

    cy.intercept('PATCH', '**/grant/deactivate', {
      fixture: paths.deactivate,
    }).as('deactivate');

    // Ensure system health and login before visiting the page
    cy.system_health();
    cy.useLogin();
    cy.visit(cbrsManagementUrl);
    cy.log('Visited cbsd ');
    cy.wait(2000);
  });

  it('should load the CBRS Overview page and default to CBSD tab', () => {
    cy.url().should('include', cbrsManagementUrl);
    cy.get('[data-testid="cbrs-heading"]').should('contain.text', 'CBRS Overview');
    cy.get('#tabs-\\:rl\\:--tab-0').should('be.visible');
    cy.get('#tabs-\\:rl\\:--tab-0').should('be.enabled');
    cy.get('[data-testid="cbsd-heading"] > .chakra-heading').should('be.visible');
    cy.get('[data-testid="cbsd-heading"] > .chakra-heading').should(
      'have.text',
      'Citizens Broadband Radio Service Devices (CBSD)'
    );
  });

  it('should load and display CBSD data', () => {
    cy.get('[data-testid="cbsd"]').click();

    cy.get('[data-testid="dataTable-container"] > .chakra-table > tbody.css-0 > :nth-child(1) > :nth-child(1)').should(
      'be.visible'
    );
    cy.get('[data-testid="dataTable-container"] > .chakra-table > tbody.css-0 > :nth-child(1) > :nth-child(1)').should(
      'have.text',
      'AG-TEST-01-002'
    );
    cy.get('[data-testid="dataTable-container"] > .chakra-table > tbody.css-0 > :nth-child(1) > :nth-child(2)').should(
      'be.visible'
    );
    cy.get('[data-testid="dataTable-container"] > .chakra-table > tbody.css-0 > :nth-child(1) > :nth-child(2)').should(
      'have.text',
      'GOOGWC02'
    );
    cy.get('[data-testid="dataTable-container"] > .chakra-table > tbody.css-0 > :nth-child(1) > :nth-child(3)').should(
      'be.visible'
    );
    cy.get('[data-testid="dataTable-container"] > .chakra-table > tbody.css-0 > :nth-child(1) > :nth-child(3)').should(
      'have.text',
      'AG-TEST-01-001'
    );
  });
  it('it should deactivate a device', () => {
    cy.get(':nth-child(1) > :nth-child(7) > [data-testid="cbsd-menu-items-button"] > svg').should('be.visible');
    cy.get(':nth-child(1) > :nth-child(7) > [data-testid="cbsd-menu-items-button"] > svg').click();
    cy.get('#menu-list-\\:r3n\\: > [data-testid="activate-cbsd"]').should('be.visible');
    cy.get('#menu-list-\\:r3n\\: > [data-testid="activate-cbsd"]').should('have.text', 'Activate');
    cy.get('#menu-list-\\:r3n\\: > [data-testid="activate-cbsd"]').should('be.disabled');
    cy.get('#menu-list-\\:r3n\\: > [data-testid="deactivate-cbsd"]').should('be.visible');
    cy.get('#menu-list-\\:r3n\\: > [data-testid="deactivate-cbsd"]').should('have.text', 'Deactivate');
    cy.get('#menu-list-\\:r3n\\: > [data-testid="deactivate-cbsd"]').should('be.enabled');
    cy.get('#menu-list-\\:r3n\\: > [data-testid="deactivate-cbsd"]').click();
    cy.get('#toast-1-title').should('be.visible');
    cy.get('#toast-1-title').should('have.text', 'Success');
    cy.get('#toast-1-description').should('have.text', 'CBSD device AG-TEST-01-002 is Deactivated.');
  });
});
