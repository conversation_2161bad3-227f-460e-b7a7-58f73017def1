/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />
import 'cypress-real-events';

const cpiCreateUrl = '/oran-du-cu-manager/cbrs-management/cpi/create';

describe('Create CPI Form', () => {
  beforeEach(() => {
    const paths = {
      search: 'cpi/search.json',
    };

    cy.task('log', 'Validating fixture paths...');
    cy.task('log', JSON.stringify(paths));

    cy.intercept('GET', '**/orc/cpi/search**', {
      fixture: paths.search,
    }).as('getCPI');

    cy.intercept('POST', '**/orc/cpi**', { statusCode: 200, body: { success: true } }).as('createCPI');

    cy.system_health();
    cy.useLogin();
    cy.visit(cpiCreateUrl);
    cy.log('Visited cbsd ');
    cy.wait(2000);
  });

  it('create cpi button should be disabled until mandatory fields are filled', () => {
    cy.get('[data-testid="create-cpi-button"]').should('be.disabled');

    cy.get('[data-testid="email-input"]').type('<EMAIL>');
    cy.get('[data-testid="create-cpi-button"]').should('be.disabled');
    cy.get('[data-testid="installer-id-input"]').type('12345');
    cy.get('[data-testid="create-cpi-button"]').should('be.disabled');
    cy.get('[data-testid="cpi-key-input"]').type('CPI_KEY_123');
    cy.get('[data-testid="create-cpi-button"]').should('not.be.disabled');
  });

  it('should submit the form when all required fields are filled', () => {
    cy.get('[data-testid="email-input"]').type('<EMAIL>');
    cy.get('[data-testid="installer-id-input"]').type('12345');
    cy.get('[data-testid="first-name-input"]').type('John');
    cy.get('[data-testid="last-name-input"]').type('Doe');
    cy.get('[data-testid="cpi-key-input"]').type('CPI_KEY_123');

    cy.get('[data-testid="create-cpi-button"]').click();

    cy.get('.chakra-toast').should('contain', 'CPI created.');
  });

  it('should navigate to the CPI list page when Cancel is clicked', () => {
    cy.get('[data-testid="cancel-button"]').click();
  });
});
