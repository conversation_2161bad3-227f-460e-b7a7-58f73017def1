/// <reference types="cypress" />

describe('EditCPIForm', () => {
  beforeEach(() => {
    const paths = {
      search: 'cpi/search.json',
    };

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/orc/cpi/search**', {
      fixture: paths.search,
    }).as('getCpiData');

    // Intercept the POST request to update the CPI data
    cy.intercept('PATCH', '**/orc/cpi/**', {
      statusCode: 200,
      body: {
        success: true,
      },
    }).as('updateCpiData');

    cy.system_health();
    cy.useLogin();
    // Visit the edit page with mocked state
    cy.visit('/oran-du-cu-manager/cbrs-management/cpi');
    cy.get('table.chakra-table tbody tr')
      .first()
      .within(() => {
        cy.get('[data-testid="cpi-menu-items-button"]').click();
        cy.get('[data-testid="edit-cpi"]').click({ force: true });
      });
  });

  it('renders form with initial values', () => {
    // Check that the form fields are populated with the correct initial values
    cy.get('input[type="email"]').should('have.value', '<EMAIL>');
    cy.get('input[name="installer_id"]').should('have.value', 'test_installer');
    cy.get('input[name="first_name"]').should('have.value', 'Test_First');
    cy.get('input[name="last_name"]').should('have.value', 'Test_Last');
  });

  it('allows editing the form fields', () => {
    // Change the values in the form fields
    cy.get('input[name="email"]').clear().type('<EMAIL>');
    cy.get('input[name="installer_id"]').clear().type('newtest_installer');
    cy.get('input[name="first_name"]').clear().type('Jane');
    cy.get('input[name="last_name"]').clear().type('Test_Last');

    // Check that the form fields have the new values
    cy.get('input[name="email"]').should('have.value', '<EMAIL>');
    cy.get('input[name="installer_id"]').should('have.value', 'newtest_installer');
    cy.get('input[name="first_name"]').should('have.value', 'Jane');
    cy.get('input[name="last_name"]').should('have.value', 'Test_Last');
  });

  it('submits the edited form data', () => {
    // Change the values in the form fields
    cy.get('input[name="email"]').clear().type('<EMAIL>');
    cy.get('input[name="installer_id"]').clear().type('newtest_installer');
    cy.get('input[name="first_name"]').clear().type('Jane');
    cy.get('input[name="last_name"]').clear().type('Test_Last');

    // Submit the form
    cy.get('button[type="submit"]').click();

    // Check for success message
    cy.contains('The CPI has been successfully updated.');
  });
});
