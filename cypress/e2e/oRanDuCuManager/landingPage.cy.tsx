/* eslint-disable cypress/no-unnecessary-waiting */
/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />
import 'cypress-real-events';

const oRanDuCuManagerUrl = '/oran-du-cu-manager';

describe('Test cell O-RAN DU CU Manager landing page', () => {
  beforeEach(() => {
    cy.system_health();
    cy.useLogin();
    cy.visit(oRanDuCuManagerUrl);
  });

  it('display Landing page', () => {
    cy.get('.chakra-container').find('h2').should('contain', 'O-RAN DU/CU Manager');
    cy.get('.chakra-container').find('[data-testid="advanced-view"]').should('exist');
    cy.get('.chakra-container').find('[data-testid="simple-view"]').should('exist');
    cy.get('.chakra-container').find('[data-testid="cbrs-mangement"]').should('exist');
  });

  it('display cbrs Landing page', () => {
    cy.navigateToCbrs();
    cy.get('.chakra-container').find('[data-testid="cbrs-heading"]').should('exist');
  });

  it('display simple view Landing page', () => {
    cy.navigateToSimpleView();
    cy.get('.chakra-container').find('[data-testid="create-deployment"]').should('exist');
    cy.get('.chakra-container').find('[data-testid="edit-view-deployment"]').should('exist');
    cy.get('[data-testid="ORAN-DU-CU-Manager-App-Bar"]').should('exist');
  });

  it('display advanced view Landing page', () => {
    cy.navigateToAdvancedView();
    cy.get('.chakra-container').find('[data-testid="config-sets"]').should('exist');
    cy.get('.chakra-container').find('[data-testid="custom-resources"]').should('exist');
    cy.get('.chakra-container').find('[data-testid="pods"]').should('exist');
    cy.get('.chakra-container').find('[data-testid="clusters"]').should('exist');
    cy.get('[data-testid="ORAN-DU-CU-Manager-App-Bar"]').should('exist');
  });
});
