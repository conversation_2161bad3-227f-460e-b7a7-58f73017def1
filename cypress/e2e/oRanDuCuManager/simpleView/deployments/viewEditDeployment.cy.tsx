/* eslint-disable cypress/no-unnecessary-waiting */
/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />
import 'cypress-real-events';

const oRanDuCuManagerUrl = '/oran-du-cu-manager';

describe('Test cell O-RAN DU CU Manager landing page', () => {
  beforeEach(() => {
    cy.system_health();
    cy.useLogin();
    cy.visit(oRanDuCuManagerUrl);
  });

  it('display simple view Landing page', () => {
    cy.navigateToSimpleView();

    cy.get('.chakra-container').find('[data-testid="how-to-use"]').should('contain', 'Summary');
    cy.get('.chakra-container').find('[data-testid="custom-resources"]').should('exist');
    cy.get('.chakra-container').find('[data-testid="interfaces"]').should('exist');
    cy.get('.chakra-container').find('[data-testid="identity"]').should('exist');
    cy.get('.chakra-container').find('[data-testid="activate-deactivate-site"]').should('exist');
    cy.get('[data-testid="ORAN-DU-CU-Manager-App-Bar"]').should('exist');
  });

  it('display custom resources', () => {
    cy.navigateToSimpleView();

    cy.get('.chakra-container').find('[data-testid="custom-resources"]').find('button').click();
    cy.get('.chakra-container').find('h2').should('contain', 'Create customs resources');
    cy.get('.chakra-container').should('contain', 'How to create site:');

    cy.get('[data-testid="ORAN-DU-CU-Manager-App-Bar"]').should('exist');
    cy.get('body').then((body) => {
      if (body.find('.chakra-table').length > 0) {
        // The table exists
        cy.get('.chakra-table').should('exist');
      } else if (body.find('.chakra-table:contains("No data")').length > 0) {
        // The "No data" text exists
        cy.contains('No data').should('exist');
      } else {
        // Neither the table nor the "No data" text exists
        throw new Error('Neither table nor "No data" text found');
      }
    });
  });

  it('display interfaces', () => {
    cy.navigateToSimpleView();

    cy.get('.chakra-container').find('[data-testid="interfaces"]').find('button').click();
    cy.get('.chakra-container').find('h2').should('contain', 'Interfaces');
    cy.get('.chakra-container').find('.react-flow').should('exist');

    cy.get('[data-testid="ORAN-DU-CU-Manager-App-Bar"]').should('exist');
  });

  it('display identity', () => {
    cy.navigateToSimpleView();

    cy.get('.chakra-container').find('[data-testid="identity"]').find('button').click();
    cy.get('.chakra-container').find('h2').should('contain', 'Identity');
    cy.get('.chakra-container').find('.react-flow').should('exist');
    cy.get('[data-testid="ORAN-DU-CU-Manager-App-Bar"]').should('exist');
  });

  it('display activate and deactivate', () => {
    cy.navigateToSimpleView();

    cy.get('.chakra-container').find('[data-testid="activate-deactivate-site"]').find('button').click();
    cy.get('.chakra-container').find('h2').should('contain', 'Activate Deactivate Site');
    cy.get('[data-testid="ORAN-DU-CU-Manager-App-Bar"]').should('exist');
    cy.get('body').then((body) => {
      if (body.find('.chakra-table').length > 0) {
        cy.get('.chakra-table').should('exist');
      } else if (body.find('.chakra-table:contains("No data")').length > 0) {
        cy.contains('No data').should('exist');
      } else {
        throw new Error('Neither table nor "No data" text found');
      }
    });
  });
});
