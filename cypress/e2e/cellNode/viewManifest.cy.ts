/* eslint-disable cypress/no-unnecessary-waiting */
/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />
import 'cypress-real-events';

const cellOverview = '/cell-overview/nodes';

describe('View Manifest from cell overview Node Tab', () => {
  beforeEach(() => {
    const paths = {
      node: 'cellNode/ViewManifest/nodes.json',
      manifestNode: 'cellNode/ViewManifest/manifestNode.json',
      networkManifestNode: 'cellNode/ViewManifest/networkManifestData.json',
      cells: 'common/cells.json',
      countries: 'common/countries.json',
      regions: 'common/regions.json',
    };

    // Logging paths to verify
    cy.task('log', 'Validating fixture paths...');
    cy.task('log', JSON.stringify(paths));
    cy.intercept('GET', '**/inv/stats/regions', {
      fixture: paths.regions,
    }).as('getRegions');

    cy.intercept('GET', '**/inv/stats/countries**', { fixture: paths.countries }).as('getCountries');

    cy.intercept('GET', '**/inv/cells**', { fixture: paths.cells }).as('getCells');

    cy.intercept('GET', '**/inv/nodes**', {
      fixture: paths.node,
    }).as('getNode');

    cy.intercept('GET', '**/inv/manifest/nodes**', {
      fixture: paths.manifestNode,
    }).as('getManifestNode');

    cy.intercept('GET', '**/inv/manifest/network**', {
      fixture: paths.networkManifestNode,
    }).as('getnetworkManifestNode');

    cy.system_health();
    cy.useLogin();
    cy.visit(cellOverview);
  });

  it('Able to view manifest on node', () => {
    cy.get('table.chakra-table').should('be.visible');

    cy.get('table.chakra-table tbody tr')
      .first()
      .within(() => {
        cy.get('[data-testid="cell-main-table-node-config-icon"]').click();
        cy.get('[data-testid="manifest-nodes"]').click({ force: true });
      });

    cy.get('[data-testid="modal_heade_manifest"]').should('be.visible');
    cy.get('[data-testid="modal_heade_manifest"]').should('have.text', 'Manifest Details');
    cy.get('[data-testid="manifest_component_wrapper"]').should('be.visible');
    cy.get('[data-testid="innerComponent-heading"]').should('be.visible');
    cy.get('[data-testid="innerComponent-heading"]').should('have.text', 'Network Manifest');
    cy.get(':nth-child(3) > [data-testid="innerComponent-key"]').should('be.visible');
    cy.get('[data-testid="modal_close_manifest"]').click();
  });
});
