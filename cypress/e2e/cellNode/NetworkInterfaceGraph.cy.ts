/* eslint-disable cypress/unsafe-to-chain-command */
/* eslint-disable cypress/no-unnecessary-waiting */
/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />

import 'cypress-real-events';
const nodeOverViewUrl = '/cell-overview/nodes';

describe('NetworkInterfaceGraphs Component Tests', () => {
  beforeEach(() => {
    const paths = {
      sites: 'cellCreation/AddCell/mockData/sites.json',
      cellReference: 'cellCreation/AddCell/mockData/cellReference.json',
      nodes: 'cellNode/NetworkInterfaceGraph/nodes.json',
      networkNodes: 'cellNode/NetworkInterfaceGraph/networkNodes.json',
      cells: 'common/cells.json',
      countries: 'common/countries.json',
      activeAlarmsCount: 'common/activeAlarmsCount.json',
      regions: 'common/regions.json',
    };

    // Logging paths to verify
    cy.task('log', 'Validating fixture paths...');
    cy.task('log', JSON.stringify(paths));

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/inv/stats/regions', {
      fixture: paths.regions,
    }).as('getRegions');

    cy.intercept('GET', '**/inv/stats/countries**', { fixture: paths.countries }).as('getCountries');

    cy.intercept('GET', '**/mc/alarms/active/count**', { fixture: paths.activeAlarmsCount }).as('getActiveCount');

    cy.intercept('GET', '**/inv/cells**', { fixture: paths.cells }).as('getCells');

    cy.intercept('GET', '**/inv/ref/sites', {
      fixture: paths.sites,
    }).as('getSites');

    cy.intercept('GET', '**/orc/network/nodes/**', {
      fixture: paths.networkNodes,
    }).as('getNetworkNodes');

    cy.intercept('GET', '**/inv/nodes**', {
      fixture: paths.nodes,
    }).as('getNodes');

    cy.system_health();
    cy.useLogin();
    cy.visit(nodeOverViewUrl);
  });

  it('verify Interface Details modal is exists', () => {
    cy.get('table.chakra-table').should('be.visible');
    cy.get('table.chakra-table tbody tr').first().click();
    cy.get('[data-testid="cells-node-SwitchComponents"]').should('be.visible').first().click();
    cy.get('button').contains('Interface Details').should('be.visible');
    cy.get('button').contains('Interface Details').click();
    cy.get('[data-testid="modal-header"]').should('be.visible').contains('Interface Details');
  });

  it('Interface Details with no chart data should not be able to click', () => {
    cy.get('table.chakra-table').should('be.visible');
    cy.get('table.chakra-table tbody tr').first().click();
    cy.get('[data-testid="cells-node-SwitchComponents"]').should('be.visible').first().click();
    cy.get('button').contains('Interface Details').should('be.visible');
    cy.get('button').contains('Interface Details').click();
    cy.get('[data-testid="modal-header"]').should('be.visible').contains('Interface Details');
    cy.get('[data-testid="modal-content"] table.chakra-table').should('be.visible');
    cy.get('[data-testid="modal-content"] table.chakra-table tbody tr').first().click();
    cy.get('[data-testid="sub-component-networkGrpah"]').should('not.exist');
  });

  it('chart data should be visible', () => {
    cy.get('table.chakra-table').should('be.visible');
    cy.get('table.chakra-table tbody tr').first().click();
    cy.get('[data-testid="cells-node-SwitchComponents"]').should('be.visible').first().click();
    cy.get('button').contains('Interface Details').should('be.visible');
    cy.get('button').contains('Interface Details').click();
    cy.get('[data-testid="modal-header"]').should('be.visible').contains('Interface Details');
    cy.get('[data-testid="modal-content"] table.chakra-table').should('be.visible');
    cy.get('[data-testid="modal-content"] table.chakra-table tbody tr').eq(1).click();
    cy.get('[data-testid="sub-component-networkGrpah"]').should('be.visible');
  });

  it('chart data should be visible as per the selected time range', () => {
    cy.get('table.chakra-table').should('be.visible');
    cy.get('table.chakra-table tbody tr').first().click();
    cy.get('[data-testid="cells-node-SwitchComponents"]').should('be.visible').first().click();
    cy.get('button').contains('Interface Details').should('be.visible');
    cy.get('button').contains('Interface Details').click();
    cy.get('[data-testid="modal-header"]').should('be.visible').contains('Interface Details');
    cy.get('[data-testid="modal-content"] table.chakra-table').should('be.visible');
    cy.get('[data-testid="modal-content"] table.chakra-table tbody tr').eq(1).click();
    cy.get('[data-testid="sub-component-networkGrpah"]').should('be.visible');
    cy.get('[data-testid="selectRange"] select').should('be.visible');
    cy.get('[data-testid="selectRange"] select').select('Last7Days');
    cy.wait(10000);
    cy.get('[data-testid="sub-component-networkGrpah"]')
      .find('.echarts-for-react')
      .should('exist')
      .then(($chart) => {
        cy.wrap($chart).find('canvas').should('be.visible');
      });
  });
});
