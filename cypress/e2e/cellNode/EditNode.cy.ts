/* eslint-disable cypress/no-unnecessary-waiting */
/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />
import 'cypress-real-events';

const cellOverview = '/cell-overview/nodes';

describe('Edit node from cell overview Node Tab', () => {
  beforeEach(() => {
    const paths = {
      node: 'cellNode/EditNode/node.json',
      sites: 'cellCreation/AddCell/mockData/sites.json',
    };

    // Logging paths to verify
    cy.task('log', 'Validating fixture paths...');
    cy.task('log', JSON.stringify(paths));

    cy.intercept('GET', '**/inv/nodes**', {
      fixture: paths.node,
    }).as('getNode');

    cy.intercept('GET', '**/inv/ref/sites', {
      fixture: paths.sites,
    }).as('getSites');

    cy.intercept('PATCH', '**/inv/cells/nodes/*', {
      statusCode: 200,
      body: { success: true },
    }).as('postNode');

    cy.system_health();
    cy.useLogin();
    cy.visit(cellOverview);
  });

  // Edit a node
  it('Edit a node', () => {
    cy.get('table.chakra-table').should('be.visible');

    cy.get('table.chakra-table tbody tr')
      .first()
      .within(() => {
        cy.get('[data-testid="cell-main-table-node-config-icon"]').click();
        cy.get('[data-testid="edit-nodes"]').click({ force: true });
      });
    cy.get('[data-testid="editNode"]').should('be.visible');
    cy.get('[data-testid="editNode-heading"]').should('be.visible');

    cy.get('[data-testid="editNode-submit"]').should('be.enabled');
    cy.get('[data-testid="editNode-submit"]').click();
    cy.wait('@postNode').its('response.statusCode').should('eq', 200);
  });
});
