/* eslint-disable cypress/no-unnecessary-waiting */
/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />
import 'cypress-real-events';
const createNodeForm = '/cell-overview/create/node';

describe('Create Node Form', () => {
  beforeEach(() => {
    const paths = {
      regions: 'cellCreation/AddCell/mockData/regions.json',
      sites: 'cellCreation/AddCell/mockData/sites.json',
      cellReference: 'cellCreation/AddCell/mockData/cellReference.json',
      postCellResponse: 'cellCreation/AddCell/mockData/postCellResponse.json',
      postNodeResponse: 'cellCreation/AddCell/mockData/postNodeResponse.json',
      serverNodes: 'cellNode/AddNode/serverNodes.json',
      switchNodes: 'cellCreation/AddCell/mockData/switchNodes.json',
      streetCell: 'cellCreation/AddCell/mockData/streetCell.json',
      networkNodes: 'cellNode/AddNode/networkNodes.json',
    };

    // Logging paths to verify
    cy.task('log', 'Validating fixture paths...');
    cy.task('log', JSON.stringify(paths));

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/inv/ref/regions', {
      fixture: paths.regions,
    }).as('getRegions');

    cy.intercept('GET', '**/inv/ref/sites', {
      fixture: paths.sites,
    }).as('getSites');

    cy.intercept('GET', '**/inv/manifest/nodes?manifest_type=server*', {
      fixture: paths.serverNodes,
    }).as('getServerNodes');

    cy.intercept('GET', '**/inv/manifest/nodes?node_type=Switch&deployable=true', {
      fixture: paths.switchNodes,
    }).as('getSwitchNodes');

    cy.intercept('GET', '**/inv/manifest/nodes?manifest_type=streetcell&deployable=true', {
      fixture: paths.streetCell,
    }).as('getStreetCellNodes');

    cy.intercept('GET', '**/inv/manifest/nodes?manifest_type=network*', {
      fixture: paths.networkNodes,
    }).as('getNetworkNodes');

    cy.intercept('GET', '**/inv/cells/ref?site_id=*', {
      fixture: paths.cellReference,
    }).as('getCellReference');

    // Skip actual POST requests and simulate success responses
    cy.intercept('POST', '**/inv/cells*', {
      statusCode: 200,
      body: { success: true },
    }).as('postCell');

    cy.intercept('POST', '**/inv/cells/*', {
      statusCode: 200,
      body: { success: true },
    }).as('postNode');

    // Ensure system health and login before visiting the page
    cy.system_health();
    cy.useLogin();

    cy.log('Visited create cell form');
    cy.visit(createNodeForm);
  });

  it('creates a node with required fields only', () => {
    cy.get('select#region').should('be.visible').select('Marlow Development United Kingdom');
    cy.get('select#site').should('be.visible').select('Ansir Test');
    cy.get('select#node_type').should('be.visible').select('server');
    cy.get('select[name="serverNodes.0.node_serial_no"]').select('test1-dauk-mrl-d');
    cy.get('select[name="serverNodes.0.lifecycle"]').select('COMMISSIONING');
  });

  it('resets the form', () => {
    // Fill the form
    cy.get('select#region').select('Marlow Development United Kingdom');
    cy.get('select#site').select('Ansir Test');
    cy.get('select#node_type').select('server');
    cy.get('select[name="serverNodes.0.node_serial_no"]').select('test1-dauk-mrl-d');
    cy.get('select[name="serverNodes.0.lifecycle"]').select('COMMISSIONING');

    // Reset form
    cy.get('[data-testid="create-node-reset-form"]').click();

    // Check if form is reset
    cy.get('select#region').should('have.value', '');
    cy.get('select#site').should('have.value', '');
    cy.get('select#node_type').should('have.value', '');
  });

  it('adds and removes multiple servers', () => {
    cy.get('select#region').select('Marlow Development United Kingdom');
    cy.get('select#site').select('Ansir Test');
    cy.get('select#node_type').select('server');

    // Add multiple servers
    cy.get('select[name="serverNodes.0.node_serial_no"]').select('test1-dauk-mrl-d');
    cy.get('select[name="serverNodes.0.lifecycle"]').select('COMMISSIONING');

    cy.get('select[name="serverNodes.0.node_serial_no"]').select('test1-dauk-mrl-d');
  });

  it('creates a node with a network', () => {
    cy.get('select#region').select('Marlow Development United Kingdom');
    cy.get('select#site').select('Ansir Test');
    cy.get('select#node_type').select('network');

    // Select network serial number
    cy.get('select[name="networkNodes.0.node_serial_no"]').select('saqib-test-firewall');

    // Select lifecycle
    cy.get('select[name="networkNodes.0.lifecycle"]').select('COMMISSIONING');
  });
});
