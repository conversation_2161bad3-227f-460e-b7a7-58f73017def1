/* eslint-disable cypress/unsafe-to-chain-command */
/* eslint-disable cypress/no-unnecessary-waiting */
/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />

import 'cypress-real-events';
describe('ClusterCard Component Tests', () => {
  const cellOverViewUrl = '/cell-overview/nodes';

  beforeEach(() => {
    const paths = {
      sites: 'cellCreation/AddCell/mockData/sites.json',
      cellReference: 'cellCreation/AddCell/mockData/cellReference.json',
      nodes: 'clusterNode/nodes.json',
      serverNodes: 'cellVsr/serverNodes.json',
      cells: 'cellVsr/cell.json',
      countries: 'common/countries.json',
      activeAlarmsCount: 'common/activeAlarmsCount.json',
      regions: 'common/regions.json',
      clusters: 'clusterNode/clusters.json',
    };

    // Logging paths to verify
    cy.task('log', 'Validating fixture paths...');
    cy.task('log', JSON.stringify(paths));

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/inv/stats/regions', {
      fixture: paths.regions,
    }).as('getRegions');

    cy.intercept('GET', '**/inv/stats/countries**', { fixture: paths.countries }).as('getCountries');

    cy.intercept('GET', '**/mc/alarms/active/count**', { fixture: paths.activeAlarmsCount }).as('getActiveCount');

    cy.intercept('GET', '**/inv/cells**', { fixture: paths.cells }).as('getCells');

    cy.intercept('GET', '**/inv/ref/sites', {
      fixture: paths.sites,
    }).as('getSites');

    cy.intercept('GET', '**/inv/cells/ref?site_id=*', {
      fixture: paths.cellReference,
    }).as('getCellReference');

    cy.intercept('GET', '**/orc/server/nodes/**', {
      fixture: paths.serverNodes,
    }).as('getServerNodes');

    cy.intercept('GET', '**/inv/nodes**', {
      fixture: paths.nodes,
    }).as('getNodes');

    cy.intercept('GET', '**/orc/anthos/cluster_status/**', {
      fixture: paths.clusters,
    }).as('getCluster');

    cy.system_health();
    cy.useLogin();
    cy.visit(cellOverViewUrl);
  });

  it('Cluster Card Details', () => {
    cy.get('table.chakra-table').should('be.visible');
    cy.get('table.chakra-table tbody tr').first().click();
    cy.get('[data-testid="cells-node-ClusterComponent"]').should('be.visible');
    cy.get('[data-testid="cluster"]').should('be.visible').first().click();
    cy.get(':nth-child(1) > .chakra-card__header > [data-testid="sub-cluster-heading"]').should('be.visible');
    cy.get(':nth-child(1) > .chakra-card__header > [data-testid="sub-cluster-heading"]').click();
    cy.get('[data-testid="sub-cluster-details-body"] > .chakra-stack').should('be.visible');
    cy.get('[data-testid="sub-cluster-render-details"] > :nth-child(1)').should('be.visible');
    cy.get('[data-testid="sub-cluster-render-details-container"]').should('be.visible');
    cy.get('[data-testid="sub-cluster-render-details-header-Conditions"]').should('be.visible');
    cy.get('[data-testid="sub-cluster-render-details-header-Conditions"]').should('have.text', 'Conditions');
    cy.get('[data-testid="sub-cluster-render-details-body"]').should('be.visible');

    cy.get('[data-testid="sub-cluster-render-details-header-Containers"]').should('be.visible');
    cy.get('[data-testid="sub-cluster-render-details-header-Containers"]').should('have.text', 'Containers');
    cy.get('[data-testid="sub-cluster-render-details-body"]').should('be.visible');
  });
});
