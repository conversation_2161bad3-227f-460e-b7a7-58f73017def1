/* eslint-disable cypress/require-data-selectors */
/* eslint-disable cypress/no-unnecessary-waiting */
/// <reference types="cypress" />
import 'cypress-real-events';

import { AUTH_TOKEN_KEY } from '../../../src/data/constants';

describe('Test the login component', () => {
  beforeEach(() => {
    cy.visit('/');
    cy.viewport(1600, 1000);
  });

  it('redirect to login page if no jwt', () => {
    localStorage.removeItem(AUTH_TOKEN_KEY);
    cy.url().should('include', 'login?redirectUrl=');
  });

  it('protected pages restricted if no jwt', () => {
    localStorage.removeItem(AUTH_TOKEN_KEY);
    cy.visit('/cell-overview');
    cy.get('.chakra-table ').should('not.exist');
  });

  it('access protected pages if user has jwt', () => {
    cy.useLogin();
    cy.visit('/cell-overview');
    cy.get('.chakra-table ').should('exist');
  });

  it('logout', () => {
    cy.useLogin();
    cy.visit('/cell-overview');
    cy.get('[data-testid=profile-icon]').click();
    cy.get('[data-testid=profile-icon-content]').find('button').contains('Logout');
  });
});

export {};
