const siteManagerUrl = '/site-manager';

describe('Test Site Manager Component', function () {
  beforeEach(function () {
    cy.system_health();
    cy.useLogin();
    cy.visit(siteManagerUrl);

    // Load fixture data and store it in the `this` context
    cy.fixture('siteManager/regions/regions').then((data) => {
      this.mockRegionsData = data;
    });

    cy.fixture('siteManager/sites/sites').then((data) => {
      this.mockSitesData = data;
    });
  });

  it('should display site manager component ', function () {
    const regionsData = this.mockRegionsData;
    const sitesData = this.mockSitesData;

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/inv/ref/regions', {
      body: regionsData,
    }).as('getRegions');

    cy.intercept('GET', '**/inv/ref/sites', {
      body: sitesData,
    }).as('getSites');

    const text_present = ['Select a region or site to view details', 'Site Manager'];
    text_present.forEach((text) => {
      cy.contains('h2', text).should('be.visible');
    });

    const button_present = ['All', 'United Kingdom', 'United States', 'Collapse All'];
    button_present.forEach((button) => {
      cy.contains('button', button).should('be.visible');
    });

    regionsData.map((region) => {
      cy.get(`[data-testid="region-${region.region_id}"]`).should('be.visible');
      cy.get(`[data-testid="region-${region.region_id}"]`).click();
      cy.get('[data-testid="region-code"]')
        .should('be.visible')
        .contains(region.region_code + ' - ' + region.region_name);
      cy.get('[data-testid="region-description"]').should('be.visible').contains(region.description);
      const address = [region.latitude, region.longitude, region.radius];
      address.forEach((address) => {
        if (address) {
          cy.get('[data-testid="region-address"]').should('be.visible').contains(address);
        }
      });

      cy.get('[data-testid="region-country"]').should('be.visible').contains(region.country_name);

      const regionSites = sitesData.filter((site) => site.region_id === region.region_id);

      regionSites.map((site) => {
        cy.get(`[data-testid="site-${site.site_id}"]`).should('be.visible');
        cy.get(`[data-testid="site-${site.site_id}"]`).click();

        cy.get('[data-testid="site-name"]').should('be.visible').contains(site.name);
        if (site.description) {
          cy.get('[data-testid="site-description"]').should('be.visible').contains(site.description);
        }

        const address = [site.latitude, site.longitude];
        address.forEach((address) => {
          if (address) {
            cy.get('[data-testid="site-address"]').should('be.visible').contains(address);
          }
        });
        if (site.country_name) {
          cy.get('[data-testid="site-country"]').should('be.visible').contains(site.country_name);
        }
        if (site.additional_info) {
          cy.get('[data-testid="site-additional-info"]').should('be.visible').contains(site.additional_info);
        }
        cy.get('[data-testid="site-cells-deployed"]').should('be.visible').contains(site.site_cells?.length);
        cy.get('[data-testid="site-nodes-deployed"]').should('be.visible').contains(site.site_nodes?.length);
      });
    });
  });

  it('should filter regions by country', function () {
    const regionsData = this.mockRegionsData;
    const sitesData = this.mockSitesData;

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/inv/ref/4g/regions', {
      body: regionsData,
    }).as('getRegions');

    cy.intercept('GET', '**/inv/ref/4g/sites', {
      body: sitesData,
    }).as('getSites');

    const text_present = ['Select a region or site to view details', 'Site Manager'];
    text_present.forEach((text) => {
      cy.contains('h2', text).should('be.visible');
    });

    const filters = ['All', 'United Kingdom', 'United States'];
    filters.forEach((filter) => {
      cy.contains('button', filter).should('be.visible');
      cy.contains('button', filter).click();

      if (filter !== 'All') {
        const filteredRegions = regionsData.filter((region) => region.country_name === filter);
        filteredRegions.map((region) => {
          cy.get(`[data-testid="region-${region.region_id}"]`).should('be.visible');
        });
      } else {
        regionsData.map((region) => {
          cy.get(`[data-testid="region-${region.region_id}"]`).should('be.visible');
        });
      }
    });
  });

  it('should create a new region', function () {
    const regionsData = this.mockRegionsData;
    const sitesData = this.mockSitesData;

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/inv/ref/regions', {
      body: regionsData,
    }).as('getRegions');

    cy.intercept('GET', '**/inv/ref/sites', {
      body: sitesData,
    }).as('getSites');

    // POST API call - after creating new region
    cy.intercept('POST', '**/inv/ref/regions', {
      statusCode: 200,
      body: {
        message: 'Region created successfully',
        region: {
          region_id: 20,
          region_code: 'GBTEST',
          region_name: 'United Kingdom',
          country_code: 'GBR',
          latitude: '51.5074',
          longitude: '0.1278',
          radius: '100',
          description: 'London, United Kingdom',
        },
      },
    }).as('createRegion');

    // GET API call - after region creation (refreshed list)
    cy.intercept('GET', '**/inv/ref/regions', {
      body: [
        ...regionsData,
        {
          region_id: 20,
          region_code: 'GBTEST',
          region_name: 'United Kingdom',
          country_code: 'GBR',
          latitude: '51.5074',
          longitude: '0.1278',
          radius: '100',
          description: 'London, United Kingdom',
        },
      ],
    }).as('getUpdatedRegions');

    // GET API call - after region creation (refreshed list)
    cy.intercept('GET', '**/inv/ref/sites', {
      body: [
        ...sitesData,
        {
          site_id: 46,
          name: 'GBTEST default site',
          address: '',
          region_id: 20,
          region_code: 'GBTEST',
          country_code: 'GBR',
          description: 'Autogenerated from Region GBTEST',
          additional_info: null,
          latitude: 10.0,
          longitude: 10.0,
          default_site: true,
          site_nodes: [],
          site_cells: [],
        },
      ],
    }).as('getUpdatedSites');

    const text_present = ['Select a region or site to view details', 'Site Manager'];
    text_present.forEach((text) => {
      cy.contains('h2', text).should('be.visible');
    });

    const button_present = ['All', 'United Kingdom', 'United States', 'Collapse All', 'Create Region', 'Create Site'];
    button_present.forEach((button) => {
      cy.contains('button', button).should('be.visible');
    });

    const formParams = {
      scenario_1: {
        params: {
          region_code: 'GBTEST',
          region_name: 'United Kingdom',
          country_code: 'GBR',
          latitude: '0.5074',
          longitude: '0.1278',
          radius: '100',
          description: 'London, United Kingdom',
        },
        errors: 'no errors',
      },
    };

    Object.keys(formParams).forEach((element) => {
      cy.contains('button', 'Create Region').click();

      // fill the form
      Object.keys(formParams[element].params).forEach((field) => {
        cy.get(`[id=${field}]`).type(formParams[element].params[field]);
      });

      if (formParams[element].errors === 'no errors') {
        cy.get('button[type="submit"]').contains('Create Region').click();

        cy.wait('@createRegion').its('response.statusCode').should('eq', 200);
        cy.wait('@getUpdatedRegions');
        cy.wait('@getUpdatedSites');

        cy.get(`[data-testid="region-${20}"]`).should('be.visible').click();
        cy.get(`[data-testid="site-${46}"]`).should('be.visible');

        // check if the success message is displayed
        cy.get('[id="toast-1-title"]').should('be.visible').contains('Region created');
        cy.get('[id="toast-1-description"]').should('be.visible').contains('Region has been created successfully');
      } else {
        cy.get('[data-testid="region-code-error"]').should('be.visible').and('contain', formParams[element].errors);
        cy.get(`[id=cancel]`).click();
      }
    });
  });

  it('should edit a region', function () {
    const regionsData = this.mockRegionsData;
    const sitesData = this.mockSitesData;

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/inv/ref/regions', {
      body: regionsData,
    }).as('getRegions');

    cy.intercept('GET', '**/inv/ref/sites', {
      body: sitesData,
    }).as('getSites');

    const updatedRegion = {
      region_id: 4,
      region_code: 'GBDEV2',
      region_name: 'Marlow Development',
      country_code: 'GBR',
      country_name: 'United Kingdom',
      description: 'Devices for nms-dev-2 in Marlow lab - Updated Description',
      default_site_id: 31,
      latitude: 0.573291,
      longitude: -0.760449,
      radius: 500,
      creation_date: '2023-08-02',
    };

    // PUT API call - after updating region
    cy.intercept('PATCH', `**/inv/ref/regions/${updatedRegion.region_code}`, {
      statusCode: 200,
      body: {
        message: 'Region updated successfully',
        region: updatedRegion,
      },
    }).as('updateRegion');

    // GET API call - after region creation (refreshed list)
    // replace the existing region with the updated region
    const updatedRegionsData = regionsData.map((region) =>
      region.region_code === updatedRegion.region_code ? updatedRegion : region
    );
    cy.intercept('GET', '**/inv/ref/regions', {
      body: updatedRegionsData,
    }).as('getUpdatedRegions');

    const text_present = ['Select a region or site to view details', 'Site Manager'];
    text_present.forEach((text) => {
      cy.contains('h2', text).should('be.visible');
    });

    const button_present = ['All', 'United Kingdom', 'United States', 'Collapse All', 'Create Region', 'Create Site'];
    button_present.forEach((button) => {
      cy.contains('button', button).should('be.visible');
    });

    // click on the region and edit it
    cy.get(`[data-testid="region-${updatedRegion.region_id}"]`).should('be.visible').click();

    // click on the edit button
    cy.contains('button', 'Edit').click();

    const disabledFields = ['region_code', 'region_name', 'country_code'];
    const editableFields = ['latitude', 'longitude', 'radius', 'description'];

    disabledFields.forEach((field) => {
      cy.get(`[id=${field}]`).should('be.disabled');
    });

    editableFields.forEach((field) => {
      // clear the existing value and enter new values
      cy.get(`[id=${field}]`).clear();
      cy.get(`[id=${field}]`).type(updatedRegion[field]);
    });

    cy.get('button[type="submit"]').contains('Update Region').click();

    cy.wait('@updateRegion');
    cy.wait('@getUpdatedRegions');

    // check if the success message is displayed
    cy.get('[id="toast-1-title"]').should('be.visible').contains('Region updated');
    cy.get('[id="toast-1-description"]').should('be.visible').contains('Region has been updated successfully');

    // check if the fields have the updated values
    editableFields.forEach((field) => {
      cy.get(`[id=${field}]`).should('have.value', updatedRegion[field]);
    });
  });

  it.only('should delete region successfully', function () {
    const regionsData = this.mockRegionsData;
    const sitesData = this.mockSitesData;

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/inv/ref/regions', {
      body: regionsData,
    }).as('getRegions');

    cy.intercept('GET', '**/inv/ref/sites', {
      body: sitesData,
    }).as('getSites');

    const deletedRegion = {
      region_id: 4,
      region_code: 'GBDEV2',
      region_name: 'Marlow Development',
      country_code: 'GBR',
      country_name: 'United Kingdom',
      description: 'Devices for nms-dev-2 in Marlow lab - Updated Description',
      default_site_id: 31,
      latitude: 0.573291,
      longitude: -0.760449,
      radius: 500,
      creation_date: '2023-08-02',
    };

    // DELETE API call - after deleting region
    cy.intercept('DELETE', `**/inv/ref/regions/${deletedRegion.region_code}`, {
      statusCode: 200,
      body: deletedRegion,
    }).as('deleteRegion');

    // GET API call - after region deletion (refreshed list)
    // remove the existing region from the regionsData
    const regionsListWithoutDeletedRegion = regionsData.filter(
      (region) => region.region_code !== deletedRegion.region_code
    );

    cy.intercept('GET', '**/inv/ref/regions', {
      body: regionsListWithoutDeletedRegion,
    }).as('getUpdatedRegions');

    const sitesListWithoutDeletedRegion = sitesData.filter((site) => site.region_code !== deletedRegion.region_code);

    cy.intercept('GET', '**/inv/ref/sites', {
      body: sitesListWithoutDeletedRegion,
    }).as('getUpdatedSites');

    const text_present = ['Select a region or site to view details', 'Site Manager'];
    text_present.forEach((text) => {
      cy.contains('h2', text).should('be.visible');
    });

    const button_present = ['All', 'United Kingdom', 'United States', 'Collapse All', 'Create Region', 'Create Site'];
    button_present.forEach((button) => {
      cy.contains('button', button).should('be.visible');
    });

    // click on the region and edit it
    cy.get(`[data-testid="region-${deletedRegion.region_id}"]`).should('be.visible').click();

    // click on the delete button
    cy.contains('button', 'Delete').click();

    cy.wait('@deleteRegion');
    cy.wait('@getUpdatedRegions');

    // check if the success message is displayed
    cy.get('[id="toast-1-title"]').should('be.visible').contains('Region deleted');
    // cy.get('[id="toast-1-description"]')
    //   .should('be.visible')
    //   .contains(`${deletedRegion.region_code} has been deleted successfully`);

    // check if the region is deleted
    cy.get(`[data-testid="region-${deletedRegion.region_id}"]`).should('not.exist');
  });

  it('should display error when deleting a region', function () {
    const regionsData = this.mockRegionsData;
    const sitesData = this.mockSitesData;

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/inv/ref/regions', {
      body: regionsData,
    }).as('getRegions');

    cy.intercept('GET', '**/inv/ref/sites', {
      body: sitesData,
    }).as('getSites');

    const deletedRegion = {
      region_id: 4,
      region_code: 'GBDEV2',
      region_name: 'Marlow Development',
      country_code: 'GBR',
      country_name: 'United Kingdom',
      description: 'Devices for nms-dev-2 in Marlow lab - Updated Description',
      default_site_id: 31,
      latitude: 0.573291,
      longitude: -0.760449,
      radius: 500,
      creation_date: '2023-08-02',
    };

    // DELETE API call - after deleting region
    cy.intercept('DELETE', `**/inv/ref/regions/${deletedRegion.region_code}`, {
      statusCode: 409,
      body: {
        message: 'Region cannot be deleted as sites exist within it',
      },
    }).as('deleteRegion');

    const text_present = ['Select a region or site to view details', 'Site Manager'];
    text_present.forEach((text) => {
      cy.contains('h2', text).should('be.visible');
    });

    const button_present = ['All', 'United Kingdom', 'United States', 'Collapse All', 'Create Region', 'Create Site'];
    button_present.forEach((button) => {
      cy.contains('button', button).should('be.visible');
    });

    // click on the region and edit it
    cy.get(`[data-testid="region-${deletedRegion.region_id}"]`).should('be.visible').click();

    // click on the delete button
    cy.contains('button', 'Delete').click();

    cy.wait('@deleteRegion');

    // check if the region is not deleted
    cy.get(`[data-testid="region-${deletedRegion.region_id}"]`).should('be.visible');

    // check if the error message is displayed
    cy.get('[id="toast-1-title"]').should('be.visible').contains('Error');
    cy.get('[id="toast-1-description"]')
      .should('be.visible')
      .contains('Region cannot be deleted as sites exist within it');
  });
});
