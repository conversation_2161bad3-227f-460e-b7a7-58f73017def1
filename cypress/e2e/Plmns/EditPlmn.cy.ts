/// <reference types="cypress" />
import 'cypress-real-events';

const cellOverview = '/cell-overview';
describe('CellPlmn Component Tests', () => {
  beforeEach(() => {
    cy.system_health();
    cy.useLogin();
    cy.visit(cellOverview);
  });

  it('should display Available PLMNs correctly', () => {
    cy.get('table.chakra-table ').should('be.visible');
    cy.get('table.chakra-table tbody tr').first().find('[data-testid="cell-main-table-cell-config-icon"]').click();
    cy.get('table.chakra-table tbody tr').first().find('[data-testid="edit-plmns"]').click();
    cy.get('[data-testid="available-plmns"]').should('be.visible');
    cy.get('[data-testid="available-plmns"] > .chakra-heading').should('have.text', 'Available PLMNs');
    cy.get('[data-testid="available-plmns-table"]').should('be.visible');
  });
  it('should display Assigned PLMNs correctly', () => {
    cy.get('table.chakra-table ').should('be.visible');
    cy.get('table.chakra-table tbody tr').first().find('[data-testid="cell-main-table-cell-config-icon"]').click();
    cy.get('table.chakra-table tbody tr').first().find('[data-testid="edit-plmns"]').click();
    cy.get('[data-testid="assigned-plmns"]').should('be.visible');
    cy.get('[data-testid="assigned-plmns"] > .chakra-heading').should('have.text', 'Assigned PLMNs');
    cy.get('[data-testid="assigned-plmns-table"]').should('be.visible');
  });
  it('should allow adding a PLMN to the assigned list', () => {
    cy.get('table.chakra-table ').should('be.visible');
    cy.get('table.chakra-table tbody tr').first().find('[data-testid="cell-main-table-cell-config-icon"]').click();
    cy.get('table.chakra-table tbody tr').first().find('[data-testid="edit-plmns"]').click();
    cy.get('[data-testid="available-plmns"]').should('be.visible');
    cy.get('[data-testid="available-plmns"] > .chakra-heading').should('have.text', 'Available PLMNs');
    cy.get('[data-testid="available-plmns-table"]').should('be.visible');
    cy.get('[data-testid="available-plmns-table"]').each(($row) => {
      const operator = $row.find('td').eq(1).text();
      // if row exist
      if (operator) {
        cy.get('[data-testid="available-plmns-add"]').should('be.visible');
        cy.get('[data-testid="available-plmns-add"]').first().click();
        cy.get('#toast-1-title').should('be.visible');
        cy.get('#toast-1-title').should('have.text', 'PLMN Added.');
      }
    });
  });
  it('should allow removing a PLMN from the assigned list', () => {
    cy.get('table.chakra-table ').should('be.visible');
    cy.get('table.chakra-table tbody tr').first().find('[data-testid="cell-main-table-cell-config-icon"]').click();
    cy.get('table.chakra-table tbody tr').first().find('[data-testid="edit-plmns"]').click();
    cy.get('[data-testid="assigned-plmns"]').should('be.visible');
    cy.get('[data-testid="assigned-plmns"] > .chakra-heading').should('have.text', 'Assigned PLMNs');
    cy.get('[data-testid="assigned-plmns-table"]').should('be.visible');
    cy.get('[data-testid="assigned-plmns-table"]').each(($row) => {
      const operator = $row.find('td').eq(1).text();
      // if row exist
      if (operator) {
        cy.get('[data-testid="assigned-plmns-remove"]').should('be.visible');
        cy.get('[data-testid="assigned-plmns-remove"]').first().click();
        cy.get('#toast-1-title').should('be.visible');
        cy.get('#toast-1-title').should('have.text', 'Success');
      }
    });
  });
});
