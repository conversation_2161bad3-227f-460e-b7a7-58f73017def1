/* eslint-disable cypress/unsafe-to-chain-command */
/* eslint-disable cypress/no-unnecessary-waiting */
/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />

import 'cypress-real-events';
describe('VsrCard Component Tests', () => {
  const cellOverViewUrl = '/cell-overview';

  beforeEach(() => {
    const paths = {
      sites: 'cellCreation/AddCell/mockData/sites.json',
      cellReference: 'cellCreation/AddCell/mockData/cellReference.json',
      nodes: 'cellVsr/nodes.json',
      serverNodes: 'cellVsr/serverNodes.json',
      cells: 'cellVsr/cell.json',
      countries: 'common/countries.json',
      activeAlarmsCount: 'common/activeAlarmsCount.json',
      regions: 'common/regions.json',
    };

    // Logging paths to verify
    cy.task('log', 'Validating fixture paths...');
    cy.task('log', JSON.stringify(paths));

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/inv/stats/regions', {
      fixture: paths.regions,
    }).as('getRegions');

    cy.intercept('GET', '**/inv/stats/countries**', { fixture: paths.countries }).as('getCountries');

    cy.intercept('GET', '**/mc/alarms/active/count**', { fixture: paths.activeAlarmsCount }).as('getActiveCount');

    cy.intercept('GET', '**/inv/cells**', { fixture: paths.cells }).as('getCells');

    cy.intercept('GET', '**/inv/ref/sites', {
      fixture: paths.sites,
    }).as('getSites');

    cy.intercept('GET', '**/inv/cells/ref?site_id=*', {
      fixture: paths.cellReference,
    }).as('getCellReference');

    cy.intercept('GET', '**/orc/server/nodes/**', {
      fixture: paths.serverNodes,
    }).as('getServerNodes');

    cy.intercept('GET', '**/inv/cells/nodes**', {
      fixture: paths.nodes,
    }).as('getNodes');

    cy.system_health();
    cy.useLogin();
    cy.visit(cellOverViewUrl);
  });

  it('Vsr Card Details', () => {
    cy.get('table.chakra-table').should('be.visible');
    cy.get('table.chakra-table tbody tr').first().click();
    cy.get('[data-testid="cell-nodes"]').should('be.visible');
    cy.get('[data-testid="cell-nodes"]').find('table.chakra-table tbody tr').should('be.visible');
    cy.get('[data-testid="cell-nodes"]').find('table.chakra-table tbody tr').first().click();
    cy.get('[data-testid="vsr-card"]').should('be.visible').first().click();
    cy.get(':nth-child(1) > :nth-child(1) > dl > .chakra-stat__label').should('be.visible');
    cy.get(':nth-child(1) > :nth-child(1) > dl > .chakra-stat__label').should('have.text', 'ID');
    cy.get(':nth-child(1) > :nth-child(1) > dl > .chakra-stat__number').should('be.visible');
    cy.get(':nth-child(1) > :nth-child(2) > dl > .chakra-stat__label').should('be.visible');
    cy.get(':nth-child(1) > :nth-child(2) > dl > .chakra-stat__label').should('have.text', 'Up Time');
    cy.get(':nth-child(1) > :nth-child(2) > dl > .chakra-stat__number').should('be.visible');
    cy.get(':nth-child(2) > :nth-child(1) > dl > .chakra-stat__label').should('be.visible');
    cy.get(':nth-child(2) > :nth-child(1) > dl > .chakra-stat__label').should('have.text', 'Vsr Version');
    cy.get('button').contains('More Data').should('be.visible');
    cy.get('button').contains('More Data').should('be.enabled');
  });
  it('verify VSR more data table is rendering', () => {
    cy.get('table.chakra-table').should('be.visible');
    cy.get('table.chakra-table tbody tr').first().click();
    cy.get('[data-testid="cell-nodes"]').should('be.visible');
    cy.get('[data-testid="cell-nodes"]').find('table.chakra-table tbody tr').should('be.visible');
    cy.get('[data-testid="cell-nodes"]').find('table.chakra-table tbody tr').first().click();
    cy.get('[data-testid="vsr-card"]').should('be.visible').first().click();
    cy.get('button').contains('More Data').should('be.visible');
    cy.get('button').contains('More Data').click();
    cy.get('[data-testid="modal-header"]').should('be.visible').contains('VSR Routes');
  });
});
