/* eslint-disable cypress/unsafe-to-chain-command */
/* eslint-disable cypress/no-unnecessary-waiting */
/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />

const cellOverViewUrl = '/cell-overview/nodes';

describe('Power Component', () => {
  beforeEach(() => {
    const paths = {
      sites: 'cellCreation/AddCell/mockData/sites.json',
      nodes: 'nodeComponent/power/nodes.json',
      node: 'nodeComponent/power/node.json',
      cells: 'cellVsr/cell.json',
      countries: 'common/countries.json',
      activeAlarmsCount: 'common/activeAlarmsCount.json',
      regions: 'common/regions.json',
    };

    // Logging paths to verify
    cy.task('log', 'Validating fixture paths...');
    cy.task('log', JSON.stringify(paths));

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/inv/stats/regions', {
      fixture: paths.regions,
    }).as('getRegions');

    cy.intercept('GET', '**/inv/stats/countries**', { fixture: paths.countries }).as('getCountries');

    cy.intercept('GET', '**/mc/alarms/active/count**', { fixture: paths.activeAlarmsCount }).as('getActiveCount');

    cy.intercept('GET', '**/inv/cells**', { fixture: paths.cells }).as('getCells');

    cy.intercept('GET', '**/inv/ref/sites', {
      fixture: paths.sites,
    }).as('getSites');

    cy.intercept('GET', '**/orc/power/nodes/GB-DEV2-POWER-0001', {
      fixture: paths.node,
    }).as('getNode');

    cy.intercept('GET', '**/inv/nodes**', {
      fixture: paths.nodes,
    }).as('getNodes');

    cy.system_health();
    cy.useLogin();
    cy.visit(cellOverViewUrl);
  });
  it('should load power node info', () => {
    cy.get('table.chakra-table').should('be.visible');
    cy.get('table.chakra-table tbody tr').first().click();
    cy.get('.css-1yudcbx').should('have.text', 'UPS');
    cy.get(':nth-child(1) > :nth-child(1) > dl > [data-testid="stats-label"]').should('be.visible');
    cy.get(':nth-child(1) > :nth-child(1) > dl > [data-testid="stats-label"]').should('have.text', 'Component id');
    cy.get(':nth-child(3) > [data-testid="stats-header-box"] > [data-testid="stats-header"]').should('be.visible');
  });
  it('should load System info', () => {
    cy.get('table.chakra-table').should('be.visible');
    cy.get('table.chakra-table tbody tr').first().click();
    cy.get(':nth-child(3) > [data-testid="stats-header-box"] > [data-testid="stats-header"]').should(
      'have.text',
      'System'
    );
    cy.get(':nth-child(3) > :nth-child(2) > :nth-child(1) > dl > [data-testid="stats-label"]').should('be.visible');
    cy.get(':nth-child(3) > :nth-child(2) > :nth-child(1) > dl > [data-testid="stats-label"]').should(
      'have.text',
      'Description'
    );
    cy.get(':nth-child(3) > :nth-child(2) > :nth-child(2) > dl > [data-testid="stats-label"]').should(
      'have.text',
      'Object Id'
    );
    cy.get(':nth-child(3) > :nth-child(3) > :nth-child(1) > dl > [data-testid="stats-label"]').should(
      'have.text',
      'Up time'
    );
    cy.get(':nth-child(3) > :nth-child(3) > :nth-child(2) > dl > [data-testid="stats-label"]').should(
      'have.text',
      'Start Time'
    );
    cy.get(':nth-child(3) > :nth-child(4) > :nth-child(1) > dl > [data-testid="stats-label"]').should(
      'have.text',
      'Name'
    );
    cy.get(':nth-child(3) > :nth-child(4) > :nth-child(2) > dl > [data-testid="stats-label"]').should(
      'have.text',
      'Contact'
    );
    cy.get(':nth-child(6) > :nth-child(1) > dl > [data-testid="stats-label"]').should('have.text', 'Services');
  });
  it('should load Input and output info', () => {
    cy.get('table.chakra-table').should('be.visible');
    cy.get('table.chakra-table tbody tr').first().click();
    // input
    cy.get(':nth-child(6) > [data-testid="stats-header"]').should('have.text', 'Input');
    cy.get(':nth-child(7) > .chakra-table > thead.css-0 > .css-0 > :nth-child(1)').should('have.text', 'Index');
    cy.get(':nth-child(7) > .chakra-table > thead.css-0 > .css-0 > :nth-child(2) > .css-k008qs > .css-1vspl9j').should(
      'have.text',
      'Voltage'
    );
    cy.get(':nth-child(7) > .chakra-table > thead.css-0 > .css-0 > :nth-child(3) > .css-k008qs > .css-1vspl9j').should(
      'have.text',
      'Current'
    );
    cy.get(':nth-child(7) > .chakra-table > thead.css-0 > .css-0 > :nth-child(4) > .css-k008qs > .css-1vspl9j').should(
      'have.text',
      'Power'
    );
    cy.get(':nth-child(7) > .chakra-table > thead.css-0 > .css-0 > :nth-child(5)').should('have.text', 'Frequency');

    // output
    cy.get(':nth-child(8) > [data-testid="stats-header"]').should('have.text', 'Output');
    cy.get(':nth-child(9) > :nth-child(1) > dl > [data-testid="stats-label"]').should('have.text', 'Source');
    cy.get(':nth-child(9) > :nth-child(2) > dl > [data-testid="stats-label"]').should('have.text', 'Frequency');
    cy.get(':nth-child(10) > .chakra-table > thead.css-0 > .css-0 > :nth-child(1)').should('have.text', 'Index');
    cy.get(':nth-child(10) > .chakra-table > thead.css-0 > .css-0 > :nth-child(2)').should('have.text', 'Voltage');
    cy.get(':nth-child(10) > .chakra-table > thead.css-0 > .css-0 > :nth-child(3) > .css-k008qs').should(
      'have.text',
      'Current'
    );
    cy.get(':nth-child(10) > .chakra-table > thead.css-0 > .css-0 > :nth-child(4)').should('have.text', 'Power');
    cy.get(':nth-child(10) > .chakra-table > thead.css-0 > .css-0 > :nth-child(5)').should('have.text', 'Percentage');
  });
});
