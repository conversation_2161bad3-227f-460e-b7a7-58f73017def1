/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />

describe('Test the Header component', () => {
  beforeEach(() => {
    cy.system_health();
    cy.useLogin();
    cy.visit('/');
  });

  it('display header image', () => {
    cy.get('[data-testid="logo-image"]').should('be.visible');
  });

  it('check light and dark mode', () => {
    cy.get('body').should('have.class', 'chakra-ui-light');
    cy.get('label').first().click();
    cy.get('body').should('have.class', 'chakra-ui-dark');
  });

  it('display profile icon', () => {
    cy.get('[data-testid=profile-icon]').should('be.visible');
  });
});

export {};
