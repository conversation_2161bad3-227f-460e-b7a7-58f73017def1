/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />
import 'cypress-real-events';

const manifestURL = '/manifest-overview/add-network-manifest';

describe('Test Power, POD, RU manifest', () => {
  beforeEach(() => {
    const paths = {
      manifest: 'manifest/manifest.json',
      pod: 'manifest/pod.json',
      power: 'manifest/power.json',
      radio: 'manifest/ru.json',
      nodes: 'manifest/node.json',
      countries: 'common/countries.json',
      regions: 'common/regions.json',
      cells: 'cellVsr/cell.json',
      versions: 'manifest/versionDevices.json',
    };

    // Logging paths to verify
    cy.task('log', 'Validating fixture paths...');
    cy.task('log', JSON.stringify(paths));

    cy.intercept('GET', '**/inv/nodes**', { fixture: paths.nodes }).as('getNodes');
    cy.intercept('GET', '**/inv/stats/countries**', { fixture: paths.countries }).as('getCountries');
    cy.intercept('GET', '**/inv/cells**', { fixture: paths.cells }).as('getCells');
    cy.intercept('GET', '**/inv/stats/regions', {
      fixture: paths.regions,
    }).as('getRegions');

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/inv/manifest/nodes**', {
      fixture: paths.manifest,
    }).as('getManifest');

    cy.intercept('GET', '**/inv/manifest/versions/**', {
      fixture: paths.versions,
    }).as('getVersionDevices');

    cy.intercept('GET', '**/inv/manifest/pods**', {
      fixture: paths.pod,
    }).as('getPod');

    cy.intercept('GET', '**/inv/manifest/power**', {
      fixture: paths.power,
    }).as('getPower');

    cy.intercept('GET', '**/inv/manifest/ru**', {
      fixture: paths.radio,
    }).as('getRu');

    // Ensure system health and login before visiting the page
    cy.system_health();
    cy.useLogin();
    cy.visit(manifestURL);
    cy.log('Visited manifest ');
    // eslint-disable-next-line cypress/no-unnecessary-waiting
    cy.wait(2000);
  });
  it('should render the form correctly', () => {
    cy.get('form').should('exist');

    // Verify the existence of form fields
    cy.get('input[placeholder="Serial Number"]').should('exist');
    cy.get('input[placeholder="Name"]').should('exist');
    cy.get('textarea[placeholder="Description"]').should('exist');

    // Verify 'Node Type' select field by its label or associated option
    cy.contains('label', 'Node Type').parent().find('select').should('exist');
    cy.get('select').find('option[value=""]').should('contain', 'Select Node Type'); // Check placeholder option

    // Verify 'Component Type' select field
    cy.contains('label', 'Component Type').parent().find('select').should('exist');

    // Verify 'Region Code' select field
    cy.contains('label', 'Region Code').parent().find('select').should('exist');

    // Verify credentials fields
    cy.get('input[placeholder="Username"]').should('exist');
    cy.get('input[placeholder="Auth"]').should('exist');
    cy.get('input[placeholder="Priv"]').should('exist');

    // Verify submit button
    cy.get('button[type="submit"]').should('exist');
  });

  it('should update Component Type options based on Node Type', () => {
    // Select "Switch" and verify Component Type options
    cy.contains('label', 'Node Type').parent().find('select').select('Switch');
    cy.contains('label', 'Component Type')
      .parent()
      .find('select')
      .find('option')
      .should('have.length', 3)
      .and('contain', 'Fibrolan')
      .and('contain', 'Juniper');

    // Select "Firewall" and verify Component Type options
    cy.contains('label', 'Node Type').parent().find('select').select('Firewall');
    cy.contains('label', 'Component Type')
      .parent()
      .find('select')
      .find('option')
      .should('have.length', 2)
      .and('contain', 'Juniper');
  });

  it('should submit the form successfully with valid data', () => {
    // Fill out the form fields
    cy.get('input[placeholder="Serial Number"]').type('12345');
    cy.get('input[placeholder="Name"]').type('Test Manifest');
    cy.get('textarea[placeholder="Description"]').type('Description for testing');

    // Select "Node Type" and "Component Type"
    cy.contains('label', 'Node Type').parent().find('select').select('Switch');
    cy.contains('label', 'Component Type').parent().find('select').select('Fibrolan');

    // Fill out remaining fields
    cy.get('input[placeholder="Model"]').type('Fibrolan Model');
    cy.get('input[placeholder="Version"]').type('v1.0');
    cy.get('input[placeholder="IP Address"]').type('***********');
    cy.get('input[placeholder="Location"]').type('Test Location');

    // Select "Region Code"
    cy.contains('label', 'Region Code').parent().find('select').select('GBDEV2');

    // Fill out credentials
    cy.get('input[placeholder="Username"]').type('testuser');
    cy.get('input[placeholder="Auth"]').type('testauth');
    cy.get('input[placeholder="Priv"]').type('testpriv');
    cy.get('input[placeholder="Auth Protocol"]').type('SHA');
    cy.get('input[placeholder="Priv Protocol"]').type('AES');

    cy.get('button[type="submit"]').click();

    cy.intercept('POST', '**/inv/manifest/network', {
      statusCode: 200,
      body: { success: true },
    }).as('createNetworkManifest');
    // Verify success message
    cy.wait('@createNetworkManifest');
    cy.contains('The network manifest has been successfully created.').should('be.visible');
  });

  it('should display an error message when the API call fails', () => {
    // Fill out the form fields
    cy.get('input[placeholder="Serial Number"]').type('12345');
    cy.get('input[placeholder="Name"]').type('Test Manifest');
    cy.get('textarea[placeholder="Description"]').type('Description for testing');

    // Select "Node Type" and "Component Type"
    cy.contains('label', 'Node Type').parent().find('select').select('Switch');
    cy.contains('label', 'Component Type').parent().find('select').select('Fibrolan');

    // Fill out remaining fields
    cy.get('input[placeholder="Model"]').type('Fibrolan Model');
    cy.get('input[placeholder="Version"]').type('v1.0');
    cy.get('input[placeholder="IP Address"]').type('***********');
    cy.get('input[placeholder="Location"]').type('Test Location');

    // Select "Region Code"
    cy.contains('label', 'Region Code').parent().find('select').select('GBDEV2');

    // Fill out credentials
    cy.get('input[placeholder="Username"]').type('testuser');
    cy.get('input[placeholder="Auth"]').type('testauth');
    cy.get('input[placeholder="Priv"]').type('testpriv');
    cy.get('input[placeholder="Auth Protocol"]').type('SHA');
    cy.get('input[placeholder="Priv Protocol"]').type('AES');
    cy.get('button[type="submit"]').click();

    cy.intercept('POST', '**/inv/manifest/network', {
      statusCode: 500,
      body: { message: 'Internal Server Error' },
    }).as('createNetworkManifestError');

    cy.wait('@createNetworkManifestError');
    cy.contains('There was an error creating the network manifest.').should('be.visible');
  });

  it('should navigate back to the manifests overview page when Cancel is clicked', () => {
    cy.get('button').contains('Cancel').click();
    cy.url().should('include', '/manifest-overview/device-manifests');
  });
});

export {};
