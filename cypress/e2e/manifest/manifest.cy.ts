/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />
import 'cypress-real-events';

const manifestURL = '/manifest-overview/device-manifests';

describe('Test Power, POD, RU manifest', () => {
  beforeEach(() => {
    const paths = {
      manifest: 'manifest/manifest.json',
      pod: 'manifest/pod.json',
      power: 'manifest/power.json',
      radio: 'manifest/ru.json',
      nodes: 'manifest/node.json',
      countries: 'common/countries.json',
      regions: 'common/regions.json',
      cells: 'cellVsr/cell.json',
      versions: 'manifest/versionDevices.json',
    };

    // Logging paths to verify
    cy.task('log', 'Validating fixture paths...');
    cy.task('log', JSON.stringify(paths));

    cy.intercept('GET', '**/inv/nodes**', { fixture: paths.nodes }).as('getNodes');
    cy.intercept('GET', '**/inv/stats/countries**', { fixture: paths.countries }).as('getCountries');
    cy.intercept('GET', '**/inv/cells**', { fixture: paths.cells }).as('getCells');
    cy.intercept('GET', '**/inv/stats/regions', {
      fixture: paths.regions,
    }).as('getRegions');

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/inv/manifest/nodes**', {
      fixture: paths.manifest,
    }).as('getManifest');

    cy.intercept('GET', '**/inv/manifest/versions/**', {
      fixture: paths.versions,
    }).as('getVersionDevices');

    cy.intercept('GET', '**/inv/manifest/pods**', {
      fixture: paths.pod,
    }).as('getPod');

    cy.intercept('GET', '**/inv/manifest/power**', {
      fixture: paths.power,
    }).as('getPower');

    cy.intercept('GET', '**/inv/manifest/ru**', {
      fixture: paths.radio,
    }).as('getRu');

    // Ensure system health and login before visiting the page
    cy.system_health();
    cy.useLogin();
    cy.visit(manifestURL);
    cy.log('Visited manifest ');
    // eslint-disable-next-line cypress/no-unnecessary-waiting
    cy.wait(2000);
  });
  it('Pod should be view in manifest', () => {
    cy.get('table.chakra-table ').should('be.visible');
    cy.get('table.chakra-table tbody tr').first().click();
    cy.get('[data-testid="innerComponent-heading"]').should('be.visible');
    cy.get('[data-testid="innerComponent-heading"]').should('have.text', 'Pod Manifest');
    cy.get(':nth-child(2) > [data-testid="innerComponent-key"]').should('be.visible');
    cy.get(':nth-child(2) > [data-testid="innerComponent-key"]').should('have.text', 'POD Name:');
    cy.get(':nth-child(2) > [data-testid="innerComponent-value"]').should('be.visible');
    cy.get(':nth-child(2) > [data-testid="innerComponent-value"]').should('have.text', 'du-5jun-1-0');
    cy.get(':nth-child(3) > [data-testid="innerComponent-key"]').should('be.visible');
    cy.get(':nth-child(3) > [data-testid="innerComponent-key"]').should('have.text', 'Cluster Name:');
    cy.get(':nth-child(3) > [data-testid="innerComponent-value"]').should('be.visible');
    cy.get(':nth-child(3) > [data-testid="innerComponent-value"]').should(
      'have.text',
      'denseair-gke-bmctl-edge-5-1-16-6'
    );
    cy.get(':nth-child(4) > [data-testid="innerComponent-key"]').should('be.visible');
    cy.get(':nth-child(4) > [data-testid="innerComponent-key"]').should('have.text', 'Site Label:');
    cy.get(':nth-child(4) > [data-testid="innerComponent-value"]').should('be.visible');
    cy.get(':nth-child(4) > [data-testid="innerComponent-value"]').should('have.text', '5jun-1');
  });

  it('Power should be view in manifest', () => {
    cy.get('table.chakra-table ').should('be.visible');
    cy.get('table.chakra-table tbody tr').eq(1).click();
    cy.get('[data-testid="innerComponent-heading"]').should('be.visible');
    cy.get('[data-testid="innerComponent-heading"]').should('have.text', 'Power Manifest');
    cy.get(':nth-child(2) > [data-testid="innerComponent-key"]').should('be.visible');
    cy.get(':nth-child(2) > [data-testid="innerComponent-key"]').should('have.text', 'Serial No:');
    cy.get(':nth-child(2) > [data-testid="innerComponent-value"]').should('be.visible');
    cy.get(':nth-child(2) > [data-testid="innerComponent-value"]').should('have.text', '83322401503852');
    cy.get(':nth-child(3) > [data-testid="innerComponent-key"]').should('be.visible');
    cy.get(':nth-child(3) > [data-testid="innerComponent-key"]').should('have.text', 'Component Type:');
    cy.get(':nth-child(3) > [data-testid="innerComponent-value"]').should('have.text', 'UPS');
    cy.get(':nth-child(4) > [data-testid="innerComponent-key"]').should('be.visible');
    cy.get(':nth-child(4) > [data-testid="innerComponent-key"]').should('have.text', 'Node Type:');
    cy.get(':nth-child(4) > [data-testid="innerComponent-value"]').should('have.text', 'Power');
  });

  it('Radio should be view in manifest', () => {
    cy.get('table.chakra-table ').should('be.visible');
    cy.get('table.chakra-table tbody tr').eq(2).click();
    cy.get('[data-testid="innerComponent-heading"]').should('be.visible');
    cy.get('[data-testid="innerComponent-heading"]').should('have.text', 'Radio Manifest');
    cy.get(':nth-child(2) > [data-testid="innerComponent-key"]').should('be.visible');
    cy.get(':nth-child(2) > [data-testid="innerComponent-key"]').click();
    cy.get(':nth-child(2) > [data-testid="innerComponent-key"]').should('have.text', 'Serial No:');
    cy.get(':nth-child(2) > [data-testid="innerComponent-value"]').should('be.visible');
    cy.get(':nth-child(2) > [data-testid="innerComponent-value"]').should('have.text', 'N8OZO2290001N01');
    cy.get(':nth-child(3) > [data-testid="innerComponent-key"]').should('be.visible');
    cy.get(':nth-child(3) > [data-testid="innerComponent-value"]').should('have.text', '1');
    cy.get(':nth-child(4) > [data-testid="innerComponent-key"]').should('be.visible');
    cy.get(':nth-child(4) > [data-testid="innerComponent-key"]').should('have.text', 'Model:');
    cy.get(':nth-child(4) > [data-testid="innerComponent-value"]').should('be.visible');
    cy.get(':nth-child(4) > [data-testid="innerComponent-value"]').should('have.text', 'E500');
  });

  it('Should be able to view node', () => {
    cy.get('table.chakra-table ').should('be.visible');
    cy.get('table.chakra-table tbody tr').first().click();
    cy.get('[data-testid="innerComponent-heading"]').should('be.visible');
    cy.get('[data-testid="innerComponent-heading"]').should('have.text', 'Pod Manifest');
    cy.get('.css-1oampnu > :nth-child(8) > [data-testid="manifest-menu-items-button"] > svg').click({ force: true });
    cy.get('#menu-list-\\:r4t\\: > [data-testid="view-nodes"]').should('have.text', 'View node');
    cy.get('#menu-list-\\:r4t\\: > [data-testid="view-nodes"]').click();
    cy.url().should('include', '/cell-overview/nodes/GB-DEV2-DU-0001');
    cy.get('.chakra-input').should('be.visible');
    cy.get('.chakra-input').should('have.value', 'GB-DEV2-DU-0001');
    cy.get('table.chakra-table ').should('be.visible');
    cy.get('table.chakra-table tbody tr').first().click();
  });
  it('Should be able to Version Manifest tab', () => {
    cy.get('#tabs-\\:rl\\:--tab-1').should('be.visible');
    cy.get('#tabs-\\:rl\\:--tab-1').should('have.text', 'Version Manifests');
    cy.get('#tabs-\\:rl\\:--tab-1').click();
    cy.get('[data-testid="Version-heading"] > .chakra-heading').should('have.text', 'Device Version Information');
    cy.get('[data-testid="dataTable-container"] > .chakra-table > tbody.css-0 > :nth-child(1) > :nth-child(1)').should(
      'be.visible'
    );
    cy.get('[data-testid="dataTable-container"] > .chakra-table > tbody.css-0 > :nth-child(1) > :nth-child(1)').should(
      'have.text',
      'dauk-mrl-d-cav2'
    );
    cy.get('[data-testid="dataTable-container"] > .chakra-table > tbody.css-0 > :nth-child(1) > :nth-child(5)').should(
      'be.visible'
    );
    cy.get('[data-testid="dataTable-container"] > .chakra-table > tbody.css-0 > :nth-child(1) > :nth-child(5)').should(
      'have.text',
      'RHEL 8.8'
    );
  });
});

export {};
