/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />
import 'cypress-real-events';

const cellOverViewUrl = '/manifest-overview/device-manifests';
describe.skip('Test manifest component', () => {
  beforeEach(() => {
    cy.system_health();
    cy.useLogin();
    cy.visit(cellOverViewUrl);
  });

  it('display table', () => {
    cy.get('table').should('be.visible');
    cy.screenshot('Table screen shot');
  });

  it('display data in table', () => {
    cy.get('table').find('tbody').find('tr').should('have.length', 5);
  });
});

describe('Test manifest components drill down', () => {
  beforeEach(() => {
    cy.system_health();
    cy.useLogin();
    cy.visit(cellOverViewUrl);
  });

  it('open streetcell manifest ', () => {
    cy.get('table').contains('AAAA34407606').click();
    cy.get('tbody').contains('Cell Manifest').should('be.visible');
    cy.get('tbody').contains('Airspan Manifest').should('be.visible');
    cy.get('tbody').contains('BlueWireless Manifest').should('be.visible');
    cy.get('tbody').contains('Dal Manifest').should('be.visible');
    cy.get('tbody').contains('Fibrolan Manifest').should('be.visible');
  });

  it('open server manifest', () => {
    cy.get('table').contains('9XJKTN3-dauk-mrl-d-cav2-kvm-druid').click();
    cy.get('tbody').contains('Server Manifest').should('be.visible');
  });

  it('open network manifest', () => {
    cy.get('table').contains('DAUK-MRL-SYSV-SW1').click();
    cy.get('tbody').contains('Network Manifest').should('be.visible');
  });

  it('open airspan4g manifest', () => {
    cy.get('table').contains('E2C85301B894').click();
    cy.get('tbody').contains('Airspan4G Manifest').should('be.visible');
  });
  it('open cluster manifest', () => {
    cy.get('table').contains('denseair-gke-bmctl-edge-5-1-16-6').click();
    cy.get('tbody').contains('Cluster Manifest').should('be.visible');
  });
});

export {};
