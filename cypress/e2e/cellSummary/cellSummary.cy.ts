/* eslint-disable cypress/no-unnecessary-waiting */
/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />
import 'cypress-real-events';

const cellOverview = '/cell-overview';

describe('Cell Summary', () => {
  beforeEach(() => {
    cy.system_health();
    cy.useLogin();
    cy.visit(cellOverview);
  });

  it('Able to view cell summary on cellOverview page', () => {
    cy.get('[data-testid="cell_summary"]').should('be.visible');
    cy.get('[data-testid="cell_summary_status_CRITICAL"]').should('be.visible');
    cy.get('[data-testid="cell_summary_status_OK"]').should('be.visible');
    cy.get('[data-testid="cell_summary_details_button"]').should('be.visible');
    cy.get('[data-testid="cell_summary_details_button"]').should('have.text', 'Show Details ');
    cy.get('[data-testid="cell_summary_details_button"]').should('be.enabled');
  });
  it('Able to view cell summary table when click on show details button', () => {
    cy.get('[data-testid="cell_summary"]').should('be.visible');
    cy.get('[data-testid="cell_summary_details_button"]').should('be.visible');
    cy.get('[data-testid="cell_summary_details_button"]').should('have.text', 'Show Details ');
    cy.get('[data-testid="cell_summary_details_button"]').click();
    cy.get('[data-testid="cell_summary_details_button"]').should('be.visible');
    cy.get('[data-testid="cell_summary_details_button"]').should('have.text', 'Hide Details ');
    cy.get('[data-testid="cell_summary_status_region_table"]').should('be.visible');
    cy.get('[data-testid="cell_summary_status_region_table_header"]').should('be.visible');
    cy.get('[data-testid="cell_summary_status_region_table_header"]').should('have.text', 'Regions / Status');
  });
  it('Able to select from the cell summary table and it should hide the table', () => {
    cy.get('[data-testid="cell_summary"]').should('be.visible');
    cy.get('[data-testid="cell_summary_details_button"]').should('be.visible');
    cy.get('[data-testid="cell_summary_details_button"]').should('have.text', 'Show Details ');
    cy.get('[data-testid="cell_summary_details_button"] > .chakra-icon').click();
    cy.get('[data-testid="cell_summary_details_button"]').should('be.visible');
    cy.get('[data-testid="cell_summary_details_button"]').should('have.text', 'Hide Details ');
    cy.get('[data-testid="cell_summary_status_region_table"]').should('be.visible');
    cy.get('[data-testid="cell_summary_status_region_table_header"]').should('be.visible');
    cy.get('[data-testid="cell_summary_status_region_table_header"]').should('have.text', 'Regions / Status');
    cy.get(':nth-child(3) > .css-1kze9ic').should('be.visible');
    cy.get(':nth-child(3) > .css-1kze9ic').click();
    cy.get('[data-testid="cell_summary_details_button"]').should('be.enabled');
    cy.get('[data-testid="cell_summary_details_button"]').should('have.text', 'Show Details ');
  });
});
