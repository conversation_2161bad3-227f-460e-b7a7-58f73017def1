[{"type": "<PERSON><PERSON>", "uid": "639acbb3-9f6a-4269-ac78-3291811c5020", "prior_uid": null, "node_id": "GB-DEV2-VM-0002", "name": "", "context": null, "resource": "druid-dauk-mrl-d-cav3-kvm-druid", "priority": 0, "phase": "Test and measurement", "parameters": {"cell_id": null, "node_id": "GB-DEV2-VM-0002", "serial_nr": "druid-dauk-mrl-d-cav3-kvm-druid", "manager_instance": null, "object_id": 1, "object_type": "raemis"}, "description": "Restart a Druid instance", "created_at": "2024-07-31T10:30:34.814116Z"}]