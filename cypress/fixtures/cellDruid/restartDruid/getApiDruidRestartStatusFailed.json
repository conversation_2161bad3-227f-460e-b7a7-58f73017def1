[{"type": "<PERSON><PERSON>", "uid": "639acbb3-9f6a-4269-ac78-3291811c5020", "prior_uid": null, "node_id": "GB-DEV2-VM-0002", "name": "", "context": null, "resource": "druid-dauk-mrl-d-cav3-kvm-druid", "priority": 0, "phase": "Test and measurement", "parameters": {"cell_id": null, "node_id": "GB-DEV2-VM-0002", "serial_nr": "druid-dauk-mrl-d-cav3-kvm-druid", "manager_instance": null, "object_id": 1, "object_type": "raemis"}, "description": "Restart a Druid instance", "created_at": "2024-07-31T10:30:34.814116Z", "info": [{"uid": "9fbb9d50-217f-40e1-afb0-bf73f94650b6", "operation_uid": "639acbb3-9f6a-4269-ac78-3291811c5020", "progress": "Results available", "data": {"detail": "{\"error_text\": \"Permission Denied\"}"}, "message": "", "updated_at": "2024-07-31T10:30:44.241970Z"}, {"uid": "26122361-f4e0-4e56-8359-aa8d0384f63b", "operation_uid": "639acbb3-9f6a-4269-ac78-3291811c5020", "progress": "Active and running", "data": {}, "message": "", "updated_at": "2024-07-31T10:30:38.488081Z"}, {"uid": "eb715b93-178e-487a-8b7e-f699d9d8e127", "operation_uid": "639acbb3-9f6a-4269-ac78-3291811c5020", "progress": "Created inactive", "data": {}, "message": "", "updated_at": "2024-07-31T10:30:38.473021Z"}]}]