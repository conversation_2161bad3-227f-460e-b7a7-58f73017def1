{"id": "GB-MR4G-VM-0001", "server": null, "druid": {"device_id": "dauk-mrl-green-druid-core", "status": "OK", "aspects": {"enode_b_count": 6, "iproute_count": 12, "ipsec_secure_association_count": 0, "net_device_count": 10, "plmn_count": 2, "radio_zone_count": 0, "sgw_count": 1, "sgw_session_count": 1, "enable_5g": false, "enable_5g_nsa": false}, "details": {"features": {"oper_state": "enabled", "product_id": "8", "system_id": "1090875381675311761", "license_id": "faf6089588675046c43a", "license_status": "0", "issue_date": "2024-04-11T08:29:02Z", "expiry_date": null, "binding_date": null, "supported_until": "2024-12-31T12:00:00Z"}, "system": {"oper_state": "enabled", "admin_state": "unlocked", "service_state": "Active", "system_id": "1090875381675311761", "license_id": "faf6089588675046c43a", "product_id": "*******.1.72afa9015::VIRTUAL::rhel8.9::x86_64::faf6089588675046c43a::normal::1090875381675311761", "software_version": "Raemis Enterprise *******-1, r72afa9015.", "restart_required": "0", "current_time": "2024-06-19T11:23:15.376000Z"}}, "networks": [{"id": 2, "ipv4_subnet": "**********", "ipv4_subnetmask": "***********", "gateway_ipv4": "0.0.0.0", "net_device": "tun_ims", "metric": "0", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 3, "ipv4_subnet": "**********", "ipv4_subnetmask": "***********", "gateway_ipv4": "0.0.0.0", "net_device": "tun_local", "metric": "0", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 4, "ipv4_subnet": "*************", "ipv4_subnetmask": "***************", "gateway_ipv4": "0.0.0.0", "net_device": "enp7s0", "metric": "100", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 5, "ipv4_subnet": "0.0.0.0", "ipv4_subnetmask": "0.0.0.0", "gateway_ipv4": "**************", "net_device": "enp7s0", "metric": "100", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 6, "ipv4_subnet": "*************", "ipv4_subnetmask": "***************", "gateway_ipv4": "0.0.0.0", "net_device": "bond0.202", "metric": "401", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 7, "ipv4_subnet": "***************", "ipv4_subnetmask": "***************", "gateway_ipv4": "0.0.0.0", "net_device": "bond0.203", "metric": "402", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 8, "ipv4_subnet": "************", "ipv4_subnetmask": "*************", "gateway_ipv4": "0.0.0.0", "net_device": "bond0.30", "metric": "400", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 9, "ipv4_subnet": "***********", "ipv4_subnetmask": "***********", "gateway_ipv4": "***************", "net_device": "bond0.203", "metric": "402", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 10, "ipv4_subnet": "**********", "ipv4_subnetmask": "*************", "gateway_ipv4": "***************", "net_device": "bond0.203", "metric": "402", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 11, "ipv4_subnet": "***********", "ipv4_subnetmask": "*************", "gateway_ipv4": "***************", "net_device": "bond0.203", "metric": "402", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 12, "ipv4_subnet": "*************", "ipv4_subnetmask": "*************", "gateway_ipv4": "0.0.0.0", "net_device": "tun_host", "metric": "0", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 13, "ipv4_subnet": "*************", "ipv4_subnetmask": "***************", "gateway_ipv4": "***************", "net_device": "bond0.203", "metric": "0", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}], "segw": {"ipsec_child_config": [{"id": 1, "name": "tunnel1_child1", "lifetime": 86400, "rekeytime": 28800, "jitter": 2880}, {"id": 2, "name": "main-segw_child1", "lifetime": 86400, "rekeytime": 28800, "jitter": 2880}, {"id": 7, "name": "tunnel2_child1", "lifetime": 86400, "rekeytime": 28800, "jitter": 2880}], "ipsec_child_config_proposal": [{"id": 1, "child_cfg": 1, "prio": 1, "prop": 4}, {"id": 5, "child_cfg": 2, "prio": 1, "prop": 6}, {"id": 7, "child_cfg": 2, "prio": 1, "prop": 7}, {"id": 12, "child_cfg": 7, "prio": 1, "prop": 4}, {"id": 13, "child_cfg": 7, "prio": 1, "prop": 6}], "ipsec_certificate": [{"filename": "denseair_root.pem", "subject": "C=GB, O=Dense Air Ltd, CN=Root", "issuer": "C=GB, O=Dense Air Ltd, CN=Root", "expiry_date": "2032-12-15T14:59:59Z", "start_date": "2022-12-15T14:59:59Z"}, {"filename": "dauk-mrl-att-druid-nhe.pem", "subject": "C=GB, O=Dense Air Ltd, CN=dauk-mrl-att-druid-nhe", "issuer": "C=GB, O=Dense Air Ltd, CN=NZ Airspan", "expiry_date": "2025-12-04T14:18:50Z", "start_date": "2023-12-04T14:18:50Z"}, {"filename": "nzairspan.pem", "subject": "C=GB, O=Dense Air Ltd, CN=NZ Airspan", "issuer": "C=GB, O=Dense Air Ltd, CN=Root", "expiry_date": "2032-12-15T14:59:59Z", "start_date": "2023-01-16T15:29:22Z"}, {"filename": "dauk-mrl-att-druid-nhe_server.pem", "subject": "C=GB, O=Dense Air Ltd, CN=dauk-mrl-att-druid-nhe_server", "issuer": "C=GB, O=Dense Air Ltd, CN=NZ Airspan", "expiry_date": "2025-12-06T17:30:17Z", "start_date": "2023-12-06T17:30:17Z"}, {"filename": "dauk-mrl-att-druid-nhe_2nd.pem", "subject": "C=GB, O=Dense Air Ltd, CN=dauk-mrl-att-druid-nhe_2nd", "issuer": "C=GB, O=Dense Air Ltd, CN=NZ Airspan", "expiry_date": "2026-08-06T11:34:01Z", "start_date": "2024-08-06T11:34:01Z"}], "ipsec_ikeconfig_proposal": [{"id": 1, "ike_cfg": 1, "prio": 1, "prop": 4}, {"id": 2, "ike_cfg": 2, "prio": 1, "prop": 3}, {"id": 3, "ike_cfg": 2, "prio": 1, "prop": 4}, {"id": 8, "ike_cfg": 7, "prio": 1, "prop": 4}, {"id": 9, "ike_cfg": 7, "prio": 1, "prop": 6}], "ipsec_peer_config": [{"id": 1, "name": "tunnel1", "type": "CLIENT", "ike_version": 2, "rekeytime": 86400, "jitter": 180, "pool": ""}, {"id": 2, "name": "main-segw", "type": "SERVER", "ike_version": 2, "rekeytime": 86400, "jitter": 180, "pool": "main-segw_pool"}, {"id": 7, "name": "tunnel2", "type": "CLIENT", "ike_version": 2, "rekeytime": 3600, "jitter": 180, "pool": ""}], "ipsec_private_key": [{"type": 1, "filename": "dauk-mrl-att-druid-nhe.pkey"}, {"type": 1, "filename": "dauk-mrl-att-druid-nhe_server.pkey"}, {"type": 1, "filename": "dauk-mrl-att-druid-nhe_2nd.pkey"}], "ipsec_secure_association": [{"name": "MarlowLab-IPv6-SeGW_child1", "creation_time": "2023-12-14T17:21:45Z", "oper_state": "enabled", "direction": "bidirectional", "local_addr": "**************", "local_id": "C=GB, O=Dense Air Ltd, CN=dauk-mrl-att-d", "remote_addr": "**************", "remote_id": "mrl-att-ipv6segw", "integrity_algorithm": "HMAC_SHA2_256_12", "encryption_algorithm": "AES_CBC", "sa_bytes_in": 11864, "sa_packets_in": 100, "sa_bytes_out": 506160, "sa_packets_out": 1489}, {"name": "tunnel1_child1", "creation_time": "2024-08-16T10:05:16Z", "oper_state": "enabled", "direction": "bidirectional", "local_addr": "**************", "local_id": "C=GB, O=Dense Air Ltd, CN=dauk-mrl-att-d", "remote_addr": "*************", "remote_id": "mrl-att-ipv6segw", "integrity_algorithm": "HMAC_SHA2_256_12", "encryption_algorithm": "AES_CBC", "sa_bytes_in": 3934927, "sa_packets_in": 24774, "sa_bytes_out": 4714538, "sa_packets_out": 25926}, {"name": "main-segw_child1", "creation_time": "2024-08-16T10:09:01Z", "oper_state": "enabled", "direction": "bidirectional", "local_addr": "*************", "local_id": "C=GB, O=Dense Air Ltd, CN=dauk-mrl-att-d", "remote_addr": "***************", "remote_id": "00A00A01B914.airspan.com", "integrity_algorithm": "HMAC_SHA1_96", "encryption_algorithm": "AES_CBC", "sa_bytes_in": 2445135, "sa_packets_in": 9386, "sa_bytes_out": 914323, "sa_packets_out": 8530}, {"name": "main-segw_child1", "creation_time": "2024-08-16T10:09:01Z", "oper_state": "enabled", "direction": "bidirectional", "local_addr": "*************", "local_id": "C=GB, O=Dense Air Ltd, CN=dauk-mrl-att-d", "remote_addr": "**************", "remote_id": "00A00A01B934.airspan.com", "integrity_algorithm": "HMAC_SHA1_96", "encryption_algorithm": "AES_CBC", "sa_bytes_in": 5173547, "sa_packets_in": 19712, "sa_bytes_out": 6182617, "sa_packets_out": 19752}, {"name": "main-segw_child1", "creation_time": "2024-08-16T10:09:01Z", "oper_state": "enabled", "direction": "bidirectional", "local_addr": "*************", "local_id": "C=GB, O=Dense Air Ltd, CN=dauk-mrl-att-d", "remote_addr": "192.168.171.21", "remote_id": "00A00A01B706.airspan.com", "integrity_algorithm": "HMAC_SHA1_96", "encryption_algorithm": "AES_CBC", "sa_bytes_in": 2542281, "sa_packets_in": 9573, "sa_bytes_out": 936344, "sa_packets_out": 8723}, {"name": "main-segw_child1", "creation_time": "2024-08-16T10:09:01Z", "oper_state": "enabled", "direction": "bidirectional", "local_addr": "*************", "local_id": "C=GB, O=Dense Air Ltd, CN=dauk-mrl-att-d", "remote_addr": "192.168.171.19", "remote_id": "00A00A01B69C.airspan.com", "integrity_algorithm": "HMAC_SHA1_96", "encryption_algorithm": "AES_CBC", "sa_bytes_in": 11762145, "sa_packets_in": 46230, "sa_bytes_out": 27163462, "sa_packets_out": 54072}, {"name": "main-segw_child1", "creation_time": "2024-08-16T10:09:01Z", "oper_state": "enabled", "direction": "bidirectional", "local_addr": "*************", "local_id": "C=GB, O=Dense Air Ltd, CN=dauk-mrl-att-d", "remote_addr": "192.168.171.25", "remote_id": "00A00A00F750.airspan.com", "integrity_algorithm": "HMAC_SHA1_96", "encryption_algorithm": "AES_CBC", "sa_bytes_in": 3939988, "sa_packets_in": 10640, "sa_bytes_out": 997898, "sa_packets_out": 8936}, {"name": "main-segw_child1", "creation_time": "2024-08-16T10:09:01Z", "oper_state": "enabled", "direction": "bidirectional", "local_addr": "*************", "local_id": "C=GB, O=Dense Air Ltd, CN=dauk-mrl-att-d", "remote_addr": "192.168.171.23", "remote_id": "00A00A00F842.airspan.com", "integrity_algorithm": "HMAC_SHA1_96", "encryption_algorithm": "AES_CBC", "sa_bytes_in": 2542215, "sa_packets_in": 9573, "sa_bytes_out": 936651, "sa_packets_out": 8726}, {"name": "main-segw_child1", "creation_time": "2024-08-16T10:10:31Z", "oper_state": "enabled", "direction": "bidirectional", "local_addr": "*************", "local_id": "C=GB, O=Dense Air Ltd, CN=dauk-mrl-att-d", "remote_addr": "***************", "remote_id": "00A00A01B634.airspan.com", "integrity_algorithm": "HMAC_SHA1_96", "encryption_algorithm": "AES_CBC", "sa_bytes_in": 2411507, "sa_packets_in": 9167, "sa_bytes_out": 899764, "sa_packets_out": 8338}, {"name": "tunnel2_child1", "creation_time": "2024-08-16T10:25:58Z", "oper_state": "enabled", "direction": "bidirectional", "local_addr": "**************", "local_id": "C=GB, O=Dense Air Ltd, CN=dauk-mrl-att-d", "remote_addr": "*************", "remote_id": "mrl-att-ipv6segw", "integrity_algorithm": "HMAC_SHA2_256_12", "encryption_algorithm": "AES_CBC", "sa_bytes_in": 0, "sa_packets_in": 0, "sa_bytes_out": 0, "sa_packets_out": 0}], "ipsec_proposal": [{"id": 1, "proposal": "aes128-sha1-modp2048"}, {"id": 2, "proposal": "aes128-sha256-modp2048"}, {"id": 3, "proposal": "aes256-sha1-modp2048"}, {"id": 4, "proposal": "aes256-sha256-modp2048"}, {"id": 6, "proposal": "aes256-sha256"}, {"id": 7, "proposal": "aes256-sha1"}], "s1client": [{"oper_state": "enabled", "admin_state": "unlocked", "name": "eNB-1052", "s1_client_type": "Macro", "enb_name": "eNB-1052", "plmn_id": "00159", "cell_identity": "269312", "max_downlink_bandwidth": 0, "max_uplink_bandwidth": 0}, {"oper_state": "enabled", "admin_state": "unlocked", "name": "315010-eNB-1052", "s1_client_type": "Macro", "enb_name": "eNB-1052", "plmn_id": "315010", "cell_identity": "269312", "max_downlink_bandwidth": 0, "max_uplink_bandwidth": 0}, {"oper_state": "enabled", "admin_state": "unlocked", "name": "eNB-1053", "s1_client_type": "Macro", "enb_name": "eNB-1053", "plmn_id": "00159", "cell_identity": "269568", "max_downlink_bandwidth": 0, "max_uplink_bandwidth": 0}, {"oper_state": "enabled", "admin_state": "unlocked", "name": "315010-eNB-1053", "s1_client_type": "Macro", "enb_name": "eNB-1053", "plmn_id": "315010", "cell_identity": "269568", "max_downlink_bandwidth": 0, "max_uplink_bandwidth": 0}]}, "sessions": [{"id": 103, "creation_date": "", "bearer_id": 5, "uplink_flow_id": 805, "downlink_flow_id": 806, "mbr_dl": 1000000, "mbr_ul": 1000000, "qci": 9, "sgw_id": 1, "imsi": "001590140010801", "s11_teid": 1114001019, "s5s8_teid": 1114001020, "apn": "*.mnc059.mcc001.gprs", "paa_ipv4": "**************", "ecgi": "00159:269502", "tai": "00159:10", "mgw_flow_group_id": 103, "ambr_dl": 1000000, "ambr_ul": 1000000, "s11_cp_ipv4": "127.0.0.1", "s5s8_cp_teid": 1114001021, "sgw": {"id": 1, "name": "SGW", "admin_state": "unlocked", "oper_state": "enabled", "node_id": 0, "creation_date": "", "mgw_ctrl_net_device": "lo", "mgw_ctrl_local_port": "8011", "mgw_ctrl_active_url": "1", "mgw_ctrl_url1": "127.0.0.1:8013", "mgw_ctrl_url2": "", "mgw_ctrl_url3": "", "dscp": "0", "sctp_read_count": "200", "s11_gtpv2_id": "1", "s5s8_gtpv2_id": "1", "s1u_ep_group_id": "1", "s5s8_ep_group_id": "1", "cdr_periodicity": "0", "cdr_failure_handling": "0", "cdr_peak_data_sampling_period": "0", "s1u_dp_ipv4_enabled": "1", "s1u_dp_ipv6_enabled": "1", "mgw_ctrl_flow_attr_1": "1", "mgw_ctrl_flow_attr_2": "2", "mgw_ctrl_flow_attr_3": "3", "mgw_ctrl_flow_attr_4": "6", "raemis_id": 0}}], "created_at": "2024-06-19T10:23:54.868704Z"}, "vsr": null, "acp": null}