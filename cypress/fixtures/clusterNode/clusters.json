{"id": "GB-DEV2-GDCV-0001", "resources": [{"name": "denseair-gke-bmctl-edge-5-1-16-6", "nodes": [{"node": "dauk-mrl-d-gdcv-host04.denseair.net", "storage": {"allocatable_bytes": 676127046561, "capacity_bytes": 751252275200}, "containers": [{"container": "cu-cp-app", "cpu": {"cpu_usage_percentage": 0.01, "cpu_usage_nano_cores": 4628581}, "memory_usage": {"memory_usage_ki": 2181140, "memory_usage_percentage": 1.23}}, {"container": "cu-up-app", "cpu": {"cpu_usage_percentage": 1.26, "cpu_usage_nano_cores": **********}, "memory_usage": {"memory_usage_ki": 3353968, "memory_usage_percentage": 1.89}}], "conditions": [{"last_heartbeat_time": "2024-04-12T16:30:52Z", "last_transition_time": "2024-04-12T16:15:50Z", "message": "Container runtime on the node is functioning properly", "reason": "ContainerRuntimeIsHealthy", "status": false, "type": "ContainerRuntimeUnhealthy"}, {"last_heartbeat_time": "2024-04-12T16:30:52Z", "last_transition_time": "2024-04-12T16:15:50Z", "message": "kubelet on the node is functioning properly", "reason": "KubeletIsHealthy", "status": false, "type": "KubeletUnhealthy"}, {"last_heartbeat_time": "2024-04-12T16:30:52Z", "last_transition_time": "2024-04-12T16:15:50Z", "message": "kernel has no deadlock", "reason": "KernelHasNoDeadlock", "status": false, "type": "KernelDeadlock"}, {"last_heartbeat_time": "2024-04-12T16:30:52Z", "last_transition_time": "2024-04-12T16:15:50Z", "message": "Filesystem is not read-only", "reason": "FilesystemIsNotReadOnly", "status": false, "type": "ReadonlyFilesystem"}, {"last_heartbeat_time": "2024-04-12T16:30:52Z", "last_transition_time": "2024-04-12T16:15:50Z", "message": "node is functioning properly", "reason": "NoFrequentUnregisterNetDevice", "status": false, "type": "FrequentUnregisterNetDevice"}, {"last_heartbeat_time": "2024-04-12T16:30:52Z", "last_transition_time": "2024-04-12T16:15:50Z", "message": "kubelet is functioning properly", "reason": "NoFrequentKubeletRestart", "status": false, "type": "FrequentKubeletRestart"}, {"last_heartbeat_time": "2024-04-12T16:30:52Z", "last_transition_time": "2024-04-12T16:15:50Z", "message": "docker is functioning properly", "reason": "NoFrequentDockerRestart", "status": false, "type": "FrequentDockerRestart"}, {"last_heartbeat_time": "2024-04-12T16:30:52Z", "last_transition_time": "2024-04-12T16:15:50Z", "message": "containerd is functioning properly", "reason": "NoFrequentContainerdRestart", "status": false, "type": "FrequentContainerdRestart"}, {"last_heartbeat_time": "2024-06-25T10:32:33Z", "last_transition_time": "2024-06-14T13:41:41Z", "message": "kubelet has sufficient memory available", "reason": "KubeletHasSufficientMemory", "status": false, "type": "MemoryPressure"}, {"last_heartbeat_time": "2024-06-25T10:32:33Z", "last_transition_time": "2024-06-14T13:41:41Z", "message": "kubelet has no disk pressure", "reason": "KubeletHasNoDiskPressure", "status": false, "type": "DiskPressure"}, {"last_heartbeat_time": "2024-06-25T10:32:33Z", "last_transition_time": "2024-06-14T13:41:41Z", "message": "kubelet has sufficient PID available", "reason": "KubeletHasSufficientPID", "status": false, "type": "PIDPressure"}, {"last_heartbeat_time": "2024-06-25T10:32:33Z", "last_transition_time": "2024-06-14T13:41:41Z", "message": "kubelet is posting ready status", "reason": "KubeletReady", "status": true, "type": "Ready"}], "status": "OK"}, {"node": "dauk-mrl-d-gdcv-host05.denseair.net", "storage": {"allocatable_bytes": 676127046561, "capacity_bytes": 751252275200}, "containers": [{"container": "cu-oam-event", "cpu": {"cpu_usage_percentage": 0.14, "cpu_usage_nano_cores": 108962698}, "memory_usage": {"memory_usage_ki": 21984, "memory_usage_percentage": 0.01}}, {"container": "cu-oam-event", "cpu": {"cpu_usage_percentage": 0.12, "cpu_usage_nano_cores": 96287343}, "memory_usage": {"memory_usage_ki": 21984, "memory_usage_percentage": 0.01}}, {"container": "du-5jun-1", "cpu": {"cpu_usage_percentage": 0.04, "cpu_usage_nano_cores": 28119723}, "memory_usage": {"memory_usage_ki": 14744, "memory_usage_percentage": 0.01}}, {"container": "event-api", "cpu": {"cpu_usage_percentage": 0.02, "cpu_usage_nano_cores": 15906934}, "memory_usage": {"memory_usage_ki": 20948, "memory_usage_percentage": 0.01}}, {"container": "du-5jun-1", "cpu": {"cpu_usage_percentage": 0.01, "cpu_usage_nano_cores": 7310283}, "memory_usage": {"memory_usage_ki": 9332, "memory_usage_percentage": 0.0}}, {"container": "du-5jun-1", "cpu": {"cpu_usage_percentage": 0.01, "cpu_usage_nano_cores": 6773380}, "memory_usage": {"memory_usage_ki": 8896, "memory_usage_percentage": 0.0}}, {"container": "vran-lcm", "cpu": {"cpu_usage_percentage": 0.01, "cpu_usage_nano_cores": 4688393}, "memory_usage": {"memory_usage_ki": 48920, "memory_usage_percentage": 0.02}}], "conditions": [{"last_heartbeat_time": "2024-04-12T16:30:52Z", "last_transition_time": "2024-04-12T16:15:50Z", "message": "Container runtime on the node is functioning properly", "reason": "ContainerRuntimeIsHealthy", "status": false, "type": "ContainerRuntimeUnhealthy"}, {"last_heartbeat_time": "2024-04-12T16:30:52Z", "last_transition_time": "2024-04-12T16:15:50Z", "message": "kubelet on the node is functioning properly", "reason": "KubeletIsHealthy", "status": false, "type": "KubeletUnhealthy"}, {"last_heartbeat_time": "2024-04-12T16:30:52Z", "last_transition_time": "2024-04-12T16:15:50Z", "message": "kernel has no deadlock", "reason": "KernelHasNoDeadlock", "status": false, "type": "KernelDeadlock"}, {"last_heartbeat_time": "2024-04-12T16:30:52Z", "last_transition_time": "2024-04-12T16:15:50Z", "message": "Filesystem is not read-only", "reason": "FilesystemIsNotReadOnly", "status": false, "type": "ReadonlyFilesystem"}, {"last_heartbeat_time": "2024-04-12T16:30:52Z", "last_transition_time": "2024-04-12T16:15:50Z", "message": "node is functioning properly", "reason": "NoFrequentUnregisterNetDevice", "status": false, "type": "FrequentUnregisterNetDevice"}, {"last_heartbeat_time": "2024-04-12T16:30:52Z", "last_transition_time": "2024-04-12T16:15:50Z", "message": "kubelet is functioning properly", "reason": "NoFrequentKubeletRestart", "status": false, "type": "FrequentKubeletRestart"}, {"last_heartbeat_time": "2024-04-12T16:30:52Z", "last_transition_time": "2024-04-12T16:15:50Z", "message": "docker is functioning properly", "reason": "NoFrequentDockerRestart", "status": false, "type": "FrequentDockerRestart"}, {"last_heartbeat_time": "2024-04-12T16:30:52Z", "last_transition_time": "2024-04-12T16:15:50Z", "message": "containerd is functioning properly", "reason": "NoFrequentContainerdRestart", "status": false, "type": "FrequentContainerdRestart"}, {"last_heartbeat_time": "2024-06-25T10:32:33Z", "last_transition_time": "2024-06-14T13:41:41Z", "message": "kubelet has sufficient memory available", "reason": "KubeletHasSufficientMemory", "status": false, "type": "MemoryPressure"}, {"last_heartbeat_time": "2024-06-25T10:32:33Z", "last_transition_time": "2024-06-14T13:41:41Z", "message": "kubelet has no disk pressure", "reason": "KubeletHasNoDiskPressure", "status": false, "type": "DiskPressure"}, {"last_heartbeat_time": "2024-06-25T10:32:33Z", "last_transition_time": "2024-06-14T13:41:41Z", "message": "kubelet has sufficient PID available", "reason": "KubeletHasSufficientPID", "status": false, "type": "PIDPressure"}, {"last_heartbeat_time": "2024-06-25T10:32:33Z", "last_transition_time": "2024-06-14T13:41:41Z", "message": "kubelet is posting ready status", "reason": "KubeletReady", "status": true, "type": "Ready"}], "status": "OK"}], "status": "OK"}]}