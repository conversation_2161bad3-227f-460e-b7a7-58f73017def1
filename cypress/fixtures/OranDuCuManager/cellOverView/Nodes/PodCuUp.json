{"id": "GB-MARL-CUUP-0007", "pod": {"status": "OK", "detail": {"conditions": [{"last_probe_time": null, "last_transition_time": "2024-09-18T14:18:57+00:00", "message": null, "reason": null, "status": "True", "type": "PodReadyToStartContainers"}, {"last_probe_time": null, "last_transition_time": "2024-09-18T14:18:53+00:00", "message": null, "reason": null, "status": "True", "type": "Initialized"}, {"last_probe_time": null, "last_transition_time": "2024-09-18T14:18:57+00:00", "message": null, "reason": null, "status": "True", "type": "Ready"}, {"last_probe_time": null, "last_transition_time": "2024-09-18T14:18:57+00:00", "message": null, "reason": null, "status": "True", "type": "ContainersReady"}, {"last_probe_time": null, "last_transition_time": "2024-09-18T14:18:53+00:00", "message": null, "reason": null, "status": "True", "type": "PodScheduled"}], "container_statuses": [{"allocated_resources": null, "container_id": "containerd://bc078f7114f3c6ee40f8b08873e2d03aecb07502f3487533ea4b7b827c0d4666", "image": "europe-west2-docker.pkg.dev/da-rad-dev02-a3e1/da-rad--docker-repo/vran.rsys/cu-up/cu-up:24.8.0-dev-197", "image_id": "europe-west2-docker.pkg.dev/da-rad-dev02-a3e1/da-rad--docker-repo/vran.rsys/cu-up/cu-up@sha256:e0c690aea1d2fdb164285d92c205f9d8cf1587a170b71df42af74fc7764cb0e5", "last_state": {"running": null, "terminated": null, "waiting": null}, "name": "cu-up-app", "ready": true, "resources": null, "restart_count": 0, "started": true, "state": {"running": {"started_at": "2024-09-18T14:18:56+00:00"}, "terminated": null, "waiting": null}, "volume_mounts": null}], "ephemeral_container_statuses": null, "host_ip": "***************", "host_ips": [{"ip": "***************"}], "init_container_statuses": null, "message": null, "nominated_node_name": null, "phase": "Running", "pod_ip": "**********", "pod_ips": [{"ip": "**********"}], "qos_class": "<PERSON><PERSON><PERSON><PERSON>", "reason": null, "resize": null, "resource_claim_statuses": null, "start_time": "2024-09-18T14:18:53+00:00"}, "crs": {"CuUpDeployment": {"status": {"configVersion": {}, "deploymentStatus": "Ready", "podStatus": {"cuup": "Ready", "oam-event-api": "Ready"}}, "content": {"apiVersion": "cuup.deployment.radisys.com/v1alpha1", "kind": "CuUpDeployment", "metadata": {"annotations": {"rel": "24.8.0-dev"}, "creationTimestamp": "2024-09-18T14:16:56Z", "generation": 2, "labels": {"cuupinstance": "sector1", "site": "18sep6c"}, "name": "cuup-18sep6c", "namespace": "default", "resourceVersion": "98284090", "uid": "9ff2da9e-8bf3-4ec2-b5cc-3b752eff5e1b"}, "spec": {"action": "NONE", "configSelector": {"appConfig": {"site": "18sep6c"}}, "crypto": {"hardwareResourceName": "generic", "hardwareResourcePrefix": "qat.intel.com", "type": "SOFTWARE"}, "deploymentProfile": "p1", "enabled": true, "exportMetrics": true, "imagePullSecrets": [{"name": "da-gcr-key"}], "images": {"cu-up": {"pullPolicy": "IfNotPresent", "repository": "europe-west2-docker.pkg.dev/da-rad-dev02-a3e1/da-rad--docker-repo/vran.rsys/cu-up/cu-up", "tag": "24.8.0-dev-197"}, "oam-event": {"pullPolicy": "IfNotPresent", "repository": "europe-west2-docker.pkg.dev/da-rad-dev02-a3e1/da-rad--docker-repo/vran.rsys/cu-up/cu-up-oam-event", "tag": "24.8.0-dev-197"}}, "logToConsole": false, "network": {"externalIP": "", "f1uInterface": {"function": "vf", "resourceName": "intel_sriov_dpdk_f1u1", "resourcePrefix": "radisys.com", "spoofCheck": false, "trust": true}, "nguInterface": {"function": "vf", "resourceName": "intel_sriov_dpdk_ngu", "resourcePrefix": "radisys.com", "spoofCheck": false, "trust": true}}, "persistency": {"core": {"enabled": false, "storage": "10Gi"}, "log": {"enabled": false, "storage": "10Gi"}, "numRunsToKeep": 10, "path": "/mnt/data/", "type": "hostPath"}, "service": {"metrics": {"enabled": false, "localExternalIp": "***********", "localPort": 32723, "sslAuth": {"enabled": false, "secretName": "sslcert-secret"}, "type": "ClusterIP"}}, "vesCollector": {"FQDN": "event-export-adapter", "path": "/eventListener", "port": 8080, "protocol": "http", "sslAuth": {"enabled": false, "secretName": "sslcert-secret"}}}, "status": {"configVersion": {}, "deploymentStatus": "Ready", "podStatus": {"cuup": "Ready", "oam-event-api": "Ready"}}}}, "CuUpAppConfig": {"status": {"specHash": "13683909188221094880"}, "content": {"apiVersion": "cuup.app.config.radisys.com/v1alpha1", "kind": "CuUpAppConfig", "metadata": {"annotations": {"rel": "24.8.0-dev"}, "creationTimestamp": "2024-09-18T14:16:56Z", "generation": 2, "labels": {"site": "18sep6c"}, "name": "cuupappconfig-18sep6c", "namespace": "default", "resourceVersion": "98283749", "uid": "d3c74670-ff0b-4367-9c19-83806f0db635"}, "spec": {"ME": {"AlarmList": [{"id": "CUUP"}, {"id": "SYSMON"}, {"id": "PROCMON"}, {"id": "SWM"}, {"id": "EVENT_API"}, {"id": "NETCONF_MGR"}, {"id": "FILE_MGR"}], "FMControl": [{"administrativeState": "Unlocked", "id": "CUUP"}, {"administrativeState": "Unlocked", "id": "SYSMON"}, {"administrativeState": "Unlocked", "id": "PROCMON"}, {"administrativeState": "Unlocked", "id": "SWM"}, {"administrativeState": "Unlocked", "id": "EVENT_API"}, {"administrativeState": "Unlocked", "id": "NETCONF_MGR"}, {"administrativeState": "Unlocked", "id": "FILE_MGR"}], "GNBCUUPFunction": [{"EP_E1": {"farEndEntity": "1", "gatewayAddress": "127.0.0.1", "id": "0", "localIpAddress": "0.0.0.0", "localIpPort": 0, "localIpPrefix": 24, "localVlanId": 7, "maxRetryCount": 5, "objectClass": "EP_E1", "objectInstance": "0", "objectPairInstance": "0", "remoteAddress": "***********", "remoteIpPort": 38462, "remoteIpPrefix": 24, "retryIntervalInSec": 1, "userLabel": "EP_E1"}, "EP_F1U": [{"farEndEntity": "1", "gatewayAddress": "0.0.0.0", "id": "0", "localIpAddress": "***********", "localVlanId": 7, "objectClass": "EP_F1U", "objectInstance": "0", "objectPairInstance": "0", "remoteAddress": "***********", "userLabel": "EP_F1U"}], "EP_NgU": [{"farEndEntity": "1", "gatewayAddress": "0.0.0.0", "id": "0", "localIpAddress": "*******", "localVlanId": 7, "objectClass": "EP_NgU", "objectInstance": "0", "objectPairInstance": "0", "remoteAddress": "*******", "userLabel": "EP_NgU"}], "EP_XnU": [{"farEndEntity": "1", "gatewayAddress": "0.0.0.0", "id": "0", "localIpAddress": "*******", "localVlanId": 7, "objectClass": "EP_XnU", "objectInstance": "0", "objectPairInstance": "0", "remoteAddress": "*******", "userLabel": "EP_XnU"}], "PlmnInfo": [{"MCC": "311", "MNC": "48", "NrCGI": [{"MCC": "311", "MNC": "48", "id": "0", "nCI": "000fa4001"}], "id": "0", "sNSSAI": [16777216, 16777217, 16777218, 50529801]}], "cuUpId": 1, "gNBId": 1001, "gNBIdLength": 22, "gnbCuUpVsConfig": {"cnType": "5gc", "maxNumRb": 5000, "maxNumUes": 5000, "msgMaxRetryCount": 3, "msgRetryIntervalInSec": 5, "timeToWaitInSec": 5, "userPlaneGWIpAddress": "2405:200:1410:1442:40::1"}, "gnbEgtpVsConfigCuUp": {"endMarkerTimerInMs": 200, "pathMgmtConfig": {"echoIntervalInSec": 60, "echoN3Requests": 1, "echoT3ResponseInSec": 1}, "sendUdpPortExtWithErrorInd": true}, "gnbIndex": 0, "gnbLogVsConfigCuUp": {"cuLog": [{"logLevel": "ERR", "moduleId": "APP"}, {"logLevel": "ERR", "moduleId": "COMMON"}, {"logLevel": "TRC", "moduleId": "OAM_AGENT"}, {"logLevel": "TRC", "moduleId": "GNB_MGR"}, {"logLevel": "TRC", "moduleId": "CU_UP_MGR"}, {"logLevel": "TRC", "moduleId": "RM"}, {"logLevel": "TRC", "moduleId": "UE_CONN_MGR"}, {"logLevel": "TRC", "moduleId": "BEARER_MGR"}, {"logLevel": "TRC", "moduleId": "CODEC_COMMON"}, {"logLevel": "ERR", "moduleId": "X2AP_CODEC"}, {"logLevel": "ERR", "moduleId": "F1AP_CODEC"}, {"logLevel": "TRC", "moduleId": "RRC_CODEC"}, {"logLevel": "ERR", "moduleId": "NGAP_CODEC"}, {"logLevel": "ERR", "moduleId": "XNAP_CODEC"}, {"logLevel": "ERR", "moduleId": "E1AP_CODEC"}, {"logLevel": "ERR", "moduleId": "SCTP_COMMON"}, {"logLevel": "ERR", "moduleId": "SCTP_CNTRL"}, {"logLevel": "ERR", "moduleId": "SCTP_TX"}, {"logLevel": "ERR", "moduleId": "SCTP_RX"}, {"logLevel": "TRC", "moduleId": "PDCP_C_CNTRL"}, {"logLevel": "TRC", "moduleId": "PDCP_C_RX"}, {"logLevel": "TRC", "moduleId": "PDCP_C_TX"}, {"logLevel": "ERR", "moduleId": "UDP_CNTRL"}, {"logLevel": "ERR", "moduleId": "UDP_TX"}, {"logLevel": "ERR", "moduleId": "UDP_DL_RX"}, {"logLevel": "ERR", "moduleId": "UDP_UL_RX"}, {"logLevel": "ERR", "moduleId": "NRUP_CODEC"}, {"logLevel": "ERR", "moduleId": "TIMER"}, {"logLevel": "ERR", "moduleId": "PM"}, {"logLevel": "ERR", "moduleId": "FM"}, {"logLevel": "ERR", "moduleId": "DSON"}, {"logLevel": "ERR", "moduleId": "TM"}, {"logLevel": "ERR", "moduleId": "UDP_RX_E1_PRIME"}, {"logLevel": "ERR", "moduleId": "UDP_TX_E1_PRIME"}, {"logLevel": "ERR", "moduleId": "LTERRC_CODEC"}, {"logLevel": "ERR", "moduleId": "E2AP_CODEC"}, {"logLevel": "TRC", "moduleId": "LIBOAM"}, {"logLevel": "ERR", "moduleId": "RRM_UT"}, {"logLevel": "INF", "moduleId": "EGTPU_COMMON"}, {"logLevel": "INF", "moduleId": "EGTPU_UPPER_TX_CNTRL"}, {"logLevel": "INF", "moduleId": "EGTPU_UPPER_RX_CNTRL"}, {"logLevel": "INF", "moduleId": "EGTPU_UPPER_TX"}, {"logLevel": "INF", "moduleId": "EGTPU_UPPER_RX"}, {"logLevel": "INF", "moduleId": "EGTPU_LOWER_TX_CNTRL"}, {"logLevel": "INF", "moduleId": "EGTPU_LOWER_RX_CNTRL"}, {"logLevel": "INF", "moduleId": "EGTPU_LOWER_TX"}, {"logLevel": "INF", "moduleId": "EGTPU_LOWER_RX"}, {"logLevel": "INF", "moduleId": "PDCP_COMMON"}, {"logLevel": "INF", "moduleId": "PDCP_TX_CNTRL"}, {"logLevel": "INF", "moduleId": "PDCP_RX_CNTRL"}, {"logLevel": "INF", "moduleId": "PDCP_TX"}, {"logLevel": "INF", "moduleId": "PDCP_RX"}, {"logLevel": "ERR", "moduleId": "CRYPTO_RX"}, {"logLevel": "ERR", "moduleId": "EGTPU_TIMER"}, {"logLevel": "ERR", "moduleId": "SDAP_COMMON"}, {"logLevel": "ERR", "moduleId": "SDAP_TX_CNTRL"}, {"logLevel": "ERR", "moduleId": "SDAP_RX_CNTRL"}, {"logLevel": "ERR", "moduleId": "SDAP_TX"}, {"logLevel": "ERR", "moduleId": "SDAP_RX"}, {"logLevel": "ERR", "moduleId": "SDAP_CODEC"}], "enableBinLog": false, "logFileName": "cu", "maxLogFileCount": 5, "maxLogFileSize": 200000000, "maxStatsFileParts": 5, "maxStatsFileSizeBytes": 200000000, "ngpLog": [{"logLevel": "ERR", "moduleId": "NGP"}, {"logLevel": "ERR", "moduleId": "MEM"}, {"logLevel": "ERR", "moduleId": "BUF"}, {"logLevel": "ERR", "moduleId": "STATS"}, {"logLevel": "ERR", "moduleId": "TIMER"}, {"logLevel": "ERR", "moduleId": "STHREAD"}, {"logLevel": "ERR", "moduleId": "CTHREAD"}, {"logLevel": "ERR", "moduleId": "SYS"}, {"logLevel": "ERR", "moduleId": "EXCP"}, {"logLevel": "TRC", "moduleId": "COMM"}, {"logLevel": "TRC", "moduleId": "SCTP"}, {"logLevel": "ERR", "moduleId": "UDP"}, {"logLevel": "ERR", "moduleId": "TCP"}, {"logLevel": "ERR", "moduleId": "MSGQ"}, {"logLevel": "ERR", "moduleId": "PRIOQ"}, {"logLevel": "ERR", "moduleId": "WORKQ"}, {"logLevel": "ERR", "moduleId": "PERF"}, {"logLevel": "FATAL", "moduleId": "HTTP2"}, {"logLevel": "FATAL", "moduleId": "THR_POOL"}, {"logLevel": "FATAL", "moduleId": "FAST_TIMER"}, {"logLevel": "ERR", "moduleId": "FAST_CRYPTO"}, {"logLevel": "FATAL", "moduleId": "TLS"}, {"logLevel": "FATAL", "moduleId": "FQDN"}], "rrcWiresharkDisector": {"enableRrcWiresharkDisector": false, "localAddress": "127.0.0.1", "remoteAddress": "127.0.0.1", "remotePort": 9999}, "statsCollectionPeriodMs": 10000}, "gnbSctpVsCfgCuUp": {"heartBeatIntervalInMs": 1500, "maxInboundStreams": 2, "maxInitAttempts": 5, "maxPathRetx": 1, "numOutboundStreams": 2, "rtoInitial": 1000, "rtomax": 1000, "rtomin": 1000}, "id": "0", "objectClass": "CUUP", "objectInstance": "0", "objectPairInstance": "0", "peeParametersList": {"environmentType": "Outdoor", "equipmentType": "RRU", "powerInterface": "AC", "siteDescription": "Prestige Tech Park", "siteIdentification": "TECH PARK", "siteLatitude": 12.9781, "siteLongitude": 77.6653}, "userLabel": "GNBCUUPFunction", "vnfParametersList": {"autoScalable": false, "flavourId": "1", "vnfInstanceId": "0", "vnfdId": "0"}}], "MeasurementControl": [{"defaultFileBasedGP": 300, "defaultFileLocation": "/rsysfs/opt/radisys/O-RAN/O-GNB/PM/", "defaultFileReportingPeriod": 15, "defaultSamplingPeriod": 60, "defaultStreamBasedGP": 0, "defaultStreamTarget": "127.0.0.1:8080", "id": "CUUP", "pMAdministrativeState": "Unlocked"}], "NtfSubscriptionControl": [{"HeartbeatControl": [{"heartbeatNtfPeriod": 300, "id": "0", "triggerHeartbeatNtf": false}], "id": "0", "notificationFilter": "HEARTBEAT", "notificationRecipientAddress": "http://*************/heartbeat", "notificationType": "VES", "scope": [{"scopeType": "BASE_ONLY"}]}], "TraceJob": [{"administrativeState": "Unlocked", "id": "0", "tjTraceCollectionEntityAddress": "127.0.0.1", "tjTraceReference": "048547000001B301", "tmClientInterfaceName": "oam", "tmTceCaCert": "/opt/openssl/certs/ca/ca.cert", "tmTceClientIp": "127.0.0.1", "tmTceClientPort": 2345, "tmTceClientPrivKey": "/opt/openssl/key/client/client.key", "tmTceIsIpv6": 0, "tmTceServerPort": 8080, "tmTceTcpKeepCnt": 20, "tmTceTcpKeepIdle": 180, "tmTceTcpKeepIntvl": 60, "tmTceTcpUserTimeOut": 18}, {"administrativeState": "Unlocked", "id": "1", "tjTraceCollectionEntityAddress": "127.0.0.1", "tjTraceReference": "048547000001B301", "tmClientInterfaceName": "oam", "tmTceCaCert": "/opt/openssl/certs/ca/ca.cert", "tmTceClientIp": "127.0.0.1", "tmTceClientPort": 2345, "tmTceClientPrivKey": "/opt/openssl/key/client/client.key", "tmTceIsIpv6": 0, "tmTceServerPort": 8080, "tmTceTcpKeepCnt": 20, "tmTceTcpKeepIdle": 180, "tmTceTcpKeepIntvl": 60, "tmTceTcpUserTimeOut": 18}], "dnPrefix": "gnb", "id": "0", "locationName": "BLR", "objectClass": "ME", "objectInstance": "0", "objectPairInstance": "0", "userDefinedState": "DRAFT", "userLabel": "ORANCuup", "vendorName": "RSYS"}, "enabled": true}, "status": {"specHash": "13683909188221094880"}}}}}}