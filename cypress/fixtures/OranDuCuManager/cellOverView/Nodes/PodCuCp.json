{"id": "GB-MARL-CUCP-0007", "pod": {"status": "OK", "detail": {"conditions": [{"last_probe_time": null, "last_transition_time": "2024-09-18T14:18:56+00:00", "message": null, "reason": null, "status": "True", "type": "PodReadyToStartContainers"}, {"last_probe_time": null, "last_transition_time": "2024-09-18T14:18:52+00:00", "message": null, "reason": null, "status": "True", "type": "Initialized"}, {"last_probe_time": null, "last_transition_time": "2024-09-18T14:19:22+00:00", "message": null, "reason": null, "status": "True", "type": "Ready"}, {"last_probe_time": null, "last_transition_time": "2024-09-18T14:19:22+00:00", "message": null, "reason": null, "status": "True", "type": "ContainersReady"}, {"last_probe_time": null, "last_transition_time": "2024-09-18T14:18:52+00:00", "message": null, "reason": null, "status": "True", "type": "PodScheduled"}], "container_statuses": [{"allocated_resources": null, "container_id": "containerd://9c4275aec172ae46a02c1e0517c759e7b6fc60e68f55011bffaa97d48ade3752", "image": "europe-west2-docker.pkg.dev/da-rad-dev02-a3e1/da-rad--docker-repo/vran.rsys/cu-cp/cu-cp:24.8.0-dev-196", "image_id": "europe-west2-docker.pkg.dev/da-rad-dev02-a3e1/da-rad--docker-repo/vran.rsys/cu-cp/cu-cp@sha256:7cdb1f713cb5d9c86de1031b49948a6ebe9391356018f72c3fb97a4e4873fa0c", "last_state": {"running": null, "terminated": null, "waiting": null}, "name": "cu-cp-app", "ready": true, "resources": null, "restart_count": 0, "started": true, "state": {"running": {"started_at": "2024-09-18T14:18:55+00:00"}, "terminated": null, "waiting": null}, "volume_mounts": null}], "ephemeral_container_statuses": null, "host_ip": "***************", "host_ips": [{"ip": "***************"}], "init_container_statuses": null, "message": null, "nominated_node_name": null, "phase": "Running", "pod_ip": "***********", "pod_ips": [{"ip": "***********"}], "qos_class": "<PERSON><PERSON><PERSON><PERSON>", "reason": null, "resize": null, "resource_claim_statuses": null, "start_time": "2024-09-18T14:18:52+00:00"}, "crs": {"CuCpDeployment": {"status": {"configVersion": {}, "deploymentStatus": "Ready", "podStatus": {"cucp": "Ready", "oam-event-api": "Ready"}}, "content": {"apiVersion": "cucp.deployment.radisys.com/v1alpha1", "kind": "CuCpDeployment", "metadata": {"annotations": {"rel": "24.8.0-dev"}, "creationTimestamp": "2024-09-18T14:16:55Z", "generation": 2, "labels": {"cucpinstance": "sector1", "site": "18sep6c"}, "name": "cucp-18sep6c", "namespace": "default", "resourceVersion": "98284198", "uid": "92918403-5d87-4861-87bf-43cd58e918a4"}, "spec": {"action": "NONE", "configSelector": {"appConfig": {"site": "18sep6c"}, "cellConfig": {"site": "18sep6c"}}, "crypto": {"hardwareResourceName": "generic", "hardwareResourcePrefix": "qat.intel.com", "type": "SOFTWARE"}, "deploymentProfile": "p1", "enabled": true, "exportMetrics": true, "imagePullSecrets": [{"name": "da-gcr-key"}], "images": {"cu-cp": {"pullPolicy": "IfNotPresent", "repository": "europe-west2-docker.pkg.dev/da-rad-dev02-a3e1/da-rad--docker-repo/vran.rsys/cu-cp/cu-cp", "tag": "24.8.0-dev-196"}, "oam-event": {"pullPolicy": "IfNotPresent", "repository": "europe-west2-docker.pkg.dev/da-rad-dev02-a3e1/da-rad--docker-repo/vran.rsys/cu-cp/cu-cp-oam-event", "tag": "24.8.0-dev-196"}}, "logToConsole": false, "persistency": {"core": {"enabled": false, "storage": "10Gi"}, "log": {"enabled": false, "storage": "10Gi"}, "numRunsToKeep": 10, "path": "/mnt/data/", "type": "hostPath"}, "service": {"e1ap": {"enabled": true, "localExternalIp": "**********", "localPort": 38462, "sslAuth": {"enabled": false, "secretName": ""}, "type": "ClusterIP"}, "f1ap": {"enabled": true, "localExternalIp": "**********", "localPort": 38472, "sslAuth": {"enabled": false, "secretName": ""}, "type": "ClusterIP"}, "metrics": {"enabled": false, "localExternalIp": "***********", "localPort": 32726, "sslAuth": {"enabled": false, "secretName": "sslcert-secret"}, "type": "ClusterIP"}, "x2ap": {"enabled": false, "localExternalIp": "***********", "localPort": 36422, "sslAuth": {"enabled": false, "secretName": ""}, "type": "ClusterIP"}, "xnap": {"enabled": false, "localExternalIp": "***********", "localPort": 38422, "sslAuth": {"enabled": false, "secretName": ""}, "type": "ClusterIP"}}, "vesCollector": {"FQDN": "event-export-adapter", "path": "/eventListener", "port": 8080, "protocol": "http", "sslAuth": {"enabled": false, "secretName": "sslcert-secret"}}}, "status": {"configVersion": {}, "deploymentStatus": "Ready", "podStatus": {"cucp": "Ready", "oam-event-api": "Ready"}}}}, "CuCellConfig": {"status": {"specHash": "4966036424179282609"}, "content": {"apiVersion": "cu.cell.config.radisys.com/v1alpha1", "kind": "CuCellConfig", "metadata": {"annotations": {"rel": "24.8.0-dev"}, "creationTimestamp": "2024-09-18T14:16:54Z", "generation": 2, "labels": {"site": "18sep6c"}, "name": "cucellconfig-18sep6c", "namespace": "default", "resourceVersion": "98283747", "uid": "1128e311-224f-46ca-b18e-2b9cfd03c7a4"}, "spec": {"ME": {"GNBCUCPFunction": [{"NRCellCU": [{"DPCIConfigurationFunction": [{"dPciConfigurationControl": true, "id": "0", "nRPciList": [{"NRPci": 0}, {"NRPci": 1}, {"NRPci": 2}, {"NRPci": 3}, {"NRPci": 4}, {"NRPci": 5}, {"NRPci": 6}, {"NRPci": 7}, {"NRPci": 8}]}], "gnbCellCuVsConfig": {"MCC": "311", "MNC": "48", "SA": {"Embb": [{"id": 1, "measObjectToAddMod": {"measObjectNr": {"absThreshCsiRsConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "absThreshSsBlocksConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "freqBandIndicatorNR": 78, "nrindexActivationFlag": true, "numCsiRsResourcesToAverage": 2, "numSsBlocksToAverage": 2, "qOffsetRangeList": {"rsrpOffsetCsiRs": "DB0", "rsrpOffsetSsb": "DB0", "rsrqOffsetCsiRs": "DB0", "rsrqOffsetSsb": "DB0", "sinrOffsetCsiRs": "DB0", "sinrOffsetSsb": "DB0"}, "quantityConfigIndex": 1, "refFreqCsiRs": 633984, "smtc1": {"duration": 5, "sf20": 0}, "ssbFrequency": 647232, "ssbSubCarrierSpacing": "KHZ30"}}, "reportConfigToAddMod": {"reportConfigNr": {"eventReport": {"eventInculdeBeamMeasurements": false, "eventMaxReportCell": 6, "eventMeasReportQuantity": {"rsrp": true}, "eventMeasReportQuantityRsIndices": {"rsrp": true}, "eventReportAmount": "R8", "eventReportInterval": "MS240", "eventRsType": "SSB", "hysteresis": 0, "maxNrofRsIndexesToReport": 8, "measTriggerQuantityOffsetA3": {"eventA3useWhiteCellList": false, "rsrp": 36}, "reportOnLeave": false, "timeToTrigger": "MS480"}}}}, {"id": 2, "measObjectToAddMod": {"measObjectNr": {"absThreshCsiRsConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "absThreshSsBlocksConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "freqBandIndicatorNR": 78, "nrindexActivationFlag": true, "numCsiRsResourcesToAverage": 2, "numSsBlocksToAverage": 2, "qOffsetRangeList": {"rsrpOffsetCsiRs": "DB0", "rsrpOffsetSsb": "DB0", "rsrqOffsetCsiRs": "DB0", "rsrqOffsetSsb": "DB0", "sinrOffsetCsiRs": "DB0", "sinrOffsetSsb": "DB0"}, "quantityConfigIndex": 1, "refFreqCsiRs": 633984, "smtc1": {"duration": 5, "sf20": 0}, "ssbFrequency": 647232, "ssbSubCarrierSpacing": "KHZ30"}}, "reportConfigToAddMod": {"reportConfigNr": {"eventReport": {"eventInculdeBeamMeasurements": false, "eventMaxReportCell": 1, "eventMeasReportQuantity": {"rsrp": true}, "eventMeasReportQuantityRsIndices": {"rsrp": true}, "eventReportAmount": "R1", "eventReportInterval": "MS240", "eventRsType": "SSB", "hysteresis": 0, "maxNrofRsIndexesToReport": 8, "measTriggerQuantityA2": {"rsrp": 20}, "reportOnLeave": false, "timeToTrigger": "MS40"}}}}], "EmbbFast": [{"id": 1, "measObjectToAddMod": {"measObjectNr": {"absThreshCsiRsConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "absThreshSsBlocksConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "freqBandIndicatorNR": 78, "nrindexActivationFlag": true, "numCsiRsResourcesToAverage": 2, "numSsBlocksToAverage": 2, "qOffsetRangeList": {"rsrpOffsetCsiRs": "DB0", "rsrpOffsetSsb": "DB0", "rsrqOffsetCsiRs": "DB0", "rsrqOffsetSsb": "DB0", "sinrOffsetCsiRs": "DB0", "sinrOffsetSsb": "DB0"}, "quantityConfigIndex": 1, "refFreqCsiRs": 634080, "smtc1": {"duration": 5, "sf20": 0}, "ssbFrequency": 634080, "ssbSubCarrierSpacing": "KHZ30"}}, "reportConfigToAddMod": {"reportConfigNr": {"eventReport": {"eventInculdeBeamMeasurements": false, "eventMaxReportCell": 6, "eventMeasReportQuantity": {"rsrp": true}, "eventMeasReportQuantityRsIndices": {"rsrp": true}, "eventReportAmount": "R8", "eventReportInterval": "MS240", "eventRsType": "SSB", "hysteresis": 0, "maxNrofRsIndexesToReport": 8, "measTriggerQuantityOffsetA3": {"eventA3useWhiteCellList": false, "rsrp": 36}, "reportOnLeave": false, "timeToTrigger": "MS480"}}}}, {"id": 2, "measObjectToAddMod": {"measObjectNr": {"absThreshCsiRsConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "absThreshSsBlocksConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "freqBandIndicatorNR": 78, "nrindexActivationFlag": true, "numCsiRsResourcesToAverage": 2, "numSsBlocksToAverage": 2, "qOffsetRangeList": {"rsrpOffsetCsiRs": "DB0", "rsrpOffsetSsb": "DB0", "rsrqOffsetCsiRs": "DB0", "rsrqOffsetSsb": "DB0", "sinrOffsetCsiRs": "DB0", "sinrOffsetSsb": "DB0"}, "quantityConfigIndex": 1, "refFreqCsiRs": 634080, "smtc1": {"duration": 5, "sf20": 0}, "ssbFrequency": 634080, "ssbSubCarrierSpacing": "KHZ30"}}, "reportConfigToAddMod": {"reportConfigNr": {"eventReport": {"eventInculdeBeamMeasurements": false, "eventMaxReportCell": 1, "eventMeasReportQuantity": {"rsrp": true}, "eventMeasReportQuantityRsIndices": {"rsrp": true}, "eventReportAmount": "R1", "eventReportInterval": "MS240", "eventRsType": "SSB", "hysteresis": 0, "maxNrofRsIndexesToReport": 8, "measTriggerQuantityA2": {"rsrp": 46}, "reportOnLeave": false, "timeToTrigger": "MS40"}}}}], "EmbbInterFreq": [{"id": 1, "measObjectToAddMod": {"measObjectNr": {"absThreshCsiRsConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "absThreshSsBlocksConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "freqBandIndicatorNR": 78, "nrindexActivationFlag": true, "numCsiRsResourcesToAverage": 2, "numSsBlocksToAverage": 2, "qOffsetRangeList": {"rsrpOffsetCsiRs": "DB0", "rsrpOffsetSsb": "DB0", "rsrqOffsetCsiRs": "DB0", "rsrqOffsetSsb": "DB0", "sinrOffsetCsiRs": "DB0", "sinrOffsetSsb": "DB0"}, "quantityConfigIndex": 1, "refFreqCsiRs": 647232, "smtc1": {"duration": 5, "sf20": 0}, "ssbFrequency": 647232, "ssbSubCarrierSpacing": "KHZ30"}}, "reportConfigToAddMod": {"reportConfigNr": {"eventReport": {"eventInculdeBeamMeasurements": false, "eventMaxReportCell": 6, "eventMeasReportQuantity": {"rsrp": true}, "eventMeasReportQuantityRsIndices": {"rsrp": true}, "eventReportAmount": "R8", "eventReportInterval": "MS240", "eventRsType": "SSB", "hysteresis": 0, "maxNrofRsIndexesToReport": 8, "measTriggerQuantityA5": {"eventA5useWhiteCellList": false, "rsrp": 34, "rsrpQ2": 46}, "reportOnLeave": false, "timeToTrigger": "MS480"}}}}, {"id": 2, "measObjectToAddMod": {"measObjectNr": {"absThreshCsiRsConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "absThreshSsBlocksConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "freqBandIndicatorNR": 78, "nrindexActivationFlag": true, "numCsiRsResourcesToAverage": 2, "numSsBlocksToAverage": 2, "qOffsetRangeList": {"rsrpOffsetCsiRs": "DB0", "rsrpOffsetSsb": "DB0", "rsrqOffsetCsiRs": "DB0", "rsrqOffsetSsb": "DB0", "sinrOffsetCsiRs": "DB0", "sinrOffsetSsb": "DB0"}, "quantityConfigIndex": 1, "refFreqCsiRs": 647232, "smtc1": {"duration": 5, "sf20": 0}, "ssbFrequency": 647232, "ssbSubCarrierSpacing": "KHZ30"}}, "reportConfigToAddMod": {"reportConfigNr": {"eventReport": {"eventInculdeBeamMeasurements": false, "eventMaxReportCell": 1, "eventMeasReportQuantity": {"rsrp": true}, "eventMeasReportQuantityRsIndices": {"rsrp": true}, "eventReportAmount": "R1", "eventReportInterval": "MS240", "eventRsType": "SSB", "hysteresis": 0, "maxNrofRsIndexesToReport": 8, "measTriggerQuantityA1": {"rsrp": 58}, "reportOnLeave": false, "timeToTrigger": "MS40"}}}}, {"id": 3, "measObjectToAddMod": {"measObjectNr": {"absThreshCsiRsConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "absThreshSsBlocksConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "freqBandIndicatorNR": 78, "nrindexActivationFlag": true, "numCsiRsResourcesToAverage": 2, "numSsBlocksToAverage": 2, "qOffsetRangeList": {"rsrpOffsetCsiRs": "DB0", "rsrpOffsetSsb": "DB0", "rsrqOffsetCsiRs": "DB0", "rsrqOffsetSsb": "DB0", "sinrOffsetCsiRs": "DB0", "sinrOffsetSsb": "DB0"}, "quantityConfigIndex": 1, "refFreqCsiRs": 647232, "smtc1": {"duration": 5, "sf20": 0}, "ssbFrequency": 647232, "ssbSubCarrierSpacing": "KHZ30"}}, "reportConfigToAddMod": {"reportConfigNr": {"eventReport": {"eventInculdeBeamMeasurements": false, "eventMaxReportCell": 1, "eventMeasReportQuantity": {"rsrp": true}, "eventMeasReportQuantityRsIndices": {"rsrp": true}, "eventReportAmount": "R1", "eventReportInterval": "MS240", "eventRsType": "SSB", "hysteresis": 0, "maxNrofRsIndexesToReport": 8, "measTriggerQuantityA2": {"rsrp": 20}, "reportOnLeave": false, "timeToTrigger": "MS40"}}}}, {"id": 4, "measObjectToAddMod": {"measObjectNr": {"absThreshCsiRsConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "absThreshSsBlocksConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "freqBandIndicatorNR": 28, "nrindexActivationFlag": true, "numCsiRsResourcesToAverage": 2, "numSsBlocksToAverage": 2, "qOffsetRangeList": {"rsrpOffsetCsiRs": "DB0", "rsrpOffsetSsb": "DB0", "rsrqOffsetCsiRs": "DB0", "rsrqOffsetSsb": "DB0", "sinrOffsetCsiRs": "DB0", "sinrOffsetSsb": "DB0"}, "quantityConfigIndex": 1, "refFreqCsiRs": 647232, "ssbFrequency": 156750, "ssbSubCarrierSpacing": "KHZ30"}}, "reportConfigToAddMod": {"reportConfigNr": {"eventReport": {"eventInculdeBeamMeasurements": false, "eventMaxReportCell": 6, "eventMeasReportQuantity": {"rsrp": true}, "eventMeasReportQuantityRsIndices": {"rsrp": true}, "eventReportAmount": "R8", "eventReportInterval": "MS240", "eventRsType": "SSB", "hysteresis": 0, "maxNrofRsIndexesToReport": 8, "measTriggerQuantityA5": {"eventA5useWhiteCellList": false, "rsrp": 34, "rsrpQ2": 46}, "reportOnLeave": false, "timeToTrigger": "MS480"}}}}], "EmbbInterFreqFast": [{"id": 1, "measObjectToAddMod": {"measObjectNr": {"absThreshCsiRsConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "absThreshSsBlocksConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "freqBandIndicatorNR": 78, "nrindexActivationFlag": true, "numCsiRsResourcesToAverage": 2, "numSsBlocksToAverage": 2, "qOffsetRangeList": {"rsrpOffsetCsiRs": "DB0", "rsrpOffsetSsb": "DB0", "rsrqOffsetCsiRs": "DB0", "rsrqOffsetSsb": "DB0", "sinrOffsetCsiRs": "DB0", "sinrOffsetSsb": "DB0"}, "quantityConfigIndex": 1, "refFreqCsiRs": 155020, "smtc1": {"duration": 5, "sf20": 0}, "ssbFrequency": 155020, "ssbSubCarrierSpacing": "KHZ30"}}, "reportConfigToAddMod": {"reportConfigNr": {"eventReport": {"eventInculdeBeamMeasurements": false, "eventMaxReportCell": 6, "eventMeasReportQuantity": {"rsrp": true}, "eventMeasReportQuantityRsIndices": {"rsrp": true}, "eventReportAmount": "R8", "eventReportInterval": "MS240", "eventRsType": "SSB", "hysteresis": 0, "maxNrofRsIndexesToReport": 8, "measTriggerQuantityA5": {"eventA5useWhiteCellList": false, "rsrp": 34, "rsrpQ2": 46}, "reportOnLeave": false, "timeToTrigger": "MS480"}}}}, {"id": 2, "measObjectToAddMod": {"measObjectNr": {"absThreshCsiRsConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "absThreshSsBlocksConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "freqBandIndicatorNR": 78, "nrindexActivationFlag": true, "numCsiRsResourcesToAverage": 2, "numSsBlocksToAverage": 2, "qOffsetRangeList": {"rsrpOffsetCsiRs": "DB0", "rsrpOffsetSsb": "DB0", "rsrqOffsetCsiRs": "DB0", "rsrqOffsetSsb": "DB0", "sinrOffsetCsiRs": "DB0", "sinrOffsetSsb": "DB0"}, "quantityConfigIndex": 1, "refFreqCsiRs": 634080, "smtc1": {"duration": 5, "sf20": 0}, "ssbFrequency": 634080, "ssbSubCarrierSpacing": "KHZ30"}}, "reportConfigToAddMod": {"reportConfigNr": {"eventReport": {"eventInculdeBeamMeasurements": false, "eventMaxReportCell": 1, "eventMeasReportQuantity": {"rsrp": true}, "eventMeasReportQuantityRsIndices": {"rsrp": true}, "eventReportAmount": "R1", "eventReportInterval": "MS240", "eventRsType": "SSB", "hysteresis": 0, "maxNrofRsIndexesToReport": 8, "measTriggerQuantityA1": {"rsrp": 58}, "reportOnLeave": false, "timeToTrigger": "MS40"}}}}, {"id": 3, "measObjectToAddMod": {"measObjectNr": {"absThreshCsiRsConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "absThreshSsBlocksConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "freqBandIndicatorNR": 78, "nrindexActivationFlag": true, "numCsiRsResourcesToAverage": 2, "numSsBlocksToAverage": 2, "qOffsetRangeList": {"rsrpOffsetCsiRs": "DB0", "rsrpOffsetSsb": "DB0", "rsrqOffsetCsiRs": "DB0", "rsrqOffsetSsb": "DB0", "sinrOffsetCsiRs": "DB0", "sinrOffsetSsb": "DB0"}, "quantityConfigIndex": 1, "refFreqCsiRs": 634080, "smtc1": {"duration": 5, "sf20": 0}, "ssbFrequency": 634080, "ssbSubCarrierSpacing": "KHZ30"}}, "reportConfigToAddMod": {"reportConfigNr": {"eventReport": {"eventInculdeBeamMeasurements": false, "eventMaxReportCell": 1, "eventMeasReportQuantity": {"rsrp": true}, "eventMeasReportQuantityRsIndices": {"rsrp": true}, "eventReportAmount": "R1", "eventReportInterval": "MS240", "eventRsType": "SSB", "hysteresis": 0, "maxNrofRsIndexesToReport": 8, "measTriggerQuantityA2": {"rsrp": 44}, "reportOnLeave": false, "timeToTrigger": "MS40"}}}}, {"id": 4, "measObjectToAddMod": {"measObjectNr": {"absThreshCsiRsConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "absThreshSsBlocksConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "freqBandIndicatorNR": 28, "nrindexActivationFlag": true, "numCsiRsResourcesToAverage": 2, "numSsBlocksToAverage": 2, "qOffsetRangeList": {"rsrpOffsetCsiRs": "DB0", "rsrpOffsetSsb": "DB0", "rsrqOffsetCsiRs": "DB0", "rsrqOffsetSsb": "DB0", "sinrOffsetCsiRs": "DB0", "sinrOffsetSsb": "DB0"}, "quantityConfigIndex": 1, "refFreqCsiRs": 156750, "ssbFrequency": 156750, "ssbSubCarrierSpacing": "KHZ120"}}, "reportConfigToAddMod": {"reportConfigNr": {"eventReport": {"eventInculdeBeamMeasurements": false, "eventMaxReportCell": 6, "eventMeasReportQuantity": {"rsrp": true}, "eventMeasReportQuantityRsIndices": {"rsrp": true}, "eventReportAmount": "R8", "eventReportInterval": "MS240", "eventRsType": "SSB", "hysteresis": 0, "maxNrofRsIndexesToReport": 8, "measTriggerQuantityA5": {"eventA5useWhiteCellList": false, "rsrp": 34, "rsrpQ2": 46}, "reportOnLeave": false, "timeToTrigger": "MS480"}}}}], "EmbbInterFreqOption2": [{"id": 1, "measObjectToAddMod": {"measObjectNr": {"absThreshCsiRsConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "absThreshSsBlocksConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "freqBandIndicatorNR": 78, "nrindexActivationFlag": true, "numCsiRsResourcesToAverage": 2, "numSsBlocksToAverage": 2, "qOffsetRangeList": {"rsrpOffsetCsiRs": "DB0", "rsrpOffsetSsb": "DB0", "rsrqOffsetCsiRs": "DB0", "rsrqOffsetSsb": "DB0", "sinrOffsetCsiRs": "DB0", "sinrOffsetSsb": "DB0"}, "quantityConfigIndex": 1, "refFreqCsiRs": 634080, "smtc1": {"duration": 5, "sf20": 0}, "ssbFrequency": 634080, "ssbSubCarrierSpacing": "KHZ30"}}, "reportConfigToAddMod": {"reportConfigNr": {"eventReport": {"eventInculdeBeamMeasurements": false, "eventMaxReportCell": 6, "eventMeasReportQuantity": {"rsrp": true}, "eventMeasReportQuantityRsIndices": {"rsrp": true}, "eventReportAmount": "R8", "eventReportInterval": "MS240", "eventRsType": "SSB", "hysteresis": 0, "maxNrofRsIndexesToReport": 8, "measTriggerQuantityA4": {"eventA4UseWhiteCellList": false, "rsrp": 36}, "reportOnLeave": false, "timeToTrigger": "MS480"}}}}, {"id": 2, "measObjectToAddMod": {"measObjectNr": {"absThreshCsiRsConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "absThreshSsBlocksConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "freqBandIndicatorNR": 78, "nrindexActivationFlag": true, "numCsiRsResourcesToAverage": 2, "numSsBlocksToAverage": 2, "qOffsetRangeList": {"rsrpOffsetCsiRs": "DB0", "rsrpOffsetSsb": "DB0", "rsrqOffsetCsiRs": "DB0", "rsrqOffsetSsb": "DB0", "sinrOffsetCsiRs": "DB0", "sinrOffsetSsb": "DB0"}, "quantityConfigIndex": 1, "refFreqCsiRs": 632064, "smtc1": {"duration": 5, "sf20": 0}, "ssbFrequency": 632064, "ssbSubCarrierSpacing": "KHZ30"}}, "reportConfigToAddMod": {"reportConfigNr": {"eventReport": {"eventInculdeBeamMeasurements": false, "eventMaxReportCell": 1, "eventMeasReportQuantity": {"rsrp": true}, "eventMeasReportQuantityRsIndices": {"rsrp": true}, "eventReportAmount": "R1", "eventReportInterval": "MS240", "eventRsType": "SSB", "hysteresis": 0, "maxNrofRsIndexesToReport": 8, "measTriggerQuantityA4": {"eventA4UseWhiteCellList": false, "rsrp": 36}, "reportOnLeave": false, "timeToTrigger": "MS40"}}}}], "EmbbIrat": [{"id": 1, "measObjectToAddMod": {"measObjectNr": {"absThreshCsiRsConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "absThreshSsBlocksConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "freqBandIndicatorNR": 78, "nrindexActivationFlag": true, "numCsiRsResourcesToAverage": 2, "numSsBlocksToAverage": 2, "qOffsetRangeList": {"rsrpOffsetCsiRs": "DB0", "rsrpOffsetSsb": "DB0", "rsrqOffsetCsiRs": "DB0", "rsrqOffsetSsb": "DB0", "sinrOffsetCsiRs": "DB0", "sinrOffsetSsb": "DB0"}, "quantityConfigIndex": 1, "refFreqCsiRs": 633984, "smtc1": {"duration": 5, "sf20": 0}, "ssbFrequency": 647232, "ssbSubCarrierSpacing": "KHZ30"}}, "reportConfigToAddMod": {"reportConfigNr": {"eventReport": {"eventInculdeBeamMeasurements": false, "eventMaxReportCell": 6, "eventMeasReportQuantity": {"rsrp": true}, "eventMeasReportQuantityRsIndices": {"rsrp": true}, "eventReportAmount": "R8", "eventReportInterval": "MS240", "eventRsType": "SSB", "hysteresis": 0, "maxNrofRsIndexesToReport": 8, "measTriggerQuantityA1": {"rsrp": 56}, "reportOnLeave": false, "timeToTrigger": "MS480"}}}}, {"id": 2, "measObjectToAddMod": {"measObjectEutra": {"allowedMeasBandwidth": "mbw100", "carrierFreq": 38948, "cellToAddModEutra": [{"cellIndexEutra": 1, "cellIndividualOffset": "DB_24", "pci": 0}], "eutra-presenceAntennaPort1": true, "eutra-qOffsetRange": "DB0", "eutraindexActivationFlag": true, "widebandRSRQMeas": true}}, "reportConfigToAddMod": {"reportConfigIRAT": {"eventReportIRAT": {"eventMaxReportCell": 8, "eventMeasReportQuantityIrat": {"rsrp": true}, "eventReportAmount": "R8", "eventReportInterval": "MS240", "hysteresis": 0, "measTriggerQuantityB2": {"rsrp": 67, "rsrpEutra": 40}, "reportOnLeave": false, "rsType": "SSB", "timeToTrigger": "MS480"}}}}, {"id": 3, "measObjectToAddMod": {"measObjectEutra": {"allowedMeasBandwidth": "mbw100", "carrierFreq": 1251, "cellToAddModEutra": [{"cellIndexEutra": 1, "cellIndividualOffset": "DB_24", "pci": 0}], "eutra-presenceAntennaPort1": true, "eutra-qOffsetRange": "DB0", "eutraindexActivationFlag": true, "widebandRSRQMeas": true}}, "reportConfigToAddMod": {"reportConfigIRAT": {"eventReportIRAT": {"eventMaxReportCell": 8, "eventMeasReportQuantityIrat": {"rsrp": true}, "eventReportAmount": "R8", "eventReportInterval": "MS240", "hysteresis": 0, "measTriggerQuantityB2": {"rsrp": 67, "rsrpEutra": 40}, "reportOnLeave": false, "rsType": "SSB", "timeToTrigger": "MS480"}}}}, {"id": 4, "measObjectToAddMod": {"measObjectEutra": {"allowedMeasBandwidth": "mbw100", "carrierFreq": 2539, "cellToAddModEutra": [{"cellIndexEutra": 1, "cellIndividualOffset": "DB_24", "pci": 0}], "eutra-presenceAntennaPort1": true, "eutra-qOffsetRange": "DB0", "eutraindexActivationFlag": true, "widebandRSRQMeas": true}}, "reportConfigToAddMod": {"reportConfigIRAT": {"eventReportIRAT": {"eventMaxReportCell": 8, "eventMeasReportQuantityIrat": {"rsrp": true}, "eventReportAmount": "R8", "eventReportInterval": "MS240", "hysteresis": 0, "measTriggerQuantityB2": {"rsrp": 67, "rsrpEutra": 40}, "reportOnLeave": false, "rsType": "SSB", "timeToTrigger": "MS480"}}}}, {"id": 5, "measObjectToAddMod": {"measObjectEutra": {"allowedMeasBandwidth": "mbw100", "carrierFreq": 2464, "cellToAddModEutra": [{"cellIndexEutra": 1, "cellIndividualOffset": "DB_24", "pci": 0}], "eutra-presenceAntennaPort1": true, "eutra-qOffsetRange": "DB0", "eutraindexActivationFlag": true, "widebandRSRQMeas": true}}, "reportConfigToAddMod": {"reportConfigIRAT": {"eventReportIRAT": {"eventMaxReportCell": 8, "eventMeasReportQuantityIrat": {"rsrp": true}, "eventReportAmount": "R8", "eventReportInterval": "MS240", "hysteresis": 0, "measTriggerQuantityB2": {"rsrp": 67, "rsrpEutra": 40}, "reportOnLeave": false, "rsType": "SSB", "timeToTrigger": "MS480"}}}}, {"id": 6, "measObjectToAddMod": {"measObjectEutra": {"allowedMeasBandwidth": "mbw100", "carrierFreq": 38700, "cellToAddModEutra": [{"cellIndexEutra": 1, "cellIndividualOffset": "DB_24", "pci": 0}], "eutra-presenceAntennaPort1": true, "eutra-qOffsetRange": "DB0", "eutraindexActivationFlag": true, "widebandRSRQMeas": true}}, "reportConfigToAddMod": {"reportConfigIRAT": {"eventReportIRAT": {"eventMaxReportCell": 8, "eventMeasReportQuantityIrat": {"rsrp": true}, "eventReportAmount": "R8", "eventReportInterval": "MS240", "hysteresis": 0, "measTriggerQuantityB2": {"rsrp": 67, "rsrpEutra": 40}, "reportOnLeave": false, "rsType": "SSB", "timeToTrigger": "MS480"}}}}, {"id": 7, "measObjectToAddMod": {"measObjectEutra": {"allowedMeasBandwidth": "mbw100", "carrierFreq": 38700, "eutra-presenceAntennaPort1": true, "eutra-qOffsetRange": "DB0", "eutraindexActivationFlag": true, "widebandRSRQMeas": true}}, "reportConfigToAddMod": {"reportConfigIRAT": {"eventReportIRAT": {"eventMaxReportCell": 8, "eventMeasReportQuantityIrat": {"rsrp": true}, "eventReportAmount": "R8", "eventReportInterval": "MS240", "hysteresis": 0, "measTriggerQuantityB2": {"rsrp": 67, "rsrpEutra": 40}, "reportOnLeave": false, "rsType": "SSB", "timeToTrigger": "MS480"}}}}], "EmbbIratFast": [{"id": 1, "measObjectToAddMod": {"measObjectNr": {"absThreshCsiRsConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "absThreshSsBlocksConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "freqBandIndicatorNR": 78, "nrindexActivationFlag": true, "numCsiRsResourcesToAverage": 2, "numSsBlocksToAverage": 2, "qOffsetRangeList": {"rsrpOffsetCsiRs": "DB0", "rsrpOffsetSsb": "DB0", "rsrqOffsetCsiRs": "DB0", "rsrqOffsetSsb": "DB0", "sinrOffsetCsiRs": "DB0", "sinrOffsetSsb": "DB0"}, "quantityConfigIndex": 1, "refFreqCsiRs": 634080, "smtc1": {"duration": 5, "sf20": 0}, "ssbFrequency": 634080, "ssbSubCarrierSpacing": "KHZ30"}}, "reportConfigToAddMod": {"reportConfigNr": {"eventReport": {"eventInculdeBeamMeasurements": false, "eventMaxReportCell": 6, "eventMeasReportQuantity": {"rsrp": true}, "eventMeasReportQuantityRsIndices": {"rsrp": true}, "eventReportAmount": "R8", "eventReportInterval": "MS240", "eventRsType": "SSB", "hysteresis": 0, "maxNrofRsIndexesToReport": 8, "measTriggerQuantityA1": {"rsrp": 56}, "reportOnLeave": false, "timeToTrigger": "MS480"}}}}, {"id": 2, "measObjectToAddMod": {"measObjectEutra": {"allowedMeasBandwidth": "mbw100", "carrierFreq": 38948, "cellToAddModEutra": [{"cellIndexEutra": 1, "cellIndividualOffset": "DB_24", "pci": 0}], "eutra-presenceAntennaPort1": true, "eutra-qOffsetRange": "DB0", "eutraindexActivationFlag": true, "widebandRSRQMeas": true}}, "reportConfigToAddMod": {"reportConfigIRAT": {"eventReportIRAT": {"eventMaxReportCell": 8, "eventMeasReportQuantityIrat": {"rsrp": true}, "eventReportAmount": "R8", "eventReportInterval": "MS240", "hysteresis": 0, "measTriggerQuantityB2": {"rsrp": 46, "rsrpEutra": 32}, "reportOnLeave": false, "rsType": "SSB", "timeToTrigger": "MS480"}}}}, {"id": 3, "measObjectToAddMod": {"measObjectEutra": {"allowedMeasBandwidth": "mbw100", "carrierFreq": 1251, "cellToAddModEutra": [{"cellIndexEutra": 1, "cellIndividualOffset": "DB_24", "pci": 0}], "eutra-presenceAntennaPort1": true, "eutra-qOffsetRange": "DB0", "eutraindexActivationFlag": true, "widebandRSRQMeas": true}}, "reportConfigToAddMod": {"reportConfigIRAT": {"eventReportIRAT": {"eventMaxReportCell": 8, "eventMeasReportQuantityIrat": {"rsrp": true}, "eventReportAmount": "R8", "eventReportInterval": "MS240", "hysteresis": 0, "measTriggerQuantityB2": {"rsrp": 46, "rsrpEutra": 24}, "reportOnLeave": false, "rsType": "SSB", "timeToTrigger": "MS480"}}}}, {"id": 4, "measObjectToAddMod": {"measObjectEutra": {"allowedMeasBandwidth": "mbw100", "carrierFreq": 2453, "cellToAddModEutra": [{"cellIndexEutra": 1, "cellIndividualOffset": "DB_24", "pci": 0}], "eutra-presenceAntennaPort1": true, "eutra-qOffsetRange": "DB0", "eutraindexActivationFlag": true, "widebandRSRQMeas": true}}, "reportConfigToAddMod": {"reportConfigIRAT": {"eventReportIRAT": {"eventMaxReportCell": 8, "eventMeasReportQuantityIrat": {"rsrp": true}, "eventReportAmount": "R8", "eventReportInterval": "MS240", "hysteresis": 0, "measTriggerQuantityB2": {"rsrp": 46, "rsrpEutra": 24}, "reportOnLeave": false, "rsType": "SSB", "timeToTrigger": "MS480"}}}}, {"id": 5, "measObjectToAddMod": {"measObjectEutra": {"allowedMeasBandwidth": "mbw100", "carrierFreq": 2528, "cellToAddModEutra": [{"cellIndexEutra": 1, "cellIndividualOffset": "DB_24", "pci": 0}], "eutra-presenceAntennaPort1": true, "eutra-qOffsetRange": "DB0", "eutraindexActivationFlag": true, "widebandRSRQMeas": true}}, "reportConfigToAddMod": {"reportConfigIRAT": {"eventReportIRAT": {"eventMaxReportCell": 8, "eventMeasReportQuantityIrat": {"rsrp": true}, "eventReportAmount": "R8", "eventReportInterval": "MS240", "hysteresis": 0, "measTriggerQuantityB2": {"rsrp": 46, "rsrpEutra": 97}, "reportOnLeave": false, "rsType": "SSB", "timeToTrigger": "MS480"}}}}, {"id": 6, "measObjectToAddMod": {"measObjectEutra": {"allowedMeasBandwidth": "mbw100", "carrierFreq": 38750, "cellToAddModEutra": [{"cellIndexEutra": 1, "cellIndividualOffset": "DB_24", "pci": 0}], "eutra-presenceAntennaPort1": true, "eutra-qOffsetRange": "DB0", "eutraindexActivationFlag": true, "widebandRSRQMeas": true}}, "reportConfigToAddMod": {"reportConfigIRAT": {"eventReportIRAT": {"eventMaxReportCell": 8, "eventMeasReportQuantityIrat": {"rsrp": true}, "eventReportAmount": "R8", "eventReportInterval": "MS240", "hysteresis": 0, "measTriggerQuantityB2": {"rsrp": 46, "rsrpEutra": 32}, "reportOnLeave": false, "rsType": "SSB", "timeToTrigger": "MS480"}}}}], "EpsFb": [{"id": 1, "measObjectToAddMod": {"measObjectEutra": {"allowedMeasBandwidth": "mbw100", "carrierFreq": 38948, "cellToAddModEutra": [{"cellIndexEutra": 1, "cellIndividualOffset": "DB_24", "pci": 0}], "eutra-presenceAntennaPort1": true, "eutra-qOffsetRange": "DB0", "eutraindexActivationFlag": true, "widebandRSRQMeas": true}}, "reportConfigToAddMod": {"reportConfigIRAT": {"eventReportIRAT": {"eventMaxReportCell": 3, "eventMeasReportQuantityIrat": {"rsrp": true}, "eventReportAmount": "R8", "eventReportInterval": "MS240", "hysteresis": 0, "measTriggerQuantityB1": {"rsrpEutra": 50}, "reportOnLeave": false, "rsType": "SSB", "timeToTrigger": "MS480"}}}}, {"id": 2, "measObjectToAddMod": {"measObjectEutra": {"allowedMeasBandwidth": "mbw100", "carrierFreq": 1251, "cellToAddModEutra": [{"cellIndexEutra": 1, "cellIndividualOffset": "DB_24", "pci": 0}], "eutra-presenceAntennaPort1": true, "eutra-qOffsetRange": "DB0", "eutraindexActivationFlag": true, "widebandRSRQMeas": true}}, "reportConfigToAddMod": {"reportConfigIRAT": {"eventReportIRAT": {"eventMaxReportCell": 3, "eventMeasReportQuantityIrat": {"rsrp": true}, "eventReportAmount": "R8", "eventReportInterval": "MS240", "hysteresis": 0, "measTriggerQuantityB1": {"rsrpEutra": 50}, "reportOnLeave": false, "rsType": "SSB", "timeToTrigger": "MS480"}}}}, {"id": 3, "measObjectToAddMod": {"measObjectEutra": {"allowedMeasBandwidth": "mbw100", "carrierFreq": 2539, "cellToAddModEutra": [{"cellIndexEutra": 1, "cellIndividualOffset": "DB_24", "pci": 0}], "eutra-presenceAntennaPort1": true, "eutra-qOffsetRange": "DB0", "eutraindexActivationFlag": true, "widebandRSRQMeas": true}}, "reportConfigToAddMod": {"reportConfigIRAT": {"eventReportIRAT": {"eventMaxReportCell": 3, "eventMeasReportQuantityIrat": {"rsrp": true}, "eventReportAmount": "R8", "eventReportInterval": "MS240", "hysteresis": 0, "measTriggerQuantityB1": {"rsrpEutra": 50}, "reportOnLeave": false, "rsType": "SSB", "timeToTrigger": "MS480"}}}}, {"id": 4, "measObjectToAddMod": {"measObjectEutra": {"allowedMeasBandwidth": "mbw100", "carrierFreq": 2464, "cellToAddModEutra": [{"cellIndexEutra": 1, "cellIndividualOffset": "DB_24", "pci": 0}], "eutra-presenceAntennaPort1": true, "eutra-qOffsetRange": "DB0", "eutraindexActivationFlag": true, "widebandRSRQMeas": true}}, "reportConfigToAddMod": {"reportConfigIRAT": {"eventReportIRAT": {"eventMaxReportCell": 3, "eventMeasReportQuantityIrat": {"rsrp": true}, "eventReportAmount": "R8", "eventReportInterval": "MS240", "hysteresis": 0, "measTriggerQuantityB1": {"rsrpEutra": 50}, "reportOnLeave": false, "rsType": "SSB", "timeToTrigger": "MS480"}}}}, {"id": 5, "measObjectToAddMod": {"measObjectEutra": {"allowedMeasBandwidth": "mbw100", "carrierFreq": 38700, "cellToAddModEutra": [{"cellIndexEutra": 1, "cellIndividualOffset": "DB_24", "pci": 0}], "eutra-presenceAntennaPort1": true, "eutra-qOffsetRange": "DB0", "eutraindexActivationFlag": true, "widebandRSRQMeas": true}}, "reportConfigToAddMod": {"reportConfigIRAT": {"eventReportIRAT": {"eventMaxReportCell": 3, "eventMeasReportQuantityIrat": {"rsrp": true}, "eventReportAmount": "R8", "eventReportInterval": "MS240", "hysteresis": 0, "measTriggerQuantityB1": {"rsrpEutra": 50}, "reportOnLeave": false, "rsType": "SSB", "timeToTrigger": "MS480"}}}}], "MeasNrPpa": [{"id": 1, "measObjectToAddMod": {"measObjectNr": {"absThreshCsiRsConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "absThreshSsBlocksConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "freqBandIndicatorNR": 78, "nrindexActivationFlag": true, "numCsiRsResourcesToAverage": 2, "numSsBlocksToAverage": 2, "qOffsetRangeList": {"rsrpOffsetCsiRs": "DB0", "rsrpOffsetSsb": "DB0", "rsrqOffsetCsiRs": "DB0", "rsrqOffsetSsb": "DB0", "sinrOffsetCsiRs": "DB0", "sinrOffsetSsb": "DB0"}, "quantityConfigIndex": 1, "refFreqCsiRs": 634080, "smtc1": {"duration": 5, "sf20": 0}, "ssbFrequency": 634080, "ssbSubCarrierSpacing": "KHZ30"}}, "reportConfigToAddMod": {"reportConfigIRAT": {"eventReportIRAT": {"eventMaxReportCell": 3, "eventMeasReportQuantityIrat": {"rsrp": true}, "eventReportAmount": "R8", "eventReportInterval": "MS240", "hysteresis": 0, "measTriggerQuantityB1": {"rsrpEutra": 50, "rsrqEutra": 20, "sinrEutra": 20}, "measTriggerQuantityB2": {"rsrp": 67, "rsrpEutra": 40, "rsrqEutra": 20, "sinrEutra": 20}, "reportOnLeave": false, "rsType": "SSB", "timeToTrigger": "MS480"}}, "reportConfigNr": {"eventReport": {"eventInculdeBeamMeasurements": false, "eventMaxReportCell": 3, "eventMeasReportQuantity": {"rsrp": true}, "eventMeasReportQuantityRsIndices": {"rsrp": true}, "eventReportAmount": "R1", "eventReportInterval": "MS240", "eventRsType": "SSB", "hysteresis": 0, "maxNrofRsIndexesToReport": 8, "measTriggerQuantityA1": {"rsrp": 20}, "measTriggerQuantityA2": {"rsrp": 20}, "measTriggerQuantityA4": {"eventA4UseWhiteCellList": false, "rsrp": 20}, "measTriggerQuantityA5": {"eventA5useWhiteCellList": false, "rsrp": 34, "rsrpQ2": 46}, "measTriggerQuantityOffsetA3": {"eventA3useWhiteCellList": false, "rsrp": 20}, "reportOnLeave": false, "timeToTrigger": "MS480"}, "periodicReport": {"maxNrofRsIndexesToReport": 8, "periodicInculdeBeamMeasurements": true, "periodicMaxReportCell": 1, "periodicMeasReportQuantity": {"rsrp": true}, "periodicReportAmount": "R1", "periodicReportInterval": "MS1024", "periodicRsType": "SSB", "useWhiteCellList": true}}}}], "Nrdc": [{"id": 1, "measObjectToAddMod": {"measObjectNr": {"absThreshCsiRsConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "absThreshSsBlocksConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "freqBandIndicatorNR": 78, "nrindexActivationFlag": true, "numCsiRsResourcesToAverage": 2, "numSsBlocksToAverage": 2, "qOffsetRangeList": {"rsrpOffsetCsiRs": "DB0", "rsrpOffsetSsb": "DB0", "rsrqOffsetCsiRs": "DB0", "rsrqOffsetSsb": "DB0", "sinrOffsetCsiRs": "DB0", "sinrOffsetSsb": "DB0"}, "quantityConfigIndex": 1, "refFreqCsiRs": 2079415, "smtc1": {"duration": 5, "sf20": 0}, "ssbFrequency": 647232, "ssbSubCarrierSpacing": "KHZ120"}}, "reportConfigToAddMod": {"reportConfigNr": {"eventReport": {"eventInculdeBeamMeasurements": false, "eventMaxReportCell": 1, "eventMeasReportQuantity": {"rsrp": true}, "eventMeasReportQuantityRsIndices": {"rsrp": true}, "eventReportAmount": "R8", "eventReportInterval": "MS240", "eventRsType": "SSB", "hysteresis": 0, "maxNrofRsIndexesToReport": 8, "measTriggerQuantityA4": {"eventA4UseWhiteCellList": false, "rsrp": 20}, "reportOnLeave": false, "timeToTrigger": "MS40"}}}}], "Vonr": [{"id": 1, "measObjectToAddMod": {"measObjectNr": {"absThreshCsiRsConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "absThreshSsBlocksConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "freqBandIndicatorNR": 78, "nrindexActivationFlag": true, "numCsiRsResourcesToAverage": 2, "numSsBlocksToAverage": 2, "qOffsetRangeList": {"rsrpOffsetCsiRs": "DB0", "rsrpOffsetSsb": "DB0", "rsrqOffsetCsiRs": "DB0", "rsrqOffsetSsb": "DB0", "sinrOffsetCsiRs": "DB0", "sinrOffsetSsb": "DB0"}, "quantityConfigIndex": 1, "refFreqCsiRs": 633984, "smtc1": {"duration": 5, "sf20": 0}, "ssbFrequency": 647232, "ssbSubCarrierSpacing": "KHZ30"}}, "reportConfigToAddMod": {"reportConfigNr": {"eventReport": {"eventInculdeBeamMeasurements": false, "eventMaxReportCell": 6, "eventMeasReportQuantity": {"rsrp": true}, "eventMeasReportQuantityRsIndices": {"rsrp": true}, "eventReportAmount": "R8", "eventReportInterval": "MS240", "eventRsType": "SSB", "hysteresis": 0, "maxNrofRsIndexesToReport": 8, "measTriggerQuantityOffsetA3": {"eventA3useWhiteCellList": false, "rsrp": 36}, "reportOnLeave": false, "timeToTrigger": "MS480"}}}}, {"id": 2, "measObjectToAddMod": {"measObjectNr": {"absThreshCsiRsConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "absThreshSsBlocksConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "freqBandIndicatorNR": 78, "nrindexActivationFlag": true, "numCsiRsResourcesToAverage": 2, "numSsBlocksToAverage": 2, "qOffsetRangeList": {"rsrpOffsetCsiRs": "DB0", "rsrpOffsetSsb": "DB0", "rsrqOffsetCsiRs": "DB0", "rsrqOffsetSsb": "DB0", "sinrOffsetCsiRs": "DB0", "sinrOffsetSsb": "DB0"}, "quantityConfigIndex": 1, "refFreqCsiRs": 633984, "smtc1": {"duration": 5, "sf20": 0}, "ssbFrequency": 647232, "ssbSubCarrierSpacing": "KHZ30"}}, "reportConfigToAddMod": {"reportConfigNr": {"eventReport": {"eventInculdeBeamMeasurements": false, "eventMaxReportCell": 1, "eventMeasReportQuantity": {"rsrp": true}, "eventMeasReportQuantityRsIndices": {"rsrp": true}, "eventReportAmount": "R1", "eventReportInterval": "MS240", "eventRsType": "SSB", "hysteresis": 0, "maxNrofRsIndexesToReport": 8, "measTriggerQuantityA2": {"rsrp": 20}, "reportOnLeave": false, "timeToTrigger": "MS480"}}}}], "VonrInterFreq": [{"id": 1, "measObjectToAddMod": {"measObjectNr": {"absThreshCsiRsConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "absThreshSsBlocksConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "freqBandIndicatorNR": 78, "nrindexActivationFlag": true, "numCsiRsResourcesToAverage": 2, "numSsBlocksToAverage": 2, "qOffsetRangeList": {"rsrpOffsetCsiRs": "DB0", "rsrpOffsetSsb": "DB0", "rsrqOffsetCsiRs": "DB0", "rsrqOffsetSsb": "DB0", "sinrOffsetCsiRs": "DB0", "sinrOffsetSsb": "DB0"}, "quantityConfigIndex": 1, "refFreqCsiRs": 647232, "smtc1": {"duration": 5, "sf20": 0}, "ssbFrequency": 647232, "ssbSubCarrierSpacing": "KHZ30"}}, "reportConfigToAddMod": {"reportConfigNr": {"eventReport": {"eventInculdeBeamMeasurements": false, "eventMaxReportCell": 6, "eventMeasReportQuantity": {"rsrp": true}, "eventMeasReportQuantityRsIndices": {"rsrp": true}, "eventReportAmount": "R8", "eventReportInterval": "MS240", "eventRsType": "SSB", "hysteresis": 0, "maxNrofRsIndexesToReport": 8, "measTriggerQuantityA5": {"eventA5useWhiteCellList": false, "rsrp": 34, "rsrpQ2": 46}, "reportOnLeave": false, "timeToTrigger": "MS480"}}}}, {"id": 2, "measObjectToAddMod": {"measObjectNr": {"absThreshCsiRsConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "absThreshSsBlocksConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "freqBandIndicatorNR": 78, "nrindexActivationFlag": true, "numCsiRsResourcesToAverage": 2, "numSsBlocksToAverage": 2, "qOffsetRangeList": {"rsrpOffsetCsiRs": "DB0", "rsrpOffsetSsb": "DB0", "rsrqOffsetCsiRs": "DB0", "rsrqOffsetSsb": "DB0", "sinrOffsetCsiRs": "DB0", "sinrOffsetSsb": "DB0"}, "quantityConfigIndex": 1, "refFreqCsiRs": 647232, "smtc1": {"duration": 5, "sf20": 0}, "ssbFrequency": 647232, "ssbSubCarrierSpacing": "KHZ30"}}, "reportConfigToAddMod": {"reportConfigNr": {"eventReport": {"eventInculdeBeamMeasurements": false, "eventMaxReportCell": 1, "eventMeasReportQuantity": {"rsrp": true}, "eventMeasReportQuantityRsIndices": {"rsrp": true}, "eventReportAmount": "R1", "eventReportInterval": "MS240", "eventRsType": "SSB", "hysteresis": 0, "maxNrofRsIndexesToReport": 8, "measTriggerQuantityA1": {"rsrp": 60}, "reportOnLeave": false, "timeToTrigger": "MS480"}}}}, {"id": 3, "measObjectToAddMod": {"measObjectNr": {"absThreshCsiRsConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "absThreshSsBlocksConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "freqBandIndicatorNR": 78, "nrindexActivationFlag": true, "numCsiRsResourcesToAverage": 2, "numSsBlocksToAverage": 2, "qOffsetRangeList": {"rsrpOffsetCsiRs": "DB0", "rsrpOffsetSsb": "DB0", "rsrqOffsetCsiRs": "DB0", "rsrqOffsetSsb": "DB0", "sinrOffsetCsiRs": "DB0", "sinrOffsetSsb": "DB0"}, "quantityConfigIndex": 1, "refFreqCsiRs": 647232, "smtc1": {"duration": 5, "sf20": 0}, "ssbFrequency": 647232, "ssbSubCarrierSpacing": "KHZ30"}}, "reportConfigToAddMod": {"reportConfigNr": {"eventReport": {"eventInculdeBeamMeasurements": false, "eventMaxReportCell": 1, "eventMeasReportQuantity": {"rsrp": true}, "eventMeasReportQuantityRsIndices": {"rsrp": true}, "eventReportAmount": "R1", "eventReportInterval": "MS240", "eventRsType": "SSB", "hysteresis": 0, "maxNrofRsIndexesToReport": 8, "measTriggerQuantityA2": {"rsrp": 20}, "reportOnLeave": false, "timeToTrigger": "MS480"}}}}], "VonrIrat": [{"id": 1, "measObjectToAddMod": {"measObjectNr": {"absThreshCsiRsConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "absThreshSsBlocksConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "freqBandIndicatorNR": 78, "nrindexActivationFlag": true, "numCsiRsResourcesToAverage": 2, "numSsBlocksToAverage": 2, "qOffsetRangeList": {"rsrpOffsetCsiRs": "DB0", "rsrpOffsetSsb": "DB0", "rsrqOffsetCsiRs": "DB0", "rsrqOffsetSsb": "DB0", "sinrOffsetCsiRs": "DB0", "sinrOffsetSsb": "DB0"}, "quantityConfigIndex": 1, "refFreqCsiRs": 633984, "smtc1": {"duration": 5, "sf20": 0}, "ssbFrequency": 647232, "ssbSubCarrierSpacing": "KHZ30"}}, "reportConfigToAddMod": {"reportConfigNr": {"eventReport": {"eventInculdeBeamMeasurements": false, "eventMaxReportCell": 1, "eventMeasReportQuantity": {"rsrp": true}, "eventMeasReportQuantityRsIndices": {"rsrp": true}, "eventReportAmount": "R8", "eventReportInterval": "MS240", "eventRsType": "SSB", "hysteresis": 0, "maxNrofRsIndexesToReport": 8, "measTriggerQuantityA1": {"rsrp": 20}, "reportOnLeave": false, "timeToTrigger": "MS480"}}}}, {"id": 2, "measObjectToAddMod": {"measObjectEutra": {"allowedMeasBandwidth": "mbw100", "carrierFreq": 38948, "cellToAddModEutra": [{"cellIndexEutra": 1, "cellIndividualOffset": "DB_24", "pci": 0}], "eutra-presenceAntennaPort1": true, "eutra-qOffsetRange": "DB0", "eutraindexActivationFlag": true, "widebandRSRQMeas": true}}, "reportConfigToAddMod": {"reportConfigIRAT": {"eventReportIRAT": {"eventMaxReportCell": 3, "eventMeasReportQuantityIrat": {"rsrp": true}, "eventReportAmount": "R8", "eventReportInterval": "MS240", "hysteresis": 0, "measTriggerQuantityB2": {"rsrp": 50, "rsrpEutra": 50}, "reportOnLeave": false, "rsType": "SSB", "timeToTrigger": "MS480"}}}}, {"id": 3, "measObjectToAddMod": {"measObjectEutra": {"allowedMeasBandwidth": "mbw100", "carrierFreq": 1251, "cellToAddModEutra": [{"cellIndexEutra": 1, "cellIndividualOffset": "DB_24", "pci": 0}], "eutra-presenceAntennaPort1": true, "eutra-qOffsetRange": "DB0", "eutraindexActivationFlag": true, "widebandRSRQMeas": true}}, "reportConfigToAddMod": {"reportConfigIRAT": {"eventReportIRAT": {"eventMaxReportCell": 3, "eventMeasReportQuantityIrat": {"rsrp": true}, "eventReportAmount": "R8", "eventReportInterval": "MS240", "hysteresis": 0, "measTriggerQuantityB2": {"rsrp": 50, "rsrpEutra": 50}, "reportOnLeave": false, "rsType": "SSB", "timeToTrigger": "MS480"}}}}, {"id": 4, "measObjectToAddMod": {"measObjectEutra": {"allowedMeasBandwidth": "mbw100", "carrierFreq": 2539, "cellToAddModEutra": [{"cellIndexEutra": 1, "cellIndividualOffset": "DB_24", "pci": 0}], "eutra-presenceAntennaPort1": true, "eutra-qOffsetRange": "DB0", "eutraindexActivationFlag": true, "widebandRSRQMeas": true}}, "reportConfigToAddMod": {"reportConfigIRAT": {"eventReportIRAT": {"eventMaxReportCell": 3, "eventMeasReportQuantityIrat": {"rsrp": true}, "eventReportAmount": "R8", "eventReportInterval": "MS240", "hysteresis": 0, "measTriggerQuantityB2": {"rsrp": 50, "rsrpEutra": 50}, "reportOnLeave": false, "rsType": "SSB", "timeToTrigger": "MS480"}}}}, {"id": 5, "measObjectToAddMod": {"measObjectEutra": {"allowedMeasBandwidth": "mbw100", "carrierFreq": 2464, "cellToAddModEutra": [{"cellIndexEutra": 1, "cellIndividualOffset": "DB_24", "pci": 0}], "eutra-presenceAntennaPort1": true, "eutra-qOffsetRange": "DB0", "eutraindexActivationFlag": true, "widebandRSRQMeas": true}}, "reportConfigToAddMod": {"reportConfigIRAT": {"eventReportIRAT": {"eventMaxReportCell": 3, "eventMeasReportQuantityIrat": {"rsrp": true}, "eventReportAmount": "R8", "eventReportInterval": "MS240", "hysteresis": 0, "measTriggerQuantityB2": {"rsrp": 50, "rsrpEutra": 50}, "reportOnLeave": false, "rsType": "SSB", "timeToTrigger": "MS480"}}}}, {"id": 6, "measObjectToAddMod": {"measObjectEutra": {"allowedMeasBandwidth": "mbw100", "carrierFreq": 38700, "cellToAddModEutra": [{"cellIndexEutra": 1, "cellIndividualOffset": "DB_24", "pci": 0}], "eutra-presenceAntennaPort1": true, "eutra-qOffsetRange": "DB0", "eutraindexActivationFlag": true, "widebandRSRQMeas": true}}, "reportConfigToAddMod": {"reportConfigIRAT": {"eventReportIRAT": {"eventMaxReportCell": 3, "eventMeasReportQuantityIrat": {"rsrp": true}, "eventReportAmount": "R8", "eventReportInterval": "MS240", "hysteresis": 0, "measTriggerQuantityB2": {"rsrp": 50, "rsrpEutra": 50}, "reportOnLeave": false, "rsType": "SSB", "timeToTrigger": "MS480"}}}}], "measConfigAnr": [{"id": 1, "measObjectToAddMod": {"measObjectNr": {"absThreshCsiRsConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "absThreshSsBlocksConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "freqBandIndicatorNR": 78, "nrindexActivationFlag": true, "numCsiRsResourcesToAverage": 2, "numSsBlocksToAverage": 2, "qOffsetRangeList": {"rsrpOffsetCsiRs": "DB0", "rsrpOffsetSsb": "DB0", "rsrqOffsetCsiRs": "DB0", "rsrqOffsetSsb": "DB0", "sinrOffsetCsiRs": "DB0", "sinrOffsetSsb": "DB0"}, "quantityConfigIndex": 1, "refFreqCsiRs": 633984, "smtc1": {"duration": 5, "sf20": 0}, "ssbFrequency": 647232, "ssbSubCarrierSpacing": "KHZ30"}}, "reportConfigToAddMod": {"reportConfigNr": {"eventReport": {"eventInculdeBeamMeasurements": false, "eventMaxReportCell": 6, "eventMeasReportQuantity": {"rsrp": true}, "eventMeasReportQuantityRsIndices": {"rsrp": true}, "eventReportAmount": "R8", "eventReportInterval": "MS240", "eventRsType": "SSB", "hysteresis": 0, "maxNrofRsIndexesToReport": 8, "measTriggerQuantityOffsetA3": {"eventA3useWhiteCellList": false, "rsrp": 37}, "reportOnLeave": false, "timeToTrigger": "MS480"}}}}, {"id": 2, "measObjectToAddMod": {"measObjectNr": {"absThreshCsiRsConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "absThreshSsBlocksConsolidation": {"thresholdRsrp": 1, "thresholdRsrq": 1, "thresholdSinr": 1}, "freqBandIndicatorNR": 78, "nrindexActivationFlag": true, "numCsiRsResourcesToAverage": 2, "numSsBlocksToAverage": 2, "qOffsetRangeList": {"rsrpOffsetCsiRs": "DB0", "rsrpOffsetSsb": "DB0", "rsrqOffsetCsiRs": "DB0", "rsrqOffsetSsb": "DB0", "sinrOffsetCsiRs": "DB0", "sinrOffsetSsb": "DB0"}, "quantityConfigIndex": 1, "refFreqCsiRs": 633984, "smtc1": {"duration": 5, "sf20": 0}, "ssbFrequency": 647232, "ssbSubCarrierSpacing": "KHZ30"}}, "reportConfigToAddMod": {"reportConfigNr": {"eventReport": {"eventInculdeBeamMeasurements": false, "eventMaxReportCell": 3, "eventMeasReportQuantity": {"rsrp": true}, "eventMeasReportQuantityRsIndices": {"rsrp": true}, "eventReportAmount": "R1", "eventReportInterval": "MS240", "eventRsType": "SSB", "hysteresis": 0, "maxNrofRsIndexesToReport": 8, "measTriggerQuantityA4": {"eventA4UseWhiteCellList": false, "rsrp": 36}, "reportOnLeave": false, "timeToTrigger": "MS480"}}}}, {"id": 3, "measObjectToAddMod": {"measObjectEutra": {"allowedMeasBandwidth": "mbw100", "carrierFreq": 38900, "cellToAddModEutra": [{"cellIndexEutra": 1, "cellIndividualOffset": "DB_24", "pci": 0}], "eutra-presenceAntennaPort1": true, "eutra-qOffsetRange": "DB0", "eutraindexActivationFlag": true, "widebandRSRQMeas": true}}, "reportConfigToAddMod": {"reportConfigIRAT": {"eventReportIRAT": {"eventMaxReportCell": 3, "eventMeasReportQuantityIrat": {"rsrp": true}, "eventReportAmount": "R8", "eventReportInterval": "MS240", "hysteresis": 0, "measTriggerQuantityB1": {"rsrpEutra": 50}, "reportOnLeave": false, "rsType": "SSB", "timeToTrigger": "MS480"}}}}, {"id": 4, "measObjectToAddMod": {"measObjectEutra": {"allowedMeasBandwidth": "mbw100", "carrierFreq": 38900, "cellToAddModEutra": [{"cellIndexEutra": 1, "cellIndividualOffset": "DB_24", "pci": 0}], "eutra-presenceAntennaPort1": true, "eutra-qOffsetRange": "DB0", "eutraindexActivationFlag": true, "widebandRSRQMeas": true}}, "reportConfigToAddMod": {"reportConfigIRAT": {"eventReportIRAT": {"eventMaxReportCell": 3, "eventMeasReportQuantityIrat": {"rsrp": true}, "eventReportAmount": "R8", "eventReportInterval": "MS240", "hysteresis": 0, "measTriggerQuantityB2": {"rsrp": 67, "rsrpEutra": 40}, "reportOnLeave": false, "rsType": "SSB", "timeToTrigger": "MS480"}}}}]}, "Tepsfb-exe": 2000, "Tepsfb-prep": 2000, "absThreshSsBlocksConsolidationSib2": {"thresholdRsrp": 5, "thresholdRsrq": 5, "thresholdSinr": 5}, "caScellProfileConfigAddTimerInSec": 5, "caScellProfileConfigReleaseTimerInSec": 2, "candidateSCellAggregationList": [{"blindCaEnable": false, "id": "0", "nci": "000fa4002", "sCellAddMeasProfileId": 1}], "cdrxEnabled": false, "cellAlarm": [{"alarmId": "ALARM_SLEEPING_CELL_DETECTION", "alarmName": "ALARM_SLEEPING_CELL_DETECTION", "thresholdCount": 0, "thresholdLevel": 0, "thresholdTimeInterval": 600}], "cellDesignatedPrimary": true, "cellReselectionPriority": 7, "cellReselectionSubPriority": 6, "deriveSsbIdxFromCell": true, "drbIntegrityEnabled": false, "drxConfig": {"anrSpecific": [{"id": 0, "longDrxCycleLength": "ms320", "shortDrxCycleLength": "ms8", "shortDrxCycleTimer": 4}], "dataSpecific": [{"id": 0, "longDrxCycleLength": "ms80", "shortDrxCycleLength": "ms5", "shortDrxCycleTimer": 2}], "interRatAnrSpecific": [{"id": 0, "longDrxCycleLength": "ms320", "shortDrxCycleLength": "ms8", "shortDrxCycleTimer": 4}], "vonrSpecific": [{"id": 0, "longDrxCycleLength": "ms40", "shortDrxCycleLength": "ms5", "shortDrxCycleTimer": 2}]}, "drxEnabled": false, "duration": 5, "embbInterFreqConfigAddTimerInSecOption2": 5, "embbInterFreqConfigReleaseTimerInSecOption2": 2, "embbInterFreqOption": "OPTION1", "enableSib3BlacklistCells": false, "enableSib4BlacklistCells": false, "enableSib5BlacklistCells": false, "encAlgo": ["NEA2"], "endSymbol": 2, "energySavingCellConfig": {"emergencyUserThreshold": 0, "energySaveActivate": false, "energySavingConnectedUeThresholdPercent": 10, "highPriorityUserThreshold": 0, "vinrUserThreshold": 0, "vonrUserThreshold": 0}, "eutraConfig": {"eutraNeighbourCell": [{"MCC": "311", "MNC": "48", "TAC": "0002", "enbId": 1, "enbIdType": "SHORT_MACRO_ENB_ID", "eutraCellIdentifier": 2, "eutraFddMode": {"dlEarfcn": 38948, "dlTxBandwidth": "EUTRA_BW_100", "ulEarfcn": 38948, "ulTxBandwidth": "EUTRA_BW_50"}, "freqBandIndEutra": 40, "isHOAllowed": true, "isRemoveAllowed": true, "qQualMinOffsetCell": 1, "qRxLevMinOffsetCell": 1, "qoffset": "dB0", "rankVal": -1, "srcOfDetection": "EMS", "targetEutraPhysicalCellId": 101}]}, "eutraQosConfig": [{"configIndex": 1, "enableUlOutOfOrderDelivery": false, "qci": 1, "reorderingTimerMs": 500, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sduDiscardTimerMs": "infinity", "snSizeDL": 12, "snSizeUL": 12, "ulDataSplitThresholdInBytes": "b100"}], "f1ProcedureGuardTimerInMs": 3000, "gnbCellAnrConfigInfo": {"anrEvent": "A4", "lteNrtSize": 256, "nrInterFrequencyNrtsize": 64, "nrNrtSize": 256}, "gnbNrCellCuRelationVsConfig": [{"MCC": "311", "MNC": "48", "TAC": "0001", "id": "0", "isHOAllowed": true, "isRemoveAllowed": true, "modeTdd": {"TDDInfo": {"nrArfcn": 647232, "nrNumRb": 273, "numFreqBands": [{"nrFreqBandInd": 78}], "subCarrierSpacingInKhz": "30"}}, "noNrdcPresent": true, "noXnHoPresent": false, "noXnPresent": false, "nrCellIdentifier": 1, "nrNeighbourCellRelationIdx": 1, "nrNeighbourGnbIpAddr": "*******", "nrNeighbourGnbIpPort": 38422, "offsetCell": "DB12", "qualMinOffsetCell": 1, "rankVal": -1, "rxLevMinOffsetCell": 3, "rxLevMinOffsetCellSul": 6, "srcOfDetection": "EMS", "targetNRGnbId": 0, "targetNRGnbIdLen": 0, "targetNRPhysicalCellId": 31, "xnStatus": "DOWN"}], "handoverExecTimerSec": 3, "handoverPrepTimerSec": 3, "initalCtxSetupFailureTimerInMs": 3000, "initialContextSetupProcTimerInMs": 2000, "initialNasProcTimerInMs": 2000, "intAlgo": ["NIA2"], "intraFreqBlackCells": [{"intraFreqBlackCellsId": 1, "start": 0}], "intraFreqReselectionThreshold": 31, "isIratHoEnabled": false, "isIratSupported": true, "layerManagementConfig": {"fastHandoverEnabled": false}, "layerManagementEnabled": false, "measEventTypeIRAT": "B2", "measurementSlots": 10111101, "mediumBitmap": 10000000, "mnXnDcOverallTimerInMs": 3000, "mnXnDcPreparationTimerInMs": 3000, "mocnCacCfg": {"mocnPlmn": [{"MCC": "311", "MNC": "48", "capacity": 50, "index": 0}, {"MCC": "405", "MNC": "990", "capacity": 25, "index": 1}, {"MCC": "405", "MNC": "991", "capacity": 25, "index": 2}, {"MCC": "405", "MNC": "992", "capacity": 0, "index": 3}, {"MCC": "405", "MNC": "993", "capacity": 0, "index": 4}, {"MCC": "405", "MNC": "994", "capacity": 0, "index": 5}]}, "mrEpsfbTimerMs": 500, "nCellChangeHigh": 16, "nCellChangeMedium": 16, "ngRelocOverallTimerInMs": 3000, "ngRelocPreparationTimerInMs": 3000, "ngUeCtxRelReqTimerInMs": 3000, "nrInterFreqEnabled": true, "nrofSSBlocksToAverage": 2, "nrppaResponseTimerSec": 4, "pMax": 23, "pathSwTimerInMs": 3000, "pduSessResRelRspTimerInMs": 3000, "pduSessResSetupRspTimerInMs": 3000, "pduSessionEstProcTimerInMs": 2000, "pduSessionModProcTimerInMs": 2000, "pduSessionRelProcTimerInMs": 2000, "prePduSessionEstInactivityTimerInMs": 30000, "pwsCellConfig": {"emergencyAreaIdList": [1, 2, 3]}, "qHyst": 3, "qQualMinReselection": -30, "qRxLevMin": {"qrxLevMinValue": -64}, "rangeToBestCell": 10, "reportIRATCgiTimerInMs": 30000, "reportIRATCgiTimerSec": 3, "rrcIratReportCgiTimerInMs": 3000, "rrcReconfigTimerInMs": 3000, "rrcReestProcTimerInMs": 2000, "rrcReestablishmentTimerInMs": 3000, "rrcSecurityModeCmdTimerInMs": 3000, "rrcSetupTimerInMs": 3000, "rrcUeCapabilityEnquiryTimerInMs": 1000, "rrmAdmissionControlConfig": [{"bearerThresholdConfig": [{"bearerArp": 1, "bearerThreshold": 100, "id": 0}, {"bearerArp": 2, "bearerThreshold": 100, "id": 1}, {"bearerArp": 3, "bearerThreshold": 100, "id": 2}, {"bearerArp": 4, "bearerThreshold": 100, "id": 3}, {"bearerArp": 5, "bearerThreshold": 100, "id": 4}, {"bearerArp": 6, "bearerThreshold": 100, "id": 5}, {"bearerArp": 7, "bearerThreshold": 100, "id": 6}, {"bearerArp": 8, "bearerThreshold": 90, "id": 7}, {"bearerArp": 9, "bearerThreshold": 90, "id": 8}, {"bearerArp": 10, "bearerThreshold": 90, "id": 9}, {"bearerArp": 11, "bearerThreshold": 90, "id": 10}, {"bearerArp": 12, "bearerThreshold": 90, "id": 11}, {"bearerArp": 13, "bearerThreshold": 90, "id": 12}, {"bearerArp": 14, "bearerThreshold": 90, "id": 13}, {"bearerArp": 15, "bearerThreshold": 90, "id": 14}], "emergencyarp": 2, "id": 0, "maxBearersPerCell": 1400, "maxUesPerCell": 600, "maxVinrBearersPerCell": 32, "maxVonrBearersPerCell": 128, "numBearerThresholdConfig": 15, "numUEPriorityConfig": 10, "preEmptionThreshold": 90, "uePriorityBucketConfig": [{"establishmentCause": "EMERGENCY", "id": 0, "ueAdmissionThreshold": 100, "uePriority": 2}, {"establishmentCause": "HIGH_PRIORITY_ACCESS", "id": 1, "ueAdmissionThreshold": 100, "uePriority": 2}, {"establishmentCause": "MPS_PRIORITY_ACCESS", "id": 2, "ueAdmissionThreshold": 100, "uePriority": 1}, {"establishmentCause": "MCS_PRIORITY_ACCESS", "id": 3, "ueAdmissionThreshold": 100, "uePriority": 1}, {"establishmentCause": "MT_ACCESS", "id": 4, "ueAdmissionThreshold": 80, "uePriority": 3}, {"establishmentCause": "MO_SIGNALLING", "id": 5, "ueAdmissionThreshold": 80, "uePriority": 3}, {"establishmentCause": "MO_DATA", "id": 6, "ueAdmissionThreshold": 80, "uePriority": 3}, {"establishmentCause": "MO_VOICE_CALL", "id": 7, "ueAdmissionThreshold": 80, "uePriority": 4}, {"establishmentCause": "MO_VIDEO_CALL", "id": 8, "ueAdmissionThreshold": 80, "uePriority": 4}, {"establishmentCause": "MO_SMS", "id": 9, "ueAdmissionThreshold": 80, "uePriority": 4}]}], "sIntraSearchQ": {"reselectionThresholdQValue": 5}, "sNonIntraSearchP": {"reselectionThresholdValue": 4}, "sNonIntraSearchQ": {"reselectionThresholdQValue": 5}, "sf20": 0, "sfHighReselection": 0, "sfMediumReselection": 0, "siConfig": ["SIB2", "SIB3", "SIB4", "SIB5", "SIB6", "SIB7", "SIB8"], "sib4ConfigInfo": {"carrierFreqListNR": [{"blackCellsListNR": [{"range": 8, "start": 300}], "carrierFreqNrActivationFlag": true, "index": 0, "nr-qOffsetRange": "DB0", "nrArfcn": 633984, "pMaxEutra": 0, "qRxLevMin": -30, "qRxLevMinSUL": {"qrxLevMinValue": -27}, "reselectionPriority": 7, "reselectionSubPriority": "oDot2", "smtc": {"duration": 5, "sf20": 0}, "ssbSubCarrierSpacing": "KHZ30", "tReselectionNr": 0, "tReselectionNrSF": {"sfHigh": "oDot75", "sfMedium": "oDot25"}, "threshXHigh": 20, "threshXHighQ": 20, "threshXLow": 7, "threshXLowQ": 0}]}, "sib5ConfigInfo": {"carrierFreqListEutra": [{"allowedMeasBandwidth": "mbw100", "blackCellsListEutra": [{"range": 8, "start": 300}], "carrierFreqEutraActivationFlag": true, "carrierModeType": "TDD", "dlCarrierFreq": 38948, "eutraPresenceAntennaPort1": true, "freqBandInd": 40, "index": 0, "pMaxEutra": 0, "qQualMin": 4, "qRxLevMin": 30, "reselectionPriority": 4, "reselectionSubPriority": "oDot2", "threshXHigh": 20, "threshXHighQ": 20, "threshXLow": 7, "threshXLowQ": 0, "ulCarrierFreq": 38948}, {"allowedMeasBandwidth": "mbw100", "blackCellsListEutra": [{"range": 8, "start": 300}], "carrierFreqEutraActivationFlag": true, "carrierModeType": "TDD", "dlCarrierFreq": 38900, "eutraPresenceAntennaPort1": true, "freqBandInd": 40, "index": 1, "pMaxEutra": 0, "qQualMin": 4, "qRxLevMin": 30, "reselectionPriority": 5, "reselectionSubPriority": "oDot2", "threshXHigh": 20, "threshXHighQ": 20, "threshXLow": 7, "threshXLowQ": 0, "ulCarrierFreq": 38900}, {"allowedMeasBandwidth": "mbw50", "blackCellsListEutra": [{"range": 8, "start": 300}], "carrierFreqEutraActivationFlag": true, "carrierModeType": "FDD", "dlCarrierFreq": 1251, "eutraPresenceAntennaPort1": true, "freqBandInd": 3, "index": 2, "pMaxEutra": 0, "qQualMin": 4, "qRxLevMin": 30, "reselectionPriority": 5, "reselectionSubPriority": "oDot2", "threshXHigh": 20, "threshXHighQ": 20, "threshXLow": 7, "threshXLowQ": 0, "ulCarrierFreq": 19251}, {"allowedMeasBandwidth": "mbw50", "blackCellsListEutra": [{"range": 8, "start": 300}], "carrierFreqEutraActivationFlag": true, "carrierModeType": "FDD", "dlCarrierFreq": 2539, "eutraPresenceAntennaPort1": true, "freqBandInd": 5, "index": 3, "pMaxEutra": 0, "qQualMin": 4, "qRxLevMin": 30, "reselectionPriority": 5, "reselectionSubPriority": "oDot2", "threshXHigh": 20, "threshXHighQ": 20, "threshXLow": 7, "threshXLowQ": 0, "ulCarrierFreq": 20539}, {"allowedMeasBandwidth": "mbw25", "blackCellsListEutra": [{"range": 8, "start": 300}], "carrierFreqEutraActivationFlag": true, "carrierModeType": "FDD", "dlCarrierFreq": 2464, "eutraPresenceAntennaPort1": true, "freqBandInd": 5, "index": 4, "pMaxEutra": 0, "qQualMin": 4, "qRxLevMin": 30, "reselectionPriority": 5, "reselectionSubPriority": "oDot2", "threshXHigh": 20, "threshXHighQ": 20, "threshXLow": 7, "threshXLowQ": 0, "ulCarrierFreq": 20464}], "tReselectionEutra": 0, "tReselectionEutraSF": {"sfHigh": "oDot75", "sfMedium": "oDot25"}}, "sliceList": [{"activationStatus": true, "nrQosConfig": [{"configIndex": 0, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 1, "reorderingTimerMs": 100, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 1, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 2, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 2, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 3, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 3, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 4, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 4, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 5, "reorderingTimerMs": 100, "rlcMode": "RLC_AM", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 5, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 6, "reorderingTimerMs": 40, "rlcMode": "RLC_AM", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 6, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 7, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 7, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 8, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 8, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 9, "reorderingTimerMs": 100, "rlcMode": "RLC_AM", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}], "sd": 0, "sliceIndex": 0, "sst": 1}, {"activationStatus": true, "nrQosConfig": [{"configIndex": 0, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 1, "reorderingTimerMs": 100, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 1, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 2, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 2, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 3, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 3, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 4, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 4, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 5, "reorderingTimerMs": 100, "rlcMode": "RLC_AM", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 5, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 6, "reorderingTimerMs": 40, "rlcMode": "RLC_AM", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 6, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 7, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 7, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 8, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 8, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 9, "reorderingTimerMs": 100, "rlcMode": "RLC_AM", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}], "sd": 1, "sliceIndex": 1, "sst": 1}, {"activationStatus": false, "nrQosConfig": [{"configIndex": 0, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 1, "reorderingTimerMs": 100, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 1, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 2, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 2, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 3, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 3, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 4, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 4, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 5, "reorderingTimerMs": 100, "rlcMode": "RLC_AM", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 5, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 6, "reorderingTimerMs": 40, "rlcMode": "RLC_AM", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 6, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 7, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 7, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 8, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 8, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 9, "reorderingTimerMs": 100, "rlcMode": "RLC_AM", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}], "sd": 2, "sliceIndex": 2, "sst": 1}, {"activationStatus": false, "nrQosConfig": [{"configIndex": 0, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 1, "reorderingTimerMs": 100, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 1, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 2, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 2, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 3, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 3, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 4, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 4, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 5, "reorderingTimerMs": 100, "rlcMode": "RLC_AM", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 5, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 6, "reorderingTimerMs": 40, "rlcMode": "RLC_AM", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 6, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 7, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 7, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 8, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 8, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 9, "reorderingTimerMs": 100, "rlcMode": "RLC_AM", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}], "sd": 3, "sliceIndex": 3, "sst": 1}, {"activationStatus": false, "nrQosConfig": [{"configIndex": 0, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 1, "reorderingTimerMs": 100, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 1, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 2, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 2, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 3, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 3, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 4, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 4, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 5, "reorderingTimerMs": 100, "rlcMode": "RLC_AM", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 5, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 6, "reorderingTimerMs": 40, "rlcMode": "RLC_AM", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 6, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 7, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 7, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 8, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 8, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 9, "reorderingTimerMs": 100, "rlcMode": "RLC_AM", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}], "sd": 4, "sliceIndex": 4, "sst": 1}, {"activationStatus": false, "nrQosConfig": [{"configIndex": 0, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 1, "reorderingTimerMs": 100, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 1, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 2, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 2, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 3, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 3, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 4, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 4, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 5, "reorderingTimerMs": 100, "rlcMode": "RLC_AM", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 5, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 6, "reorderingTimerMs": 40, "rlcMode": "RLC_AM", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 6, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 7, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 7, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 8, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 8, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 9, "reorderingTimerMs": 100, "rlcMode": "RLC_AM", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}], "sd": 5, "sliceIndex": 5, "sst": 1}, {"activationStatus": false, "nrQosConfig": [{"configIndex": 0, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 1, "reorderingTimerMs": 100, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 1, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 2, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 2, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 3, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 3, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 4, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 4, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 5, "reorderingTimerMs": 100, "rlcMode": "RLC_AM", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 5, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 6, "reorderingTimerMs": 40, "rlcMode": "RLC_AM", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 6, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 7, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 7, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 8, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 8, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 9, "reorderingTimerMs": 100, "rlcMode": "RLC_AM", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}], "sd": 6, "sliceIndex": 6, "sst": 1}, {"activationStatus": false, "nrQosConfig": [{"configIndex": 0, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 1, "reorderingTimerMs": 100, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 1, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 2, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 2, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 3, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 3, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 4, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 4, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 5, "reorderingTimerMs": 100, "rlcMode": "RLC_AM", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 5, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 6, "reorderingTimerMs": 40, "rlcMode": "RLC_AM", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 6, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 7, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 7, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 8, "reorderingTimerMs": 40, "rlcMode": "RLC_UM_BIDIRECTIONAL", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}, {"configIndex": 8, "defaultDrb": false, "dscp": 0, "enableUlOutOfOrderDelivery": false, "fiveQi": 9, "reorderingTimerMs": 100, "rlcMode": "RLC_AM", "sdapHeaderDL": true, "sdapHeaderUL": true, "sduDiscardTimerMs": "infinity", "snSizeDL": 18, "snSizeUL": 18, "ulDataSplitThresholdInBytes": "b100"}], "sd": 7, "sliceIndex": 7, "sst": 1}], "snXnDcOverallTimerInMs": 3000, "snXnDcPreparationTimerInMs": 3000, "srbConfig": [{"srbConfigInfo": {"reorderingTimerMs": 500}, "srbId": 1}, {"srbConfigInfo": {"reorderingTimerMs": 500}, "srbId": 2}], "staleUEReleaseFailureGuardTimerSec": 120, "tEvaluation": 30, "tHystNormal": 30, "tReselectionNr": 1, "threshServingLowP": {"reselectionThresholdValue": 9}, "threshServingLowQ": {"reselectionThresholdQValue": 6}, "ueCapabilityTriggerAfterSMCProc": true, "ueInactivityTimerSec": "day30", "ueMeasBasedCAEnabled": false, "ueReconfigProcTimerInMs": 2000, "ueSearchRateInScheduledAnr": 5, "xnRelocOverallTimerInMs": 3000, "xnRelocPreparationTimerInMs": 3000, "xnRetriveUeCtxTimerInMs": 3000}, "id": "vendorA_ORUAA100_FR1918010111", "nCI": "000fa4001", "objectClass": "NRCellCU", "objectInstance": "0", "objectPairInstance": "0", "pLMNId": [{"MCC": "311", "MNC": "48"}], "peeParametersList": {"environmentType": "Outdoor", "equipmentType": "RRU", "powerInterface": "AC", "siteDescription": "prestige tech park", "siteIdentification": "Tech Park", "siteLatitude": 12.9781, "siteLongitude": 77.6653}, "rRMPolicy": "rRMPolicy", "rRMPolicyNSSIId": "NssidPolicy", "rRMPolicyRatio": 8, "rRMPolicyType": 0, "sCellUlconfigured": "NILL", "tmCellControlConfig": [{"id": 0, "tccTraceDepth": "VENDORMINIMUM", "tccTraceReportingFormat": "STREAMING", "tmAdministrativeState": "Unlocked", "tmTracePauseCpuThreshold": "critical", "tmTracePauseMemThreshold": "critical", "tmTraceResumeCpuThreshold": "warning", "tmTraceResumeMemThreshold": "warning"}], "userLabel": "NRCellCU", "vnfParametersList": {"autoScalable": false, "flavourId": "1", "vnfInstanceId": "1", "vnfdId": "1"}}], "gNBId": 1001, "gNBIdLength": 22, "id": "0"}], "id": "0"}, "enabled": true}, "status": {"specHash": "4966036424179282609"}}}, "CuCpAppConfig": {"status": {"specHash": "17084884611101622773"}, "content": {"apiVersion": "cucp.app.config.radisys.com/v1alpha1", "kind": "CuCpAppConfig", "metadata": {"annotations": {"rel": "24.8.0-dev"}, "creationTimestamp": "2024-09-18T14:16:54Z", "generation": 2, "labels": {"site": "18sep6c"}, "name": "cucpappconfig-18sep6c", "namespace": "default", "resourceVersion": "98283743", "uid": "bc95d8ae-db04-411e-8ea2-1ed95bcbf2d3"}, "spec": {"ME": {"AlarmList": [{"id": "CUCP"}, {"id": "SYSMON"}, {"id": "PROCMON"}, {"id": "SWM"}, {"id": "EVENT_API"}, {"id": "NETCONF_MGR"}, {"id": "FILE_MGR"}], "FMControl": [{"administrativeState": "Unlocked", "id": "CUCP"}, {"administrativeState": "Unlocked", "id": "SYSMON"}, {"administrativeState": "Unlocked", "id": "PROCMON"}, {"administrativeState": "Unlocked", "id": "SWM"}, {"administrativeState": "Unlocked", "id": "EVENT_API"}, {"administrativeState": "Unlocked", "id": "NETCONF_MGR"}, {"administrativeState": "Unlocked", "id": "FILE_MGR"}], "GNBCUCPFunction": [{"EP_NgC": [{"farEndEntity": "1", "gnbNgcVsConfig": {"AMF-Pointer": 3, "AMF-Region-id": 9, "AMF-Set-id": 6, "MCC": "311", "MNC": "48", "amfActivationFlag": true, "defaultAMF": false, "ngcAlarm": [{"alarmId": "ALARM_EXCESSIVE_RESET_MSGS_WITH_AMF", "alarmName": "ALARM_EXCESSIVE_RESET_MSGS_WITH_AMF", "thresholdCount": 0, "thresholdLevel": 0, "thresholdTimeInterval": 600}], "ngcGwAddress": "127.0.0.1", "ngcLocalIpPrefix": 24, "ngcRemoteIpPrefix": 24}, "id": "0", "localIpAddress": "0.0.0.0", "localIpPort": 0, "localVlanId": 7, "objectClass": "EP-NG-C", "objectInstance": "0", "objectPairInstance": "0", "remoteAddress": "***********", "remoteIpPort": 38412, "userLabel": "EP_NgC"}], "cuUpId": [1], "gNB-type": "SA", "gNBCUName": "CU1", "gNBId": 1001, "gNBIdLength": 22, "gnbCuCpVsConfig": {"amfLoadBalancePolicy": "ALL", "anrConfig": {"anrEnabled": true, "anrEvent": "A4", "anrNrArfcn": 633984, "blackListedPlmn": [{"MCC": "999", "MNC": "99", "index": 0}], "eutra321TimeOutInSec": 3, "hoBasedAnrEnabled": true, "hoBasedInterFreqAnrEnable": true, "interFreqAnrMeasEvent": "A4", "interRatAnrEnabled": false, "interRatAnrEvent": "B1", "interRatHoBasedAnrEnabled": false, "macroXnEnhancementEnabled": false, "nr321TimeOutInSec": 3, "ranking": {"offset": 24, "periodicityTimerHrs": 12, "time": {"hrs": 12, "min": 0}, "weightage": "0.5"}, "schedule": {"durationTimerMin": 10, "hoCountThreshold": 10, "nbrPurgeTimerHrs": 3, "nbrPurgeTimerMin": 0, "rankPeriodicityMin": 0, "repeatTimerHrs": 12, "repeatTimerMin": 0, "time": {"hrs": 12, "min": 0}}, "scheduledInterFreqAnrEnable": false, "waitTimer": 10, "xnRanNodeUpdateIntervalInSec": 10, "xnRetryTimer": 60, "xnTimeToWaitIeValueInSec": "v10s"}, "caEnabled": false, "cnType": "epc", "cuAlarm": [{"alarmId": "ALARM_ALL_AMF_COMM_DOWN", "alarmName": "ALARM_ALL_AMF_COMM_DOWN", "thresholdCount": 0, "thresholdLevel": 0, "thresholdTimeInterval": 600}], "defaultPagingDrx": "rf128", "duId": [1], "energySavingGnbConfig": {"switchOffRspTimer": 20}, "epsfb_blind_redir_earfcn": 1251, "epsfb_blind_redir_enabled": false, "eutraRedirectionInfo": [{"bandwidth": "bw20", "cnType": "epc", "earfcn": 38700, "index": 0}, {"bandwidth": "bw20", "cnType": "epc", "earfcn": 38948, "index": 1}, {"bandwidth": "bw20", "cnType": "epc", "earfcn": 1251, "index": 2}, {"bandwidth": "bw20", "cnType": "epc", "earfcn": 2539, "index": 3}, {"bandwidth": "bw20", "cnType": "epc", "earfcn": 2464, "index": 4}], "hoBasedEpsfbEnabled": false, "ipPreferenceUplane": "ipv4", "lcsEnabled": false, "maxConnectionAttemptsPerCycle": 10, "maxNumPduSessions": 4, "maxNumRb": 5000, "maxNumUes": 5000, "memoryUsageReporPeriodicityInMins": 0, "memoryUsageReportingEnabled": false, "memoryUsageReportingMaxIterations": 1, "msgMaxRetryCount": 3, "msgRetryIntervalInSec": 5, "ngFlexEnabled": false, "ngHoEnabled": true, "pagingCpuThresholdConfig": "critical", "pagingMemThresholdConfig": "critical", "pciCollisionConfusionEnabled": true, "pwsEnabled": true, "pwsGnbConfig": {"maxSegmentSibSize": 150, "pwsCancelEnabled": true, "pwsFailureIndicationEnabled": true, "pwsProcTimerInMs": 5000, "pwsRestartEnabled": true}, "reestablishmentEnabled": true, "rrmAcCpuThresholdConfig": "critical", "rrmAcMemThresholdConfig": "critical", "sliceManagerIpAddress": "*************", "sliceManagerPort": 4343, "timeToWaitInSec": 5, "traceCpuThresholdConfig": "critical", "traceMemThresholdConfig": "critical", "ueCapabilityEnquiryEutraFreqBands": [{"freqBandId": 3}, {"freqBandId": 5}, {"freqBandId": 40}], "ueCapabilityEnquiryNrFreqBands": [{"freqBandId": 78}], "ueCapabilitySplitEnabled": false, "unpreparedCellContextRetrievalEnabled": false, "vovinrDisabled": false}, "gnbIndex": 0, "gnbLogVsConfigCuCp": {"cuLog": [{"logLevel": "ERR", "moduleId": "PM"}, {"logLevel": "INF", "moduleId": "FM"}, {"logLevel": "ERR", "moduleId": "TM"}, {"logLevel": "TRC", "moduleId": "OAM_AGENT"}, {"logLevel": "TRC", "moduleId": "LIBOAM"}, {"logLevel": "TRC", "moduleId": "GNB_MGR"}, {"logLevel": "ERR", "moduleId": "APP"}, {"logLevel": "ERR", "moduleId": "COMMON"}, {"logLevel": "ERR", "moduleId": "CU_UP_MGR"}, {"logLevel": "TRC", "moduleId": "RM"}, {"logLevel": "TRC", "moduleId": "UE_CONN_MGR"}, {"logLevel": "TRC", "moduleId": "BEARER_MGR"}, {"logLevel": "ERR", "moduleId": "CODEC_COMMON"}, {"logLevel": "ERR", "moduleId": "X2AP_CODEC"}, {"logLevel": "ERR", "moduleId": "F1AP_CODEC"}, {"logLevel": "ERR", "moduleId": "RRC_CODEC"}, {"logLevel": "ERR", "moduleId": "NGAP_CODEC"}, {"logLevel": "ERR", "moduleId": "XNAP_CODEC"}, {"logLevel": "ERR", "moduleId": "E1AP_CODEC"}, {"logLevel": "ERR", "moduleId": "SCTP_COMMON"}, {"logLevel": "ERR", "moduleId": "SCTP_CNTRL"}, {"logLevel": "ERR", "moduleId": "SCTP_TX"}, {"logLevel": "ERR", "moduleId": "SCTP_RX"}, {"logLevel": "TRC", "moduleId": "PDCP_C_CNTRL"}, {"logLevel": "TRC", "moduleId": "PDCP_C_RX"}, {"logLevel": "TRC", "moduleId": "PDCP_C_TX"}, {"logLevel": "ERR", "moduleId": "UDP_CNTRL"}, {"logLevel": "ERR", "moduleId": "UDP_TX"}, {"logLevel": "ERR", "moduleId": "UDP_DL_RX"}, {"logLevel": "ERR", "moduleId": "UDP_UL_RX"}, {"logLevel": "ERR", "moduleId": "NRUP_CODEC"}, {"logLevel": "ERR", "moduleId": "TIMER"}, {"logLevel": "TRC", "moduleId": "DSON"}, {"logLevel": "ERR", "moduleId": "UDP_RX_E1_PRIME"}, {"logLevel": "ERR", "moduleId": "UDP_TX_E1_PRIME"}, {"logLevel": "ERR", "moduleId": "LTERRC_CODEC"}, {"logLevel": "ERR", "moduleId": "E2AP_CODEC"}, {"logLevel": "TRC", "moduleId": "LIBOAM"}, {"logLevel": "ERR", "moduleId": "RRM_UT"}, {"logLevel": "INF", "moduleId": "EGTPU_COMMON"}, {"logLevel": "INF", "moduleId": "EGTPU_UPPER_TX_CNTRL"}, {"logLevel": "INF", "moduleId": "EGTPU_UPPER_RX_CNTRL"}, {"logLevel": "INF", "moduleId": "EGTPU_UPPER_TX"}, {"logLevel": "INF", "moduleId": "EGTPU_UPPER_RX"}, {"logLevel": "INF", "moduleId": "EGTPU_LOWER_TX_CNTRL"}, {"logLevel": "INF", "moduleId": "EGTPU_LOWER_RX_CNTRL"}, {"logLevel": "INF", "moduleId": "EGTPU_LOWER_TX"}, {"logLevel": "INF", "moduleId": "EGTPU_LOWER_RX"}, {"logLevel": "INF", "moduleId": "PDCP_COMMON"}, {"logLevel": "INF", "moduleId": "PDCP_TX_CNTRL"}, {"logLevel": "INF", "moduleId": "PDCP_RX_CNTRL"}, {"logLevel": "INF", "moduleId": "PDCP_TX"}, {"logLevel": "INF", "moduleId": "PDCP_RX"}, {"logLevel": "ERR", "moduleId": "CRYPTO_RX"}, {"logLevel": "ERR", "moduleId": "EGTPU_TIMER"}, {"logLevel": "ERR", "moduleId": "SDAP_COMMON"}, {"logLevel": "ERR", "moduleId": "SDAP_TX_CNTRL"}, {"logLevel": "ERR", "moduleId": "SDAP_RX_CNTRL"}, {"logLevel": "ERR", "moduleId": "SDAP_TX"}, {"logLevel": "ERR", "moduleId": "SDAP_RX"}, {"logLevel": "ERR", "moduleId": "SDAP_CODEC"}], "enableBinLog": false, "logFileName": "cu", "maxLogFileCount": 5, "maxLogFileSize": 200000000, "maxStatsFileParts": 5, "maxStatsFileSizeBytes": 200000000, "ngpLog": [{"logLevel": "ERR", "moduleId": "NGP"}, {"logLevel": "ERR", "moduleId": "MEM"}, {"logLevel": "ERR", "moduleId": "BUF"}, {"logLevel": "ERR", "moduleId": "STATS"}, {"logLevel": "ERR", "moduleId": "TIMER"}, {"logLevel": "ERR", "moduleId": "STHREAD"}, {"logLevel": "ERR", "moduleId": "CTHREAD"}, {"logLevel": "ERR", "moduleId": "SYS"}, {"logLevel": "ERR", "moduleId": "EXCP"}, {"logLevel": "TRC", "moduleId": "COMM"}, {"logLevel": "TRC", "moduleId": "SCTP"}, {"logLevel": "ERR", "moduleId": "UDP"}, {"logLevel": "ERR", "moduleId": "TCP"}, {"logLevel": "ERR", "moduleId": "MSGQ"}, {"logLevel": "ERR", "moduleId": "PRIOQ"}, {"logLevel": "ERR", "moduleId": "WORKQ"}, {"logLevel": "ERR", "moduleId": "PERF"}, {"logLevel": "FATAL", "moduleId": "HTTP2"}, {"logLevel": "FATAL", "moduleId": "THR_POOL"}, {"logLevel": "FATAL", "moduleId": "FAST_TIMER"}, {"logLevel": "ERR", "moduleId": "FAST_CRYPTO"}, {"logLevel": "FATAL", "moduleId": "TLS"}, {"logLevel": "FATAL", "moduleId": "FQDN"}], "rrcWiresharkDisector": {"enableRrcWiresharkDisector": false, "localAddress": "127.0.0.1", "remoteAddress": "127.0.0.1", "remotePort": 9999}, "statsCollectionPeriodMs": 10000}, "gnbSctpVsCfgCuCp": {"e1ClientLocalIpAddress": ["0.0.0.0"], "e1ClientLocalIpPrefix": [24], "e1GwAddress": ["0.0.0.0"], "e1ServerLocalIpAddress": "0.0.0.0", "e1ServerLocalIpPrefix": 24, "e1ServerLocalPort": 38462, "f1cGwAddress": "0.0.0.0", "f1cServerLocalIpAddress": "0.0.0.0", "f1cServerLocalIpPrefix": 24, "f1cServerLocalPort": 38472, "heartBeatIntervalInMs": 5000, "maxInboundStreams": 2, "maxInitAttempts": 5, "maxPathRetx": 1, "numOutboundStreams": 2, "rtoInitial": 1000, "rtomax": 1000, "rtomin": 1000, "x2cGwAddress": "0.0.0.0", "x2cServerLocalIpAddress": "0.0.0.0", "x2cServerLocalIpPrefix": 24, "xncClientLocalIpAddress": ["0.0.0.0"], "xncClientLocalIpPrefix": [24], "xncClientLocalPort": [38423], "xncGwAddress": ["0.0.0.0"], "xncServerLocalIpAddress": "0.0.0.0", "xncServerLocalIpPrefix": 24, "xncServerLocalPort": 38422}, "id": "0", "objectClass": "CUCP", "objectInstance": "0", "objectPairInstance": "0", "pLMNId": [{"MCC": "311", "MNC": "48"}], "peeParametersList": {"environmentType": "Outdoor", "equipmentType": "RRU", "powerInterface": "AC", "siteDescription": "Prestige Tech Park", "siteIdentification": "TECH PARK", "siteLatitude": 12.9781, "siteLongitude": 77.6653}, "userLabel": "GNBCUCPFunction", "vnfParametersList": {"autoScalable": false, "flavourId": "1", "vnfInstanceId": "0", "vnfdId": "0"}}], "MeasurementControl": [{"defaultFileBasedGP": 300, "defaultFileLocation": "/rsysfs/opt/radisys/O-RAN/O-GNB/PM/", "defaultFileReportingPeriod": 15, "defaultSamplingPeriod": 60, "defaultStreamBasedGP": 0, "defaultStreamTarget": "127.0.0.1:8080", "id": "CUCP", "pMAdministrativeState": "Unlocked"}], "NtfSubscriptionControl": [{"HeartbeatControl": [{"heartbeatNtfPeriod": 300, "id": "0", "triggerHeartbeatNtf": false}], "id": "0", "notificationFilter": "HEARTBEAT", "notificationRecipientAddress": "http://*************/heartbeat", "notificationType": "VES", "scope": [{"scopeType": "BASE_ONLY"}]}], "TraceJob": [{"administrativeState": "Unlocked", "id": "0", "tjTraceCollectionEntityAddress": "127.0.0.1", "tjTraceReference": "048547000001B301", "tmClientInterfaceName": "oam", "tmTceCaCert": "/opt/openssl/certs/ca/ca.cert", "tmTceClientIp": "127.0.0.1", "tmTceClientPort": 2345, "tmTceClientPrivKey": "/opt/openssl/key/client/client.key", "tmTceIsIpv6": 0, "tmTceServerPort": 8080, "tmTceTcpKeepCnt": 20, "tmTceTcpKeepIdle": 180, "tmTceTcpKeepIntvl": 60, "tmTceTcpUserTimeOut": 18}, {"administrativeState": "Unlocked", "id": "1", "tjTraceCollectionEntityAddress": "127.0.0.1", "tjTraceReference": "048547000001B301", "tmClientInterfaceName": "oam", "tmTceCaCert": "/opt/openssl/certs/ca/ca.cert", "tmTceClientIp": "127.0.0.1", "tmTceClientPort": 2345, "tmTceClientPrivKey": "/opt/openssl/key/client/client.key", "tmTceIsIpv6": 0, "tmTceServerPort": 8080, "tmTceTcpKeepCnt": 20, "tmTceTcpKeepIdle": 180, "tmTceTcpKeepIntvl": 60, "tmTceTcpUserTimeOut": 18}], "dnPrefix": "gnb", "id": "0", "locationName": "BLR", "objectClass": "ME", "objectInstance": "0", "objectPairInstance": "0", "userDefinedState": "DRAFT", "userLabel": "ORANCucp", "vendorName": "RSYS"}, "enabled": true}, "status": {"specHash": "17084884611101622773"}}}}}}