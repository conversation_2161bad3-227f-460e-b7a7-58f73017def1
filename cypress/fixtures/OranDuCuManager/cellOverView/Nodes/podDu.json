{"id": "GB-MARL-DU-0003", "pod": {"status": "OK", "detail": {"conditions": [{"last_probe_time": null, "last_transition_time": "2024-09-17T12:09:53+00:00", "message": null, "reason": null, "status": "True", "type": "PodReadyToStartContainers"}, {"last_probe_time": null, "last_transition_time": "2024-09-17T12:09:50+00:00", "message": null, "reason": null, "status": "True", "type": "Initialized"}, {"last_probe_time": null, "last_transition_time": "2024-09-17T12:10:05+00:00", "message": null, "reason": null, "status": "True", "type": "Ready"}, {"last_probe_time": null, "last_transition_time": "2024-09-17T12:10:05+00:00", "message": null, "reason": null, "status": "True", "type": "ContainersReady"}, {"last_probe_time": null, "last_transition_time": "2024-09-17T12:09:50+00:00", "message": null, "reason": null, "status": "True", "type": "PodScheduled"}], "container_statuses": [{"allocated_resources": null, "container_id": "containerd://d2fd536e878e2b9128fd00ae259969ce20443e9fcc905591c1f83bf118434789", "image": "europe-west2-docker.pkg.dev/da-rad-dev02-a3e1/da-rad--docker-repo/vran.rsys/du/du-l2:24.8.0-dev-195", "image_id": "europe-west2-docker.pkg.dev/da-rad-dev02-a3e1/da-rad--docker-repo/vran.rsys/du/du-l2@sha256:e2ba24b176be538f266363ba0d0817d6de356813bc763d801847e5e4089facb7", "last_state": {"running": null, "terminated": null, "waiting": null}, "name": "du-l2", "ready": true, "resources": null, "restart_count": 0, "started": true, "state": {"running": {"started_at": "2024-09-17T12:09:53+00:00"}, "terminated": null, "waiting": null}, "volume_mounts": null}], "ephemeral_container_statuses": null, "host_ip": "***************", "host_ips": [{"ip": "***************"}], "init_container_statuses": null, "message": null, "nominated_node_name": null, "phase": "Running", "pod_ip": "***********", "pod_ips": [{"ip": "***********"}], "qos_class": "<PERSON><PERSON><PERSON><PERSON>", "reason": null, "resize": null, "resource_claim_statuses": null, "start_time": "2024-09-17T12:09:50+00:00"}, "crs": {"DuCellConfig": {"status": {"specHash": "7281081246989863546"}, "content": {"apiVersion": "du.cell.config.radisys.com/v1alpha1", "kind": "DuCellConfig", "metadata": {"annotations": {"rel": "24.8.0-dev"}, "creationTimestamp": "2024-09-17T12:04:32Z", "generation": 3, "labels": {"site": "du-ansir-17-09"}, "name": "ducellconfig-du-ansir-17-09", "namespace": "default", "resourceVersion": "97591285", "uid": "4a480c58-820a-48db-9f7d-b1a04da91a28"}, "spec": {"duCellConfig": {"ME": {"GNBDUFunction": [{"NRCellDU": [{"administrativeState": "Unlocked", "arfcnDL": 503202, "arfcnSUL": 2079415, "arfcnUL": 503202, "bSChannelBwSUL": 1, "bSChannelBwUL": 1, "gnbCellDuVsCfg": {"XranConfig": {"DynamicMultiSectionEna": 0, "DynamicSectionEna": 0, "DynamicSectionEnaUL": 0, "EnableCp": 1, "EnableCsirsTrans": 0, "ExtBfwDl0": "0,0,0,0,0,0", "ExtBfwDl1": "0,0,0,0,0,0", "ExtBfwDl2": "0,0,0,0,0,0", "ExtBfwDl3": "0,0,0,0,0,0", "ExtBfwDl4": "0,0,0,0,0,0", "ExtBfwDl5": "0,0,0,0,0,0", "ExtBfwDl6": "0,0,0,0,0,0", "ExtBfwDl7": "0,0,0,0,0,0", "ExtBfwUl0": "0,0,0,0,0,0", "ExtBfwUl1": "0,0,0,0,0,0", "ExtBfwUl2": "0,0,0,0,0,0", "ExtBfwUl3": "0,0,0,0,0,0", "ExtBfwUl4": "0,0,0,0,0,0", "ExtBfwUl5": "0,0,0,0,0,0", "ExtBfwUl6": "0,0,0,0,0,0", "ExtBfwUl7": "0,0,0,0,0,0", "Gps_Alpha": 0, "Gps_Beta": 0, "MaxSectionsPerSlot": 6, "MaxSectionsPerSymbol": 6, "PrbElemDl0": "0,273,0,14,0,0,1,9,0,0,0", "PrbElemDl1": "50,25,0,14,1,1,0,16,1,0,0", "PrbElemDl2": "72,36,0,14,3,1,1,9,1,0,0", "PrbElemDl3": "144,48,0,14,4,1,1,9,1,0,0", "PrbElemDl4": "144,36,0,14,5,1,1,9,1,0,0", "PrbElemDl5": "180,36,0,14,6,1,1,9,1,0,0", "PrbElemDl6": "216,36,0,14,7,1,1,9,1,0,0", "PrbElemDl7": "252,21,0,14,8,1,1,9,1,0,0", "PrbElemSrs0": "0,273,0,14,0,0,1,9,0,0,0", "PrbElemSrs1": "0,273,0,14,0,0,1,9,0,0,0", "PrbElemSrs2": "72,36,0,14,3,1,1,9,1,0,0", "PrbElemSrs3": "108,36,0,14,4,1,1,9,1,0,0", "PrbElemSrs4": "144,36,0,14,5,1,1,9,1,0,0", "PrbElemSrs5": "180,36,0,14,6,1,1,9,1,0,0", "PrbElemSrs6": "216,36,0,14,7,1,1,9,1,0,0", "PrbElemSrs7": "252,21,0,14,8,1,1,9,1,0,0", "PrbElemUl0": "0,273,0,14,0,0,1,9,0,0,0", "PrbElemUl1": "0,273,0,14,0,0,1,8,0,0,0", "PrbElemUl2": "72,36,0,14,3,1,1,9,1,0,0", "PrbElemUl3": "108,36,0,14,4,1,1,9,1,0,0", "PrbElemUl4": "144,36,0,14,5,1,1,9,1,0,0", "PrbElemUl5": "180,36,0,14,6,1,1,9,1,0,0", "PrbElemUl6": "216,36,0,14,7,1,1,9,1,0,0", "PrbElemUl7": "252,21,0,14,8,1,1,9,1,0,0", "RadioTypeIndex": 0, "T1a_max_cp_dl": 470, "T1a_max_cp_ul": 429, "T1a_max_up": 196, "T1a_min_cp_dl": 258, "T1a_min_cp_ul": 285, "T1a_min_up": 50, "T2a_max_cp_dl": 429, "T2a_max_cp_ul": 429, "T2a_max_up": 428, "T2a_min_cp_dl": 285, "T2a_min_cp_ul": 285, "T2a_min_up": 71, "Ta3_max": 32, "Ta3_min": 20, "Ta4_max": 75, "Ta4_min": 0, "Tadv_cp_dl": 25, "band-sector-bitmask": 16128, "ccid-bitmask": 240, "dIAxCOffSet": 0, "nCCID": 0, "nPrbElemDl": 1, "nPrbElemSrs": 0, "nPrbElemUl": 1, "nPrimeRUPortIndex": 0, "nRFDesPrimMacAddress": "00:11:22:33:44:66", "nRFDesSecMacAddress": "00:11:22:33:44:66", "nRUPortNum": 1, "nSecondRUPortIndex": 0, "numCCPerRu": 1, "o-du-port-bitmask": 49152, "oRuType": 4, "oru0naecOffsetCsirs": 8, "oru_id": 0, "prachAxCOffSet": 0, "ru-port-bitmask": 15, "uIAxCOffSet": 0, "xRANNumDLPRBs": 0, "xRANNumULPRBs": 0, "xRANSFNWrap": 1, "xranCompHdrType": 0, "xranCompMethod": 1, "xranModCompEna": 0, "xranPrachCompMethod": 1, "xranPrachiqWidth": 9, "xraniqWidth": 9}, "cellAlarm": [{"alarmId": "ALARM_CELL_ADMIN_LOCKED", "alarmName": "ALARM_CELL_ADMIN_LOCKED", "thresholdCount": 0, "thresholdLevel": 0, "thresholdTimeInterval": 600}, {"alarmId": "ALARM_CELL_SETUP_FAIL", "alarmName": "ALARM_CELL_SETUP_FAIL", "thresholdCount": 0, "thresholdLevel": 0, "thresholdTimeInterval": 600}, {"alarmId": "ALARM_CELL_L1_COMM_FAIL", "alarmName": "ALARM_CELL_L1_COMM_FAIL", "thresholdCount": 0, "thresholdLevel": 0, "thresholdTimeInterval": 600}, {"alarmId": "ALARM_CELL_TTI_STRETCH", "alarmName": "ALARM_CELL_TTI_STRETCH", "thresholdCount": 0, "thresholdLevel": 0, "thresholdTimeInterval": 600}, {"alarmId": "ALARM_CELL_DL_PRB_UTILIZATION_HIGH", "alarmName": "ALARM_CELL_DL_PRB_UTILIZATION_HIGH", "thresholdCount": 2, "thresholdLevel": 80, "thresholdTimeInterval": 600}, {"alarmId": "ALARM_CELL_DL_PRB_UTILIZATION_LOW", "alarmName": "ALARM_CELL_DL_PRB_UTILIZATION_LOW", "thresholdCount": 2, "thresholdLevel": 20, "thresholdTimeInterval": 600}], "cellCenterThreshold": 50, "cellPathLossOffset": 50, "cellPfsCfgInfo": {"dlProactiveGrantTmr": "40ms", "fairnessCoeff": 5, "gbrServedRateCoeff": 5, "maxDlBoThresholdFactor": 100, "maxUlBoThresholdFactor": 100, "maxVonrPktSize": 512, "mbrServedRateCoeff": 5, "minDlBitRate": 1200000, "nr5qiCoeff": 5, "pdbCoeff": 5, "pfsMinBitRateDelta": 40, "totalGbrServedRatePriolvl": 100, "totalSlicePriolvl": 10, "totalUePfsPriolvl": 100, "tptCoEff": 5, "uePfsCoeff": 5, "ulProactiveGrantTmr": "20ms"}, "cellRsrpOffset": 50, "cellRsrpThreshold": 50, "cfgCmn": {"numDlSlot": 3, "numDlSlotP2": 4, "numDlSymbol": 12, "numDlSymbolP2": 0, "numUlSlot": 2, "numUlSlotP2": 0, "numUlSymbol": 0, "numUlSymbolP2": 0, "p2Pres": 1}, "csiRsCfgInfo": [{"cdmType": "CSI_RS_CDM_TYPE_FD_CDM2", "csiFreqNumCrb": 64, "csiFreqStartCrb": 0, "csiImElementPattern": {"csiImPatternType": "CSI_IM_PATTERN1", "csiImSubcarrierLocation": "CSI_IM_SC_LOCATION_S4", "csiImSymbolLocation": 13}, "csiRptPeriodicity": "csiRptPeriodicity_slots160", "csiRsDensity": "CSI_RS_DENSITY_ONE", "csiRsOffset": 17, "csiRsPeriodicity": "CSI_RS_RES_PRDCTY_SLOT160", "csiRsPwrControlOffsetSS": "db0", "csiRsRowType": "CSI_FREQ_DOM_ALLOC_ROW4", "dot5EvenOdd": "CSI_RS_DENSITY_DOT5_EVEN_RB", "firstSym": 13, "id": "0", "isCriRsrpEnable": false, "isCsiImEnable": true, "isCsiRsEnable": true, "n1n2": "R<PERSON>_CB_TYPE1_SINGLE_PANEL_RSTRCN_2_1", "otherRowType": "OTHER_FREQ_DOM_ALLOC_NONE", "reportCfgType": "REPORT_CFG_TYPE1", "rowBitmap": 1, "secondSym": 12, "secondSymPres": false, "typ1RptCfg": {"aperCsiEnable": true, "aperCsiScellEnable": false}, "typ2RptCfg": {"aperCsiEnable": true, "aperCsiScellEnable": false, "numberOfBeams": 2, "phaseAplhabetSize": 4, "subbandAmplitude": false}}, {"cdmType": "CSI_RS_CDM_TYPE_FD_CDM2", "csiFreqNumCrb": 64, "csiFreqStartCrb": 0, "csiImElementPattern": {"csiImPatternType": "CSI_IM_PATTERN1", "csiImSubcarrierLocation": "CSI_IM_SC_LOCATION_S4", "csiImSymbolLocation": 13}, "csiRptPeriodicity": "csiRptPeriodicity_slots160", "csiRsDensity": "CSI_RS_DENSITY_ONE", "csiRsOffset": 639, "csiRsPeriodicity": "CSI_RS_RES_PRDCTY_SLOT160", "csiRsPwrControlOffsetSS": "db0", "csiRsRowType": "CSI_FREQ_DOM_ALLOC_OTHER", "dot5EvenOdd": "CSI_RS_DENSITY_DOT5_EVEN_RB", "firstSym": 12, "id": "1", "isCriRsrpEnable": false, "isCsiImEnable": true, "isCsiRsEnable": false, "n1n2": "R<PERSON>_CB_TYPE1_SINGLE_PANEL_RSTRCN_2_1", "otherRowType": "OTHER_FREQ_DOM_ALLOC_ROW16", "reportCfgType": "REPORT_CFG_TYPE1", "rowBitmap": 51, "secondSym": 10, "secondSymPres": true, "typ1RptCfg": {"aperCsiEnable": true, "aperCsiScellEnable": false}, "typ2RptCfg": {"aperCsiEnable": true, "aperCsiScellEnable": false, "numberOfBeams": 2, "phaseAplhabetSize": 4, "subbandAmplitude": false}}], "csiTrsCfgInfo": {"isTrsEnable": false, "trsFirstSymbolInFirstSlot": 6, "trsFirstSymbolInSecondSlot": 6, "trsNumRb": 276, "trsOffset": 15, "trsPeriodicity": "sl80", "trsPwrControlOffsetSS": "db0", "trsStartRb": 0}, "deploymentMode": "SA", "dlCAConfigInfo": {"CaBoDistType": 1, "ScellActiveBoThr": 6400, "ScellActiveThr": 3, "ScellDeActiveBoThr": 1250, "ScellDeActiveThr": 8, "ScellGroupActDeact": 0}, "dlCfgCmn": {"bwpInactivityTimer": "BWP_INACTIVITY_TMR_IN_MS_MS2560", "dlBwpCfg": [{"bwpId": 0, "cntrlResourceSetSearchSpaceCfg": {"aggrLvl16Candidates": 0, "aggrLvl1Candidates": 8, "aggrLvl2Candidates": 6, "aggrLvl4Candidates": 0, "aggrLvl8Candidates": 0, "avgAggregationLvl": 2, "isCandidatesPerAggrLvlConfigurable": true, "type3SearchSpaceCfg": {"aggrLvl16Candidates": 0, "aggrLvl1Candidates": 2, "aggrLvl2Candidates": 2, "aggrLvl4Candidates": 2, "aggrLvl8Candidates": 0, "avgAggregationLvl": 2, "enableType3SearchSpace": true, "isCandidatesPerAggrLvlConfigurable": true}}, "cyclicPrefix": "Normal", "mu": "KHz30", "numRb": 273, "resourceAllocCfg": 1, "resourceAllocType": 1, "startRb": 0}], "dlFreqInfo": {"absArfcnSsb": 500106, "bSChannelBwDL": "40MHZ", "nrFreqBand": [41], "subCarrierCfg": [{"carrierBw": 273, "offsetToCarrier": 0, "subCarrierSpacing": "KHz30"}]}, "numCceRsvdForCmnPdcch": 1}, "dmrsDlCfg": {"dmrsAdditionalPosDl": "pos2", "dmrsTypeDl": "type1", "maxLenDl": "len1"}, "dmrsUlCfg": {"dmrsAdditionalPosUl": "pos1", "dmrsTypeUl": "type1", "maxLenUl": "len1", "numCdmGrpWithoutData": 2, "transPrecodingDisabledUl": {"istransPrecodingDisabled": true}, "transPrecodingEnabledUl": {"istransPrecodingEnabled": false, "nPUSCHId": 1, "seqGrpHopping": "disabled", "seqHopping": "enabled"}}, "enablePdschRateMatchCoreset": false, "enablePdschRateMatchCsiRs": false, "enableRateMatchSsbPbch": false, "featureSetBitMap0": 558901255, "featureSetBitMap1": 0, "freqRangeType": "FR1_LT_6", "isStartRntiPres": true, "l1-CfgInfo": {"mibParams": {"cellBarred": "notBarred", "intraFreqReselection": "allowed"}, "nDlCarrierK0": 1, "nUlCarrierK0": 1, "sib1Params": {"cellAccessInfo": {"pLMNIdentityList": [{"cellId": 1, "cellReservedOperatorUse": "notRese<PERSON>d", "pLMNIdList": [{"MCC": "311", "MNC": "48", "pLMNIdIndex": 1}], "pLMNIdentityInfoIndex": 1, "ranac": 1, "tacBitMap": 1}]}, "cellSelectionInfo": {"qQualMin": {"rsrpLvl": -30}, "qRxLevMin": {"rsrpLvl": -64}}, "ueTmr": {"n310": "N20", "n311": "N1", "t300": "MS1000", "t301": "MS300", "t304": "MS500", "t310": "MS2000", "t311": "MS3000", "t319": "MS1000"}}}, "ltenrCoTraficPatternType": {"activateSfn": 2, "bitWidthSize": 10, "cellId": 1, "dlTrafficPattern": 830, "isLteNrCoEnable": false, "isSulFreqMute": false, "ltePucchRbInEdge": 3, "mbSfnControlSym": 2, "sharingType": "UL_AND_DL_SHARING", "ulTrafficPattern": 830}, "macCfgCmn": {"bcchMcs": 0, "cellCentreUePlThreshold": 65, "cellEdgeUePlThreshold": 70, "cyclicPrefixType": "Normal", "dlBlerTolerance": 10, "dlBlockWindowSize": 1, "dlCcchCqi": 1, "dlDeltaMcsIms": 5, "dlDeltaMcsSrb": 5, "dlDeltaMcsVonr": 0, "dlLaStepdown": 30, "dlLaStepup": 3, "dlMinAckNackSamples": 20, "dlMinAcks2IncrStep": 3, "dlModulation": "QAM256", "dlNumAntPorts": 4, "dlRank": 4, "dlSeedOllaInterval": 80, "dlTimingWindowSize": 320, "dynamicRatThresholdFactor": 4, "enableDciFormat0_1": true, "enableUlPrbRandomization": true, "enable_pdsch_in_ssb_slot": true, "initialDlMcs": 5, "initialUlMcs": 0, "isPenaltyBasedNgbrSchedEnable": true, "lowDlcqiThreshold": 6, "lowUlcqiThreshold": 6, "maxAccumulatedTpc": 10, "maxDeltaK2": 5, "maxDlHqTx": 4, "maxDlMcs": 27, "maxDlUePerTTI": 16, "maxDlUeWithCePerTti": 2, "maxDlUeWithGbrPerTti": 12, "maxDlUeWithHighBoPerTti": 2, "maxDlUeWithImsPerTti": 2, "maxDlUeWithNonGbrPerTti": 16, "maxDlUeWithSrbPerTti": 16, "maxDlUeWithVoicePerTti": 6, "maxDlVonrTbHqTx": 4, "maxMsg3UePerTTI": 4, "maxMsg4HqTx": 4, "maxPathlossTh": 100, "maxRbUlProActGrant": 10, "maxTargetSinrIdx": 27, "maxTargetSinrRange": 2, "maxUlHqTx": 4, "maxUlMcs": 27, "maxUlUePerTTI": 16, "maxUlUeWithGbrPerTti": 12, "maxUlUeWithHighBsrPerTti": 2, "maxUlUeWithImsPerTti": 2, "maxUlUeWithNonGbrPerTti": 16, "maxUlUeWithSrbPerTti": 16, "maxUlUeWithVoicePerTti": 6, "maxUlVonrTbHqTx": 4, "mcsDecrementByStep1": 1, "mcsDecrementByStep2": 2, "minAccumulatedTpc": -10, "minDeltaK1": 4, "minDeltaK2": 4, "minPathlossTh": 75, "minTargetSinrIdx": 10, "minTargetSinrRange": 2, "nOflayerDecrement": 1, "nOfmcsDecrement": 2, "numCoOrdinateCells": 1, "numRxAnt": 4, "numSsb": 1, "numTxAnt": 4, "numUeRat0OptThreshold": 2, "ocnsConfig": {"ocnsLoadPercentage": 0, "ocnsMcs": 27, "ocnsRank": 4}, "pcchMcs": 0, "penaltyThresholdCoefficient": 1, "phrConfig": {"phrModeOtherCg": false, "phrPeriodicTimer": "SF20", "phrProhibitPeriodicTimer": "SF10", "phrTxPwrChangeFactor": "DB3", "phrType2OtherCell": false, "schdPhrHdl": true}, "preambleSize": 4, "preambleStart": 47, "rarMcs": 0, "rat0AllocFactor": 4, "ssPbchBurstSetSize": 1, "tUlSyncTime": "500ms", "taTmrInMs": "INFINITY", "tagId": 0, "targetSinrOffset": 5, "tpcUpdateInterval": 80, "ulBlerTolerance": 10, "ulBlockWindowSize": 10, "ulCcchCqi": 1, "ulDeltaMcsIms": 5, "ulDeltaMcsSr": 5, "ulDeltaMcsSrb": 5, "ulDeltaMcsVonr": 0, "ulLaStepdown": 30, "ulLaStepup": 3, "ulLyr1ToLyr2ThresholdMcs": 14, "ulLyr2ToLyr1ThresholdMcs": 8, "ulMinCrcSamples": 20, "ulMinCrcs2IncrStep": 3, "ulModulation": "QAM64", "ulNumOfAntPorts": 4, "ulProActGrantTimer": 20, "ulRank": 2, "ulRbLimitPerMcs": {"mcs0RbLimit": 273, "mcs1RbLimit": 273, "mcs2RbLimit": 273, "mcs3RbLimit": 273, "mcs4RbLimit": 273, "mcs5RbLimit": 273, "mcs6RbLimit": 273, "mcs7RbLimit": 273, "mcs8RbLimit": 273, "mcs9RbLimit": 273}, "ulSeedOllaInterval": 120, "ulTimingWindowSize": 1280, "vonrDlLaStepdown": 30, "vonrDlLaStepup": 3, "vonrUlLaStepdown": 30, "vonrUlLaStepup": 3}, "maxNumOfCaUes": 512, "maxNumUes": 512, "maxPdschPrb": 273, "maxPuschPrb": 273, "maxScellCsiPayloadSize": 11, "maxScellDlUePerTTI": 16, "measGap": {"measGapLength": "ms6", "measGapPatternId": 1, "measGapRepPeriodicity": "ms80", "measGapTimingAdvance": "ms0"}, "modeType": "TDD", "nCarrierIndex": 0, "nDlPreCoderEnable": true, "nEpreRatioOfPDCCHToSSB": 0, "nEpreRatioOfPDSCHToSSB": 0, "nEpreRatioOfPdcchDmrsToSSB": 0, "nEpreRatioOfPdschDmrsToSSB": 0, "nRCGI": {"nrCellId": "000fa4001", "nrCgipLMNId": {"MCC": "311", "MNC": "48"}}, "nSsbPwr": -27, "nTimingAdvanceOffset": "nTimingAdvance_25600", "nrMu": "KHz30", "numAvailPuschSym": 12, "numPucchCellGrp": 0, "pCCHcfgInfo": {"defaultPagCycle": "rf128", "monitorType": "sCS120KHZoneT", "n": "QUARTER_T", "ns": "one", "numOfPoPerPf": 1, "occasionOfPo": [1]}, "prbUsageStatsTimer": 30, "rfDlGainOffset": 27, "rlcCfg": {"eutraQosGrp": [{"qci": 1, "ulLcPriority": 4}, {"qci": 9, "ulLcPriority": 4}], "nrQfiGrp": [{"fiveqi": 0, "ulLcPriority": 4}, {"fiveqi": 1, "ulLcPriority": 4}, {"fiveqi": 2, "ulLcPriority": 4}, {"fiveqi": 5, "ulLcPriority": 4}, {"fiveqi": 8, "ulLcPriority": 4}, {"fiveqi": 9, "ulLcPriority": 4}]}, "scellNrMu": "KHz30", "servedPlmnList": [{"servedPlmnId": {"MCC": "311", "MNC": "48"}, "servedPlmnIdx": 1, "sliceList": [{"activeStatus": true, "defaultSlice": true, "dlprbPercentage": 100, "priority": 0, "sd": 0, "sliceIdx": 1, "sst": 1, "ulprbPercentage": 100}]}], "si-SchedInfo": {"sib2Periodcity": "rf128", "sib3Periodicity": "rf256", "sib4Periodicity": "rf512", "sib5Periodicity": "rf512", "sib6Periodicity": "rf512", "sib7Periodicity": "rf512", "sibs": [{"sibType": "DU_SIB_TYPE_2", "valueTag": 2}]}, "ssbSmtcDuration": "sf5", "ssbSmtcOffset": 0, "ssbSmtcPeriodicity": "sf20", "ulCfgCmn": {"addnlUlCoresetEnable": false, "ulBwpCfg": [{"avoidCmnPucchCyclicMux": true, "bwpId": 0, "cellEdgeUePercentage": 0, "cyclicPrefix": "Normal", "cyclicShift": {"F1cyclicShiftBitmap": 85, "HarqcyclicShiftBitmap": 65, "SrcyclicShiftBitmap": 1, "numOfF1CyclicShift": 4, "numOfHarqCyclicShift": 1, "numOfSrCyclicShift": 1}, "distributePucchInBwpEdges": 1, "f0SetMaxSize": "8", "f0f2SymLength": 1, "f1f3SymLength": 4, "hoppingId": 0, "maxCodeRate": "zeroDot15", "msg3PuschGrpHopping": false, "mu": "KHz30", "numRb": 273, "p0Nominal": -90, "pucchCmnCfgPres": true, "pucchGrpHopping": "neither", "pucchPwrCfg": {"deltaFpucchF0": 0, "deltaFpucchF1": 0, "deltaFpucchF2": 0, "deltaFpucchF3": 0, "deltaFpucchF4": 0, "p0PucchVal": 0, "p0nominal": -90}, "pucchResourceCmn": 0, "puschCmnCfgPres": true, "puschCodebookSubset": "nonCoherent", "puschPwrCfg": {"alpha": "ALL", "deltaMcsEnabled": false, "isAccumulated": true, "msg3alpha": "ALL", "msg3deltaPreamble": 0, "p0": 0, "p0nominal": -90}, "puschTransformPrecoder": false, "puschTxCfg": "codeBook", "rachCfgInfo": {"cbPreamblePerSsb": 48, "contentResolutionTmr": "sf64", "grpBPreambleCfg": {"msg3SizeGrpA": "b56", "numOfRachPreambleGrpA": 1}, "maxMsg3Tx": 4, "msg1Scs": "KHz30", "nTimingAdvanceOffset": "nTimingAdvance_25600", "preambleFormat": "ORAN_RACH_FORMAT_0", "rachGenCfg": {"highSpeedFlag": false, "msg1Fdm": 1, "msg1FreqStart": 0, "prachCfgIdx": 156, "prachFreqOffset": 0, "prachRestrictSet": 0, "preambleRcvdTgtPwr": -90, "preambleTransMax": "n10", "pwrRampingStep": "dB2", "rachRspWindow": "sl80", "zeroCorrelationZoneCfg": 6}, "restrictedSetCfg": "RACH_UNREST", "rootSeqType": "L139Int", "rootSeqVal": 1, "rsrpThresholdSsb": 0, "ssbsPerRach": "SSB_ONE", "totalNumOfRachPreamble": 64}, "resourceAllocCfg": 1, "resourceAllocType": 1, "simultaneousHarqAckCsi": true, "srPeriodicity": "sl40", "srsCfg": {"alpha": "ALL", "combCyclicShiftBitmap": 585, "combOffsetBitmap": 15, "groupOrSequenceHopping": "neither", "ktc": 4, "maxSrsUePerSlot": 8, "maxUeSrsCodebook": 600, "numRBs": 272, "numSrsSym": 1, "p0": -90, "srsResourceType": "periodic", "srsSymbolAssignment": 0}, "startRb": 0, "uciOnPusch": {"alphaScaling": 0, "avoidPuschBasedOnUciType": "avoidNone", "semiStaticBetaOffsetAckIdx1": 5, "semiStaticBetaOffsetAckIdx2": 7, "semiStaticBetaOffsetAckIdx3": 7, "semiStaticBetaOffsetCsiPart1Idx1": 14, "semiStaticBetaOffsetCsiPart1Idx2": 14, "semiStaticBetaOffsetCsiPart2Idx1": 14, "semiStaticBetaOffsetCsiPart2Idx2": 14}}], "ulFreqInfo": {"addtionalSpectrumEmission": 0, "bSChannelBwUl": "40MHZ", "freqShft7p5khz": "7P5KHZ_DISBL", "nrFreqBand": [41], "pMax": 23, "subCarrierCfg": [{"carrierBw": 273, "offsetToCarrier": 0, "subCarrierSpacing": "KHz30"}]}, "ulTargetCqi": {}}}, "id": "vendorA_ORUAA100_FR1918010111", "nCI": "000000001", "nRPCI": 11, "nRTAC": "0001", "objectClass": "NRCellDU", "objectInstance": "0", "objectPairInstance": "0", "pLMNId": [{"MCC": "311", "MNC": "48"}], "peeParametersList": {"environmentType": "Outdoor", "equipmentType": "RRU", "powerInterface": "AC", "siteDescription": "prestige tech park", "siteIdentification": "Tech Park", "siteLatitude": 12.9781, "siteLongitude": 77.6653}, "s-NSSAI": [1, 2], "userLabel": "NRCellDU", "vnfParametersList": {"autoScalable": false, "flavourId": "1", "vnfInstanceId": "0", "vnfdId": "1"}}], "id": "0"}], "id": "0"}}, "enabled": false}, "status": {"specHash": "7281081246989863546"}}}}}}