{"id": "GB-DEV2-GDCV-0001", "resources": [{"name": "denseair-gke-bmctl-edge-5-1-16-6", "nodes": [{"node": "dauk-mrl-d-gdcv-host05.denseair.net", "storage": {"allocatable_bytes": 676127046561, "capacity_bytes": 751252275200}, "containers": [{"container": "cu-cp-app", "cpu": {"cpu_usage_percentage": 2.06, "cpu_usage_nano_cores": 990260009}, "memory_usage": {"memory_usage_ki": 2166424, "memory_usage_percentage": 1.22}}, {"container": "cucp-oam-event-api", "cpu": {"cpu_usage_percentage": 0.18, "cpu_usage_nano_cores": 86712589}, "memory_usage": {"memory_usage_ki": 15200, "memory_usage_percentage": 0.01}}, {"container": "cu-up-app", "cpu": {"cpu_usage_percentage": 4.25, "cpu_usage_nano_cores": 2039156905}, "memory_usage": {"memory_usage_ki": 2873912, "memory_usage_percentage": 1.62}}, {"container": "cuup-oam-event-api", "cpu": {"cpu_usage_percentage": 0.16, "cpu_usage_nano_cores": 77031543}, "memory_usage": {"memory_usage_ki": 14344, "memory_usage_percentage": 0.01}}, {"container": "du-l2", "cpu": {"cpu_usage_percentage": 6.29, "cpu_usage_nano_cores": 3018984296}, "memory_usage": {"memory_usage_ki": 263292, "memory_usage_percentage": 0.15}}, {"container": "du-oam-cell-state-manager", "cpu": {"cpu_usage_percentage": 0.01, "cpu_usage_nano_cores": 4993664}, "memory_usage": {"memory_usage_ki": 7048, "memory_usage_percentage": 0}}, {"container": "du-oam-event-api", "cpu": {"cpu_usage_percentage": 0.02, "cpu_usage_nano_cores": 8694546}, "memory_usage": {"memory_usage_ki": 21676, "memory_usage_percentage": 0.01}}, {"container": "du-oam-hbmon", "cpu": {"cpu_usage_percentage": 0.01, "cpu_usage_nano_cores": 5259818}, "memory_usage": {"memory_usage_ki": 5952, "memory_usage_percentage": 0}}, {"container": "du-oam-syncmon", "cpu": {"cpu_usage_percentage": 0.01, "cpu_usage_nano_cores": 5589244}, "memory_usage": {"memory_usage_ki": 5904, "memory_usage_percentage": 0}}, {"container": "linuxptp-ptppmc", "cpu": {"cpu_usage_percentage": 0.01, "cpu_usage_nano_cores": 6562643}, "memory_usage": {"memory_usage_ki": 10368, "memory_usage_percentage": 0.01}}, {"container": "linuxptp-phc2sys", "cpu": {"cpu_usage_percentage": 0, "cpu_usage_nano_cores": 96580}, "memory_usage": {"memory_usage_ki": 2676, "memory_usage_percentage": 0}}, {"container": "linuxptp-ptp4l", "cpu": {"cpu_usage_percentage": 0.01, "cpu_usage_nano_cores": 3167992}, "memory_usage": {"memory_usage_ki": 8064, "memory_usage_percentage": 0}}, {"container": "vran-lcm", "cpu": {"cpu_usage_percentage": 0, "cpu_usage_nano_cores": 938241}, "memory_usage": {"memory_usage_ki": 41756, "memory_usage_percentage": 0.02}}], "conditions": [{"last_heartbeat_time": "2024-10-02T16:55:33Z", "last_transition_time": "2024-09-06T17:07:10Z", "message": "containerd is functioning properly", "reason": "NoFrequentContainerdRestart", "status": false, "type": "FrequentContainerdRestart"}, {"last_heartbeat_time": "2024-10-02T16:55:33Z", "last_transition_time": "2024-09-06T17:07:10Z", "message": "kubelet on the node is functioning properly", "reason": "KubeletIsHealthy", "status": false, "type": "KubeletUnhealthy"}, {"last_heartbeat_time": "2024-10-02T16:55:33Z", "last_transition_time": "2024-09-06T17:07:10Z", "message": "kernel has no deadlock", "reason": "KernelHasNoDeadlock", "status": false, "type": "KernelDeadlock"}, {"last_heartbeat_time": "2024-10-02T16:55:33Z", "last_transition_time": "2024-09-06T17:07:10Z", "message": "Filesystem is not read-only", "reason": "FilesystemIsNotReadOnly", "status": false, "type": "ReadonlyFilesystem"}, {"last_heartbeat_time": "2024-10-02T16:55:33Z", "last_transition_time": "2024-09-06T17:07:10Z", "message": "node is functioning properly", "reason": "NoFrequentUnregisterNetDevice", "status": false, "type": "FrequentUnregisterNetDevice"}, {"last_heartbeat_time": "2024-10-02T16:55:33Z", "last_transition_time": "2024-09-06T17:07:10Z", "message": "Container runtime on the node is functioning properly", "reason": "ContainerRuntimeIsHealthy", "status": false, "type": "ContainerRuntimeUnhealthy"}, {"last_heartbeat_time": "2024-10-02T16:55:33Z", "last_transition_time": "2024-09-06T17:07:10Z", "message": "kubelet is functioning properly", "reason": "NoFrequentKubeletRestart", "status": false, "type": "FrequentKubeletRestart"}, {"last_heartbeat_time": "2024-10-02T16:55:33Z", "last_transition_time": "2024-09-06T17:07:10Z", "message": "docker is functioning properly", "reason": "NoFrequentDockerRestart", "status": false, "type": "FrequentDockerRestart"}, {"last_heartbeat_time": "2024-10-02T16:55:51Z", "last_transition_time": "2024-08-29T17:23:05Z", "message": "kubelet has sufficient memory available", "reason": "KubeletHasSufficientMemory", "status": false, "type": "MemoryPressure"}, {"last_heartbeat_time": "2024-10-02T16:55:51Z", "last_transition_time": "2024-08-29T17:23:05Z", "message": "kubelet has no disk pressure", "reason": "KubeletHasNoDiskPressure", "status": false, "type": "DiskPressure"}, {"last_heartbeat_time": "2024-10-02T16:55:51Z", "last_transition_time": "2024-08-29T17:23:05Z", "message": "kubelet has sufficient PID available", "reason": "KubeletHasSufficientPID", "status": false, "type": "PIDPressure"}, {"last_heartbeat_time": "2024-10-02T16:55:51Z", "last_transition_time": "2024-09-06T15:16:43Z", "message": "kubelet is posting ready status", "reason": "KubeletReady", "status": true, "type": "Ready"}], "status": "OK"}, {"node": "dauk-mrl-d-gdcv-host04.denseair.net", "storage": {"allocatable_bytes": 167250729093, "capacity_bytes": 185834143744}, "containers": [{"container": "linuxptp-phc2sys", "cpu": {"cpu_usage_percentage": 0, "cpu_usage_nano_cores": 73895}, "memory_usage": {"memory_usage_ki": 448, "memory_usage_percentage": 0}}, {"container": "linuxptp-ptp4l", "cpu": {"cpu_usage_percentage": 0, "cpu_usage_nano_cores": 2805296}, "memory_usage": {"memory_usage_ki": 10404, "memory_usage_percentage": 0.01}}, {"container": "linuxptp-ptppmc", "cpu": {"cpu_usage_percentage": 0.01, "cpu_usage_nano_cores": 8820483}, "memory_usage": {"memory_usage_ki": 13568, "memory_usage_percentage": 0.01}}], "conditions": [{"last_heartbeat_time": "2024-10-02T16:56:12Z", "last_transition_time": "2024-09-18T09:23:44Z", "message": "node is functioning properly", "reason": "NoFrequentUnregisterNetDevice", "status": false, "type": "FrequentUnregisterNetDevice"}, {"last_heartbeat_time": "2024-10-02T16:56:12Z", "last_transition_time": "2024-09-18T09:23:44Z", "message": "kernel has no deadlock", "reason": "KernelHasNoDeadlock", "status": false, "type": "KernelDeadlock"}, {"last_heartbeat_time": "2024-10-02T16:56:12Z", "last_transition_time": "2024-09-18T09:23:44Z", "message": "Filesystem is not read-only", "reason": "FilesystemIsNotReadOnly", "status": false, "type": "ReadonlyFilesystem"}, {"last_heartbeat_time": "2024-10-02T16:56:12Z", "last_transition_time": "2024-09-18T09:23:54Z", "message": "kubelet on the node is functioning properly", "reason": "KubeletIsHealthy", "status": false, "type": "KubeletUnhealthy"}, {"last_heartbeat_time": "2024-10-02T16:56:12Z", "last_transition_time": "2024-09-18T09:23:44Z", "message": "Container runtime on the node is functioning properly", "reason": "ContainerRuntimeIsHealthy", "status": false, "type": "ContainerRuntimeUnhealthy"}, {"last_heartbeat_time": "2024-10-02T16:56:12Z", "last_transition_time": "2024-09-18T09:23:44Z", "message": "kubelet is functioning properly", "reason": "NoFrequentKubeletRestart", "status": false, "type": "FrequentKubeletRestart"}, {"last_heartbeat_time": "2024-10-02T16:56:12Z", "last_transition_time": "2024-09-18T09:23:44Z", "message": "docker is functioning properly", "reason": "NoFrequentDockerRestart", "status": false, "type": "FrequentDockerRestart"}, {"last_heartbeat_time": "2024-10-02T16:56:12Z", "last_transition_time": "2024-09-18T09:23:44Z", "message": "containerd is functioning properly", "reason": "NoFrequentContainerdRestart", "status": false, "type": "FrequentContainerdRestart"}, {"last_heartbeat_time": "2024-10-02T16:54:04Z", "last_transition_time": "2024-09-18T09:23:45Z", "message": "kubelet has sufficient memory available", "reason": "KubeletHasSufficientMemory", "status": false, "type": "MemoryPressure"}, {"last_heartbeat_time": "2024-10-02T16:54:04Z", "last_transition_time": "2024-09-18T09:23:45Z", "message": "kubelet has no disk pressure", "reason": "KubeletHasNoDiskPressure", "status": false, "type": "DiskPressure"}, {"last_heartbeat_time": "2024-10-02T16:54:04Z", "last_transition_time": "2024-09-18T09:23:45Z", "message": "kubelet has sufficient PID available", "reason": "KubeletHasSufficientPID", "status": false, "type": "PIDPressure"}, {"last_heartbeat_time": "2024-10-02T16:54:04Z", "last_transition_time": "2024-09-18T09:23:45Z", "message": "kubelet is posting ready status", "reason": "KubeletReady", "status": true, "type": "Ready"}], "status": "OK"}], "status": "OK", "ptpstate": {"kind": "PtpState", "metadata": {"creationTimestamp": "2024-09-02T11:56:29Z", "name": "ptpstate-object"}, "spec": {"clockStatus": "LOCKED"}}, "alarms": []}]}