[{"node_serial_no": "dauk-mrl-d-cav2", "component_type": "SERVER", "node_type": "MeshRoot", "lifecycle": "DECOMMISSIONED", "version": "RHEL 8.8", "manifest_type": "server", "node_id": "dauk-mrl-d-cav2", "roles": [], "site_id": 11, "site_name": "<PERSON><PERSON> Test", "country_code": "GBR", "country_name": "United Kingdom", "region_id": 4, "region_code": "DEV2", "region_name": "Marlow Development"}, {"node_serial_no": "dauk-mrl-d-dev-host01", "component_type": "SERVER", "node_type": "MeshRoot", "lifecycle": "FACTORY", "version": "RHEL 8.7", "manifest_type": "server", "node_id": "GB-DEV2-MR-0001", "roles": ["VSR"], "site_id": 11, "site_name": "<PERSON><PERSON> Test", "country_code": "GBR", "country_name": "United Kingdom", "region_id": 4, "region_code": "DEV2", "region_name": "Marlow Development"}, {"node_serial_no": "dauk-mrl-k-gdcv-host01t", "component_type": "SERVER", "node_type": "MeshRoot", "lifecycle": "FACTORY", "version": "Red Hat 8.5.0-16", "manifest_type": "server", "node_id": "GB-MARL-MR-0001", "roles": ["PDN"], "site_id": 11, "site_name": "<PERSON><PERSON> Test", "country_code": "GBR", "country_name": "United Kingdom", "region_id": 4, "region_code": "DEV2", "region_name": "Marlow Development"}, {"node_serial_no": "83322401503852", "component_type": "UPS", "node_type": "Power", "lifecycle": "STAGING", "version": "VERFW:01955.04", "manifest_type": "power", "node_id": "GB-DEV2-POWER-0001", "roles": ["UPS"], "site_id": 7, "site_name": "CoMP Lab", "country_code": "GBR", "country_name": "United Kingdom", "region_id": 4, "region_code": "DEV2", "region_name": "Marlow Development"}, {"node_serial_no": "dauk-mrl-k-gdcv-host02", "component_type": "SERVER", "node_type": "Server", "lifecycle": "OPERATIONAL", "version": "RedHat-8.9", "manifest_type": "server", "node_id": "GB-MARL-SERV-0001", "roles": [], "site_id": 16, "site_name": "Radisys Room", "country_code": "GBR", "country_name": "United Kingdom", "region_id": 1, "region_code": "MARL", "region_name": "<PERSON><PERSON>"}, {"node_serial_no": "dauk-mrl-att-poc04", "component_type": "SERVER", "node_type": "Server", "lifecycle": "OPERATIONAL", "version": "RedHat-8.9", "manifest_type": "server", "node_id": "GB-MR4G-SERV-0001", "roles": [], "site_id": 13, "site_name": "Marlow Lab 4G 1", "country_code": "GBR", "country_name": "United Kingdom", "region_id": 6, "region_code": "MR4G", "region_name": "Marlow 4G"}, {"node_serial_no": "dauk-mrl-d-gdcv-host05", "component_type": "SERVER", "node_type": "Server", "lifecycle": "OPERATIONAL", "version": "RedHat-8.9", "manifest_type": "server", "node_id": "GB-MARL-SERV-0003", "roles": [], "site_id": 16, "site_name": "Radisys Room", "country_code": "GBR", "country_name": "United Kingdom", "region_id": 1, "region_code": "MARL", "region_name": "<PERSON><PERSON>"}, {"node_serial_no": "dauk-mrl-d-gdcv-host04", "component_type": "SERVER", "node_type": "Server", "lifecycle": "OPERATIONAL", "version": "RedHat-8.7", "manifest_type": "server", "node_id": "GB-MARL-SERV-0002", "roles": [], "site_id": 16, "site_name": "Radisys Room", "country_code": "GBR", "country_name": "United Kingdom", "region_id": 1, "region_code": "MARL", "region_name": "<PERSON><PERSON>"}, {"node_serial_no": "dauk-mrl-d-gdcv-host06", "component_type": "SERVER", "node_type": "Server", "lifecycle": "OPERATIONAL", "version": "RedHat-8.7", "manifest_type": "server", "node_id": "GB-MARL-SERV-0004", "roles": [], "site_id": 16, "site_name": "Radisys Room", "country_code": "GBR", "country_name": "United Kingdom", "region_id": 1, "region_code": "MARL", "region_name": "<PERSON><PERSON>"}, {"node_serial_no": "9XJKTN3-dauk-mrl-d-cav2-kvm-druid", "component_type": "SERVER", "node_type": "Server-VM", "lifecycle": "STAGING", "version": "RHEL 8.8", "manifest_type": "server", "node_id": "GB-DEV2-VM-0001", "roles": ["DU"], "site_id": 13, "site_name": "Marlow Lab 4G 1", "country_code": "GBR", "country_name": "United Kingdom", "region_id": 6, "region_code": "MR4G", "region_name": "Marlow 4G"}, {"node_serial_no": "HGV40C3-dauk-mrl-att-druid-nhe", "component_type": "SERVER", "node_type": "Server-VM", "lifecycle": "OPERATIONAL", "version": "RedHat-8.9", "manifest_type": "server", "node_id": "GB-MR4G-VM-0002", "roles": ["NHE"], "site_id": 13, "site_name": "Marlow Lab 4G 1", "country_code": "GBR", "country_name": "United Kingdom", "region_id": 6, "region_code": "MR4G", "region_name": "Marlow 4G"}, {"node_serial_no": "GP081D3-dauk-mrl-green-druid", "component_type": "SERVER", "node_type": "Server-VM", "lifecycle": "OPERATIONAL", "version": "RedHat-8.9", "manifest_type": "server", "node_id": "GB-MR4G-VM-0001", "roles": ["CORE"], "site_id": 13, "site_name": "Marlow Lab 4G 1", "country_code": "GBR", "country_name": "United Kingdom", "region_id": 6, "region_code": "MR4G", "region_name": "Marlow 4G"}, {"node_serial_no": "DAUK-MRL-BLACK-VEN1-RX", "component_type": "FIBROLAN", "node_type": "Switch", "lifecycle": "COMMISSIONING", "version": "8.23.1.1-B9", "manifest_type": "network", "node_id": "GB-MARL-SW-0002", "roles": [], "site_id": 3, "site_name": "Marlow Lab 1", "country_code": "GBR", "country_name": "United Kingdom", "region_id": 1, "region_code": "MARL", "region_name": "<PERSON><PERSON>"}, {"node_serial_no": "falcon-nr35-st", "component_type": "FIBROLAN", "node_type": "Switch", "lifecycle": "COMMISSIONING", "version": "********", "manifest_type": "network", "node_id": "GB-DEV2-SW-0001", "roles": ["NTP", "SW"], "site_id": 8, "site_name": "<PERSON><PERSON> Test", "country_code": "GBR", "country_name": "United Kingdom", "region_id": 4, "region_code": "DEV2", "region_name": "Marlow Development"}, {"node_serial_no": "GREEN-FALCON-1", "component_type": "FIBROLAN", "node_type": "Switch", "lifecycle": "OPERATIONAL", "version": "********", "manifest_type": "network", "node_id": "GB-MARL-SW-0001", "roles": ["NTP"], "site_id": 8, "site_name": "<PERSON><PERSON> Test", "country_code": "GBR", "country_name": "United Kingdom", "region_id": 4, "region_code": "DEV2", "region_name": "Marlow Development"}, {"node_serial_no": "DAUK-MRL-SYSV-SW1", "component_type": "JUNIPER", "node_type": "Switch", "lifecycle": "COMMISSIONING", "version": "21.4R3.15", "manifest_type": "network", "node_id": "GB-DEV2-SW-0002", "roles": [], "site_id": 13, "site_name": "Marlow Lab 4G 1", "country_code": "GBR", "country_name": "United Kingdom", "region_id": 6, "region_code": "MR4G", "region_name": "Marlow 4G"}]