[{"type": "Lock", "uid": "0861d5de-0f42-4761-90ba-c5125d34e589", "prior_uid": null, "node_id": "E8587200F750", "name": "", "context": null, "resource": "E8587200F750", "priority": 0, "phase": "Initial configuration", "parameters": {"cell_id": null, "node_id": "E8587200F750", "serial_nr": "E8587200F750", "manager_instance": "acp_marlow_4g"}, "description": "Test Lock an Airspan node after administration", "created_at": "2024-12-30T21:37:16.530268Z"}]