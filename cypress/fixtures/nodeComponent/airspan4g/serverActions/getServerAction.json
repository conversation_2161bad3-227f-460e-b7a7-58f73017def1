[{"type": "Unlock", "uid": "0861d5de-0f42-4761-90ba-c5125d34e589", "prior_uid": null, "node_id": "E8587200F750", "name": "", "context": null, "resource": "E8587200F750", "priority": 0, "phase": "Initial configuration", "parameters": {"cell_id": null, "node_id": "E8587200F750", "serial_nr": "E8587200F750", "manager_instance": "acp_marlow_4g"}, "description": "Lock an Airspan node after administration", "created_at": "2024-12-30T21:37:16.530268Z", "info": [{"uid": "0eb05c4b-1e74-4c53-ab29-31672cbe37ad", "operation_uid": "0861d5de-0f42-4761-90ba-c5125d34e589", "progress": "Results available", "data": {"detail": "ACP: [ acp_marlow_4g ], node: [ E8587200F750 ] unlocked successfully"}, "message": "", "updated_at": "2024-12-30T21:37:27.437613Z"}, {"uid": "3b3a0b66-7294-4f8a-925a-2d7dd209d6b7", "operation_uid": "0861d5de-0f42-4761-90ba-c5125d34e589", "progress": "Active and running", "data": {}, "message": "", "updated_at": "2024-12-30T21:37:21.059346Z"}, {"uid": "b6a72588-7368-4574-bfec-c9d782249ea7", "operation_uid": "0861d5de-0f42-4761-90ba-c5125d34e589", "progress": "Created inactive", "data": {}, "message": "", "updated_at": "2024-12-30T21:37:21.053910Z"}]}]