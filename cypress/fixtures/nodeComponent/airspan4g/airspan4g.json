{"id": "E8587200F750", "airspan_4g_node": {"id": 197, "nodeName": "AS1030  ATT 1588 Shelf 6", "enbConfig": {"managedMode": "Managed", "autoHardwareSwap": "Disabled", "name": "AS1030  ATT 1588 Shelf 6", "hardware": "AirSpeed 1030", "description": "DHCP / PKI - ************** / AS1030-1", "site": "Auto Discovery Site", "region": "Auto Discovery Region", "latitude": 51.573789, "longitude": -0.759728, "nbifEventAlarmForwarding": "Enabled", "eNodeBType": "Multi-Carrier HeNB", "heNodeBName": "AS1030_501", "eNodeBID": 269501, "pnpHardwareId": "E8587200F750", "lteCellList": [{"cellNumber": "1", "cellIdentityHex": "000041C", "prachRsi": 0, "trackingAreaCode": 10, "lsmEmergencyVolteSupport": false, "prachFreqOffset": 4, "cellAdvancedConfigurationProfile": "VMO2_SR15.2 AirSpeed Defaults_256QAM", "radioProfile": {"id": 59, "profileType": "UserDefined", "name": "ATT_Test_SR19_AS1030_10db_20Mhz_FC2_Ch6", "hardwareCategory": "AirSpeed", "cbrsEnabled": true, "cbrsFreqRange": "Custom", "cbrsLowFreq": 3650, "cbrsHighFreq": 3670, "palPermitted": false, "duplexMode": "TDD", "bandwidthMhz": "20", "mfbiEnabled": false, "frameConfig": "DL60UL20SP20", "txPower": 10, "rmMode": "Disabled", "ssfType": "SSF7", "addlSpectrumEmission": "NS01", "ecidMode": "Enabled", "ecidTimer": 1000, "otdoaMode": "Disabled", "isPreferredCbrsFreqEnabled": false, "cbrsChannelPriority": "None", "cbrsBand": "B48", "periodicSpectrumInquiry": false, "cbrsRfScanning": "Disabled"}, "mobilityProfile": "SR17.0 AirSpeed", "embmsProfile": {"id": 8, "profileType": "System", "name": "SR15.2 AirSpeed Disabled", "hardwareCategory": "AirSpeed", "enabled": false}, "trafficManagementProfile": {"id": 8, "profileType": "System", "name": "SR15.5 AirSpeed Defaults", "hardwareCategory": "AirSpeed", "loadSheddingMode": "Disabled", "dataInactivityTimer": 4, "qci1And2InactivityTimer": 35, "volteMutingRedirectionMode": "Disabled"}, "callTraceProfile": {"id": 8, "profileType": "System", "name": "AirSpeed Call Trace Disabled", "hardwareCategory": "AirSpeed", "description": "", "callTraceEnabled": "Disabled", "ueTraceEnabled": "Disabled"}, "cellBarringPolicy": "<PERSON><PERSON><PERSON><PERSON>", "isEnabled": true, "isDiscovered": true, "csfbCdma2kCellParam": {}, "emergencyAreaIds": {"eaids": []}, "csfbCdma2kSib8Param": {"multipleSid": "Disabled", "multipleNid": "Disabled", "homeReg": "Disabled", "foreignSidReg": "Disabled", "foreignNidReg": "Disabled", "parameterReg": "Disabled", "powerUpReg": "Disabled", "powerDownReg": "Enabled"}, "csfbCdma2kMobilityParam": {"mobilityParametersOctet": "00", "sidIncluded": false, "nidIncluded": false, "multSidsIncluded": false, "multNidsIncluded": false, "regZoneIncluded": false, "totalZonesIncluded": false, "zoneTimerIncluded": false, "packetZoneIdIncluded": false, "pzIdHystParametersIncluded": false, "pRevIncluded": false, "minPRevIncluded": false, "negSlotCycleIndexSupIncluded": false, "encryptModeIncluded": false, "encSupportedIncluded": false, "sigEncryptSupIncluded": false, "msgIntegritySupIncluded": false, "sigIntegritySupInclIncluded": false, "sigIntegritySupIncluded": false, "authIncluded": false, "maxNumAltSoIncluded": false, "useSyncIdIncluded": false, "msInitPosLocSupIndIncluded": false, "mobQosIncluded": false, "bandClassInfoReqIncluded": false, "bandClassIncluded": false, "bypassRegIndIncluded": false, "altBandClassIncluded": false, "maxAddServInstanceIncluded": false, "homeRegIncluded": false, "forSidRegIncluded": false, "forNidRegIncluded": false, "powerUpRegIncluded": false, "powerDownRegIncluded": false, "parameterRegIncluded": false, "regPrdIncluded": false, "regDistIncluded": false, "prefMsidTypeIncluded": false, "extPrefMsidTypeIncluded": false, "meidReqdIncluded": false, "mccIncluded": false, "imsi1112Included": false, "imsiTSupportedIncluded": false, "reconnectMsgIndIncluded": false, "rerModeSupportedIncluded": false, "tkzModeSupportedIncluded": false, "tkzIdIncluded": false, "pilotReportIncluded": false, "sdbSupportedIncluded": false, "autoFcsoAllowedIncluded": false, "sdbInRcnmIndIncluded": false, "fpcFchIncluded": false, "tAddIncluded": false, "pilotIncIncluded": false, "randIncluded": false, "lpSecIncluded": false, "ltmOffIncluded": false, "dayLtIncluded": false, "gcsnal2AckTimerIncluded": false, "gcsnaSequenceContextTimerIncluded": false}, "csfb2gRimParams": {}, "csgMode": "Open", "cbsdDetails": {"fccId": "PIDAS1030A", "callSign": "", "category": "B", "antennaGain": 10, "cpiInfoSource": "SAS", "isMeasurementCapability": false, "isEnhancedGroup": false}}, {"cellNumber": "2", "cellIdentityHex": "", "prachRsi": null, "trackingAreaCode": null, "lsmEmergencyVolteSupport": null, "prachFreqOffset": 4, "cellAdvancedConfigurationProfile": "VMO2_SR15.2 AirSpeed Defaults_256QAM", "radioProfile": {"id": 61, "profileType": "UserDefined", "name": "ATT_Test_SR19_AS1030_10db_20Mhz_FC2_Ch5", "hardwareCategory": "AirSpeed", "cbrsEnabled": true, "cbrsFreqRange": "Custom", "cbrsLowFreq": 3630, "cbrsHighFreq": 3650, "palPermitted": false, "duplexMode": "TDD", "bandwidthMhz": "20", "mfbiEnabled": false, "frameConfig": "DL60UL20SP20", "txPower": 10, "rmMode": "Disabled", "ssfType": "SSF7", "addlSpectrumEmission": "NS01", "ecidMode": "Enabled", "ecidTimer": 1000, "otdoaMode": "Disabled", "isPreferredCbrsFreqEnabled": false, "cbrsChannelPriority": "None", "cbrsBand": "B48", "periodicSpectrumInquiry": false, "cbrsRfScanning": "Disabled"}, "mobilityProfile": "SR17.0 AirSpeed", "embmsProfile": {"id": 8, "profileType": "System", "name": "SR15.2 AirSpeed Disabled", "hardwareCategory": "AirSpeed", "enabled": false}, "trafficManagementProfile": {"id": 8, "profileType": "System", "name": "SR15.5 AirSpeed Defaults", "hardwareCategory": "AirSpeed", "loadSheddingMode": "Disabled", "dataInactivityTimer": 4, "qci1And2InactivityTimer": 35, "volteMutingRedirectionMode": "Disabled"}, "callTraceProfile": {"id": 8, "profileType": "System", "name": "AirSpeed Call Trace Disabled", "hardwareCategory": "AirSpeed", "description": "", "callTraceEnabled": "Disabled", "ueTraceEnabled": "Disabled"}, "cellBarringPolicy": null, "isEnabled": true, "isDiscovered": true, "csfbCdma2kCellParam": {}, "emergencyAreaIds": {}, "csfbCdma2kSib8Param": {"multipleSid": "Disabled", "multipleNid": "Disabled", "homeReg": "Disabled", "foreignSidReg": "Disabled", "foreignNidReg": "Disabled", "parameterReg": "Disabled", "powerUpReg": "Disabled", "powerDownReg": "Enabled"}, "csfbCdma2kMobilityParam": {"mobilityParametersOctet": "00", "sidIncluded": false, "nidIncluded": false, "multSidsIncluded": false, "multNidsIncluded": false, "regZoneIncluded": false, "totalZonesIncluded": false, "zoneTimerIncluded": false, "packetZoneIdIncluded": false, "pzIdHystParametersIncluded": false, "pRevIncluded": false, "minPRevIncluded": false, "negSlotCycleIndexSupIncluded": false, "encryptModeIncluded": false, "encSupportedIncluded": false, "sigEncryptSupIncluded": false, "msgIntegritySupIncluded": false, "sigIntegritySupInclIncluded": false, "sigIntegritySupIncluded": false, "authIncluded": false, "maxNumAltSoIncluded": false, "useSyncIdIncluded": false, "msInitPosLocSupIndIncluded": false, "mobQosIncluded": false, "bandClassInfoReqIncluded": false, "bandClassIncluded": false, "bypassRegIndIncluded": false, "altBandClassIncluded": false, "maxAddServInstanceIncluded": false, "homeRegIncluded": false, "forSidRegIncluded": false, "forNidRegIncluded": false, "powerUpRegIncluded": false, "powerDownRegIncluded": false, "parameterRegIncluded": false, "regPrdIncluded": false, "regDistIncluded": false, "prefMsidTypeIncluded": false, "extPrefMsidTypeIncluded": false, "meidReqdIncluded": false, "mccIncluded": false, "imsi1112Included": false, "imsiTSupportedIncluded": false, "reconnectMsgIndIncluded": false, "rerModeSupportedIncluded": false, "tkzModeSupportedIncluded": false, "tkzIdIncluded": false, "pilotReportIncluded": false, "sdbSupportedIncluded": false, "autoFcsoAllowedIncluded": false, "sdbInRcnmIndIncluded": false, "fpcFchIncluded": false, "tAddIncluded": false, "pilotIncIncluded": false, "randIncluded": false, "lpSecIncluded": false, "ltmOffIncluded": false, "dayLtIncluded": false, "gcsnal2AckTimerIncluded": false, "gcsnaSequenceContextTimerIncluded": false}, "csfb2gRimParams": {}, "csgMode": "Open", "cbsdDetails": {"fccId": "PIDAS1030A", "callSign": "", "category": "B", "antennaGain": 10, "cpiInfoSource": "SAS", "isMeasurementCapability": false, "isEnhancedGroup": false, "isFrequencyReuse": false}}], "altitude": 46, "systemDefaultProfile": "SR20.5v2 AirSpeed system defaults", "advancedConfigProfile": "SR15.2 AirSpeed Defaults", "networkProfile": "PnP_Test_SR19_AirSpeed_NHE_RED", "syncProfile": "VMO2_Test_SR15.0v2 AS1032 1588 w GPS via SFP failover", "securityProfile": "SR15.2 AirSpeed Default", "sonProfile": "SR14.2 AirSpeed Disabled_AutoPCI-RSI_TL", "managementProfile": "VMO2_Test_SR17.0 AirSpeed: PKI 5 mins_Fault", "multiCellProfile": "AirSpeed Default Enabled", "neighbourProfile": "SR15.5v1 AirSpeed Neighbour Management Profile", "faultManagementProfile": "SR17.5 AirSpeed Default", "cellToUse": "MultiCell", "interfaceToUseForManagement": "Management", "managementIpAddress": "***********", "managementSubnetMask": "***************", "isS1CInterfaceEnabled": "Disabled", "interfaceToUseForS1C": "S1C", "s1CIpAddress": "0.0.0.0", "s1CSubnetMask": "0.0.0.0", "isS1UInterfaceEnabled": "Disabled", "interfaceToUseForS1U": "S1U", "s1UIpAddress": "0.0.0.0", "s1USubnetMask": "0.0.0.0", "isX2CInterfaceEnabled": "Disabled", "interfaceToUseForX2C": "X2C", "x2CIpAddress": "0.0.0.0", "x2CSubnetMask": "0.0.0.0", "isX2UInterfaceEnabled": "Disabled", "interfaceToUseForX2U": "X2U", "x2UIpAddress": "0.0.0.0", "x2USubnetMask": "0.0.0.0", "isCallTraceInterfaceEnabled": "Disabled", "interfaceToUseForCallTrace": "CallTraceServer", "callTraceIpAddress": "0.0.0.0", "callTraceSubNetMask": "0.0.0.0", "isPtpSlaveInterfaceEnabled": "Disabled", "interfaceToUseForPtpSlave": "1588Ptp", "ptpSlaveIpAddress": "0.0.0.0", "ptpSlaveSubnetMask": "0.0.0.0", "isCSonServerInterfaceEnabled": "Disabled", "interfaceToUseForCSonServer": "CSonServer", "cSonServerIpAddress": "0.0.0.0", "cSonServerSubnetMask": "0.0.0.0", "isM2InterfaceEnabled": "Disabled", "interfaceToUseForM2": "M2", "m2IpAddress": "0.0.0.0", "m2SubnetMask": "0.0.0.0", "isM1InterfaceEnabled": "Disabled", "interfaceToUseForM1": "M1", "m1IpAddress": "0.0.0.0", "m1SubnetMask": "0.0.0.0", "isTwampSenderInterfaceEnabled": "Disabled", "interfaceToUseForTwampSender": "TwampSender", "twampSenderIpAddress": "0.0.0.0", "twampSenderSubNetMask": "0.0.0.0", "isS1CSeGwInterfaceEnabled": "Disabled", "interfaceToUseForS1CSeGw": "SeGW1", "s1CSeGwIpAddress": "0.0.0.0", "s1CSeGwSubnetMask": "0.0.0.0", "isS1USeGwInterfaceEnabled": "Disabled", "interfaceToUseForS1USeGw": "SeGW2", "s1USeGwIpAddress": "0.0.0.0", "s1USeGwSubnetMask": "0.0.0.0", "ipRouteListContainer": {"ipRouteList": []}, "isEnbNeighbourManagementGroupEnabled": false, "isAutoX2ControlForNeighboursEnabled": true, "isX2ConfigurationUpdateForNeighboursEnabled": true, "defaultX2ControlStateForNeighbours": "Automatic", "ledMode": "Enabled"}, "enbSnmpDetail": {"SnmpPort": 161, "SnmpTimeoutInMilliSec": 5000, "SnmpVersion": "v2C", "SnmpReadContext": "DenseAir", "SnmpWriteContext": "D3ns341r321!", "Snmpv3Access": null, "Snmpv3Password": null}, "platformVersion": "*************", "applicationVersion": "*************", "status": "UNKNOWN", "reason": "CBSD State=Authorized, SAS Response=Success [heartbeat], CBSD ID=PIDAS1030A/332d0c5e3212bc0a6761e60dda303fa1b5b6ed60, EARFCN=56340, Bandwidth=20 MHz, Tx Power=10 dBm"}}