{"id": "GB-MR4G-VM-0002", "server": {"server_id": "HGV40C3-dauk-mrl-att-druid-nhe", "name": "dauk-mrl-att-druid-nhe", "ip_address": "*************", "node_type": "Server-VM", "timing_type": "NTP", "cluster_name": null, "access_method": "ssh", "last_attempted": "2025-01-06T13:52:00.125683Z", "last_contacted": "2025-01-06T13:52:00.125683Z", "subsystems": [{"name": "ptp", "status": "UNKNOWN", "cause": "PTP Unknown Status", "reason": "cluster is not available", "repairs": [""], "results": null}, {"name": "memory", "status": "OK", "cause": "Mem used is within the acceptable range", "reason": "Mem Used%: 5.38% is below threshold: 85.0%", "repairs": [""], "results": {"Mem": {"total": "31888MB", "used": "1716MB", "free": "26364MB", "shared": "1562MB", "buff/cache": "3806MB", "available": "28150MB"}, "Swap": {"total": "0MB", "used": "0MB", "free": "0MB", "shared": null, "buff/cache": null, "available": null}}}, {"name": "ntp", "status": "OK", "cause": "Report", "reason": null, "repairs": [""], "results": {"Local_time": "Mon 2025-01-06 13:52:12 GMT", "Universal_time": "Mon 2025-01-06 13:52:12 UTC", "RTC_time": "Mon 2025-01-06 13:52:13", "Time_zone": "Europe/London (GMT, +0000)", "System_clock_synchronized": "yes", "NTP_service": "active", "RTC_in_local_TZ": "no"}}, {"name": "cpu", "status": "OK", "cause": "Cpu idle is within the acceptable range", "reason": "Cpu Idle%: 94.62% is above threshold: 15.0%", "repairs": [""], "results": {"PM": {"PM": "PM", "CPU": "7", "%usr": "3.43", "%nice": "0.01", "%sys": "1.48", "%iowait": "0.01", "%irq": "0.37", "%soft": "0.09", "%steal": "0.00", "%guest": "0.00", "%gnice": "0.00", "%idle": "94.62"}}}, {"name": "disk", "status": "OK", "cause": "Disk usage is within the acceptable range", "reason": "Disk Used%: 25.0% is below threshold: 80.0%", "repairs": [""], "results": {"devtmpfs": {"Size": "16GB", "Used": "0B", "Avail": "16GB", "Use%": "0%", "Mounted on": "/dev"}, "/dev/vda3": {"Size": "100GB", "Used": "28GB", "Avail": "72GB", "Use%": "28%", "Mounted on": "/"}, "/dev/vda2": {"Size": "100MB", "Used": "5.8MB", "Avail": "95MB", "Use%": "6%", "Mounted on": "/boot/efi"}, "total": {"Size": "116GB", "Used": "28GB", "Avail": "88GB", "Use%": "25%", "Mounted on": "-"}}}], "status": "OK", "reason": "", "cause": null, "repairs": [""], "version": "Red Hat Enterprise Linux 8.10 (Ootpa)"}, "druid": {"device_id": "dauk-mrl-att-druid-nhe", "status": "OK", "aspects": {"enode_b_count": 0, "iproute_count": 19, "ipsec_secure_association_count": 4, "net_device_count": 18, "plmn_count": 1, "radio_zone_count": 0, "sgw_count": 0, "sgw_session_count": 0, "s1server_enb_count": 2, "users_count": 0, "enable_5g": false, "enable_5g_nsa": false}, "details": {"features": {"oper_state": "enabled", "product_id": "8", "system_id": "1102081919056657533", "license_id": "14820895886772892389", "license_status": "0", "issue_date": "2024-12-19T14:34:28Z", "expiry_date": "2025-06-30T12:00:00Z", "binding_date": null, "supported_until": "2025-06-30T12:00:00Z", "max_nbr_of_subs": "0", "max_cells": "0", "max_enbs": "0", "max_pdns": "6", "max_s1_clients": "20", "enbgw_max_enbs": "100", "enbgw_max_active_subs": "400"}, "system": {"id": 1, "oper_state": "enabled", "admin_state": "unlocked", "service_state": "Active", "system_id": "1102081919056657533", "license_id": "14820895886772892389", "product_id": "*******.P2.1.df48af083::VIRTUAL::rhel8.10::x86_64::14820895886772892389::normal::1102081919056657533", "software_version": "Raemis Enterprise *******.P2-1, rdf48af083.", "restart_required": "0", "current_time": "2025-01-06T13:52:46.533000Z"}}, "networks": {"ip_route": [{"id": 2, "ipv4_subnet": "**********", "ipv4_subnetmask": "***********", "gateway_ipv4": "0.0.0.0", "net_device": "tun_ims", "metric": "0", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 3, "ipv4_subnet": "**********", "ipv4_subnetmask": "***********", "gateway_ipv4": "0.0.0.0", "net_device": "tun_local", "metric": "0", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 4, "ipv4_subnet": "*************", "ipv4_subnetmask": "***************", "gateway_ipv4": "0.0.0.0", "net_device": "eth0", "metric": "103", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 5, "ipv4_subnet": "0.0.0.0", "ipv4_subnetmask": "0.0.0.0", "gateway_ipv4": "*************", "net_device": "eth0", "metric": "103", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 11, "ipv4_subnet": "*************", "ipv4_subnetmask": "***************", "gateway_ipv4": "0.0.0.0", "net_device": "bond0.55", "metric": "401", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 12, "ipv4_subnet": "*************", "ipv4_subnetmask": "***************", "gateway_ipv4": "*************", "net_device": "bond0.55", "metric": "401", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 13, "ipv4_subnet": "***************", "ipv4_subnetmask": "***************", "gateway_ipv4": "*************", "net_device": "bond0.55", "metric": "401", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 14, "ipv4_subnet": "*************", "ipv4_subnetmask": "***************", "gateway_ipv4": "0.0.0.0", "net_device": "bond0.56", "metric": "402", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 18, "ipv4_subnet": "**************", "ipv4_subnetmask": "***************", "gateway_ipv4": "0.0.0.0", "net_device": "bond0.64", "metric": "403", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 19, "ipv4_subnet": "*************", "ipv4_subnetmask": "***************", "gateway_ipv4": "**************", "net_device": "bond0.64", "metric": "403", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 21, "ipv4_subnet": "*************", "ipv4_subnetmask": "***************", "gateway_ipv4": "**************", "net_device": "bond0.64", "metric": "403", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 22, "ipv4_subnet": "**************", "ipv4_subnetmask": "***************", "gateway_ipv4": "0.0.0.0", "net_device": "bond0.68", "metric": "405", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 23, "ipv4_subnet": "*************", "ipv4_subnetmask": "***************", "gateway_ipv4": "0.0.0.0", "net_device": "bond0.54", "metric": "400", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 24, "ipv4_subnet": "**************", "ipv4_subnetmask": "***************", "gateway_ipv4": "*************", "net_device": "bond0.54", "metric": "400", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 25, "ipv4_subnet": "***************", "ipv4_subnetmask": "***************", "gateway_ipv4": "*************", "net_device": "bond0.54", "metric": "400", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 27, "ipv4_subnet": "**************", "ipv4_subnetmask": "***************", "gateway_ipv4": "*************", "net_device": "bond0.54", "metric": "400", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 28, "ipv4_subnet": "**************", "ipv4_subnetmask": "***************", "gateway_ipv4": "0.0.0.0", "net_device": "bond0.67", "metric": "404", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 29, "ipv4_subnet": "**************", "ipv4_subnetmask": "***************", "gateway_ipv4": "0.0.0.0", "net_device": "segw_ipsec_ipv", "metric": "0", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 30, "ipv4_subnet": "**************", "ipv4_subnetmask": "***************", "gateway_ipv4": "0.0.0.0", "net_device": "segw_ipsec_ipv", "metric": "0", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}], "pdn": []}, "segw": {"ipsec_child_config": [{"id": 1, "name": "tunnel1_child1", "lifetime": 86400, "rekeytime": 28800, "jitter": 2880}, {"id": 2, "name": "main-segw_child1", "lifetime": 86400, "rekeytime": 28800, "jitter": 2880}, {"id": 7, "name": "ipsec_ipv4_tunnel1_child1", "lifetime": 86400, "rekeytime": 28800, "jitter": 2880}, {"id": 8, "name": "main-segw-2_child1", "lifetime": 86400, "rekeytime": 28800, "jitter": 2880}], "ipsec_child_config_proposal": [{"id": 1, "child_cfg": 1, "prio": 1, "prop": 4}, {"id": 5, "child_cfg": 2, "prio": 1, "prop": 6}, {"id": 7, "child_cfg": 2, "prio": 1, "prop": 7}, {"id": 12, "child_cfg": 7, "prio": 1, "prop": 4}, {"id": 14, "child_cfg": 8, "prio": 1, "prop": 6}, {"id": 15, "child_cfg": 8, "prio": 1, "prop": 7}], "ipsec_certificate": [{"filename": "denseair_root.pem", "subject": "C=GB, O=Dense Air Ltd, CN=Root", "issuer": "C=GB, O=Dense Air Ltd, CN=Root", "expiry_date": "2032-12-15T14:59:59Z", "start_date": "2022-12-15T14:59:59Z"}, {"filename": "dauk-mrl-att-druid-nhe.pem", "subject": "C=GB, O=Dense Air Ltd, CN=dauk-mrl-att-druid-nhe", "issuer": "C=GB, O=Dense Air Ltd, CN=NZ Airspan", "expiry_date": "2025-12-04T14:18:50Z", "start_date": "2023-12-04T14:18:50Z"}, {"filename": "nzairspan.pem", "subject": "C=GB, O=Dense Air Ltd, CN=NZ Airspan", "issuer": "C=GB, O=Dense Air Ltd, CN=Root", "expiry_date": "2032-12-15T14:59:59Z", "start_date": "2023-01-16T15:29:22Z"}, {"filename": "dauk-mrl-att-druid-nhe_server.pem", "subject": "C=GB, O=Dense Air Ltd, CN=dauk-mrl-att-druid-nhe_server", "issuer": "C=GB, O=Dense Air Ltd, CN=NZ Airspan", "expiry_date": "2025-12-06T17:30:17Z", "start_date": "2023-12-06T17:30:17Z"}, {"filename": "DruidAttMocn_DenseAirLtd_GB_MarlowLab_1_c2de2bc7cc83_cert.pem", "subject": "C=US, L=GB, O=DenseAirLtd, OU=Druid, CN=c2de2bc7cc83.DenseAirLtd.GB-MarlowLab-1.druid.com", "issuer": "C=IE, O=Druid Software, CN=Druid ATT Mocn CA", "expiry_date": "2029-10-24T10:51:56Z", "start_date": "2024-10-24T10:51:56Z"}, {"filename": "dauk-mrl-att-druid-nhe_server_2nd.pem", "subject": "C=GB, O=Dense Air Ltd, CN=dauk-mrl-att-druid-nhe_server_2nd", "issuer": "C=GB, O=Dense Air Ltd, CN=NZ Airspan", "expiry_date": "2026-08-06T15:41:46Z", "start_date": "2024-08-06T15:41:46Z"}, {"filename": "DruidAttMocnCA_cert.pem", "subject": "C=IE, O=Druid Software, CN=Druid ATT Mocn CA", "issuer": "C=IE, O=Druid Software, CN=Druid ATT Mocn CA", "expiry_date": "2053-09-24T23:31:34Z", "start_date": "2023-10-02T23:31:34Z"}, {"filename": "ipsec_ipv4_tunnel1_CMPv2Cert.pem", "subject": "C=US, O=DenseAirLtd, OU=Druid, CN=c2de2bc7cc83.DenseAirLtd.GB-MarlowLab-1.druid.com, L=GB", "issuer": "C=GB, O=Dense Air Ltd, CN=NZ Airspan", "expiry_date": "2026-10-29T15:55:30Z", "start_date": "2024-10-29T15:55:30Z"}], "ipsec_ikeconfig_proposal": [{"id": 1, "ike_cfg": 1, "prio": 1, "prop": 4}, {"id": 2, "ike_cfg": 2, "prio": 1, "prop": 3}, {"id": 3, "ike_cfg": 2, "prio": 1, "prop": 4}, {"id": 8, "ike_cfg": 7, "prio": 1, "prop": 4}, {"id": 10, "ike_cfg": 8, "prio": 1, "prop": 1}, {"id": 11, "ike_cfg": 8, "prio": 1, "prop": 4}], "ipsec_peer_config": [{"id": 1, "name": "tunnel1", "type": "CLIENT", "ike_version": 2, "rekeytime": 86400, "jitter": 180, "pool": ""}, {"id": 2, "name": "main-segw", "type": "SERVER", "ike_version": 2, "rekeytime": 86400, "jitter": 180, "pool": "main-segw_pool"}, {"id": 7, "name": "ipsec_ipv4_tunnel1", "type": "CLIENT", "ike_version": 2, "rekeytime": 3600, "jitter": 180, "pool": ""}, {"id": 8, "name": "main-segw-2", "type": "SERVER", "ike_version": 2, "rekeytime": 3600, "jitter": 180, "pool": "main-segw-2_pool"}], "ipsec_private_key": [{"type": 1, "filename": "dauk-mrl-att-druid-nhe.pkey"}, {"type": 1, "filename": "dauk-mrl-att-druid-nhe_server.pkey"}, {"type": 1, "filename": "DruidAttMocn_DenseAirLtd_GB_MarlowLab_1_c2de2bc7cc83_key.pem"}, {"type": 1, "filename": "dauk-mrl-att-druid-nhe_server_2nd.pkey"}, {"type": 1, "filename": "ipsec_ipv4_tunnel1_CMPv2Key.pem"}], "ipsec_secure_association": [{"name": "ipsec_ipv4_tunnel1_child1", "creation_time": "2024-12-12T13:47:16Z", "oper_state": "enabled", "direction": "bidirectional", "local_addr": "**************", "local_id": "C=US, O=DenseAirLtd, OU=Druid, CN=c2de2b", "remote_addr": "*************", "remote_id": "mrl-att-ipv6segw", "integrity_algorithm": "HMAC_SHA2_256_12", "encryption_algorithm": "AES_CBC", "sa_bytes_in": 931408, "sa_packets_in": 10124, "sa_bytes_out": 931408, "sa_packets_out": 10124}, {"name": "tunnel1_child1", "creation_time": "2024-12-12T13:50:52Z", "oper_state": "enabled", "direction": "bidirectional", "local_addr": "**************", "local_id": "C=GB, O=Dense Air Ltd, CN=dauk-mrl-att-d", "remote_addr": "*************", "remote_id": "C=GB, O=Dense Air Ltd, CN=denseair.net", "integrity_algorithm": "HMAC_SHA2_256_12", "encryption_algorithm": "AES_CBC", "sa_bytes_in": 1561736, "sa_packets_in": 13939, "sa_bytes_out": 1604604, "sa_packets_out": 14305}, {"name": "main-segw-2_child1", "creation_time": "2025-01-06T13:40:32Z", "oper_state": "enabled", "direction": "bidirectional", "local_addr": "**************", "local_id": "C=GB, O=Dense Air Ltd, CN=dauk-mrl-att-d", "remote_addr": "**************", "remote_id": "00A00A00F750.airspan.com", "integrity_algorithm": "HMAC_SHA2_256_12", "encryption_algorithm": "AES_CBC", "sa_bytes_in": 4325897, "sa_packets_in": 11697, "sa_bytes_out": 1100402, "sa_packets_out": 9825}, {"name": "main-segw-2_child1", "creation_time": "2025-01-06T13:40:32Z", "oper_state": "enabled", "direction": "bidirectional", "local_addr": "**************", "local_id": "C=GB, O=Dense Air Ltd, CN=dauk-mrl-att-d", "remote_addr": "**************", "remote_id": "00A00A01B69C.airspan.com", "integrity_algorithm": "HMAC_SHA2_256_12", "encryption_algorithm": "AES_CBC", "sa_bytes_in": 5729664, "sa_packets_in": 17988, "sa_bytes_out": 1299998, "sa_packets_out": 13442}], "ipsec_proposal": [{"id": 1, "proposal": "aes128-sha1-modp2048"}, {"id": 2, "proposal": "aes128-sha256-modp2048"}, {"id": 3, "proposal": "aes256-sha1-modp2048"}, {"id": 4, "proposal": "aes256-sha256-modp2048"}, {"id": 6, "proposal": "aes256-sha256"}, {"id": 7, "proposal": "aes256-sha1"}], "s1client": [{"oper_state": "enabled", "admin_state": "unlocked", "name": "eNB-1052", "s1_client_type": "Macro", "enb_name": "eNB-1052", "plmn_id": "00159", "cell_identity": "269312", "max_downlink_bandwidth": 0, "max_uplink_bandwidth": 0}, {"oper_state": "enabled", "admin_state": "unlocked", "name": "315010-eNB-1052-CN-DruidIPv6", "s1_client_type": "Macro", "enb_name": "eNB-1052", "plmn_id": "315010", "cell_identity": "269312", "max_downlink_bandwidth": 0, "max_uplink_bandwidth": 0}, {"oper_state": "enabled", "admin_state": "unlocked", "name": "eNB-1053", "s1_client_type": "Macro", "enb_name": "eNB-1053", "plmn_id": "00159", "cell_identity": "269568", "max_downlink_bandwidth": 0, "max_uplink_bandwidth": 0}, {"oper_state": "enabled", "admin_state": "unlocked", "name": "315010-eNB-1053-CN-DruidIPv4", "s1_client_type": "Macro", "enb_name": "eNB-1053", "plmn_id": "315010", "cell_identity": "269568", "max_downlink_bandwidth": 0, "max_uplink_bandwidth": 0}]}, "sessions": [], "enodebs": [{"id": 1, "oper_state": "enabled", "admin_state": "unlocked", "name": "", "tac": 10, "cell_id": 269334, "enb_id": 1, "enodeb": {"id": 1, "oper_state": "enabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 1052, "name": "eNB-1052", "sctp_address": "*************:46570", "last_inform_time": null}}, {"id": 2, "oper_state": "enabled", "admin_state": "unlocked", "name": "", "tac": 10, "cell_id": 269570, "enb_id": 2, "enodeb": {"id": 2, "oper_state": "enabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 1053, "name": "eNB-1053", "sctp_address": "*************:41545", "last_inform_time": null}}, {"id": 3, "oper_state": "enabled", "admin_state": "unlocked", "name": "", "tac": 10, "cell_id": 269336, "enb_id": 1, "enodeb": {"id": 1, "oper_state": "enabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 1052, "name": "eNB-1052", "sctp_address": "*************:46570", "last_inform_time": null}}, {"id": 4, "oper_state": "enabled", "admin_state": "unlocked", "name": "", "tac": 10, "cell_id": 269502, "enb_id": 1, "enodeb": {"id": 1, "oper_state": "enabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 1052, "name": "eNB-1052", "sctp_address": "*************:46570", "last_inform_time": null}}, {"id": 5, "oper_state": "enabled", "admin_state": "unlocked", "name": "", "tac": 10, "cell_id": 269501, "enb_id": 1, "enodeb": {"id": 1, "oper_state": "enabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 1052, "name": "eNB-1052", "sctp_address": "*************:46570", "last_inform_time": null}}, {"id": 6, "oper_state": "enabled", "admin_state": "unlocked", "name": "", "tac": 10, "cell_id": 269333, "enb_id": 1, "enodeb": {"id": 1, "oper_state": "enabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 1052, "name": "eNB-1052", "sctp_address": "*************:46570", "last_inform_time": null}}, {"id": 7, "oper_state": "disabled", "admin_state": "unlocked", "name": "", "tac": 0, "cell_id": 268800, "enb_id": 3, "enodeb": {"id": 3, "oper_state": "disabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 1050, "name": "at&t-marlow-1050", "sctp_address": "**********:36412", "last_inform_time": null}}, {"id": 8, "oper_state": "disabled", "admin_state": "unlocked", "name": "", "tac": 1, "cell_id": 1050, "enb_id": 4, "enodeb": {"id": 4, "oper_state": "disabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 4, "name": "at&t-marlow-1", "sctp_address": "**********:36412", "last_inform_time": null}}, {"id": 10, "oper_state": "disabled", "admin_state": "unlocked", "name": "", "tac": 10, "cell_id": 67502337, "enb_id": 6, "enodeb": {"id": 6, "oper_state": "disabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 263681, "name": "AS1030 Cetin NQT", "sctp_address": "***************:36412", "last_inform_time": null}}, {"id": 11, "oper_state": "enabled", "admin_state": "unlocked", "name": "", "tac": 10, "cell_id": 269335, "enb_id": 1, "enodeb": {"id": 1, "oper_state": "enabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 1052, "name": "eNB-1052", "sctp_address": "*************:46570", "last_inform_time": null}}, {"id": 13, "oper_state": "enabled", "admin_state": "unlocked", "name": "", "tac": 10, "cell_id": 269338, "enb_id": 1, "enodeb": {"id": 1, "oper_state": "enabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 1052, "name": "eNB-1052", "sctp_address": "*************:46570", "last_inform_time": null}}, {"id": 14, "oper_state": "enabled", "admin_state": "unlocked", "name": "", "tac": 10, "cell_id": 269057, "enb_id": 7, "enodeb": {"id": 7, "oper_state": "enabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 1051, "name": "green_veNB", "sctp_address": "***********:36412", "last_inform_time": null}}, {"id": 15, "oper_state": "enabled", "admin_state": "unlocked", "name": "", "tac": 10, "cell_id": 269058, "enb_id": 7, "enodeb": {"id": 7, "oper_state": "enabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 1051, "name": "green_veNB", "sctp_address": "***********:36412", "last_inform_time": null}}, {"id": 16, "oper_state": "enabled", "admin_state": "unlocked", "name": "", "tac": 10, "cell_id": 269312, "enb_id": 1, "enodeb": {"id": 1, "oper_state": "enabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 1052, "name": "eNB-1052", "sctp_address": "*************:46570", "last_inform_time": null}}, {"id": 17, "oper_state": "enabled", "admin_state": "unlocked", "name": "", "tac": 10, "cell_id": 269056, "enb_id": 7, "enodeb": {"id": 7, "oper_state": "enabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 1051, "name": "green_veNB", "sctp_address": "***********:36412", "last_inform_time": null}}], "created_at": "2025-01-06T13:53:07.939188Z"}, "vsr": null, "acp": null}