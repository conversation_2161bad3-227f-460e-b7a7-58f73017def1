{"id": "GB-MR4G-VM-0001", "server": {"server_id": "GP081D3-dauk-mrl-green-druid", "name": "dauk-mrl-green-druid", "ip_address": "*************", "node_type": "Server-VM", "timing_type": "NTP", "cluster_name": null, "access_method": "ssh", "last_attempted": "2025-01-06T14:01:00.152321Z", "last_contacted": "2025-01-06T14:00:00.150946Z", "subsystems": [{"name": "ptp", "status": "UNKNOWN", "cause": "PTP Unknown Status", "reason": "cluster is not available", "repairs": [""], "results": null}, {"name": "memory", "status": "OK", "cause": "Mem used is within the acceptable range", "reason": "Mem Used%: 16.22% is below threshold: 85.0%", "repairs": [""], "results": {"Mem": {"total": "7696MB", "used": "1248MB", "free": "4384MB", "shared": "376MB", "buff/cache": "2063MB", "available": "5769MB"}, "Swap": {"total": "0MB", "used": "0MB", "free": "0MB", "shared": null, "buff/cache": null, "available": null}}}, {"name": "ntp", "status": "OK", "cause": "Report", "reason": null, "repairs": [""], "results": {"Local_time": "Mon 2025-01-06 14:00:12 GMT", "Universal_time": "Mon 2025-01-06 14:00:12 UTC", "RTC_time": "Mon 2025-01-06 14:00:13", "Time_zone": "Europe/London (GMT, +0000)", "System_clock_synchronized": "yes", "NTP_service": "active", "RTC_in_local_TZ": "no"}}, {"name": "disk", "status": "OK", "cause": "Disk usage is within the acceptable range", "reason": "Disk Used%: 5.0% is below threshold: 80.0%", "repairs": [""], "results": {"devtmpfs": {"Size": "3.8GB", "Used": "0B", "Avail": "3.8GB", "Use%": "0%", "Mounted on": "/dev"}, "/dev/vda3": {"Size": "250GB", "Used": "12GB", "Avail": "239GB", "Use%": "5%", "Mounted on": "/"}, "/dev/vda2": {"Size": "100MB", "Used": "5.8MB", "Avail": "95MB", "Use%": "6%", "Mounted on": "/boot/efi"}, "total": {"Size": "254GB", "Used": "12GB", "Avail": "243GB", "Use%": "5%", "Mounted on": "-"}}}, {"name": "cpu", "status": "OK", "cause": "Cpu idle is within the acceptable range", "reason": "Cpu Idle%: 95.67% is above threshold: 15.0%", "repairs": [""], "results": {"PM": {"PM": "PM", "CPU": "7", "%usr": "2.61", "%nice": "0.01", "%sys": "1.14", "%iowait": "0.17", "%irq": "0.33", "%soft": "0.06", "%steal": "0.01", "%guest": "0.00", "%gnice": "0.00", "%idle": "95.67"}}}], "status": "OK", "reason": null, "cause": null, "repairs": [""], "version": "Red Hat Enterprise Linux 8.10 (Ootpa)"}, "druid": {"device_id": "dauk-mrl-green-druid-core", "status": "ERROR", "aspects": {"enode_b_count": 6, "iproute_count": 16, "ipsec_secure_association_count": 0, "net_device_count": 10, "plmn_count": 1, "radio_zone_count": 0, "sgw_count": 1, "sgw_session_count": 0, "s1server_enb_count": 0, "users_count": 242, "enable_5g": false, "enable_5g_nsa": false}, "details": {"features": {"oper_state": "enabled", "product_id": "8", "system_id": "1090875381675311761", "license_id": "faf6089588675046c43a", "license_status": "0", "issue_date": "2024-04-11T08:29:02Z", "expiry_date": null, "binding_date": null, "supported_until": "2024-12-31T12:00:00Z", "max_nbr_of_subs": "270", "max_cells": "10", "max_enbs": "10", "max_pdns": "10", "max_s1_clients": "", "enbgw_max_enbs": "", "enbgw_max_active_subs": ""}, "system": {"id": 1, "oper_state": "enabled", "admin_state": "unlocked", "service_state": "Active", "system_id": "1090875381675311761", "license_id": "faf6089588675046c43a", "product_id": "*******.1.72afa9015::VIRTUAL::rhel8.10::x86_64::faf6089588675046c43a::normal::1090875381675311761", "software_version": "Raemis Enterprise *******-1, r72afa9015.", "restart_required": "0", "current_time": "2025-01-06T14:01:14.026000Z"}}, "networks": {"ip_route": [{"id": 4, "ipv4_subnet": "*************", "ipv4_subnetmask": "***************", "gateway_ipv4": "0.0.0.0", "net_device": "enp7s0", "metric": "100", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 5, "ipv4_subnet": "0.0.0.0", "ipv4_subnetmask": "0.0.0.0", "gateway_ipv4": "**************", "net_device": "enp7s0", "metric": "100", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 6, "ipv4_subnet": "*************", "ipv4_subnetmask": "***************", "gateway_ipv4": "0.0.0.0", "net_device": "bond0.202", "metric": "401", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 7, "ipv4_subnet": "***************", "ipv4_subnetmask": "***************", "gateway_ipv4": "0.0.0.0", "net_device": "bond0.203", "metric": "402", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 8, "ipv4_subnet": "************", "ipv4_subnetmask": "*************", "gateway_ipv4": "0.0.0.0", "net_device": "bond0.30", "metric": "400", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 9, "ipv4_subnet": "***********", "ipv4_subnetmask": "***********", "gateway_ipv4": "***************", "net_device": "bond0.203", "metric": "402", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 10, "ipv4_subnet": "**********", "ipv4_subnetmask": "*************", "gateway_ipv4": "***************", "net_device": "bond0.203", "metric": "402", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 11, "ipv4_subnet": "***********", "ipv4_subnetmask": "*************", "gateway_ipv4": "***************", "net_device": "bond0.203", "metric": "402", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 13, "ipv4_subnet": "*************", "ipv4_subnetmask": "***************", "gateway_ipv4": "***************", "net_device": "bond0.203", "metric": "0", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 14, "ipv4_subnet": "***********", "ipv4_subnetmask": "*************", "gateway_ipv4": "**************", "net_device": "bond0.30", "metric": "400", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 15, "ipv4_subnet": "**********", "ipv4_subnetmask": "*************", "gateway_ipv4": "**************", "net_device": "bond0.30", "metric": "400", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 16, "ipv4_subnet": "*************", "ipv4_subnetmask": "*************", "gateway_ipv4": "0.0.0.0", "net_device": "tun_host", "metric": "0", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 17, "ipv4_subnet": "**********", "ipv4_subnetmask": "***********", "gateway_ipv4": "0.0.0.0", "net_device": "tun_ims", "metric": "0", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 18, "ipv4_subnet": "**********", "ipv4_subnetmask": "***********", "gateway_ipv4": "0.0.0.0", "net_device": "tun_local", "metric": "0", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 19, "ipv4_subnet": "*************", "ipv4_subnetmask": "*************", "gateway_ipv4": "**************", "net_device": "bond0.30", "metric": "400", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 20, "ipv4_subnet": "*************", "ipv4_subnetmask": "*************", "gateway_ipv4": "**************", "net_device": "bond0.30", "metric": "400", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}], "pdn": [{"id": 1, "apn": "*", "primary_dns": "*******", "secondary_dns": "*******", "ue_mtu": "1450", "ipv4_pool_id": "1", "ep_group_id": "2", "allow_multiple_connections": "0", "primary_ipv6_dns": "", "secondary_ipv6_dns": "", "ipv6_prefix_pool_id": "0", "pcscf_ipv4_addr": "", "pcscf_ipv6_addr": "", "ipv4_config_method": "", "ipv4_config_timeout": "20", "use_mac_mappings": "0", "raemis_id": "0", "sticky_ip_assignment": "0", "ipv4pools": "/api/pdn/ipv4pools?pdn_id=1"}, {"id": 2, "apn": "ims", "primary_dns": "", "secondary_dns": "", "ue_mtu": "1392", "ipv4_pool_id": "2", "ep_group_id": "3", "allow_multiple_connections": "0", "primary_ipv6_dns": "", "secondary_ipv6_dns": "", "ipv6_prefix_pool_id": "0", "pcscf_ipv4_addr": "**********", "pcscf_ipv6_addr": "", "ipv4_config_method": "", "ipv4_config_timeout": "20", "use_mac_mappings": "0", "raemis_id": "0", "sticky_ip_assignment": "0", "ipv4pools": "/api/pdn/ipv4pools?pdn_id=2"}, {"id": 3, "apn": "emergency", "primary_dns": "", "secondary_dns": "", "ue_mtu": "1392", "ipv4_pool_id": "2", "ep_group_id": "3", "allow_multiple_connections": "0", "primary_ipv6_dns": "", "secondary_ipv6_dns": "", "ipv6_prefix_pool_id": "0", "pcscf_ipv4_addr": "**********", "pcscf_ipv6_addr": "", "ipv4_config_method": "", "ipv4_config_timeout": "20", "use_mac_mappings": "0", "raemis_id": "0", "sticky_ip_assignment": "0", "ipv4pools": "/api/pdn/ipv4pools?pdn_id=3"}, {"id": 4, "apn": "vzwims", "primary_dns": "", "secondary_dns": "", "ue_mtu": "1392", "ipv4_pool_id": "2", "ep_group_id": "3", "allow_multiple_connections": "0", "primary_ipv6_dns": "", "secondary_ipv6_dns": "", "ipv6_prefix_pool_id": "0", "pcscf_ipv4_addr": "**********", "pcscf_ipv6_addr": "", "ipv4_config_method": "", "ipv4_config_timeout": "20", "use_mac_mappings": "0", "raemis_id": "0", "sticky_ip_assignment": "0", "ipv4pools": "/api/pdn/ipv4pools?pdn_id=4"}, {"id": 6, "apn": "r1.denseair", "primary_dns": "*******", "secondary_dns": "*******", "ue_mtu": "1392", "ipv4_pool_id": "0", "ep_group_id": "2", "allow_multiple_connections": "0", "primary_ipv6_dns": "", "secondary_ipv6_dns": "", "ipv6_prefix_pool_id": "0", "pcscf_ipv4_addr": "", "pcscf_ipv6_addr": "", "ipv4_config_method": "", "ipv4_config_timeout": "20", "use_mac_mappings": "0", "raemis_id": "0", "sticky_ip_assignment": "0", "ipv4pools": "/api/pdn/ipv4pools?pdn_id=6"}, {"id": 8, "apn": "eurocom", "primary_dns": "*******", "secondary_dns": "*******", "ue_mtu": "1392", "ipv4_pool_id": "1", "ep_group_id": "2", "allow_multiple_connections": "0", "primary_ipv6_dns": "", "secondary_ipv6_dns": "", "ipv6_prefix_pool_id": "0", "pcscf_ipv4_addr": "", "pcscf_ipv6_addr": "", "ipv4_config_method": "", "ipv4_config_timeout": "20", "use_mac_mappings": "0", "raemis_id": "0", "sticky_ip_assignment": "0", "ipv4pools": "/api/pdn/ipv4pools?pdn_id=8"}, {"id": 9, "apn": "internet.00101", "primary_dns": "*******", "secondary_dns": "*******", "ue_mtu": "1392", "ipv4_pool_id": "1", "ep_group_id": "2", "allow_multiple_connections": "0", "primary_ipv6_dns": "", "secondary_ipv6_dns": "", "ipv6_prefix_pool_id": "0", "pcscf_ipv4_addr": "", "pcscf_ipv6_addr": "", "ipv4_config_method": "", "ipv4_config_timeout": "20", "use_mac_mappings": "0", "raemis_id": "0", "sticky_ip_assignment": "0", "ipv4pools": "/api/pdn/ipv4pools?pdn_id=9"}, {"id": 10, "apn": "inet.mnc059.mcc001.gprs", "primary_dns": "*******", "secondary_dns": "*******", "ue_mtu": "1392", "ipv4_pool_id": "1", "ep_group_id": "2", "allow_multiple_connections": "0", "primary_ipv6_dns": "", "secondary_ipv6_dns": "", "ipv6_prefix_pool_id": "0", "pcscf_ipv4_addr": "", "pcscf_ipv6_addr": "", "ipv4_config_method": "", "ipv4_config_timeout": "20", "use_mac_mappings": "0", "raemis_id": "0", "sticky_ip_assignment": "0", "ipv4pools": "/api/pdn/ipv4pools?pdn_id=10"}, {"id": 11, "apn": "radisys", "primary_dns": "*******", "secondary_dns": "*******", "ue_mtu": "1392", "ipv4_pool_id": "0", "ep_group_id": "2", "allow_multiple_connections": "0", "primary_ipv6_dns": "", "secondary_ipv6_dns": "", "ipv6_prefix_pool_id": "0", "pcscf_ipv4_addr": "", "pcscf_ipv6_addr": "", "ipv4_config_method": "", "ipv4_config_timeout": "20", "use_mac_mappings": "0", "raemis_id": "0", "sticky_ip_assignment": "0", "ipv4pools": "/api/pdn/ipv4pools?pdn_id=11"}]}, "segw": {"ipsec_child_config": [], "ipsec_child_config_proposal": [], "ipsec_certificate": [], "ipsec_ikeconfig_proposal": [], "ipsec_peer_config": [], "ipsec_private_key": [], "ipsec_secure_association": [], "ipsec_proposal": [{"id": 1, "proposal": "aes128-sha1-modp2048"}, {"id": 2, "proposal": "aes128-sha256-modp2048"}, {"id": 3, "proposal": "aes256-sha1-modp2048"}, {"id": 4, "proposal": "aes256-sha256-modp2048"}], "s1client": []}, "sessions": [], "enodebs": [{"id": 1, "oper_state": "enabled", "admin_state": "unlocked", "name": "", "tac": 10, "cell_id": 269334, "enb_id": 1, "enodeb": {"id": 1, "oper_state": "enabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 1052, "name": "eNB-1052", "sctp_address": "*************:46570", "last_inform_time": null}}, {"id": 2, "oper_state": "enabled", "admin_state": "unlocked", "name": "", "tac": 10, "cell_id": 269570, "enb_id": 2, "enodeb": {"id": 2, "oper_state": "enabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 1053, "name": "eNB-1053", "sctp_address": "*************:41545", "last_inform_time": null}}, {"id": 3, "oper_state": "enabled", "admin_state": "unlocked", "name": "", "tac": 10, "cell_id": 269336, "enb_id": 1, "enodeb": {"id": 1, "oper_state": "enabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 1052, "name": "eNB-1052", "sctp_address": "*************:46570", "last_inform_time": null}}, {"id": 4, "oper_state": "enabled", "admin_state": "unlocked", "name": "", "tac": 10, "cell_id": 269502, "enb_id": 1, "enodeb": {"id": 1, "oper_state": "enabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 1052, "name": "eNB-1052", "sctp_address": "*************:46570", "last_inform_time": null}}, {"id": 5, "oper_state": "enabled", "admin_state": "unlocked", "name": "", "tac": 10, "cell_id": 269501, "enb_id": 1, "enodeb": {"id": 1, "oper_state": "enabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 1052, "name": "eNB-1052", "sctp_address": "*************:46570", "last_inform_time": null}}, {"id": 6, "oper_state": "enabled", "admin_state": "unlocked", "name": "", "tac": 10, "cell_id": 269333, "enb_id": 1, "enodeb": {"id": 1, "oper_state": "enabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 1052, "name": "eNB-1052", "sctp_address": "*************:46570", "last_inform_time": null}}, {"id": 7, "oper_state": "disabled", "admin_state": "unlocked", "name": "", "tac": 0, "cell_id": 268800, "enb_id": 3, "enodeb": {"id": 3, "oper_state": "disabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 1050, "name": "at&t-marlow-1050", "sctp_address": "**********:36412", "last_inform_time": null}}, {"id": 8, "oper_state": "disabled", "admin_state": "unlocked", "name": "", "tac": 1, "cell_id": 1050, "enb_id": 4, "enodeb": {"id": 4, "oper_state": "disabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 4, "name": "at&t-marlow-1", "sctp_address": "**********:36412", "last_inform_time": null}}, {"id": 10, "oper_state": "disabled", "admin_state": "unlocked", "name": "", "tac": 10, "cell_id": 67502337, "enb_id": 6, "enodeb": {"id": 6, "oper_state": "disabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 263681, "name": "AS1030 Cetin NQT", "sctp_address": "***************:36412", "last_inform_time": null}}, {"id": 11, "oper_state": "enabled", "admin_state": "unlocked", "name": "", "tac": 10, "cell_id": 269335, "enb_id": 1, "enodeb": {"id": 1, "oper_state": "enabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 1052, "name": "eNB-1052", "sctp_address": "*************:46570", "last_inform_time": null}}, {"id": 13, "oper_state": "enabled", "admin_state": "unlocked", "name": "", "tac": 10, "cell_id": 269338, "enb_id": 1, "enodeb": {"id": 1, "oper_state": "enabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 1052, "name": "eNB-1052", "sctp_address": "*************:46570", "last_inform_time": null}}, {"id": 14, "oper_state": "enabled", "admin_state": "unlocked", "name": "", "tac": 10, "cell_id": 269057, "enb_id": 7, "enodeb": {"id": 7, "oper_state": "enabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 1051, "name": "green_veNB", "sctp_address": "***********:36412", "last_inform_time": null}}, {"id": 15, "oper_state": "enabled", "admin_state": "unlocked", "name": "", "tac": 10, "cell_id": 269058, "enb_id": 7, "enodeb": {"id": 7, "oper_state": "enabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 1051, "name": "green_veNB", "sctp_address": "***********:36412", "last_inform_time": null}}, {"id": 16, "oper_state": "enabled", "admin_state": "unlocked", "name": "", "tac": 10, "cell_id": 269312, "enb_id": 1, "enodeb": {"id": 1, "oper_state": "enabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 1052, "name": "eNB-1052", "sctp_address": "*************:46570", "last_inform_time": null}}, {"id": 17, "oper_state": "enabled", "admin_state": "unlocked", "name": "", "tac": 10, "cell_id": 269056, "enb_id": 7, "enodeb": {"id": 7, "oper_state": "enabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 1051, "name": "green_veNB", "sctp_address": "***********:36412", "last_inform_time": null}}], "created_at": "2025-01-06T14:01:56.421455Z"}, "vsr": null, "acp": null}