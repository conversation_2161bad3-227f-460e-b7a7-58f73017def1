/// <reference types="vitest" />
/// <reference types="vite/client" />
/// <reference types="vite-plugin-svgr/client" />

import react from '@vitejs/plugin-react';
import type { Plugin } from 'postcss';
import { defineConfig, loadEnv } from 'vite';
import svgr from 'vite-plugin-svgr';
interface EnvConfig {
  VITE_INVENTORY_MANAGER_API_URL: string;
  VITE_METRICS_COLLECTOR_API_URL: string;
  VITE_NMS_ORCHESTRATOR_API_URL: string;
  VITE_NMS_DUCUMANAGER_API_URL: string;
  VITE_NMS_CONTROLLER_API_URL: string;
  VITE_ENV_NAME: string;
}
export type EnvironmentName = 'dev_1' | 'dev2_01' | 'test_1';

export default defineConfig(({ mode }) => {
  const env: EnvConfig = loadEnv(mode, process.cwd(), '') as unknown as EnvConfig;
  const isProd = mode === 'prod';
  const mapEnv: Record<EnvironmentName, string> = {
    dev_1: 'dev1',
    dev2_01: 'dev2',
    test_1: 'test1',
  };
  const dynamicInvPath = `/nms/${
    mapEnv[env.VITE_ENV_NAME as EnvironmentName] ? mapEnv[env.VITE_ENV_NAME as EnvironmentName] + '/' : ''
  }inv`;
  const dynamicOrcPath = `/nms/${
    mapEnv[env.VITE_ENV_NAME as EnvironmentName] ? mapEnv[env.VITE_ENV_NAME as EnvironmentName] + '/' : ''
  }orc`;

  const dynamicMcPath = `/nms/${
    mapEnv[env.VITE_ENV_NAME as EnvironmentName] ? mapEnv[env.VITE_ENV_NAME as EnvironmentName] + '/' : ''
  }mc`;
  const dynamicRadPath = `/nms/${
    mapEnv[env.VITE_ENV_NAME as EnvironmentName] ? mapEnv[env.VITE_ENV_NAME as EnvironmentName] + '/' : ''
  }rad`;

  const dynamicControllerPath = `/nms/${
    mapEnv[env.VITE_ENV_NAME as EnvironmentName] ? mapEnv[env.VITE_ENV_NAME as EnvironmentName] + '/' : ''
  }controller`;

  return {
    plugins: [svgr(), react()],
    css: {
      postcss: {
        plugins: [removeZoom],
      },
    },
    define: {
      APP_VERSION: JSON.stringify(process.env.npm_package_version),
    },
    server: {
      port: 8081,
      open: true,
      proxy: {
        [dynamicInvPath]: isProd ? 'https://api.denseair.net' : env.VITE_INVENTORY_MANAGER_API_URL,
        [dynamicMcPath]: isProd ? 'https://api.denseair.net' : env.VITE_NMS_ORCHESTRATOR_API_URL,
        [dynamicOrcPath]: isProd ? 'https://api.denseair.net' : env.VITE_METRICS_COLLECTOR_API_URL,
        [dynamicRadPath]: isProd ? 'https://api.denseair.net' : env.VITE_NMS_DUCUMANAGER_API_URL,
        [dynamicControllerPath]: isProd ? 'https://api.denseair.net' : env.VITE_NMS_CONTROLLER_API_URL,
      },
    },

    test: {
      globals: true,
      environment: 'jsdom',
      setupFiles: './src/test/setupTest.ts',
      coverage: {
        provider: 'istanbul',
        reporter: ['text', 'json', 'html'],
      },
    },
    build: {
      chunkSizeWarningLimit: 100,
      rollupOptions: {
        onwarn(warning, warn) {
          if (warning.code === 'MODULE_LEVEL_DIRECTIVE') {
            return;
          }
          warn(warning);
        },
        external: ['@fortawesome/fontawesome-svg-core'],
      },
    },
  };
});

const removeZoom: Plugin = {
  postcssPlugin: 'remove-zoom',
  Once(root) {
    root.walkDecls((decl) => {
      if (decl.prop === 'zoom') {
        decl.remove();
      }
    });
  },
};
