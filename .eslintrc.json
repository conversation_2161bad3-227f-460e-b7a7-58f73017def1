{
  "env": {
    "browser": true,
    "es2021": true,
    "node": true
  },
  "settings": {
    "react": {
      "version": "detect"
    }
  },
  "extends": [
    "eslint:recommended",
    "plugin:react/recommended",
    "plugin:@typescript-eslint/eslint-recommended",
    "plugin:@typescript-eslint/recommended",
    "plugin:jsx-a11y/recommended",
    "plugin:prettier/recommended"
  ],
  "overrides": [
    {
      "files": ["src/**/*.[jt]s?(x)"]
    },
    {
      "files": ["src/**/*Slice.ts"],
      "rules": { "no-param-reassign": ["error", { "props": false }] }
    },
    // CSS props - https://emotion.sh/docs/eslint-plugin-react
    // {
    //   "files": ["src/**/*.[jt]s?(x)"],
    //   "rules": {
    //     "react/no-unknown-property": ["error", { "ignore": ["css"] }]
    //   }
    // },
    {
      // 3) Now we enable eslint-plugin-testing-library rules or preset only for matching testing files!
      "files": ["**/__tests__/**/*.[jt]s?(x)", "**/?(*.)+(spec|test).[jt]s?(x)"],
      "extends": ["plugin:testing-library/react"]
    },
    {
      //Cypress overrides
      "files": ["./cypress/**/*.cy.ts"],
      "extends": ["plugin:cypress/recommended"],
      "plugins": ["cypress"],
      "rules": {
        "cypress/no-force": "warn",
        "cypress/assertion-before-screenshot": "warn",
        "cypress/require-data-selectors": "warn",
        "cypress/no-pause": "error"
      },
      "env": {
        "cypress/globals": true
      }
    }
  ],
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaFeatures": {
      "jsx": true
    },
    "ecmaVersion": "latest",
    "sourceType": "module"
  },
  "plugins": ["react", "react-hooks", "@typescript-eslint", "jsx-a11y", "prettier", "cypress"],
  "rules": {
    "react-hooks/rules-of-hooks": "error",
    "react-hooks/exhaustive-deps": "warn",
    "react/prop-types": "off",
    "react/react-in-jsx-scope": "off",
    "@typescript-eslint/explicit-module-boundary-types": "off",
    "@typescript-eslint/no-non-null-assertion": "off",
    // for testing library
    "testing-library/await-async-query": "error",
    "testing-library/no-await-sync-query": "error",
    "testing-library/no-debugging-utils": "warn",
    "testing-library/no-dom-import": "off"
  }
}
